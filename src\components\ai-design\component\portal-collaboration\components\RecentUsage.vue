<template>
  <div class="recent-usage h-[316px]">
    <div class="recent-usage__header text-lg">最近使用·{{ totalCount }}</div>

    <div class="recent-usage__content">
      <div class="recent-usage__grid h-[280px]" v-if="currentPageData.length" :class="{ 'recent-usage__grid--three-columns': isWideScreen }">
        <div
          v-for="item in currentPageData"
          :key="item.id"
          class="recent-usage__item"
          @click="handleItemClick(item)"
        >
          <div class="recent-usage__item-content">
            <div class="recent-usage__item-title mb-[4px] w-full ellipsis text-md">{{ item.subject }}</div>
            <div v-if="item.belongOrg" class="recent-usage__item-org ellipsis text-sm">归属机构:{{ item.belongOrg }}</div>
          </div>
        </div>
      </div>

      <!-- 分页组件 -->
      <div v-if="showPagination && currentPageData.length" class="recent-usage__pagination">
        <div class="pagination">
          <div
            class="pagination__btn w-[22px] h-[22px]"
            :class="{ 'pagination__btn--disabled': currentPage === 1 }"
            @click="prevPage"
          >
            <i class="iconfont ai-icon-zuo" />
          </div>
          <div
            class="pagination__btn w-[22px] h-[22px]"
            :class="{ 'pagination__btn--disabled': currentPage === totalPages }"
            @click="nextPage"
          >
            <i class="iconfont ai-icon-you" />
          </div>
        </div>
      </div>

      <PortalEmptyColumn
        class="empty-column"
        v-if="!currentPageData.length"
        :image="EmptyPendingImg"
        text="暂无数据哦~"
        :width="90"
        :height="90"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import PortalEmptyColumn from '@/components/portalEmptyColumn/index.vue';
import EmptyPendingImg from '@/assets/imgs/empty_pending.png';

interface RecentUsageItem {
  id: string
  subject: string
  belongOrg: string
}

interface Props {
  data?: RecentUsageItem[]
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

const emit = defineEmits<{
  itemClick: [item: RecentUsageItem]
}>()

// 响应式状态
const isWideScreen = ref(false)
const currentPage = ref(1)

// 计算属性
const totalCount = computed(() => props.data.length)
const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
const showPagination = computed(() => totalCount.value > pageSize.value)
const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return props.data.slice(start, end)
})
const pageSize = computed(() => {
  const rowsPerPage = 4;
  const columnsPerPage = isWideScreen.value ? 3 : 2;
  return rowsPerPage * columnsPerPage;
})

// 方法
const checkScreenSize = () => {
  isWideScreen.value = window.innerWidth > 1440
}

const handleItemClick = (item: RecentUsageItem) => {
  emit('itemClick', item)
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// 生命周期
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style scoped lang="less">
.recent-usage {
  &__header {
    border-radius: 12px 12px 0 0;
    background: rgba(255, 255, 255, 0.7);
    padding: 10px 16px 2px;
    font-weight: @font-weight-600;
    line-height: 24px;
  }

  &__content {
    position: relative;
    height: 280px;
    overflow: hidden;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(4, 70px);
    background: rgba(255, 255, 255, 0.7);
    border-radius: 0 0 12px 12px;
    align-items: start;

    &--three-columns {
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(4, 70px);
    }
  }

  &__item {
    padding: 12px 16px;
    cursor: pointer;
    min-width: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    &:hover {
      .recent-usage__item-title {
        color: #4379FF;
      }
    }
  }

  &__item-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
    min-width: 0;
  }

  &__item-title {
    font-weight: @font-weight-600;
    line-height: 22px;
  }

  &__item-org {
    color: rgba(0, 0, 0, 0.6);
    list-style: 20px;
  }

  &__pagination {
    position: absolute;
    bottom: 8px;
    right: 8px;
    display: flex;
    justify-content: flex-end;
    z-index: 10;

  }

  .empty-column {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 0 0 12px 12px;
  }
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;

  &__btn {
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #8E94A2;

    &--disabled {
      cursor: not-allowed;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.33) 19.98%, rgba(255, 255, 255, 0.44) 79.94%);
      color: #D8DADF;
    }
  }
}
</style>
