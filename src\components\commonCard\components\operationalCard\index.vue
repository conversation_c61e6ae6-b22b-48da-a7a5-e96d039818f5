<template>
  <div class="operationalCard">
    <!-- 卡片标题描述 -->
    <PluginDesction :title="cardData?.data?.pluginName" :total="cardData?.data?.pageInfo?.total" />
    <!-- 卡片内容 -->
     <div class="item-box">
      <ListItem v-for="(item, index) in cardData?.data?.result" :key="index" :item="item" />
     </div>
     <!-- 更多操作 -->
     <CheckButton v-if="cardData?.data?.pageInfo?.total > 5" :type="cardData?.data?.moreButtonType" @handleClick="handleClickMore" />

    
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, PropType } from "vue"
import PluginDesction from "../pluginDesction.vue"
import CheckButton from '../check-button.vue'
import ListItem from './listItem.vue'
import type { ChatItem } from '@/types/index';

const props = defineProps({
   chatData: {
     type: Object as PropType<ChatItem>,
     default: () => {}
   },
   cardData: {
    type: Object as PropType<any>,
    default: () => {}
   }
})
const handleClickMore = () => {
  console.log('=====> cardData', props.cardData)
}


</script>
<style lang="less" scoped>
  .operationalCard{
    padding: 0px 2px;
  }
</style>
