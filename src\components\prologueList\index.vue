<template>
  <div class="prologue_wrap">
    <div v-if="astlist.length > 0" class="prologue_shirt" ref="scrollContainer">
      <div
        v-for="(item, index) in astlist"
        :class="{
          prologue_item: true,
          active_prologue_item: index === currentIndex,
        }"
        :key="item.id"
        @mousedown="preventBlur($event)"
        @click="() => sendMsg(item)"
        :ref="(el) => (itemRefs[index] = el)"
      >
        <div class="w-4 h-4 overflow-hidden rounded-full">
          <i class="iconfont ai-icon-xing xing_color" />
        </div>
        <div class="sec_tiDec_com">
          <div class="prologue_title ellipsis">{{ item.name }}</div>
        </div>
      </div>
    </div>
    <CustomEmpty v-else-if="!loading" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import type { TypeResponse } from '@/types/api';
import type { AssistInfo, ChatUserParams } from '@/types/index';
import CustomEmpty from '@/components/empty/index.vue';
// a-b-c
import { Image, message } from 'ant-design-vue';
// 1-2-3
import { getAssistIntroduce } from '@/api/common';
import { useHomeSearch, useTempRunningAssistInfo } from '@/stores/homeSearch';
import { useChatList } from '@/stores/chatList';
import cardInstance from '@/stores/card';

const uChatList = useChatList();
const uTempRanAst = useTempRunningAssistInfo();
const loading = ref<Boolean>(false);
// props
const props = defineProps<{
  toOpertePrologue: (val: 'show' | 'hide') => void;
}>();

const currentIndex = ref<undefined | number>(undefined);
const assistantInfo = ref<any>({});
// 助手列表
const astlist = ref<AssistInfo[]>([]);
const scrollContainer = ref<HTMLElement | null>(null);
const itemRefs = ref<any[]>([]);

// 获取助手列表
const getPrologue = async (id: string) => {
  loading.value = true;
  try {
    const res: TypeResponse = (await getAssistIntroduce(id)) as TypeResponse;
    loading.value = false;
    if (res && res.code === '0') {
      const { prologuePreQuestions = [], ...others } = res.data;
      assistantInfo.value = others;
      if (prologuePreQuestions?.length) {
        astlist.value = [...prologuePreQuestions];
      }
    } else {
      message.error(res.message);
    }
    // console.log('api助手列表______', res);
  } catch (error) {
    loading.value = false;
    console.log('错误', error);
  }
};
// 鼠标事件
const preventBlur = (e: any) => {
  e.preventDefault();
};

// 发送消息
const sendMsg = (item: AssistInfo) => {
  cardInstance.sendMessage(item.name);
};

// 滚动到可视区域
const scrollIntoViewIfNeeded = (index: number | undefined) => {
  if (index !== undefined && itemRefs.value[index]) {
    const itemElement = itemRefs.value[index];
    const container = scrollContainer.value;
    if (itemElement && container) {
      const itemRect = itemElement.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      if (itemRect.top < containerRect.top) {
        itemElement.scrollIntoView({ block: 'nearest' });
      } else if (itemRect.bottom > containerRect.bottom) {
        itemElement.scrollIntoView({ block: 'nearest' });
      }
    }
  }
};

// 键盘事件
const toUpOrDownFnc = (e: KeyboardEvent) => {
  if (e.key == 'ArrowUp') {
    if (currentIndex.value === undefined || currentIndex.value <= 0) {
      currentIndex.value = astlist.value.length - 1;
    } else {
      currentIndex.value--;
    }
  }
  if (e.key == 'ArrowDown') {
    if (currentIndex.value === undefined || currentIndex.value >= astlist.value.length - 1) {
      currentIndex.value = 0;
    } else {
      currentIndex.value++;
    }
  }
  if (e.key == 'Enter') {
    if (currentIndex.value != undefined) {
      e.preventDefault();
      e.stopPropagation();
      currentIndex.value = undefined;
    }
  }
  scrollIntoViewIfNeeded(currentIndex.value);
};

// 操作开场白列表
const toOpertePrologue = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  const prologueValue = target.getAttribute('data-prologue');
  if (prologueValue !== 'prologue') {
    props.toOpertePrologue('hide');
  }
};

// 周期
onMounted(() => {
  // 只获取菜单上得助手id
  const assistantId = uTempRanAst.astInfo?.id;
  // 获取开场白数据和
  if (assistantId) {
    getPrologue(assistantId);
  }
  document.addEventListener('keydown', toUpOrDownFnc);
  document.addEventListener('click', toOpertePrologue);
});
onUnmounted(() => {
  document.removeEventListener('keydown', toUpOrDownFnc);
  document.removeEventListener('click', toOpertePrologue);
});
</script>

<style scoped lang="less">
.prologue_wrap {
  max-height: 240px;
  padding: 8px;
  border-radius: 8px;
  backdrop-filter: blur(100px);
  background: #ffffff;
  box-shadow: 0px 0px 12px 0px #00000014;
  position: relative;
  left: 0%;
  .prologue_shirt {
    height: 100%;
    max-height: 214px;
    overflow-y: auto;
  }
  .prologue_shirt::-webkit-scrollbar {
    width: 4px;
  }
  .prologue_shirt::-webkit-scrollbar-thumb {
    border-radius: 999px;
    background: #ddd;
    -webkit-box-shadow: inset 0 0 2x rgba(0, 0, 0, 0.5);
  }
  .prologue_shirt::-webkit-scrollbar-track {
    -webkit-box-shadow: none;
  }
  .prologue_item {
    // margin-right: 4px;
    height: 38px;
    // margin-bottom: 4px;
    display: flex;
    align-items: center;
    padding: 8px 8px;
    border-radius: 4px;
    .xing_color {
      color: #8692ff;
    }
    .sec_tiDec_com {
      flex: 1;
      display: flex;
      height: 100%;
      align-items: center;
      margin-left: 8px;
      overflow-x: hidden;
      .prologue_title {
        font-family: PingFang SC;
        font-weight: @font-weight-500;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0%;
        color: #000000e5;
      }
    }
  }
  .prologue_item:hover,
  .active_prologue_item {
    background-color: #f6f6f8;
    cursor: pointer;
    border-radius: 8px;
  }
  .actived_prologue_item {
    background: rgba(237, 242, 252, 1);
    border-radius: 8px;
    .prologue_title {
      color: @sky !important;
    }
  }
}
</style>
