/*
 * @Author: 代琪 <EMAIL>
 * @Date: 2025-06-28 12:46:32
 * @LastEditors: 代琪 <EMAIL>
 * @LastEditTime: 2025-07-16 19:01:53
 * @FilePath: \ai-assistant-web\src\components\ai-design\component\components.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: 代琪 <EMAIL>
 * @Date: 2025-06-28 12:46:32
 * @LastEditors: 代琪 <EMAIL>
 * @LastEditTime: 2025-07-16 10:43:16
 * @FilePath: \ai-assistant-web\src\components\ai-design\component\components.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export { default as CoMiHeader } from './comi-header';
export { default as PortalMessage } from './portal-message';
export { default as IntelligentQuestionCount } from './intelligent-question-count';
export { default as IntelligentSearch } from './intelligent-search';
export { default as Recruitment } from './recruitment';
export { default as PortalCollaboration } from './portal-collaboration/index.vue';
export { default as PortalEdoc } from './portal-edoc/index.vue';

// export type { AffixProps } from './affix';
// export { default as Affix } from './affix';

// export type { AnchorProps, AnchorLinkProps } from './anchor';
// export { default as Anchor, AnchorLink } from './anchor';
