<template>
  <div :class="['portal-wrapper relative w-full h-full flex pt-[8px]']">
    <div class="relative content-wrapper flex-1 flex flex-col justify-center items-center" :class="{'v8-content-wrapper': useGlobalStore.globalState.comiContainerName === 'v8'}">
      <!-- <PortalIndexContent
        v-if="!empty"
        :userName="userName"
        :layoutConfig="layoutConfig"
        class=""
      /> -->
      <PortalView class="flex-1" v-if="view" :view="view" :isCopilot="!isPortal || (isPortal && showDobuleScreen)"></PortalView>
      <div
        class="portal-footer-contatiner"
        :class="{
          'empty-box': empty,
          'portal-footer-wrapper': true,
        }"
      >
        <!-- <div v-if="empty">
          <div class="logo flex justify-center items-center">
            <img src="@/assets/imgs/welcome-min.gif" class="w-[100px] h-[100px]" alt="logo" />
          </div>
          <p class="flex justify-center items-center">
            <span class="user-text text-[16px] font-bold">HI {{ userName }}~</span>
            <span class="welcome-text text-[24px] font-bold ml-[8px] gradient-text-welcome"
              >CoMi帮您提高办公效率</span
            >
          </p>
        </div> -->
        <!-- <AiFooter
          class="portal-footer-wrapper"
          :showNewTopicBtn="false"
        /> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { RowNode } from '@/types/portalTypes';
import AiFooter from '@/components/aiFooter/index.vue';
import { defineComponent, onMounted, computed, ref, onUnmounted, inject, defineOptions } from 'vue';
import { useUserInfo } from '@/stores/global';
import { usePortalStore } from '@/stores/portal';
import PortalView from '@/components/ai-design/PortalView.vue';
import { useChatList } from '@/stores/chatList';
import { useGlobal } from '@/stores/global';

const useGlobalStore = useGlobal(); 


defineOptions({
  name: 'PortalIndex',
});

const isPortal = inject('isPortal');

const uChatList = useChatList();
// 右侧布局信息
const layoutConfig = ref<RowNode>();

const portalStore = usePortalStore();

const view = computed(() => {
    return portalStore.view;
});

const showDobuleScreen = computed(() => {
  return uChatList.dynamicData.dobuleScreenData.show;
});

// 左侧菜单
const { userInfo } = useUserInfo();
// 用户名
const userName = computed(() => {
  return useUserInfo().userInfo?.name;
});
const empty = ref(false);



/*
    PortalEmpty 进入无数据 相关 start ------------------------------------------
  */

/*
    公用  start
  */


onUnmounted(() => {});
</script>

<style scoped lang="less">
.portal-wrapper {
  display: flex;
  flex-direction: row;

  .content-wrapper {
    width: 100%;
    .portal-footer-contatiner {
      margin-top: 10px;
      max-width: 824px;
    }
    .portal-footer-wrapper {
      width: 100%;
      position: relative;
      z-index: 3;
    }

    .empty-box {
      font-family: PingFang SC;
      height: 100%;
      padding-top: 110px;
      box-sizing: border-box;

      .logo {
        margin-bottom: 10px;
      }
    }
  }
  .v8-content-wrapper{
    justify-content: space-between;
    padding-top: 56px;
  }
}

.square-bg {
  background-image: none;
  background: #f9fafe;
}
.welcome-text {
  background: linear-gradient(to right, #ab7dfe, #5873f6);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>

<style>
.dialog_bg {
  background: #fafafa;
  background: var(--theme-brand0, #fafafa);
}

.dialog_box {
  z-index: 1000;
}

.dialog_box .common_button {
  min-width: 34px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
}

.dialog_box .common_button.common_button_gray,
.dialog_box .common_button_gray {
  background: #99948c;
  border: solid 1px #99948c;
  color: #fff;
}

.dialog_box .common_button.common_button_gray:hover,
.dialog_box .common_button_gray:hover {
  border: solid 1px #99948c;
  background: #a8a5a0;
  color: #fff;
}

.dialog_shadow {
  top: 5px;
  left: 5px;
  opacity: 0.2;
  filter: alpha(opacity=20);
}

.dialog_main {
  border: 1px solid #e4e4e4;
  background: #fafafa;
  background: var(--theme-brand0, #fafafa);
  box-shadow:
    0 6px 16px -8px rgba(0, 0, 0, 0.08),
    0 9px 28px 0 rgba(0, 0, 0, 0.05),
    0 12px 48px 16px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  border-radius: 2px;
}

.dialog_main .dialog_main_head {
  height: 50px;
  cursor: move;
  box-sizing: border-box;
  border-bottom: 1px solid #f1f1f1;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
  background-color: #fff;
}

.dialog_main .dialog_main_head .dialog_title {
  margin: 0 10px 0 20px;
  line-height: 49px;
  font-size: 14px;
  font-weight: 400;
}

.dialog_main .dialog_close,
.dialog_main .dialog_close_msg {
  cursor: pointer;
  position: absolute;
  width: 15px;
  height: 15px;
  margin: 0;
  z-index: 1000;
  right: 20px;
  top: 17.5px;
}

.dialog_main .dialog_main_body .dialog_main_content {
  z-index: 899;
}

.dialog_main .dialog_main_body .dialog_main_iframe {
  z-index: 900;
  position: absolute;
  background: url('/seeyon/skin/dist/images/loading.gif?V=V8_3_u7tuf0x_732110') center no-repeat
    #000;
  opacity: 0.25;
  filter: alpha(opacity=25);
  display: none;
}

.dialog_main .dialog_main_content_html {
  font-size: 12px;
}

.dialog_main_footer {
  height: 52px;
  clear: both;
  box-sizing: border-box;
  border-top: 1px solid #f1f1f1;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.dialog_close:hover,
.dialog_close_msg:hover,
.layui-layer-close1:hover {
  color: inherit;
}

.dialog_min {
  background: url('/seeyon/skin/dist/images/control_icon.png?V=V8_3_u7tuf0x_732110')
    no-repeat -16px -16px;
  _background: url('/seeyon/skin/dist/images/control_icon.gif?V=V8_3_u7tuf0x_732110')
    no-repeat -16px -16px;
  cursor: pointer;
  width: 16px;
  height: 16px;
  margin: 15px 15px 0 0;
}

.dialog_max {
  background: url('/seeyon/skin/dist/images/control_icon.png?V=V8_3_u7tuf0x_732110')
    no-repeat -16px -16px;
  _background: url('/seeyon/skin/dist/images/control_icon.gif?V=V8_3_u7tuf0x_732110')
    no-repeat -16px -16px;
  cursor: pointer;
  width: 16px;
  height: 16px;
  margin: 15px 15px 0 0;
  background: url('/seeyon/skin/dist/images/control_icon.png?V=V8_3_u7tuf0x_732110')
    no-repeat -32px -16px;
  _background: url('/seeyon/skin/dist/images/control_icon.gif?V=V8_3_u7tuf0x_732110')
    no-repeat -32px -16px;
}

.dialog_auto {
  background: url('/seeyon/skin/dist/images/control_icon.png?V=V8_3_u7tuf0x_732110')
    no-repeat -16px -16px;
  _background: url('/seeyon/skin/dist/images/control_icon.gif?V=V8_3_u7tuf0x_732110')
    no-repeat -16px -16px;
  cursor: pointer;
  width: 16px;
  height: 16px;
  margin: 15px 15px 0 0;
  background: url('/seeyon/skin/dist/images/control_icon.png?V=V8_3_u7tuf0x_732110')
    no-repeat -48px -16px;
  _background: url('/seeyon/skin/dist/images/control_icon.gif?V=V8_3_u7tuf0x_732110')
    no-repeat -48px -16px;
}

html #layuicss-skinlayercss {
  display: none;
  position: absolute;
  width: 1989px;
}

.layui-layer,
.layui-layer-shade {
  position: fixed;
  _position: absolute;
  pointer-events: auto;
}

.layui-layer-shade {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.layui-layer {
  -webkit-overflow-scrolling: touch;
}

.layui-layer {
  top: 150px;
  left: 0;
  margin: 0;
  padding: 0;
  background-color: #fff;
  -webkit-background-clip: content;
  box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

.layui-layer-close {
  position: absolute;
}

.layui-layer-content {
  position: relative;
  overflow: hidden;
}

.layui-layer-content fieldset {
  border: 1px solid #ccc;
  font-size: 12px;
  padding: 5px;
}

.layui-layer-border {
  border: 1px solid #b2b2b2;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
}

.layui-layer-load {
  background: url('/seeyon/skin/dist/images/dialog/default/loading-1.gif?V=V8_3_u7tuf0x_732110')
    #eee center center no-repeat;
}

.layui-layer-ico {
  background: url('/seeyon/skin/dist/images/dialog/default/icon.png?V=V8_3_u7tuf0x_732110')
    no-repeat;
}

.layui-layer-btn a,
.layui-layer-dialog .layui-layer-ico,
.layui-layer-setwin a {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  vertical-align: top;
}

.layui-layer-move {
  display: none;
  position: fixed;
  *position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  cursor: move;
  opacity: 0;
  filter: alpha(opacity=0);
  background-color: #fff;
  z-index: 2000;
}

.layui-layer-resize {
  position: absolute;
  width: 15px;
  height: 15px;
  right: 0;
  bottom: 0;
  cursor: se-resize;
}

.layui-layer {
  -webkit-animation-fill-mode: none;
  animation-fill-mode: none;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
}
@-webkit-keyframes bounceIn {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    -ms-transform: scale(0.5);
    transform: scale(0.5);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.layer-anim {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

@-webkit-keyframes zoomInDown {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.1) translateY(-2000px);
    transform: scale(0.1) translateY(-2000px);
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }

  60% {
    opacity: 1;
    -webkit-transform: scale(0.475) translateY(60px);
    transform: scale(0.475) translateY(60px);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}

@keyframes zoomInDown {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.1) translateY(-2000px);
    -ms-transform: scale(0.1) translateY(-2000px);
    transform: scale(0.1) translateY(-2000px);
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }

  60% {
    opacity: 1;
    -webkit-transform: scale(0.475) translateY(60px);
    -ms-transform: scale(0.475) translateY(60px);
    transform: scale(0.475) translateY(60px);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}

.layer-anim-01 {
  -webkit-animation-name: zoomInDown;
  animation-name: zoomInDown;
}

@-webkit-keyframes fadeInUpBig {
  0% {
    opacity: 0;
    -webkit-transform: translateY(2000px);
    transform: translateY(2000px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInUpBig {
  0% {
    opacity: 0;
    -webkit-transform: translateY(2000px);
    -ms-transform: translateY(2000px);
    transform: translateY(2000px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}

.layer-anim-02 {
  -webkit-animation-name: fadeInUpBig;
  animation-name: fadeInUpBig;
}

@-webkit-keyframes zoomInLeft {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.1) translateX(-2000px);
    transform: scale(0.1) translateX(-2000px);
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }

  60% {
    opacity: 1;
    -webkit-transform: scale(0.475) translateX(48px);
    transform: scale(0.475) translateX(48px);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}

@keyframes zoomInLeft {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.1) translateX(-2000px);
    -ms-transform: scale(0.1) translateX(-2000px);
    transform: scale(0.1) translateX(-2000px);
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }

  60% {
    opacity: 1;
    -webkit-transform: scale(0.475) translateX(48px);
    -ms-transform: scale(0.475) translateX(48px);
    transform: scale(0.475) translateX(48px);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}

.layer-anim-03 {
  -webkit-animation-name: zoomInLeft;
  animation-name: zoomInLeft;
}

@-webkit-keyframes rollIn {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-100%) rotate(-120deg);
    transform: translateX(-100%) rotate(-120deg);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateX(0) rotate(0);
    transform: translateX(0) rotate(0);
  }
}

@keyframes rollIn {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-100%) rotate(-120deg);
    -ms-transform: translateX(-100%) rotate(-120deg);
    transform: translateX(-100%) rotate(-120deg);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateX(0) rotate(0);
    -ms-transform: translateX(0) rotate(0);
    transform: translateX(0) rotate(0);
  }
}

.layer-anim-04 {
  -webkit-animation-name: rollIn;
  animation-name: rollIn;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.layer-anim-05 {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

@-webkit-keyframes shake {
  0%,
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
}

@keyframes shake {
  0%,
  100% {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translateX(-10px);
    -ms-transform: translateX(-10px);
    transform: translateX(-10px);
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translateX(10px);
    -ms-transform: translateX(10px);
    transform: translateX(10px);
  }
}

.layer-anim-06 {
  -webkit-animation-name: shake;
  animation-name: shake;
}

@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-webkit-keyframes bounceOut {
  100% {
    opacity: 0;
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
  }

  30% {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }

  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes bounceOut {
  100% {
    opacity: 0;
    -webkit-transform: scale(0.7);
    -ms-transform: scale(0.7);
    transform: scale(0.7);
  }

  30% {
    -webkit-transform: scale(1.05);
    -ms-transform: scale(1.05);
    transform: scale(1.05);
  }

  0% {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.layer-anim-close {
  -webkit-animation-name: bounceOut;
  animation-name: bounceOut;
  -webkit-animation-duration: 0.2s;
  animation-duration: 0.2s;
}

.layui-layer-title {
  padding: 0 80px 0 20px;
  height: 42px;
  line-height: 42px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  color: #121212;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: #f8f8f8;
}

.layui-layer-setwin {
  position: absolute;
  right: 20px;
  top: 0;
  font-size: 0;
  line-height: 50px;
  height: 50px;
}

.layui-layer-setwin a {
  position: relative;
  width: 16px;
  height: 16px;
  margin-left: 10px;
  font-size: 14px;
  color: #8c8c8c;
}

.layui-layer-setwin .layui-layer-min cite {
  position: absolute;
  width: 14px;
  height: 2px;
  left: 0;
  top: 50%;
  margin-top: -1px;
  background-color: #2e2d3c;
  cursor: pointer;
  _overflow: hidden;
}

.layui-layer-setwin .layui-layer-min:hover cite {
  background-color: #2d93ca;
  background-color: var(--theme-brand5, #2d93ca);
}

.layui-layer-setwin .layui-layer-max {
  background-position: -32px -40px;
}

.layui-layer-setwin .layui-layer-max:hover {
  background-position: -16px -40px;
}

.layui-layer-setwin .layui-layer-maxmin {
  background-position: -65px -40px;
}

.layui-layer-setwin .layui-layer-maxmin:hover {
  background-position: -49px -40px;
}

.layui-layer-setwin .layui-layer-close1 {
  cursor: pointer;
}

.layui-layer-setwin .layui-layer-close2 {
  position: absolute;
  right: -28px;
  top: -28px;
  width: 30px;
  height: 30px;
  margin-left: 0;
  background-position: -149px -31px;
  *right: -18px;
  _display: none;
}

.layui-layer-setwin .layui-layer-close2:hover {
  background-position: -180px -31px;
}

.layui-layer-dialog {
  min-width: 260px;
}

.layui-layer-dialog .layui-layer-content {
  position: relative;
  padding: 20px;
  line-height: 24px;
  word-break: break-all;
  overflow: hidden;
  font-size: 14px;
  overflow-x: hidden;
  overflow-y: auto;
}

.layui-layer-dialog .layui-layer-content .layui-layer-ico {
  position: absolute;
  top: 16px;
  left: 15px;
  _left: -40px;
  width: 30px;
  height: 30px;
}

.layui-layer-ico1 {
  background-position: -30px 0;
}

.layui-layer-ico2 {
  background-position: -60px 0;
}

.layui-layer-ico3 {
  background-position: -90px 0;
}

.layui-layer-ico4 {
  background-position: -120px 0;
}

.layui-layer-ico5 {
  background-position: -150px 0;
}

.layui-layer-ico6 {
  background-position: -180px 0;
}

.layui-layer-rim {
  border: 6px solid #8d8d8d;
  border: 6px solid rgba(0, 0, 0, 0.3);
  border-radius: 5px;
  box-shadow: none;
}

.layui-layer-msg {
  min-width: 180px;
  border: 1px solid #d3d4d3;
  box-shadow: none;
}

.layui-layer-hui {
  min-width: 100px;
  background-color: #000;
  filter: alpha(opacity=60);
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border: 0;
}

.layui-layer-hui .layui-layer-content {
  padding: 12px 25px;
  text-align: center;
}

.layui-layer-dialog .layui-layer-padding {
  padding: 20px 20px 20px 55px;
  text-align: left;
}

.layui-layer-page .layui-layer-content {
  position: relative;
  overflow: auto;
}

.layui-layer-page .layui-layer-btn {
  padding-top: 10px;
}

.layui-layer .layui-layer-btn {
  height: 31px;
  overflow: hidden;
  border-top: 1px solid #f1f1f1;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  box-sizing: content-box;
  text-align: right;
  padding: 10px 10px 10px 0;
  font-size: 12px;
}

.layui-layer-nobg {
  background: 0;
}

.layui-layer-iframe iframe {
  display: block;
  width: 100%;
}

.layui-layer-loading {
  border-radius: 100%;
  background: 0;
  box-shadow: none;
  border: 0;
}

.layui-layer-loading .layui-layer-content {
  width: 60px;
  height: 24px;
  background: url(../images/dialog/default/loading-0.gif?V=V8_3_u7tuf0x_732110) no-repeat;
}

.layui-layer-loading .layui-layer-loading1 {
  width: 37px;
  height: 37px;
  background: url(../images/dialog/default/loading-1.gif?V=V8_3_u7tuf0x_732110) no-repeat;
}

.layui-layer-ico16,
.layui-layer-loading .layui-layer-loading2 {
  width: 32px;
  height: 32px;
  background: url(../images/dialog/default/loading-2.gif?V=V8_3_u7tuf0x_732110) no-repeat;
}

.layui-layer-tips {
  background: 0;
  box-shadow: none;
  border: 0;
}

.layui-layer-tips .layui-layer-content {
  position: relative;
  line-height: 22px;
  min-width: 12px;
  padding: 5px 10px;
  font-size: 12px;
  _float: left;
  border-radius: 2px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  background-color: #000;
  color: #fff;
}

.layui-layer-tips .layui-layer-close {
  right: -2px;
  top: -1px;
}

.layui-layer-tips i.layui-layer-TipsG {
  position: absolute;
  width: 0;
  height: 0;
  border-width: 8px;
  border-color: transparent;
  border-style: dashed;
  *overflow: hidden;
}

.layui-layer-tips i.layui-layer-TipsB,
.layui-layer-tips i.layui-layer-TipsT {
  left: 5px;
  border-right-style: solid;
  border-right-color: #000;
}

.layui-layer-tips i.layui-layer-TipsT {
  bottom: -8px;
}

.layui-layer-tips i.layui-layer-TipsB {
  top: -8px;
}

.layui-layer-tips i.layui-layer-TipsL,
.layui-layer-tips i.layui-layer-TipsR {
  top: 1px;
  border-bottom-style: solid;
  border-bottom-color: #000;
}

.layui-layer-tips i.layui-layer-TipsR {
  left: -8px;
}

.layui-layer-tips i.layui-layer-TipsL {
  right: -8px;
}

.layui-layer-iconext {
  background: url(../default/images/dialog/default/icon-ext.png?V=V8_3_u7tuf0x_732110) no-repeat;
}

.layui-layer-prompt .layui-layer-input {
  display: block;
  width: 220px;
  height: 30px;
  margin: 0 auto;
  line-height: 30px;
  padding: 0 5px;
  border: 1px solid #ccc;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.1) inset;
  color: #333;
}

.layui-layer-prompt textarea.layui-layer-input {
  width: 300px;
  height: 100px;
  line-height: 20px;
}

.layui-layer-prompt .layui-layer-content {
  padding: 20px;
}

.layui-layer-prompt .layui-layer-btn {
  padding-top: 0;
}

.layui-layer-tab {
  box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.4);
}

.layui-layer-tab .layui-layer-title {
  padding-left: 0;
  border-bottom: 1px solid #ccc;
  background-color: #eee;
  overflow: visible;
}

.layui-layer-tab .layui-layer-title span {
  position: relative;
  float: left;
  min-width: 80px;
  max-width: 260px;
  padding: 0 20px;
  text-align: center;
  cursor: default;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.layui-layer-tab .layui-layer-title span.layui-layer-tabnow {
  height: 43px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  background-color: #fff;
  z-index: 10;
}

.layui-layer-tab .layui-layer-title span:first-child {
  border-left: 0;
}

.layui-layer-tabmain {
  line-height: 24px;
  clear: both;
}

.layui-layer-tabmain .layui-layer-tabli {
  display: none;
}

.layui-layer-tabmain .layui-layer-tabli.xubox_tab_layer {
  display: block;
}

.xubox_tabclose {
  position: absolute;
  right: 10px;
  top: 5px;
  cursor: pointer;
}

.layui-layer-photos {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
}

.layui-layer-photos .layui-layer-content {
  overflow: hidden;
  text-align: center;
}

.layui-layer-photos .layui-layer-phimg img {
  position: relative;
  width: 100%;
  display: inline-block;
  *display: inline;
  *zoom: 1;
  vertical-align: top;
}

.layui-layer-imgbar,
.layui-layer-imguide {
  display: none;
}

.layui-layer-imgnext,
.layui-layer-imgprev {
  position: absolute;
  top: 50%;
  width: 27px;
  _width: 44px;
  height: 44px;
  margin-top: -22px;
  outline: 0;
  blr: expression(this.onFocus=this.blur());
}

.layui-layer-imgprev {
  left: 10px;
  background-position: -5px -5px;
  _background-position: -70px -5px;
}

.layui-layer-imgprev:hover {
  background-position: -33px -5px;
  _background-position: -120px -5px;
}

.layui-layer-imgnext {
  right: 10px;
  _right: 8px;
  background-position: -5px -50px;
  _background-position: -70px -50px;
}

.layui-layer-imgnext:hover {
  background-position: -33px -50px;
  _background-position: -120px -50px;
}

.layui-layer-imgbar {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 32px;
  line-height: 32px;
  background-color: rgba(0, 0, 0, 0.8);
  background-color: #000\9;
  filter: Alpha(opacity=80);
  color: #fff;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 0;
}

.layui-layer-imgtit * {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  vertical-align: top;
  font-size: 12px;
}

.layui-layer-imgtit a {
  max-width: 65%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: #fff;
}

.layui-layer-imgtit a:hover {
  color: #fff;
  text-decoration: underline;
}

.layui-layer-imgtit em {
  padding-left: 10px;
  font-style: normal;
}

@media screen and (max-width: 1100px) {
  .layui-layer-iframe {
    -webkit-overflow-scrolling: touch;
  }
}

.layui-layer {
  box-shadow:
    0 6px 16px -8px rgba(0, 0, 0, 0.08),
    0 9px 28px 0 rgba(0, 0, 0, 0.05),
    0 12px 48px 16px rgba(0, 0, 0, 0.03);
}

.layui-layer-title {
  padding: 0 80px 0 20px;
  height: 49px;
  line-height: 50px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: content-box;
  border-bottom: 1px solid #f1f1f1;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.layui-layer-setwin .layui-layer-close1:hover {
  opacity: 1;
}

.layui-layer .layui-layer-btn .common_button {
  min-width: 34px;
  text-align: center;
  cursor: pointer;
}

.layui-layer.layer-noHeader {
  border-radius: 5px;
  box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

#layui-layer-btn-dialog-reset-button {
  float: left;
  margin-left: 20px;
}
.layui-layer-title {
  background: #fff;
  color: #000;
}

.layui-layer .layui-layer-btn {
  background: #fff;
  color: #333;
}
.dialog_main_footer {
  background: #fff;
}
.dialog_close,
.dialog_close_msg,
.layui-layer-close1 {
  position: absolute;
  cursor: pointer;
  width: 15px;
  height: 15px;
  background: url('/seeyon/skin/dist/images/dialog_close.png?V=V8_0_2024_07_05') no-repeat;
  margin: 0;
  z-index: 1000;
  right: 10px;
  top: 18px;
}
</style>
