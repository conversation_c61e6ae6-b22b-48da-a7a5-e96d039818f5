<template>
  <!-- 附件项容器 -->
  <div class="flex flex-col gap-[8px]">
    <Attachment
      v-for="(attachment, index) of list"
      :key="index"
      :attachment="attachment"
    />
  </div>
</template>

<script setup lang="ts">
// 引入 Vue 的计算属性和 PropType 工具类型
import type { PropType } from 'vue'
import { toRefs, computed } from 'vue'
import Attachment  from './attachment.vue';
import type { CardResponse } from '../../types';
// 定义附件数据结构接口
interface Attachment {
  id?: string;             // 附件可选的唯一 ID
  name: string;       // 文件名 (必需)
  url?: string;            // 可选的下载链接
  type?: string;           // 可选的文件类型提示
}

// 定义组件的 Props
const props = defineProps({
  cardData: {
    type: Object as PropType<CardResponse>,
    required: true,
  }
});
const { cardData } = toRefs(props)

const list = computed(() => {
  if(cardData.value.data.result) {
    return cardData.value.data.result[0] && cardData.value.data.result[0]?.renderInfo.data
  }
  return []
})




</script>

<style scoped lang="less">

</style>
