<template>
  <div
    :class="[
      'relative',
      'flex',
      'w-full',
      'h-full',
      'overflow-hidden',
      'bg-setting',
      (isFullScreen && isPortal) ? 'fullscreen_container' : 'normal_container',
      hasChatList ? 'fullscreen_container_gary' : '',
    ]"
    :style="{
      backgroundImage: isPortal ? fullBackImg : '',
    }"
    ref="containerRef"
  >
    <!-- minWidth: setMinWidth + 'px', -->
    <!-- <LeftMenu ref="leftMenuRef" @toggleCollapsed="toggleCollapsed" v-if="isFullScreen" /> -->
    <CommonMenu
      ref="leftMenuRef"
      @toggleCollapsed="toggleCollapsed"
      v-if="isPortal"
      v-viewMode="['1.0','1.1','A8','A9']"
      :menuState="'portalExpand'"
    />
    <!-- 门户模式 -->
    <div
      :class="[
        'w-0',
        'float-left',
        'flex',
        'flex-1',
        'flex-col',
        'flex-shrink-0',
        'relative',
        { squarebg: isFullScreenSquare },
      ]"
      v-if="isPortal"
    >
      <div class="content-wrap">
        <section class="content-wrap-left">
          <div
            class="flex-1 overflow-y-hidden"
            :class="{
              'dobule-screen-content': showDobuleScreen,
              content: !showDobuleScreen,
              'dobule-screen-content-full': dobuleScreenIsFull,
              noPaddingTop: canBack || !isFullScreen || nPortalTopPadding,
              '2xl:max-w-[1162px] xl:max-w-[824px] lg:max-w-[824px] md:max-w-[824px] sm:max-w-[824px]': !showDobuleScreen
            }"
          >
            <DobuleScreenContainer v-show="showDobuleScreen" />
            <div
              class="w-full h-full flex flex-col  chat-wrap  px-3"
              v-show="!dobuleScreenIsFull"
            >
              <div class="container-wrap flex flex-1">
                <keep-alive :exclude="['HistoryList', 'AssistantSquare', 'TopMenu', 'SearchInput']">
                  <component :is="componentName" />
                </keep-alive>
              </div>
              <AiFooter
                v-if="isShowFooter && reloadFooter"
                :showNewTopicBtn="showNewTopicBtn"
              />
            </div>
          </div>
        </section>

      </div>
    </div>
    <!-- 侧边栏模式 -->
    <div
      v-if="!isPortal"
      :class="[
        'w-0',
        'float-left',
        'flex',
        'flex-1',
        'flex-row',
        'flex-shrink-0',
        'relative',
        'justify-end',
        'sider-container'
      ]"
    >
      <div
        class="silder-left"
        :style="{ width: sliderLeftWidth + 'px' }"
      >
        <!-- <div
        class="silder-line"
        :class="{ 'silder-line-active': isDragging }"
      ></div> -->
        <!-- :class="{ 'no-transition': isDragging }" -->
        <!-- @mousedown="startDrag" -->
        <DobuleScreenContainer />
      </div>
      <div
        class="silder-right bg-setting"
        :style="{
          backgroundImage: !isPortal ? fullBackImg : '',
        }"
      >

        <TopMenu></TopMenu>
        <div class="content-wrap">
          <section class="content-wrap-left">
            <div
              class="flex flex-1 overflow-y-hidden flex-col px-3"
              v-show="!dobuleScreenIsFull"
            >
              <div class="container-wrap flex flex-1">
                <component :is="componentName" />
              </div>
              <AiFooter
                v-if="isShowFooter && reloadFooter"
                :showNewTopicBtn="showNewTopicBtn"
              />
            </div>
          </section>
          <div class="content-wrap-right">
            <CommonMenu
              ref="leftMenuRef"
              @toggleCollapsed="toggleCollapsed"
              :menuState="'silder'"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, provide, inject, watch, nextTick } from 'vue';
import { getGeneralAssist } from '@/api/common';
import { useGeneralAsit, useGlobal, useUserInfo, useMenu, useCustomData } from '@/stores/global';
import BarProtal from '@/assets/imgs/bar-protal.png'; // 侧边栏工作台
import BarBg from '@/assets/imgs/bar-bg.png'; // 侧边栏对话
import SeachBg from '@/assets/imgs/search-bg.jpg'; // 智能搜索
import DialogDubleBg from '@/assets/imgs/dialog-duble-bg.png'; // 对话和双屏模式
import PortalBg from '@/assets/imgs/portal_bg.jpg'; // 工作台
import SquareBg from '@/assets/imgs/square-bg.jpg'; // 智能体广场
import CreationBg from '@/assets/imgs/creation-bg.jpg'; // 智能创作
import QuestionNumBg from '@/assets/imgs/question-num-bg.jpg'; // 智能问数
import FullMenuBg from '@/assets/imgs/full-menu.png'; // 全量菜单
import { usePortalStore } from '@/stores/portal';
import TopMenu from '@/components/topMenu/index.vue';
import RightMenu from '@/components/rightMenu/index.vue';
import FullScreenHeader from '@/components/fullscreenHeader/index.vue';
import { getUserAvatar, getUserInfo } from '@/api/portal';
import AiDialogue from '@/components/aiDialogue/index.vue';
import AssistantSquare from '@/components/assistantSquare/index.vue';
import HistoryList from '@/components/historyList/index.vue';
import PortalIndex from '@/components/portalIndex/index.vue';
import { useTempRunningAssistInfo } from '@/stores/homeSearch';
import { useStateRouter, useStateRouteGuards } from '@/stores/stateRouter';
import ComiSettingContent from '@/components/comiSetting/content.vue';
import BackBtn from '@/components/common/backBtn/index.vue';
import ComiAbout from '@/components/comiSetting/about.vue';
import DobuleScreenContainer from '@/components/dobuleScreenContainer/index.vue';
import FullMenu from '@/components/fullMenu/index.vue';

import { useChatList } from '@/stores/chatList';
import CommonMenu from '@/components/common/menu/index.vue';
import PortalCreate from '@/components/portalCreate/index.vue';
import KnowledgeSource from '@/components/knowledgeSource/index.vue';
import { getQueryString } from '@/utils/common';
import { getUsualAssistList } from '@/api/usualAssistant';
import LeftMenu from '@/components/leftMenu/index.vue';
import IntelligentQuestionCount from '@/components/ai-design/component/intelligent-question-count/index.vue';
import IntelligentSearch from '@/components/ai-design/component/intelligent-search/index.vue';
import { commonBusinessClass } from '@/utils/commonBusinessClass';
import { throttle } from '@/utils/tool';
import AiFooter from '@/components/aiFooter/index.vue';
import Recruitment from '@/components/ai-design/component/recruitment/index.vue';
import PortalEdoc from '@/components/ai-design/component/portal-edoc/index.vue';
import PortalCollaboration from '@/components/ai-design/component/portal-collaboration/index.vue';

const isPortal = getQueryString('isPortal') === 'true' ? true : false;
const reloadFooter = ref(true);

const chatListStore = useChatList();
const sdkInstance = inject('sdkInstance') as any;

// 助手
const uTmpAstIfo = useTempRunningAssistInfo();
const stateRouter = useStateRouter();
const useGlobalStore = useGlobal();
const uMenu = useMenu();
const useCustomDataStore = useCustomData();

const uGeneralAsit = useGeneralAsit();
const { setUserInfo } = useUserInfo();
const {initConfig} = usePortalStore();
if (isPortal) {
  useGlobalStore.changeState('isFullScreen', true);
  // 初始化引导页
  setTimeout(() => {
    window.guideAPI?.init({ keys: ['step1', 'step3', 'step5', 'step6', 'step2'], itemKey: 'ai' });
    // window.guideAPI?.init({ keys: ['step1', 'step2', 'step3', 'step4', 'step5', 'step6'], itemKey: 'ai' });
  }, 300)
} else {
  // 初始化引导页
  setTimeout(() => {
    window.guideAPI?.init({ keys: [ 'step1', 'step7', 'step6', 'step0', 'step2'], itemKey: 'ai-copilot' });
    // window.guideAPI?.init({ keys: ['step0', 'step1', 'step7', 'step6', 'step2'], itemKey: 'ai-copilot' });
  }, 300)
}
provide('isPortal', isPortal);
provide('isCopilot', !isPortal);

const containerRef = ref<HTMLElement | null>(null);
const leftMenuRefWidth = ref(98);

// 拖拽相关变量
const sliderLeftWidth = ref(0); // 初始宽度为45%
const isDragging = ref(false);
const startX = ref(0);
const startWidth = ref(0);
const nPortalTopPadding = computed(() => {
  return uMenu.currentMenuInfo?.id === 'comi' && isPortal && chatListStore.dynamicData.allCardData.length === 0;
});

// 助手跳转标识
const hasAssistantRedirect = ref(false);

// 侧边栏全屏
const isFullScreen = computed(() => {
  return useGlobalStore.globalState.isFullScreen;
});

// 是否有对话数据
const hasChatList = computed(() => {
  const hasData = chatListStore.dynamicData.allCardData.length > 0;
  return hasData;
});

// 是否显示返回按钮
const canBack = computed(() => {
  return (
    isFullScreen.value &&
    stateRouter.currentRoute.context?.canBack &&
    !chatListStore.dynamicData.dobuleScreenData.show
  );
});

const currentMenuKey = computed(() => {
  return uMenu.currentMenuInfo?.id || 'comi';
});

// 双屏知识源是否全屏
const dobuleScreenIsFull = computed(() => {
  return chatListStore.dynamicData.dobuleScreenIsFull;
});


// 设置内容最小宽度
const setMinWidth = computed(() => {
  let contentMinWidth = 358;
  if (isFullScreen.value) {
    return 160 + leftMenuRefWidth.value + contentMinWidth;
  } else {
    return 450;
  }
});


// import BarBg from '@/assets/imgs/bar-bg.png'; // 侧边栏对话
// import BarProtal from '@/assets/imgs/bar-protal.png'; // 侧边栏工作台
// 设置背景颜色
const fullBackImg = computed(() => {
  const { currentMenuInfo } = uMenu as any;
  const currentMenuId = currentMenuInfo?.id;
  let bg = '';

  if (isPortal) {
    switch (currentMenuId) {
      case 'comi':
        if(chatListStore.dynamicData.allCardData.length > 0 || useCustomDataStore.customDataObj?.collApproveData){
          bg = DialogDubleBg;
        } else {
          bg = PortalBg;
        }
        break;
      case 'qa':
        bg = QuestionNumBg;
        break;
      case 'office':
        bg = SeachBg;
        break;
      case 'create':
      case 'OA-Assistant-collaboration':
      case 'OA-Assistant-edoc':
      case 'OA-Assistant-recruitment':
        bg = CreationBg;
        break;
      case 'square':
        bg = SquareBg;
        break;
      case 'fullMenu':
        bg = FullMenuBg;
        break;
      default:
        bg = DialogDubleBg;
        break;
    }
  } else {
    switch (currentMenuId) {
      case 'comi':
        if (chatListStore.dynamicData.allCardData.length > 0 || useCustomDataStore.customDataObj?.collApproveData) {
          bg = BarBg;
        } else {
          bg = BarProtal;
        }
        break;
      default:
        bg = BarBg;
        break;
    }
  }

  return `url(${bg})`;
  // return `url(${ClosePic})`
})

// 展示双屏
const showDobuleScreen = computed(() => {
  if (isPortal && chatListStore.dynamicData.dobuleScreenData.show) {
    setTimeout(() => {
      window.guideAPI?.init({ keys: ['step8'], itemKey: 'ai-container' });
    }, 300);
  }
  return chatListStore.dynamicData.dobuleScreenData.show;
});


// 监听双屏状态变化，更新左侧宽度
watch(() => showDobuleScreen.value, (newValue) => {
  // 同步双屏状态到SDK
  if (sdkInstance && sdkInstance.setExpandDoubleContainer) {
    sdkInstance.setExpandDoubleContainer(newValue);
  }

  if (isPortal) return;
  if (newValue) {
    const topWindow = window.top || window;
    sliderLeftWidth.value = topWindow.innerWidth*0.8 - 450;
    setTimeout(()=>{
      window.guideAPI?.init({ keys: ['step9'], itemKey: 'ai-container'});
    }, 300);
  } else {
    // 当双屏隐藏时，设置为0
    sliderLeftWidth.value = 0;
    const businessClass = commonBusinessClass();
    if (businessClass && typeof businessClass.expand === 'function') {
      businessClass.expand('0', '450px');
    }
  }
}, { immediate: true }); // immediate: true 确保在初始化时就执行一次

// 是否是智能体，用于区分菜单智能体和非智能体
const isAsit = computed(() => {
  return uMenu.currentMenuInfo?.isAsit;
});

// 主内容区域
const componentName = computed(() => {
  console.log('currentMenuKey.value', isAsit.value);
  switch (currentMenuKey.value) {
    case 'comi': // 超级助手
      // case 'office':
      // case 'qa':
      // 如果是全屏，无话记录和历史会话，显示门户组件
      // if (
      //   isFullScreen.value &&
      //   !hasChatList.value &&
      //   !chatListStore.dynamicData.historyModel &&
      //   !showDobuleScreen.value
      // ) {
      //   return PortalIndex;
      // }
      return AiDialogue;
    case 'office':
      // 智能搜索
      if (!hasChatList.value && !showDobuleScreen.value && !isAsit.value) {
        return IntelligentSearch;
      }
      return AiDialogue;
    case 'qa':
      // 智能问数
      if (!hasChatList.value && !showDobuleScreen.value && !isAsit.value) {
        return IntelligentQuestionCount;
      }
      return AiDialogue;
    case 'square': // 智能体
      return AssistantSquare;
    case 'history': // 历史会话
      return HistoryList;
    case 'create': // 智能创作
      return PortalCreate;
    case 'setting':
      return ComiSettingContent;
    case 'fullMenu':
      if(!showDobuleScreen.value) {
        return FullMenu;
      }
      return AiDialogue;
    case 'OA-Assistant-recruitment':
      return Recruitment;
    case 'OA-Assistant-edoc':
      return PortalEdoc;
    case 'OA-Assistant-collaboration':
      if (!hasChatList.value && !showDobuleScreen.value) {
        return PortalCollaboration;
      }
      return AiDialogue;
    // case 'about':
    //   return ComiAbout;
    default:
      return AiDialogue;
  }
});
//是否显示发起新对话
const showNewTopicBtn = computed(() => {
  // if(componentName)
  const name = componentName.value.name;
  if ((currentMenuKey.value === 'comi' && !hasChatList.value && isPortal) || name === 'IntelligentQuestionCount' || name === 'IntelligentSearch' || name === 'PortalCollaboration') {
    return false;
  } else {
    return true;
  }
});
//是否显示输入框组件
const isShowFooter = computed(() => {
  const name = componentName.value.name;
  if (name === 'IntelligentQuestionCount' || name === 'IntelligentSearch' || name === 'AiDialogue' || name === 'PortalCollaboration') {
    return true;
  } else {
    return false;
  }
});
watch(() => currentMenuKey.value, (newVal) => {
  useCustomDataStore.setCustomData('collApproveData', null);
  reloadFooter.value = false;
  nextTick(() => {
    reloadFooter.value = true;
  })
})
// 全屏智能体
const isFullScreenSquare = computed(() => {
  return isFullScreen.value && currentMenuKey.value === 'square';
});

// 非全屏 且非智能体页面 且非历史会话页面 展示顶部菜单
const showTopMenu = computed(() => {
  return !isFullScreen.value && !isPortal;
});

// 重定向助手  TODO: 这一段逻辑后续抽出来形成公共方法
const redirectToAssit = async (assistId?: string, defaultSendMsg?: boolean, callback?: any, showProlog?: boolean) => {
  if (assistId || (sdkInstance?.preConfig && sdkInstance.preConfig.defaultAssistId)) {
    // 标记有助手跳转
    hasAssistantRedirect.value = true;

    const defaultAssistId = assistId || sdkInstance.preConfig.defaultAssistId;
    // 开始重定向，如果已经是自己了，不再执行
    if (currentMenuKey.value == defaultAssistId) {
      // TODO 要告知是否静默 发消息，如果要发，这里设置为true，定位到智能体的时候，不请求开场白，直接发消息后再清除isHide,如果不静默发消息，这里就不需要缓存，会请求开场白
      // if ((sdkInstance?.preConfig && sdkInstance.preConfig.defaultSendMsg) || defaultSendMsg) {
      //   localStorage.setItem('isHide', 'true')
      // }
      if (callback) {
        callback();
      }
      return;
    }
    // 先接口调用判断一下，常用助手是否存在，不存在则不跳转
    getUsualAssistList(7, defaultAssistId).then(async (res: any) => {
      if (res && res.code == 0 && res.data && res.data.length > 0) {
        const item = res.data.find((item: any) => item.id == defaultAssistId);
        let aist = item;
        if (!aist) {
          aist = {
            id: defaultAssistId,
          };
        }
        await uMenu.changeMenu(item, { isAsit: true, isRedirectAist: true });
        chatListStore.chatActions.setDynamicData('showProlog', showProlog || false);
        if (callback) {
          callback();
        }
      }
    });
  } else {
    if (callback) {
      callback();
    }
  }
};


// 菜单收缩，设置容器最小宽度
const toggleCollapsed = (val: boolean) => {
  if (val) {
    leftMenuRefWidth.value = 98;
  } else {
    leftMenuRefWidth.value = 220;
  }
};

// 获取超级助手
const getGenetalAsit = async () => {
  try {
    // 获取通用助手
    if (uGeneralAsit.generalAsit?.id) {
      return;
    }
    const res: any = await getGeneralAssist();
    if (res && res.code == 0 && res.data) {
      const { prologuePreQuestions = [], ...asitInfo } = res.data;
      const val = {
        ...asitInfo,
        prologuePreQuestions: prologuePreQuestions || [],
        isComi: true,
      };
      uGeneralAsit.setGeneralAsit(val);
      uTmpAstIfo.changeAssistInfo(val);
      chatListStore.chatActions.setDynamicData('selectedAgentInfo', val);
      return val;
    }
  } catch (error) {
    console.error('获取通用助手失败:', error);
  }
};

// 获取用户信息
const getCurUserInfo = async () => {
  let data;
  try {
    const result = (await getUserInfo()) as any;
    data = result.data;
    const postIdArr = [data?.currentUser?.postId || ''];
    useGlobalStore.changeState('posts', postIdArr);
  }catch(error) {
    console.error('获取用户信息失败:', error);
  }
  if (data) {
    const avatarUrl = getUserAvatar(data.memberId);
    const userInfo = {
      ...data,
      avatar: avatarUrl || data.avatar,
      name: data.memberName,
      postName: data.postName,
      id: data.memberId
    };
    setUserInfo(userInfo);
  }
};

// 累积的消息类型
let accumulatedMessageTypes = new Set<string>();

// 创建节流函数，5秒内只允许调用一次
const throttledEmitRefresh = throttle((messageList: any[]) => {
  // 累积新的消息类型
  messageList.forEach(type => accumulatedMessageTypes.add(type));

  sdkInstance.emitRefresh('refreshData', {
    action: Array.from(accumulatedMessageTypes),
    // messages: data.messages
  });

  // 发送后清空累积
  accumulatedMessageTypes.clear();
}, 5000);

const onMessage = async (data: any) => {

  if (data.messages && sdkInstance.waitForInitialization) {
    await sdkInstance.waitForInitialization();
    // 类型转换函数
    const transformMessageType = (type: string) => {
      if (type && type.indexOf('message.link.mt') > -1) {
        return 'meeting';
      }
      return 'pending';
    };

    // 先转换类型再去重
    const messageList = [...new Set(data.messages.map((item: any) => transformMessageType(item.L)).filter((item: any) => !!item))];

    // 使用节流函数调用
    throttledEmitRefresh(messageList);
  }
};
const onError = (data: any) => {
  console.log('onError', data);
  // 退出登录的情况
  if (data.type === 'logout' && isPortal) {
    const ctxPath = (window as any)._ctxPath || '/seeyon';
    alert(data.message);

    try {
      if (top["removeOnbeforeunload"]) {
        top["removeOnbeforeunload"]()
      }
      const topWindwow = window?.top || window;
      topWindwow.location.href = encodeURI(ctxPath + "/main.do?method=logout&reason=" + data.message)
    } catch (a) {
    }
  }
};

const hideAiWrite = (event: Event) => {
  const target = event.target as HTMLElement | null;
  if (target?.closest('.ai_write')) {
    return;
  }
  let aiWriteArr = window.top?.document.querySelectorAll('.ai_write'); //从新建事项点进去打开ai帮我写，关闭页面没被关闭
  if (!aiWriteArr?.length) {
    aiWriteArr = document.querySelectorAll('.ai_write');
  }
  for (let i = 0; i < aiWriteArr.length; i++) {
    (aiWriteArr[i] as HTMLElement).style.display = 'none';
  }
}

// 周期
onMounted(async () => {
  sdkInstance.bind('redirectToAssit', redirectToAssit);


  const jumpAgentCode = getQueryString('jumpAgentCode') || '';
  if (jumpAgentCode) {
    // 等待 SDK 完全初始化后再执行重定向
    try {
      if (sdkInstance.waitForInitialization) {
        await sdkInstance.waitForInitialization();
      }
      // SDK 就绪后执行跳转
      if (jumpAgentCode) {
        redirectToAssit(jumpAgentCode, false, null, true);
      }
    } catch (error) {
      console.error('SDK 初始化失败:', error);
      // 即使初始化失败，也尝试执行跳转（降级处理）
      if (jumpAgentCode) {
        redirectToAssit(jumpAgentCode, false, null, true);
      }
    }
  }


  // window.addEventListener('resize', resizeContainer, false);
  if (useGlobalStore.globalState.comiContainerName == 'v8') {
    let userInfo = {
      deptName: localStorage.getItem('currentUser')?.useTenantName || '',
      id: localStorage.getItem('currentUser')?.userId || '',
      postName: '',
      avatar: localStorage.getItem('loginAvatar') || '',
      name: localStorage.getItem('loginName') || '',
    };
    setUserInfo(userInfo);
    initConfig();
  } else {
    await getCurUserInfo();
    initConfig();
    window.initMessage && window.initMessage(5, onMessage, onError, useUserInfo().userInfo);
  }

  // 只有在没有助手跳转时才默认跳转到超级助手
  if (!hasAssistantRedirect.value) {
    await getGenetalAsit();
    // 初始化加载，预设菜单id超级助手
    uMenu.changeMenu({
      id: 'comi',
    });
  }
  window.addEventListener('click', hideAiWrite, true);

  // 等待 SDK 完全初始化后再绑定 isPortal 参数
  try {
    if (sdkInstance.waitForInitialization) {
      await sdkInstance.waitForInitialization();
    }

    // 绑定 isPortal 参数到 SDK
    if (sdkInstance.setIsPortal) {
      sdkInstance.setIsPortal(isPortal);
    }

    // 绑定 expandDoubleContainer 参数到 SDK
    if (sdkInstance.setExpandDoubleContainer) {
      sdkInstance.setExpandDoubleContainer(showDobuleScreen.value);
    }
  } catch (error) {
    console.error('SDK 初始化失败:', error);
  }
  sdkInstance.bind('pushCustomCard', (result) => {
    if (uMenu.currentMenuInfo?.id === 'comi') {
      useCustomDataStore.setCustomData('collApproveData', result?.data[0]);
      useCustomDataStore.setCustomData('needNewChat', null);
      // TODO: 临时解决，产品说的清空聊天记录
      chatListStore.chatActions.setDynamicData('allCardData', [])
    }
  });
  // window.comiEventBus?.$on('pushCustomCard',(result)=>{
  //   if(uMenu.currentMenuInfo?.id === 'comi') {
  //     useCustomDataStore.setCustomData('collApproveData', result?.data[0]);
  //     useCustomDataStore.setCustomData('needNewChat', null);
  //   }
  // });
});

// 监听路由
onUnmounted(() => {
  // 恢复光标样式
  document.body.style.cursor = '';

  // 重置拖拽状态
  isDragging.value = false;

  // 清理可能残留的覆盖层
  const overlay = document.getElementById('drag-overlay');
  if (overlay) {
    document.body.removeChild(overlay);
  }
  window.removeEventListener('click', hideAiWrite, true);
});
</script>

<style scoped lang="less">
.close_icon {
  cursor: pointer;
  display: flex;
}

:deep(.history_drawer) {
  align-items: center;
  position: absolute !important;
}

:deep(.ant-drawer-extra) {
  position: relative;
}

.content-wrap {
  display: flex;
  flex: 1;
  flex-direction: row;
  min-height: 0px;
  justify-content: space-between;
  position: relative;

  .content-wrap-left {
    flex: 1;
    min-width: 0;
    display: flex;
    justify-content: center;
    min-height: 100px;
    width: 100%;
  }

  .content-wrap-right {
    width: 68px;
  }

  .fullscreen_header {
    position: absolute;
    top: 0;
    right: 0;
    // width: 100%;
    // display: flex;
    // justify-content: flex-end;
  }

  .content {
    width: 100%;
    min-width: 358px;
    padding-top: 56px;
    box-sizing: border-box;
  }

  .noPaddingTop {
    padding-top: 0px !important;
  }

  .container-wrap {
    min-height: 100px;
  }
}

.normal_container {
  display: flex;
  flex-direction: row;
  background: #edf2fc;
}

.fullscreen_container {
  display: flex;
  // flex-direction: row-reverse;
  height: 100%;
  width: 100%;
  padding: 0;
  box-sizing: border-box;
  min-width: 760px;
  background-image: url('@/assets/imgs/portal_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .left-content {
    display: flex;
    flex: 1;
    flex-direction: row;
  }

  .content-wrap {
    flex-direction: column;
    flex: 1;
    min-height: 0px;
    min-width: 0px;
    align-items: center;
    overflow: hidden;

    .content {
      width: 100%;
      box-sizing: content-box;
      padding-left: 80px;
      padding-right: 80px;

      // margin: 0 80px;
      // width: calc(100% - 160px);
      .chat-wrap {
        padding: 0;
      }
    }

    .history_wrap {
      width: 150px;
    }

    .dobule-screen-content {
      z-index: 99;
      display: flex;
      width: 100%;
      // padding-top: 56px;
      // padding-bottom: 16px;
      // box-sizing: border-box;
      gap: 4px;

      .double-screen-container {
        width: calc(100% - 454px);
      }

      .chat-wrap {
        width: 450px;
        // margin-top: 56px;
      }

      &.dobule-screen-content-full {
        .double-screen-container {
          width: calc(100% - 16px);
        }
      }
    }
  }
}

.fullscreen_container.fullscreen_container_gary {
  background-image: url('@/assets/imgs/portal_gray.png') !important;
}

.squarebg {
  background: transparent;
}

.sider-container {
  position: relative;
  z-index: 1;
  height: 100%;

  .silder-left {
    display: flex;
    flex-direction: row;
    box-shadow: -10px 0px 38px 0px rgba(0, 0, 0, 0.1);
    // transition: width 0.2s ease-in-out;
    position: relative;
    z-index: 2;
    overflow: hidden;

    &.no-transition {
      transition: none;
    }

    .silder-line {
      width: 3px;
      height: 100%;
      cursor: col-resize;
      flex-shrink: 0;
      background: transparent;

      &:hover {
        background: rgba(67, 121, 255, 1);
      }

      &:active {
        background: rgba(67, 121, 255, 1);
      }
    }

    .silder-line-active {
      background: rgba(67, 121, 255, 1);
    }

    .double-screen-container {
      flex: 1;
      height: 100%;
      margin: 0;
      border-radius: 0;
      border-top-left-radius: 12px;
      border-bottom-left-radius: 12px;
    }
  }

  .silder-right {
    width: 450px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: -1px 0px 38px 0px rgba(26, 30, 40, 0.1);
    position: relative;
    z-index: 1;

    .content-wrap {
      flex: 1;
    }
  }
}

.bg-setting {
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: local;
  min-height: 100%;
}
</style>
