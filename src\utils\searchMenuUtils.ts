import axios from 'axios'
import type { MenuItem } from '@/components/fullMenu/types/menu'
import { getMenuData, saveSearchHistory as saveHistory<PERSON>pi, getSearchHistory as getHistory<PERSON>pi } from '@/api/menu/index'
import type { MenuApiConfig } from '@/api/menu/index'
import { createMenuOpenHandler } from './menuOpenHandler'

interface MenuCache {
  menuNodes: any[]
  menuTree: any[]
  menuMap: Record<string, any>
  spaceId: string
  menuSource?: string
  hasCache: boolean
  change: boolean
  loaded: boolean
}

interface SearchMenuConfig {
  portalId?: string
  spaceId?: string
  menuSource?: string
}

class SearchMenuUtils {
  menuNodes: any[] = []
  menuTree: any[] = []
  menuMap: Record<string, any> = {}
  cache: MenuCache = {
    menuNodes: [],
    menuTree: [],
    menuMap: {},
    spaceId: "",
    hasCache: false,
    change: false,
    loaded: false
  }
  isReady: boolean = false
  spaceId: string = ""
  portalId: string = ""
  noSearchMenuData: boolean = true
  menuSource: string = "2"
  dataTime: number = 0
  readyList: Array<(dataTime: number, changed: boolean) => void> = []
  searchBackUp: any[] = []
  private menuOpenHandler: any = null

  constructor(spaceId?: string, portalId?: string) {
    this.spaceId = spaceId || this.getSpaceId()
    this.portalId = portalId || this.getPortalId()
    this.menuOpenHandler = createMenuOpenHandler(this.portalId, this.spaceId)
    this.init(undefined, this.spaceId)
  }

  // 设置缓存
  setCache(): void {
    this.cache.menuNodes = this.menuNodes
    this.cache.menuTree = this.menuTree
    this.cache.menuMap = this.menuMap
    this.cache.spaceId = this.spaceId
    this.cache.menuSource = this.menuSource

    // 如果有值，则判断有缓存
    if (this.menuNodes && this.menuNodes.length > 0) {
      this.cache.hasCache = true
    } else {
      this.cache.hasCache = false
    }
    // 是否变更了数据
    this.cache.change = false
  }

  // 读取缓存菜单
  loadCache(): void {
    this.menuNodes = this.cache.menuNodes
    this.menuTree = this.cache.menuTree
    this.menuMap = this.cache.menuMap
    this.spaceId = this.cache.spaceId
    this.menuSource = this.cache.menuSource || "2"
    // 读取后更新ready状态
    this.isReady = true
    // 是否变更了数据
    this.cache.change = false
    this.cache.loaded = true
  }

  // 获取空间ID
  getSpaceId(): string {
    // 在Vue项目中，可能需要从路由或其他地方获取spaceId
    // 这里提供一个默认实现
    return new URLSearchParams(window.location.search).get('spaceId') || 'default'
  }

  // 获取Portal ID
  getPortalId(): string {
    // 在Vue项目中，可能需要从路由或其他地方获取portalId
    // 这里提供一个默认实现
    return new URLSearchParams(window.location.search).get('portalId') || 'default_portal'
  }

  // 初始化
  async init(callback?: (changed: boolean) => void, spaceId?: string): Promise<void> {
    // 开始缓存状态
    this.setCache()
    // 缓存后清除状态
    this.menuNodes = []
    this.menuTree = []
    this.menuMap = {}
    this.isReady = false
    this.spaceId = spaceId || this.getSpaceId()

    // 使用正确的portalId和spaceId
    const data: SearchMenuConfig = {
      portalId: this.portalId,
      spaceId: this.spaceId,
      menuSource: this.menuSource,
    }

    this.dataTime = new Date().getTime()

    try {
      // 这里应该调用真实的菜单API
      const response = await this.fetchMenuData(data)
      await this.handleMenuResponse(response, callback)
    } catch (error) {
      console.error('Failed to fetch menu data:', error)
      // 使用模拟数据
      const mockResponse = this.getMockMenuResponse()
      await this.handleMenuResponse(mockResponse, callback)
    }
  }

  // 获取菜单数据 - 调用真实API
  private async fetchMenuData(data: SearchMenuConfig): Promise<any> {
    try {
      console.log('SearchMenuUtils fetchMenuData - portalId:', data.portalId, 'spaceId:', data.spaceId)

      // 尝试调用真实的API
      const apiConfig: MenuApiConfig = {
        portalId: data.portalId,
        spaceId: data.spaceId,
        menuSource: data.menuSource
      }

      const response = await getMenuData(apiConfig)

      return response
    } catch (error) {
      console.warn('Failed to fetch real menu data, using mock data:', error)
      // 如果API调用失败，返回模拟数据
      return {
        code: 1,
        data: {
        }
      }
    }
  }

  // 获取模拟菜单响应
  private getMockMenuResponse(): any {
    return {
      code: '0',
      data: {
        dataMenu: [
          {
            id: '1',
            name: '我的待办',
            url: '/workspace/pending',
            target: '_blank',
            items: []
          },
          {
            id: '2',
            name: '新建流程',
            url: '/collaboration/new',
            target: '_blank',
            items: []
          }
        ]
      }
    }
  }

  // 处理菜单响应
  private async handleMenuResponse(response: any, callback?: (changed: boolean) => void): Promise<void> {

    // 从缓存读取了，不再执行后续更新
    if (this.cache.loaded) {
      this.cache.loaded = false
      return
    }

    if (response.code === '0' && response.data && response.data.dataMenu) {
      const dataMenu = response.data.dataMenu

      this.menuTree = dataMenu

      for (let i = 0; i < dataMenu.length; i++) {
        // 全递归遍历
        this.loadingDataMenu(dataMenu[i], null, this.cache.menuTree[i])
      }

      // 修改ready状态
      this.isReady = true
      this.dataTime = new Date().getTime()

      // 触发ready回调
      for (let k = 0; k < this.readyList.length; k++) {
        // 通知变更，外部重新获取
        this.readyList[k](this.dataTime, this.cache.change)
      }

      // 释放内存
      this.readyList = []

      if (callback) {
        callback(this.cache.change)
      }
    } else {
      this.isReady = true
    }
  }

  // 加载菜单数据
  private loadingDataMenu(dataMenu: any, pmenu: any, cacheDataMenu: any): void {
    // 判断数据是否变更
    if (!this.cache.change && (!cacheDataMenu || dataMenu.id !== cacheDataMenu.id)) {
      // 判断缓存数据有变化
      this.cache.change = true
    }

    // 数据补充
    dataMenu.idKey = "menu_" + dataMenu.id
    dataMenu.pIdKey = pmenu ? ("menu_" + pmenu.id) : ""
    dataMenu.nameKey = dataMenu.name
    dataMenu.searchNameKey = dataMenu.name.toLowerCase()
    dataMenu.urlKey = dataMenu.url
    dataMenu.target = dataMenu.target || ""
    dataMenu.children = dataMenu.items

    // 拼接map
    this.menuMap[dataMenu.idKey] = dataMenu
    // 拼接menu
    this.menuNodes.push(dataMenu)

    if (dataMenu.items && dataMenu.items.length) {
      for (let i = 0; i < dataMenu.items.length; i++) {
        let sendCacheDataItem = null
        if (cacheDataMenu && cacheDataMenu.items) {
          sendCacheDataItem = cacheDataMenu.items[i]
        }
        // 全递归遍历
        this.loadingDataMenu(dataMenu.items[i], dataMenu, sendCacheDataItem)
      }
    }
  }

  // 注册ready回调
  ready(callback: (dataTime: number, changed: boolean) => void): void {
    if (typeof callback === 'function') {
      if (this.isReady) {
        callback(this.dataTime, this.cache.change)
      } else {
        // 如果有缓存,并且spaceId没变，则直接换行并且查询
        if (this.cache.hasCache &&
          this.spaceId === this.cache.spaceId &&
          this.menuSource === this.cache.menuSource) {
          this.loadCache()
          callback(this.dataTime, false)
        } else {
          this.readyList.push(callback)
        }
      }
    }
  }

  // 搜索节点树
  searchNodeTree(searchValue: string, nodes?: any[]): any[] {
    const menuTree = nodes || this.menuTree
    if (!menuTree.length) {
      return []
    }

    // 复制一下节点，避免修改到原树
    const menuResult: any[] = []
    for (let i = 0; i < menuTree.length; i++) {
      const node = menuTree[i]
      const val = searchValue.toLowerCase()
      if (node.searchNameKey && node.searchNameKey.indexOf(val) > -1) {
        menuResult.push(JSON.parse(JSON.stringify(node)))
        continue
      }

      let children = node.children || []
      if (!children.length) {
        continue
      }

      children = this.searchNodeTree(searchValue, children)
      if (!children.length) {
        continue
      }
      menuResult.push({ ...node, children: children })
    }
    return menuResult
  }

  // 搜索方法
  search(searchValue: string, callback?: (results: any[]) => void): any[] {
    // 禁止再次触发搜索，只允许retry触发
    let list: any[] = []

    if (!this.isReady) {
      this.ready(() => {
        this.search(searchValue, callback)
      })
      return []
    }

    if (searchValue) {
      // 如果还没有初始化完成，则重新递归回调
      if (this.menuNodes) {
        list = this.searchNodeTree(searchValue, this.menuTree)
        if (!list || !list.length) {
          list = this.searchBackUp
          this.noSearchMenuData = true
        } else {
          this.searchBackUp = list
          this.noSearchMenuData = false
        }
      }
    } else {
      // 如果没有输入值，则清空
      list = this.menuTree
      this.searchBackUp = []
      this.noSearchMenuData = false
    }

    if (callback) {
      callback(list)
    }
    return list
  }

  // 保存菜单搜索点击记录
  async saveSearchHistory(node: any, callback?: (result: any) => void): Promise<void> {
    if (!node || !node.idKey) {
      return
    }

    const menuId = node.idKey
    if (isNaN(Number(menuId)) && menuId.indexOf('menu_') < 0) {
      return
    }

    try {
      // 调用真实的保存历史记录API
      const result = await saveHistoryApi(this.portalId, menuId)

      if (callback) {
        callback(result)
      }
    } catch (error) {
      console.error('Failed to save search history:', error)
      if (callback) {
        callback({ code: -1, message: 'Failed to save history' })
      }
    }
  }

  // 获取搜索历史记录
  async getSearchHistory(callback: (history: any[]) => void): Promise<void> {
    try {

      // 调用真实的获取历史记录API
      const response = await getHistoryApi(this.portalId, 1, 6)

      if (response.code === 0 && response.data) {
        callback(response.data)
      } else {
        // 如果API返回错误，使用模拟数据
        callback([])
      }
    } catch (error) {
      console.warn('Failed to fetch search history, using mock data:', error)
      // 如果API调用失败，使用模拟数据
      callback([])
    }
  }

  /**
   * 简化版showMenu方法，使用MenuOpenHandler处理菜单打开
   * @param oUrl 菜单URL
   * @param id 菜单ID
   * @param target 打开目标
   * @param resourceCode 资源代码
   * @param _obj 触发对象
   * @param tabName 标签名称
   * @param from 来源
   * @param styleObj 样式对象
   * @param onHistorySaved 历史保存完成后的回调
   */
  showMenu(oUrl: string, id?: string, target?: string, resourceCode?: string, _obj?: any, tabName?: string, from?: string, styleObj?: any, onHistorySaved?: () => void): void {
    // 保存搜索历史
    this.saveSearchHistory({
      idKey: id
    }, (result) => {
      // 历史保存完成后调用回调
      if (onHistorySaved) {
        onHistorySaved()
      }
    })

    // 使用MenuOpenHandler处理菜单打开
    if (this.menuOpenHandler) {
      this.menuOpenHandler.handleMenuOpen(oUrl, id, target, resourceCode, _obj, tabName, from, styleObj)
    }
  }
}

// 创建单例实例
let searchMenuUtilsInstance: SearchMenuUtils | null = null

export function createSearchMenuUtils(spaceId?: string, portalId?: string): SearchMenuUtils {
  if (!searchMenuUtilsInstance) {
    searchMenuUtilsInstance = new SearchMenuUtils(spaceId, portalId)
  }
  return searchMenuUtilsInstance
}

export function getSearchMenuUtils(): SearchMenuUtils | null {
  return searchMenuUtilsInstance
}

export default SearchMenuUtils
