export interface MenuItem {
  id: string
  idKey: string
  nameKey: string
  urlKey?: string
  resourceCode?: string
  children?: MenuItem[]
  parentId?: string
  target?: string
  sort?: number
  // 导航相关字段
  openType?: string
  navType?: string
  spaceType?: string
  // 标记是否为收藏块
  isFavoritesBlock?: boolean
  // 标记是否为导航块
  isNavigationBlock?: boolean
  // 标记是否为导航项（搜索结果中的导航项）
  isNavigationItem?: boolean
  // 标记是否为数据块（用于搜索结果分组）
  isBlock?: boolean
}

export interface SpaceItem {
  id: string
  navName: string
  navType: string
  openType: string
  menuIndex: number
  sort: number
}

export interface MenuSearchResponse {
  data: MenuItem[]
  total: number
  success: boolean
}

export interface FavoriteResponse {
  data: MenuItem[]
  success: boolean
}

export interface HistoryResponse {
  data: MenuItem[]
  success: boolean
}

export interface MenuTreeResponse {
  data: MenuItem[]
  success: boolean
}

// 扩展Window类型 - 现在主要用于showMenu函数
declare global {
  interface Window {
    showMenu?: (url: string, idKey: string, target?: string, params?: any, data?: any, nameKey?: string) => void
  }
}

export {}
