<template>
  <div
    :class="{
      history_menu_box: true,
      history_menu_box_fullscreen: uGlobal.globalState.isFullScreen,
    }"
  >
    <div class="history_menu_header" v-if="isPortal">
      <div class="history_menu_title">历史会话</div>
      <div class="history_search">
        <SearchInput
          class="search_ipt ellipsis"
          type="handleSearchHistory"
          placeholder="搜索会话"
        />
      </div>
    </div>

    <!-- <Spin :spinning="loading" :wrapperClassName="wrapperClassName"> -->
    <div class="history_list" :class="{ no_more: isScrollToBottom }" ref="hisChatWrap">
      <div v-if="searchHistoryList.length > 0">
        <div
          v-for="[category] in Object.entries(historyList)"
          :key="category"
          :class="{
            history_category_list: true,
            history_category_list_no_title: !showTitleAndNoTime,
          }"
        >
          <div
            class="history_category_title"
            v-if="historyList[category].length > 0 && showTitleAndNoTime"
          >
            {{ getCategoryTitle(category) }}
          </div>
          <div
            v-for="(item, index) in historyList[category]"
            :key="index"
            @click="() => goSecThisItem(item, index)"
            :class="{
              history_item: true,
              history_item_time: !showTitleAndNoTime,
              history_item_active: index === currentItemInx,
            }"
          >
            <!--展示态-->
            <div
              :class="{
                item_usual: true,
                item_usual_noTime: showTitleAndNoTime,
              }"
              v-if="item.isShowUsual"
            >
              <div class="item_usual_left">
                <img
                  :src="item.iconUrl"
                  v-if="item.iconUrl && !item.superFlag"
                  class="w-[18px] h-[18px] item-icon"
                />
                <ItemTitle :item="item" />
              </div>

              <!-- <div class="item_name" v-if="searchText.length>0" v-html="item.name"></div> -->
              <div class="item_time_operation">
                <div class="item_time" v-if="!showTitleAndNoTime">{{ item.time }}</div>
                <div class="item_operation">
                  <!-- 编辑 -->
                  <i
                    class="iconfont ai-icon-bianji edit-icon"
                    @click.stop="() => goEditItem(item, index, category)"
                  ></i>
                  <!-- 删除 -->
                  <Popconfirm
                    :getPopupContainer="
                      (triggerNode: HTMLElement) =>
                        triggerNode.parentNode?.parentNode?.parentNode as HTMLElement
                    "
                    overlayClassName="own_pop_cfirm"
                    title="确认删除这条历史会话？"
                    ok-text="是"
                    cancel-text="否"
                    trigger="click"
                    @confirm.stop="(e) => confirm(e, item)"
                    @cancel.stop="cancel"
                  >
                    <template #icon><ExclamationCircleFilled class="popconfirmIcon" /></template>
                    <i class="iconfont ai-icon-shanchusvg delete-icon" @click.stop></i>
                  </Popconfirm>
                </div>
              </div>
            </div>
            <!-- 操作态 -->
            <div
              v-else
              :class="{ item_edit: true, item_edit_noTime: showTitleAndNoTime }"
              @click.stop
            >
              <Input
                ref="editEleRef"
                :bordered="false"
                :autofocus="true"
                v-model:value="item.name"
                class="edit_txt_ele"
                @pressEnter="() => pressHisName(item, index, category)"
                :maxlength="199"
              >
                <template #suffix>
                  <div class="edit_operation">
                    <i
                      class="iconfont ai-icon-duihao"
                      @click.stop="() => pressHisName(item, index, category)"
                    />
                    <i
                      class="iconfont ai-icon-cha"
                      @click.stop="() => cancleHisName(item, index, category)"
                    />
                  </div>
                </template>
              </Input>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else-if="!loading && searchHistoryList.length === 0"
        class="flex items-center justify-center w-full h-full"
      >
        <CustomEmpty />
      </div>
      <VueEternalLoading
        position="bottom"
        :style="{ 'text-align': 'center' }"
        :container="hisChatWrap"
        v-model:is-initial="isInitial"
        :load="load"
      >
        <template #no-results>
          <div></div>
        </template>
        <template #no-more>
          <div></div>
        </template>
        <template #loading>
          <AiIdentify :loadingStatus="true"></AiIdentify>
        </template>
      </VueEternalLoading>
    </div>
    <!-- </Spin> -->
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, watch, computed, defineOptions } from 'vue';
import type { TypeResponse } from '@/types/api';
// @-#
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { VueEternalLoading } from '@ts-pro/vue-eternal-loading';
import AiIdentify from '@/components/aiIdentify/index.vue';
// a-b-c
import { Input, Image, Popconfirm, message, Empty, Spin } from 'ant-design-vue';
import moment from 'moment';
import CustomEmpty from '@/components/empty/index.vue';
// 1-2-3
import {
  getHistoryAsisList,
  editSessionHisName,
  delSessionHis,
} from '@/api/historyConversation/index';
import type { LoadAction } from 'node_modules/@ts-pro/vue-eternal-loading/src/main';
import ItemTitle from './itemTitle.vue';
import { useGeneralAsit, useGlobal, useMenu } from '@/stores/global';
import { useChatList } from '@/stores/chatList';
import SearchInput from '@/components/common/search-input/index.vue';
import emitter from '@/utils/bus';

defineOptions({ name: 'HistoryList' });

type listItem = {
  id: string;
  name: string;
  time: string;
  isShowUsual: boolean;
  iconUrl: string;
  superFlag: number;
};

const isPortal = inject('isPortal');

const uGlobal = useGlobal();
const uMenu = useMenu();
const { chatActions } = useChatList();

const props = defineProps<{
  toResetHistoryList?: (val: string, assistantId: string) => void;
}>();

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const hisChatWrap = ref<HTMLElement>(); // 列表外盒dom实例
// 输入框实例
const editEleRef = ref<any>(null);
const loading = ref(false); // 加载状态

const searchText = ref(''); //keyword
const searchHistoryList = ref<listItem[]>([]); //list
const historyList = ref<any>({
  today: [],
  thisMonth: [],
  earlier: [],
});

const pageInfo = ref({
  pageNumber: 1,
  pageSize: 40,
  needTotal: true,
  total: 0,
});
const currentItemInx = ref(-1); // 点选激活序号
const noMore = ref(false); // 是否没有更多数据
const isScrollToBottom = ref(false); // 是否滚动到底部

const isInitial = ref(true); // 是否初次已经加载
const currentEditOldItemInfo = ref({ id: '', name: '', time: '', isShowUsual: false });

const wrapperClassName = computed(() => {
  let className = 'spin_box';
  if (searchHistoryList.value.length === 0) {
    className += ' spin_box_empty';
  }
  return className;
});

// 显示分组菜单，但不显示时间
const showTitleAndNoTime = ref(true); // 是否展示标题和时间
//上一次编辑的历史记录
const lastEditItem = ref(null);

const getCategoryTitle = (category: string): string => {
  switch (category) {
    case 'today':
      return '今天';
    case 'thisMonth':
      return '本月';
    case 'earlier':
      return '更早';
    default:
      return '未知';
  }
};

// 获取历史会话列表
const gettingHistoryList = async (val: any) => {
  loading.value = true;

  try {
    const res: TypeResponse = (await getHistoryAsisList(val)) as TypeResponse;
    loading.value = false;
    console.log('api历史会话列表', res);
    if (res && res.code === '0') {
      const { content = [], pageInfo: opageInfo } = res.data;
      // 渲染
      searchHistoryList.value = [
        ...searchHistoryList.value,
        ...content.map((d: any) => {
          // delingHisSession(d.id)  // 批量删除历史会话
          return {
            id: d.id,
            name: d.name,
            time: moment(d.updateTime).format('YYYY-MM-DD HH:mm:ss'),
            isShowUsual: true,
            iconUrl: d.iconUrl || '',
            superFlag: d.superFlag,
            assistantId: d.assistantId,
          };
        }),
      ];
      pageInfo.value = opageInfo;

      // 设置高亮文本
      if (val?.params?.name) {
        const keyword = val?.params?.name;
        const newData = searchHistoryList.value.map((item: any) => {
          const parts = item.name.split(new RegExp(`(${keyword})`));
          // console.log(parts);

          const textParts = parts.filter(Boolean).map((part: string) => ({
            text: part,
            isHighlight: part === keyword,
          }));
          let hightName = '';
          textParts.forEach((part: any, index: number) => {
            if (part.isHighlight) {
              hightName += `<span class="highlight">${part.text}</span>`;
            } else {
              hightName += part.text;
            }
          });
          return {
            ...item,
            hightName,
            isHighlight: true,
          };
        });
        searchHistoryList.value = newData;
      }
      // historyList.value = categorizeHistory(searchHistoryList.value);
      // 去重是因为修改编辑会触发load和编辑刷新两次请求，会导致列表重复，暂时通过去重处理
      historyList.value = categorizeHistory(uniqueArray(searchHistoryList.value));
    } else {
      message.error(res.message);
      searchHistoryList.value = [];
    }
  } catch (error) {
    loading.value = false;
    searchHistoryList.value = [];
  }
};

// 去重
const uniqueArray = (arr: any[]): any[] => {
  const seen = new Set<string>();
  return arr.filter((item) => {
    const itemString = JSON.stringify(item);
    return seen.has(itemString) ? false : seen.add(itemString);
  });
};

const handleSearchEvent = (data: any) => {
  const val = data.params.searchValue;
  searchText.value = val;
  if (data.type === 'pressEnter' || data.type === 'click') {
    goSearchHistory();
  } else if (data.type === 'handleSearchHistory') {
    handleChange(data.params.searchValue);
  }
};

const handleChange = (searchValue?: string) => {
  if (!searchValue) {
    goSearchHistory();
  }
};

// 搜索历史会话
const goSearchHistory = (num?: number) => {
  if (!loading.value) {
    if (searchText.value) {
      showTitleAndNoTime.value = false;
    } else {
      showTitleAndNoTime.value = true;
    }
    pageInfo.value.pageNumber = num || 1;
    if (pageInfo.value.pageNumber == 1) {
      searchHistoryList.value = [];
      historyList.value = {
        today: [],
        thisMonth: [],
        earlier: [],
      };
    }

    const { pageNumber, pageSize, needTotal } = pageInfo.value;
    gettingHistoryList({
      pageInfo: {
        pageNumber,
        pageSize,
        needTotal,
      },
      params: {
        name: searchText.value,
      },
    });
  }
};
// 选中当前会话
const goSecThisItem = async (itm: any, inx: number) => {
  if (currentItemInx.value == inx) {
    return;
  }
  currentItemInx.value = inx;

  // 点击的时候，设置historySessionId
  chatActions.setDynamicData('historySessionId', itm.id);
  chatActions.setDynamicData('historyModel', true);
  const isComi = itm?.superFlag === 1;
  if (props.toResetHistoryList) {
    props.toResetHistoryList(itm.id, itm.assistantId);
  } else {
    // 区分一下，如果是超级助理，传递comi，其他则传递对用助手id
    uMenu.changeMenu(
      {
        id: isComi ? 'comi' : itm.assistantId,
      },
      {
        isAsit: !isComi,
        fromHistory: true,
      },
    );
  }
};
//编辑
const goEditItem = (itm: any, inx: number, category: string) => {
  // 临时保存
  currentEditOldItemInfo.value = {
    ...itm,
  };
  // 恢复上一次的状态，这种方式避免去遍历所有列表内容
  if (lastEditItem.value) {
    lastEditItem.value.isShowUsual = true;
  }
  historyList.value[category][inx].isShowUsual = false;
  //更新上一次的信息
  lastEditItem.value = historyList.value[category][inx];

  // 主动聚焦
  nextTick(() => {
    if (editEleRef.value && editEleRef.value[0]) {
      editEleRef.value[0].focus();
    }
  });
};

// 修改会话名称
const pressHisName = (itm: any, inx: number, category: string) => {
  if (itm.name.trim().length == 0) {
    return;
  }
  historyList.value[category][inx] = {
    ...itm,
    isShowUsual: true,
  };
  editingHisName(itm.id, itm.name);
};
// 恢复会话名称
const cancleHisName = (itm: any, inx: number, category: string) => {
  historyList.value[category][inx] = {
    ...itm,
    ...currentEditOldItemInfo.value,
    isShowUsual: true,
  };
};

// 确认删除历史会话
const confirm = (e: any, itm: any) => {
  isInitial.value = false;
  delingHisSession(itm.id);
};
// 取消删除历史会话
const cancel = (e: any) => {};
// 编辑历史会话-api
const editingHisName = (id: string, name: string) => {
  editSessionHisName(id, name).then((res: any) => {
    if (res && res.code === '0') {
      message.success('保存成功');
      // 刷新列表
      goSearchHistory();
    } else {
      message.error(res.message);
    }
  });
};
// 删除历史会话-api
const delingHisSession = (id: string) => {
  delSessionHis(id).then((res: any) => {
    if (res && res.code === '0') {
      message.success('删除成功');
      // 刷新列表
      searchText.value = '';
      goSearchHistory();
    } else {
      message.error(res.message);
    }
    isInitial.value = true;
  });
};

// 更多加载
const load = async (action: LoadAction, payload: any) => {
  const { pageNumber, pageSize, needTotal } = pageInfo.value;
  await gettingHistoryList({
    pageInfo: {
      pageNumber,
      pageSize,
      needTotal,
    },
    params: {
      name: searchText.value,
    },
  });

  if (pageNumber < pageInfo.value.total / pageSize) {
    // console.log('需要加载');
    noMore.value = false;

    // 需要加载
    pageInfo.value.pageNumber++;
    action.loaded(searchHistoryList.value.length, pageSize);
  } else {
    console.log('无需加载');
    // 无需加载
    action.noMore();
    noMore.value = true;
  }
};
defineExpose({ menuList: searchHistoryList });

onMounted(() => {
  emitter.on('emitEvent', handleSearchEvent);
});
// 周期
onUnmounted(() => {
  emitter.off('emitEvent', handleSearchEvent);
});

function categorizeHistory(data: listItem[]) {
  const today = new Date(); // 假设今天是2025-04-17
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();

  const result: { today: any[]; thisMonth: any[]; earlier: any[] } = {
    today: [],
    thisMonth: [],
    earlier: [],
  };

  data.forEach((item: listItem) => {
    const timerParse = item.time.replace(/-/g, '/');
    const itemDate = new Date(timerParse);
    const itemMonth = itemDate.getMonth();
    const itemYear = itemDate.getFullYear();
    if (
      itemDate.getDate() === today.getDate() &&
      itemMonth === currentMonth &&
      itemYear === currentYear
    ) {
      result.today.push(item);
    } else if (itemMonth === currentMonth && itemYear === currentYear) {
      result.thisMonth.push(item);
    } else {
      result.earlier.push(item);
    }
  });
  return result;
}
</script>

<style scoped lang="less">
.history_menu_box {
  // width: 300px;
  height: 100%;
  width: 100%;
  // padding: 12px;
  border-radius: 8px;
  // border: 1px solid rgba(255, 255, 255, 1);
  // background: rgba(255, 255, 255, 0.6);
  // box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  // padding: 0 12px 12px 12px;
  margin: 0 auto;

  .history_menu_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 56px;
  }

  .history_menu_title {
    //styleName: 20px/Semibold;
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: @font-weight-500;
    line-height: 28px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #000000;
  }
  .history_search {
    caret-color: @sky;
    width: 240px;
    .search_ipt {
      // width: 276px !important;
      height: 32px;
      border-radius: 4px;
      border: 1px solid #00000033;

      &:hover,
      &:focus,
      &:active {
        border-color: @sky;
      }
    }
    .go_next {
      cursor: pointer;
      font-size: 14px;
      color: #00000066;
    }
  }

  .history_category_list {
    // margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
    .history_category_title {
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-size: 12px;
      line-height: 20px;
      color: #00000066;
      padding: 4px 12px;
    }
  }

  .history_category_list_no_title {
    margin-bottom: 0px;
  }

  .spin_box {
    flex: 1;
    min-height: 1px;
    overflow: auto;
    max-height: fit-content;
  }
  .history_list {
    flex: 1;
    overflow-y: auto;
    width: 100%;
    background-color: #ffffffbf;
    padding: 4px;
    border-radius: 8px;

    .history_item {
      width: 100%;
      // min-height: 58px;
      border-radius: 4px;
      cursor: pointer;
      // padding: 8px;
      height: 40px;
      border: 1px solid transparent;

      .item_usual {
        width: 100%;
        height: 100%;
        padding: 8px 12px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .item_usual_left {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex: 1;
          min-width: 1px;

          > div {
            flex: 1;
            min-width: 0;
          }

          .item-icon {
            margin-right: 4px;
            border-radius: 50%;
          }
        }
        .item_name {
          min-height: 22px;
          //styleName: 14px/Regular;
          font-family: PingFang SC;
          font-size: 14px;
          font-weight: @font-weight-400;
          line-height: 22px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
        }
        .item_time_operation {
          height: 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 2px;

          .item_time {
            height: 100%;
            //styleName: 12px/Regular;
            line-height: 20px;
            font-family: PingFang SC;
            font-size: 12px;
            font-weight: @font-weight-400;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: rgba(0, 0, 0, 0.4);
          }
          .item_operation {
            width: 40px;
            height: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            display: none;
            i {
              color: rgba(0, 0, 0, 0.4);
              font-size: 14px;
            }
            .edit-icon:hover {
              color: @sky;
            }
            .delete-icon:hover {
              color: @error-color;
            }
          }
        }
      }

      .item_usual_noTime {
        display: flex;
        align-items: center;
        flex-direction: row;

        .item_time_operation {
          margin-left: 12px;
          .item_operation {
            height: 22px;
            line-height: 22px;
            i {
              margin-top: 2px;
            }
          }
        }
      }
      .item_edit {
        width: 100%;
        min-height: 50px;
        height: 100%;
        background-color: white;
        .edit_txt_ele {
          width: 100%;
          height: 100%;
          border: 1px solid @sky;
          padding: 7px 11px;
          border-radius: 4px;
          resize: none;
          color: #000000;
          //styleName: 14px/Regular;
          font-family: PingFang SC;
          font-size: 14px;
          font-weight: @font-weight-400;
          line-height: 22px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          :deep(.ant-input) {
            height: 42px;
          }
          :deep(.ant-input-suffix) {
            width: 36px;
            height: 42px;
            display: flex;
            align-items: center;
            .edit_operation {
              width: 100%;
              display: flex;
              // justify-content: space-between;
              i {
                color: rgba(0, 0, 0, 0.4);
                font-size: 16px;
              }
              .ai-icon-duihao {
                margin-right: 8px;
              }
              // i:hover {
              //   color: #7559f8 !important;
              // }
            }
          }
        }
      }
      .item_edit_noTime {
        min-height: 30px;
        .edit_txt_ele {
          line-height: 22px;
          :deep(.ant-input) {
            height: 22px;
          }
          :deep(.ant-input-suffix) {
            height: 22px;
          }
        }
      }
    }
    .history_item:hover {
      background: #edf2fc;
      border-color: #edf2fc;
      .item_usual {
        .item_operation {
          display: flex;
          .edit-icon {
            color: @sky;
          }
          .delete-icon {
            color: @error-color;
          }
        }
      }
    }
    .history_item_active {
      background: #f4f3f6;
    }
    div.history_item_time {
      height: 60px;
    }
  }
}
</style>

<style lang="less" scoped>
// 全屏样式
.history_menu_box_fullscreen {
  // padding: 10px !important;
  padding: 0;
  padding-bottom: 12px;

  .history_menu_header {
    height: 32px;
    margin-bottom: 16px;
    margin-top: 5px;

    .history_menu_title {
      font-size: 24px;
      line-height: 32px;
    }

    .history_search {
      display: flex;
      align-items: center;
      margin-top: 0px;
    }
  }
}
</style>

<style lang="less">
.history_list {
  .ant-popconfirm .ant-popconfirm-message > .ant-popconfirm-message-icon {
    .popconfirmIcon {
      display: flex;
      color: #f9a314;
    }
  }
}
.history_search {
  .ant-input-affix-wrapper {
    > input.ant-input {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .ant-input-prefix {
      margin-inline-end: 8px;
    }
    .ant-input-clear-icon {
      display: flex;
      align-items: center;
    }
  }
  .ant-input-affix-wrapper-focused {
    box-shadow: none;
  }
}
.history_menu_box {
  .spin_box {
    .ant-spin-spinning {
      max-height: 100%;
    }
  }
  .spin_box_empty {
    max-height: 100% !important;
    .ant-spin-container {
      height: 100%;
      display: flex;
    }
  }
}
</style>
