<!--
 * @Author: 代琪 <EMAIL>
 * @Date: 2025-07-16 16:30:26
 * @LastEditors: 代琪 <EMAIL>
 * @LastEditTime: 2025-07-19 09:51:08
 * @FilePath: \ai-assistant-web\src\components\ai-design\component\portal-edoc\components\SectionCard.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="section-card">
    <div class="section-card__header">
      <h2 class="section-card__title">{{ data.title || '' }}</h2>
      <p class="section-card__subtitle">{{ data.subtitle || '' }}</p>
    </div>

    <div class="section-card__content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface SectionData {
  title: string
  subtitle: string
}

interface Props {
  data: SectionData
}

const props = defineProps<Props>()
</script>

<style scoped lang="less">
.section-card {
  position: relative;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.528) 19.98%, rgba(255, 255, 255, 0.704) 79.94%);

  &__header {
    margin-bottom: 16px;
  }

  &__title {
    position: absolute;
    height: 30px;
    top: -15px;
    left: 0;
    font-size: 14px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    border-bottom-right-radius: 16px;
    padding-right: 12px;
    padding-left: 12px;
    line-height: 30px;
    color: #fff;
    background: linear-gradient(99.3deg, #1D65FF 0%, #8AAFFF 52.29%, #A1E8FF 99.29%);
  }

  &__subtitle {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
  }

  &__content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    justify-items: center;

    // 当只有2个卡片时，居中显示
    &:has(> *:nth-child(2):last-child) {
      grid-template-columns: repeat(2, 1fr);
      max-width: 600px;
      margin: 0 auto;
    }
  }
}

.portal-edoc__section2{
  .section-card__title {
    background: linear-gradient(99.3deg, #04D1A1 0%, #33DAFF 52.29%, #99EEFF 100%);
  }
}

.portal-edoc__section3 {
  .section-card__title {
    background: linear-gradient(99.3deg, #3D28FF 0%, #909BFF 52.29%, #E9BAFF 99.29%);
  }
}
</style>
