import fs from 'fs'
import path from 'path'
import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import viteCompression from "vite-plugin-compression";
import { visualizer } from "rollup-plugin-visualizer";
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import federation from "@originjs/vite-plugin-federation";
import topLevelAwait from 'vite-plugin-top-level-await';
import { resolve } from 'node:path'
import autopreFixer from 'autoprefixer'


const isProd = process.env.NODE_ENV === 'production'

// 开发环境的代理配置
const v8DevProxy = {
  '/ai-manager': 'https://pre.seeyonv8.com/seeyon/ai-platform',
  '/serviceworker': 'https://pre.seeyonv8.com/',
  '/service': 'https://pre.seeyonv8.com/',
  '/main': 'https://pre.seeyonv8.com/',
  '/portal': 'https://pre.seeyonv8.com/',
  '/portal-frame': 'https://pre.seeyonv8.com/',
  '/i18n': 'https://pre.seeyonv8.com/',
  '/login': 'https://pre.seeyonv8.com/',
  '/static': 'https://pre.seeyonv8.com/',
  '/main-mobile/': 'https://pre.seeyonv8.com/',
  '/portal-mobile': 'https://pre.seeyonv8.com/',
  '/cdp-data-mobile': 'https://pre.seeyonv8.com/',
  '/schedulemanagement1879887445425932710-mobile': 'https://pre.seeyonv8.com/',
  '/billarchive2776130152975518413-mobile': 'https://pre.seeyonv8.com/',
  '/app-approval-mobile': 'https://pre.seeyonv8.com/',
  '/seeyon': 'https://pre.seeyonv8.com/',
  '/sportal': 'https://pre.seeyonv8.com/',
  '/seeyon-ai': 'https://pre.seeyonv8.com/',
  // '/ai-manager': 'http://10.101.129.2:8080/',
};


const developEnv = '10.2.4.60';
// const developEnv = '10.2.4.56:81';
// const developEnv = '************';
// const developEnv = '10.101.129.5';
const v5DevProxy = {
  // '/seeyon': {
  //   target: 'http://************/',
  //   changeOrigin: true,
  //   rewrite: (path:any) => path.replace(/^\/seeyon/, '/seeyon'),
  // },
  '/ai-manager': `http://${developEnv}/seeyon/ai-platform`,
  '^/[^/]+/ai-manager': `http://${developEnv}/seeyon/ai-platform`,
  '/ai-static': `http://${developEnv}/`,
  '/static': `http://${developEnv}/`,
  '/seeyon': `http://${developEnv}/`,
  '/sportal': `http://${developEnv}/`,
  '/custom-card-package': `http://${developEnv}/`,
}

const host =  '127.0.0.1';
const port = 8088;
// 生成代理配置
const createProxy = (proxy: Record<string, string>, mode?: string) => {
  const result: Record<string, any> = {};
  for (const [path, target] of Object.entries(proxy)) {
    result[path] = {
      target,
      changeOrigin: true,
      configure: mode === 'v5.development' ? (proxy: any, options: any) => {
        // 处理请求头 - 在发送请求前设置
        // if(path !== '/seeyon'){
        //   proxy.on('proxyReq', (proxyReq: any, req: any, res: any) => {
        //     const url = new URL(target);
        //     proxyReq.setHeader('Host', url.host);
        //     proxyReq.setHeader('Origin', url.origin);
        //     proxyReq.setHeader('Referer', target);
        //     // 防止nginx反向代理验证ip
        //     proxyReq.setHeader('x-forwarded-for', '');
        //     proxyReq.setHeader('x-forwarded-port', '');
        //     proxyReq.setHeader('x-forwarded-host', '');
        //   });
        // }

        // 处理响应 - 修改cookie路径
        proxy.on('proxyRes', (proxyRes: any, req: any, res: any) => {
          const cookies = proxyRes.headers['set-cookie'];
          if (cookies) {
            const newCookies = cookies.map((cookie: string) =>
              cookie.replace(/Path=\/seeyon/g, 'Path=/')
            );
            proxyRes.headers['set-cookie'] = newCookies;
          }
          const url = new URL(target);
          req.headers['Host'] = url.host;
          req.headers['Origin'] = url.host;
          req.headers['Referer'] = target;
          // 防止nginx反向代理验证ip
          req.headers['x-forwarded-for'] = '';
          req.headers['x-forwarded-port'] = '';
          req.headers['x-forwarded-host'] = '';
        });
      } : undefined
    }
    if(path === '/seeyon' && result[path]){
      result[path].rewrite = (rpath:any) => rpath.replace(/^\/seeyon/, '/seeyon');
      result[path].changeOrigin = false;
      // result[target] = developEnv;

    }
    if(path === '^/[^/]+/ai-manager'){
      result[path].rewrite = (path: string) => {
        const newPath = path.replace(/^\/[^/]+\/ai-manager/, '/ai-manager');
        return newPath;
      };
    }
  }
  return result;
};

export default defineConfig(({ mode }) => {
  return {
    root: resolve(__dirname, 'src/views'),
    base: process.env.NODE_ENV == 'development' ? '/' : '/ai-static/ai-copilot/',
    // 核心  打包配置
    plugins: [
      vue(),
      AutoImport({
        imports: ['vue'],
      }),
      viteCompression({
        algorithm: 'gzip',
        ext: '.gz',
        // 包含 png 文件
        filter: /\.(js|css|json|txt|html|ico|svg|png)(\?.*)?$/i,
        threshold: 10240, // 大于10k的文件才压缩
        deleteOriginFile: false, //压缩后是否删除源文件
      }),
      visualizer({
        open: true, //build后，是否自动打开分析页面，默认false
        gzipSize: true, //是否分析gzip大小
        brotliSize: true, //是否分析brotli大小
        //filename: 'stats.html'//分析文件命名
      }),
      vueDevTools(),
      federation({
        name: 'comi-custom-card',
        remotes: {
          remote_app: "/seeyon/ai-platform/ai-static/ai-copilot/public/custom-card-package/dist/assets/remoteEntry.js",
        },
        shared: ['vue','@seeyon/seeyon-comi-plugins-library']
      }),
      topLevelAwait({
        // 每个chunk模块的顶级await promise的导出名称
        promiseExportName: "__tla",
        // 每个chunk模块的顶级await promise的导入名称
        promiseImportName: i => `__tla_${i}`
      }),
      // 自定义插件：复制 public 目录到 dist/public
      {
        name: 'copy-public-to-subdirectory',
        generateBundle() {
          // 在生产环境下，手动复制 public 目录
          if (isProd) {
            const publicDir = path.resolve(__dirname, 'public')
            const outputPublicDir = path.resolve(__dirname, 'dist/public')

            // 确保输出目录存在
            if (!fs.existsSync(outputPublicDir)) {
              fs.mkdirSync(outputPublicDir, { recursive: true })
            }

            // 复制 public 目录到 dist/public
            const copyRecursiveSync = (src: string, dest: string) => {
              const exists = fs.existsSync(src)
              const stats = exists && fs.statSync(src)
              const isDirectory = exists && stats && stats.isDirectory()

              if (isDirectory) {
                if (!fs.existsSync(dest)) {
                  fs.mkdirSync(dest, { recursive: true })
                }
                fs.readdirSync(src).forEach((childItemName) => {
                  copyRecursiveSync(path.join(src, childItemName), path.join(dest, childItemName))
                })
              } else {
                fs.copyFileSync(src, dest)
              }
            }

            if (fs.existsSync(publicDir)) {
              fs.readdirSync(publicDir).forEach((item) => {
                const srcPath = path.join(publicDir, item)
                const destPath = path.join(outputPublicDir, item)
                copyRecursiveSync(srcPath, destPath)
              })
            }
          }
        }
      }
    ],
    // css配置
    css: {
      // less配置
      preprocessorOptions: {
        less: {
          additionalData: '@import "@/assets/css/var.less";',
          javascriptEnabled: true,
        },
      },
      // 关键代码
      // postcss: {
      //   plugins: [
      //     autopreFixer({
      //         // 自动添加前缀
      //         overrideBrowserslist: [
      //             'Android 4.1',
      //             'iOS 7.1',
      //             'Chrome > 31',
      //             'ff > 31',
      //             'ie >= 8',
      //             //'last 2 versions', // 所有主流浏览器最近2个版本
      //         ],
      //         grid: true,
      //     }),
      //   ],
      // }
    },
    // 绝对路径配置
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '#': fileURLToPath(new URL('./public', import.meta.url)),
      },
      // 别名支持 此处必须有.vue  否则有的vscode不予支持
      extensions: [
        ".mjs",
        ".js",
        ".ts",
        ".jsx",
        ".tsx",
        ".json",
      ],
    },
    // 服务端
    server: {
      host: host,
      port: port,
      fs: {
        // 允许访问src目录和node_modules
        allow: [
          path.resolve(__dirname, 'src'),
          path.resolve(__dirname, 'node_modules')
        ],
        strict: false
      },
      proxy: {
        '^/node_modules': {
          target: 'http://localhost:8088',
          changeOrigin: true,
          rewrite: path => [process.cwd(), path].join(''),
        },
        ...createProxy(mode === 'v8.development' ? v8DevProxy : v5DevProxy, mode),
      },
      open: mode === 'v8.development' ? '/seeyon/login' : '/seeyon/'
    },
    // 打包去掉无用信息
    esbuild: process.env.NODE_ENV == 'development'?{}:{
      // drop: ["debugger"],
      // pure: ["console.log"],
    },
    build: {
      // outDir: 'dist',
      // sourcemap: process.env.NODE_ENV == 'development',
      outDir: resolve(__dirname, 'dist'),
      sourcemap: !isProd,
      // 禁用默认的 public 目录复制，使用自定义插件处理
      copyPublicDir: false,
      rollupOptions: {
        input: {
          // 'index': resolve(__dirname, `src/views/home/<USER>
          // 'portal': resolve(__dirname, `src/views/portal/index.html`),
          // 'singleAssistant': resolve(__dirname, `src/views/singleAssistant/index.html`),
          'newConversation': resolve(__dirname, `src/views/newConversation/index.html`),
          // 'assistantSquare': resolve(__dirname, `src/views/assistantSquare/index.html`),
          'biPreview': resolve(__dirname, `src/views/biPreview/index.html`),
          // 'searchResult': resolve(__dirname, `src/views/searchResult/index.html`),
          // 'historyList': resolve(__dirname, `src/views/historyList/index.html`),
          // 'historyConversation': resolve(__dirname, `src/views/historyConversation/index.html`),
          'chartPreview': resolve(__dirname, `src/views/chartPreview/index.html`),
          'portal-editor': resolve(__dirname, `src/views/portal-editor/index.html`),
        },
        output: {
          // 扁平化输出结构
          entryFileNames: 'js/[name].[hash].js',
          chunkFileNames: 'js/[name].[hash].js',
          assetFileNames: ({ name }) => {
            const ext = path.extname(name ?? '')
            const extName = ext.substring(1)
            // 图片文件
            if (['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp'].includes(extName)) {
              return `images/[name].[hash]${ext}`
            }
            // CSS 文件
            else if (extName === 'css') {
              return `css/[name].[hash]${ext}`
            }
            // 字体文件
            else if (['ttf', 'woff', 'woff2', 'eot', 'otf'].includes(extName)) {
              return `fonts/[name].[hash]${ext}`
            }
            // 其他资源文件
            return `assets/${extName}/[name].[hash]${ext}`
          }
        }
      }
    }
  }
});
