<template>
  <div
    class="relative w-full pb-2 btm_dlg ai-footer"
    :class="{
      'empty-footer': isEmpty,
      un_ck_btn: isAnswering,
      'has-file': dataAnalysisFiles?.fileList.length > 0
    }"
  >
  <!-- , -->
    <div v-if="!hideInputBox" class="relative flex items-end justify-between w-full mb-[10px]" :class="{'sidebar-toolbar':isMinWidth}">
      <PortalFooterAgent
        :class="{
        un_ck_btn: isAnswering
        }"
        v-if="
          showFooterAgent
        "
        v-onlyV5
        :agentList="showMenuList"
        @handleAgentClick="handleAgentClick"
        class="agent_wrap"
      />
      <!-- 占位 -->
      <div v-else></div>
      <SpecIcon
        v-if="showNewTopicBtn"
        @go-operate="addNewChat"
        :class="['new_btn', isAnswering ? 'un_ck_btn' : '']"
      >
        <template #default> {{ isMinWidth ? '新对话' :'发起新对话' }} </template>
        <template v-if="!isMinWidth" #tail_icon>
          <img class="img" src="@/assets/imgs/table_card_logo.png" alt="" />
        </template>
      </SpecIcon>
    </div>
    <DataAnalysis
        v-model:open="showDataAnalysis"
        :chatSessionId="uChatList.dynamicData.chatSessionId"
        ref="dataAnalysisRef"
        class="data-analysis self-drawer-container"
        @getRusultFiles="getRusultFiles"
      />
      <CollaborationTemplate class="self-drawer-container" v-if="showCollTemplate" @handleTemplateClick="handleTemplateClick" @closeTemplate="closeTemplate"></CollaborationTemplate>
      <QuickTile v-if="isShowQuickTile" class="self-drawer-container" @closeTile="closeTile" @handleShortcut="handleShortcut"></QuickTile>
    <!-- 底部输入框 -->
    <div class="relative input_wrap">
      <ElasticIpt
        ref="inputBox"
        v-if="!hideInputBox"
        :class="{ chatting_wrap: isFinish }"
        :transShowSal="isShowSal"
        :transCkAssist="ckAssist"
        :toOperteSal="operatingSal"
        :toOpertePrologue="operatePrologue"
        :isGeneralAssist="isGeneralAssist"
        :dataAnalysisFiles="dataAnalysisFiles"
        :quickMsg="quickMsg"
        :clearQuikcMsg="clearQuikcMsg"
      >
        <!-- 辅助助手 -->
        <template #checkAssist v-if="ckAssist.length > 0 && ckAssist[0].type != 'general'">
          <CheckAssist :ckAssist="ckAssist" :goDelSecAssist="goDelSecAssist" />
        </template>
      </ElasticIpt>
      <!-- 助手列表 -->
      <div class="absolute w-full assistnat_list" ref="assistsRef">
        <SearchAssistList
          v-if="isShowSal"
          :transCkAst="ckAssist"
          @toGoSecItem="goingSecItem"
          @toCloseSal="isShowSal = false"
        />
      </div>
      <!-- 开场白列表 -->
      <div class="absolute w-full prologue_list" ref="prologueRef">
        <PrologueList v-if="isShowPropolugeList" :toOpertePrologue="operatePrologue" />
      </div>
    </div>

    <!-- tip信息 -->
    <div v-if="!isEmpty" class="ai_tip">AI生成内容仅供参考，请注意甄别信息准确性</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, inject, watch } from 'vue';
// @ts-ignore
import { aiToast } from '@/plugins/ai-toast';
// a-b-c
import ElasticIpt from '@/components/elasticIpt/index.vue';
import SearchAssistList from '@/components/searchAssistList/index.vue';
import PrologueList from '@/components/prologueList/index.vue';
import SpecIcon from '@/components/specButton/index.vue';
import CheckAssist from '@/components/checkAssist/index.vue';
// 1-2-3
import {
  useHomeSearch,
  useCheckAssistantParams,
  useTempRunningAssistInfo,
} from '@/stores/homeSearch';
import { useGeneralAsit, useGlobal, useMenu, useCustomData } from '@/stores/global';
import { useChatListAndElasticIpt, useConfig, useCustomMenu } from '@/hooks/portal';
import '@seeyon/seeyon-comi-plugins-library/dist/seeyon-comi-plugins-library.css';
import { useChatList } from '@/stores/chatList';
import cardInstance from '@/stores/card';
import PortalFooterAgent from '@/components/portalFooterAgent/index.vue';
import DataAnalysis from '@/components/dataAnalysis/index.vue';
import CollaborationTemplate from '@/components/collaborationTemplate/index.vue';
import QuickTile from '@/components/quickTile/index.vue';
import { message } from 'ant-design-vue';
import { openDobuleScreen } from '@/utils/storesUtils';
import {AGENT_CODE} from '@/utils/constant';
const useGlobalStore = useGlobal();
const useCustomDataStore = useCustomData();
const config = ref<any>(null);
const { currentMenu } = useCustomMenu(config);
const { menuLists } = useConfig(currentMenu, config);
const uGlobal = useGlobal();
const viewMode = inject('viewMode') as string;

const props = defineProps({
  showNewTopicBtn: {
    // 是否显示新开会话按钮
    type: Boolean,
    default: true,
  },
  oaEmpty: {
    // 协同数据是未
    type: Boolean,
    default: false,
  },
});
const isPortal = inject('isPortal');
const { changeState, globalState } = useGlobal();
const uGeneralAsit = useGeneralAsit();
const uChatList = useChatList();
const {
  ckAssist,
  isShowSal,
  isShowPropolugeList,
  isFinish,
  goingSecItem,
  goDelSecAssist,
  operatingSal,
  operatePrologue,
} = useChatListAndElasticIpt();
const uMenu = useMenu();
const uTempRanAst = useTempRunningAssistInfo();
const assistsRef = ref(); // 助手dom实例
const isHideInput = ref(false);
const showDataAnalysis = ref(false);
const quickMsg = ref('');
const dataAnalysisFiles = ref();
const showCollTemplate = ref(false);
const isShowQuickTile = ref(false);

const showMenuList = computed(()=>{
  return menuLists.value.filter(item => {
    if(!item.role && !item.excludeRole) { //没有设置任何权限
      return item;
    }else if(item?.role && judgeInArray(globalState.posts, item.role)?.length > 0) {
      return item;
    }else if(item?.excludeRole && judgeInArray(globalState.posts, item.excludeRole)?.length === 0) {
      return item;
    }
  })
});
// 是否是超级助手
const isGeneralAssist = computed(() => {
  return ckAssist.length && ckAssist[0]?.isComi ? true : false;
});
//是否是侧边栏
const isMinWidth = computed(()=>{
  return uChatList.dynamicData.dobuleScreenData.show || !globalState.isFullScreen;
});
const sdkInstance = inject('sdkInstance') as any;
const hideInputBox = computed(() => {
  if (isHideInput.value) {
    return true;
  }
  if (
    sdkInstance.preConfig &&
    sdkInstance.preConfig.hideInputBoxAssistIds.includes(ckAssist[0]?.id)
  ) {
    return true;
  }
  return false;
});
sdkInstance.bind('hideInputBox', (v: any) => {
  isHideInput.value = v;
});

const inputBox = ref();

// 超级助理且是全屏并且无对话记录和无协同数据的时候，显示新状态
const isEmpty = computed(() => {
  return (
    props.oaEmpty &&
    uChatList.dynamicData.allCardData.length === 0 &&
    isGeneralAssist &&
    globalState.isFullScreen
  );
});

// 是否显示底部智能体菜单 -- comi显示
const showFooterAgent = computed(() => {
  return menuLists?.value?.length  && uMenu?.currentMenuInfo?.id === 'comi' && useGlobalStore.globalState.comiContainerName === 'v5' && ['1.1','A6'].includes(viewMode);
});

// 是否正在回答中
const isAnswering = computed(() => {
  return uChatList.dynamicData.isAnswering;
});

const judgeInArray = (arr: any[] , arr1: any[]) => {
  return arr.filter((item)=> arr1.includes(item));
};
// 快捷问题发送
const handleAgentClick = (id: string, item:any = null) => {
  // 如果在回答过程中，禁止快捷问题触发

  if (isAnswering.value) {
    return;
  }
  if(item?.from === "quickOperation") {
    // 切换快捷操作的时候，清空快捷问题和文件
    quickMsg.value = '';
    if (dataAnalysisFiles.value) {
      dataAnalysisFiles.value.fileList = [];
      dataAnalysisFiles.value.confileList = [];
    }
    
  // if(quickKey.includes(id)) {
    // url跳转
    if(item.url) {
      jumpToTarget(item.url);
      return;
    }
    const params = {
      inputTemplate: item.inputTemplate || [],
      // assistant: ''
    };
    if(id === 'createColl') {
      showCollTemplate.value = true;
    }else if(id === 'shortcut') {
      isShowQuickTile.value = true;
    }else if(id === 'dataAnalysis') {
      showDataAnalysis.value = true;
    }
    // Demo: 临时处理假数据, 看后续是否需要
    // params.assistant = {
    //             "id": "7604650180473671046",
    //             "code": "assist-8217067799309565656",
    //             "name": "协同办公处理",
    //             "introduce": "协同办公处理",
    //             "iconUrl": "",
    //             "outputType": 0,
    //             "labels": [
    //                 {
    //                     "id": "1505734044249834843",
    //                     "name": "w123",
    //                     "type": 0,
    //                     "createTime": 1749015914343
    //                 }
    //             ],
    //             "createUserId": "1914265650923954176",
    //             "createUserName": null,
    //             "lastUseTime": null,
    //             "createTime": 1745412752267,
    //             "updateTime": 1751262215478,
    //             "openRecommend": 1,
    //             "fileUpload": 0,
    //             "type": 0,
    //             "sort": null,
    //             "isSysPreset": null
    //         };
    // if(params.assistant){
    //   changeSearchParams(params.assistant, 2);
    // }
    sdkInstance.sendMsg('', params);
    return;
  }else {
    cardInstance.sendMessage(id);
  }
};

const jumpToTarget = (url: string) => {
  // 如果是以http/https开头，判断是否同源，如果同源则双屏打开，不是同源就window.open打开, 如果不是http/https开头，默认双屏打开
  const regExp = /^https?:\/\//;
  if(regExp.test(url)) {
    const isSame = isSameOrigin(url);
    if(isSame) {
      openDoubleCommon(url);
    } else {
      clearInputTemplate();
      window.open(url);
    }
  }else {
    openDoubleCommon(url);
  }
}
const isSameOrigin = (url: string) => {
  const currentUrl = window.location.href;
  const currentOrigin = new URL(currentUrl).origin;
  const targetOrigin = new URL(url).origin;
  return currentOrigin === targetOrigin;
};
// 获取文件上传结果
const getRusultFiles = (obj: any) => {
  console.log('getRusultFiles', obj);
  showDataAnalysis.value = false;
  dataAnalysisFiles.value = obj;
  quickMsg.value = '针对上传文件中的数据，帮我做简要分析';
};

// 用于发送消息后，清空快捷消息，避免再次赋值同样的，输入框值不更新
const clearQuikcMsg = () => {
  quickMsg.value = '';
  closeSelfContainer(null, true);
};

// 开启一个新会话
const addNewChat = () => {
  const chartListLen = uChatList.dynamicData.allCardData.length || 0;
  if (chartListLen === 0) {
    aiToast({
      content: '已是最新对话',
    });
  } else {
    // 如果是非门户，点击的时候，如果打开了双屏知识源，这个时候要关闭双屏知识源
    if (!isPortal) {
      changeState('isFullScreen', false);
    }
    inputBox.value?.handleNewTopicClear();
    changeState('isNewTopic', true);
    if (uTempRanAst.astInfo.isComi) {
      goDelSecAssist();
    }
    uChatList.chatActions.setDynamicData('historyModel', false);
    uChatList.chatActions.clearAllData();
    useCustomDataStore.setCustomData('collApproveData', null);
    uChatList.chatActions.setNewChatSessionId();
  }
};

// 根据菜单信息初始化参数
const changeSearchParams = (newVal: any, model: number) => {
  if (newVal?.id) {
    ckAssist.length = 0;
    if(model === 2){
      ckAssist.push({
        ...newVal
      });
    }else{
      const isComi = uTempRanAst.astInfo.isComi;
      const curAsit = isComi ? uGeneralAsit.generalAsit : newVal;
      if (curAsit) {

        ckAssist.push({
          ...curAsit,
          type: 'general',
        });
      }
    }

  }
};

//处理模版点击
const handleTemplateClick = (template: any) => {

  let url = "";
  let options = {};
  if(template.id === 'newColl') { //新建自由协同
    url = "/seeyon/collaboration/collaboration.do?method=newColl&fromNewItem=true&rescode=F01_newColl&showTab=true";
    options = {url: url};
  }else {
    // 模板协同
    url = "/seeyon/collaboration/collaboration.do?method=newColl&fromNewItem=true&showTab=true&templateId=" + template.id + (window.top.CsrfGuard?.getUrlSurffix()||'');
    options = {url: url, ...template};
  }
  openDoubleCommon(url);
};
//处理磁贴点击
const handleShortcut = (shortcut: any) => {
  let url = shortcut.url || shortcut.portletUrl;
  if(url?.indexOf("/") !== 0 || !url) { //不是可跳转的链接
    message.warning('暂不支持，开发中尽请期待');
    return;
  }else if(url.indexOf('/seeyon') !== 0) {
    url = '/seeyon' + url;
  }
  openDoubleCommon(url);
}
const openDoubleCommon = (url: string) => {
  useGlobal().changeState('preLinkIsTodoList', false);
  clearInputTemplate();
  openDobuleScreen(url, 'iframe');
};
const closeTemplate = () => {
  showCollTemplate.value = false;
};

const closeTile = () => {
  isShowQuickTile.value = false;
};
const closeSelfContainer = (e: Event, closeDrawer: boolean) => {
  const target = e?.target;
  const selfContainerArr = document.querySelectorAll('.self-drawer-container');
  if(!target?.closest('.self-drawer-container') || closeDrawer) { //如果是点击的该区域，则不关闭
    showDataAnalysis.value = false;
    showCollTemplate.value = false;
    isShowQuickTile.value = false;
  }
};
const clearInputTemplate = () =>{
  const params = {
    inputTemplate: [],
  };
  sdkInstance.sendMsg('', params);
}
// 周期
onMounted(async () => {
  // 从主页跳入 或者 刷新
  changeSearchParams(uMenu.currentMenuInfo);
  document.addEventListener('click', closeSelfContainer, true);
});

// 监听路由
onUnmounted(() => {
  document.removeEventListener('click', closeSelfContainer, true);
});

watch(
  () => uMenu.currentMenuInfo,
  (newVal) => {
    changeSearchParams(newVal);
  },
  {
    deep: true,
  },
);
watch(()=>useCustomDataStore.customDataObj?.needNewChat, (newVal)=>{
  if(newVal) {
    // 停止会话
    cardInstance.interruptAction();
    addNewChat();
  }
})
</script>

<style scoped lang="less">
.ai-footer {
  position: relative;
  max-width: 824px;
  margin: 0 auto;
  .un_ck_btn {
    pointer-events: none;
  }
  .agent_wrap {
    // flex: 1;
    flex-wrap: wrap;
    gap: 10px;
  }
  .new_btn {
    margin-left: 10px;
    padding: 1px 1px 1px 12px;
    gap: 11px;
    background: linear-gradient(90deg, #d1dfff 0%, #cde9ff 27.88%, #eaecff 58.17%, #e9edfd 96.63%);

    .img {
      width: 28px;
      height: 28px;
    }
  }
  .sidebar-toolbar {
    .new_btn {
      border-radius: 20px;
      padding: 8px;
    }
  }
  .search_box_wrap {
    margin-top: 0px;
  }
  .input_wrap {
    position: relative;
  }
  .assistnat_list,
  .prologue_list {
    top: -9px;
    transform: translateY(-100%);
    z-index: 100;
  }
  .data-analysis {
    position: absolute;
    width: 100%;
    left: 0;
    bottom: calc(100% + 10px);
    z-index: 100;
  }
  .ai_tip {
    margin-top: 8px;
    font-family: PingFang SC;
    font-weight: @font-weight-400;
    font-size: 10px;
    line-height: 20px;
    letter-spacing: 0px;
    text-align: center;
    color: rgba(0, 0, 0, 0.25);
    text-align: center;
  }
}
.empty-footer {
  display: flex;
  flex-direction: column-reverse;

  .input_wrap {
    margin: 24px 0 16px 0;
    .assistnat_list,
    .prologue_list {
      top: -10px;
    }
    .data-analysis {
      top: -200px;
    }
  }
}
.has-file{
  .data-analysis {
      bottom: 182px;
    }
}
</style>
