import { ref, onMounted, reactive, computed, nextTick, provide } from 'vue'
import type { MenuItemType, UserType } from '@/types/portalTypes'
import ComiLogo from '@/assets/imgs/comi.png';
import { getUserInfo, getUserAvatar } from '@/api/portal/index'
import { buildUUID } from '@/utils/uuid';
import type { AssistInfo, ChatUserParams } from '@/types/index';
import { cancelChat } from '@/api/common';
import portalApi from '@/api/portal/portal-api'
import { useTempRunningAssistInfo } from '@/stores/homeSearch';
import { useGeneralAsit } from '@/stores/global';
import { useChatList } from '@/stores/chatList';


export const usePortalEvents = (emit: any) => {

  const searchingAnswers = (val: any) => {
    emit('searchingAnswers', val)
    console.log('searchingAnswers')
  }

  const operatingSal = (val: any) => {
    emit('operatingSal', val)
    console.log('operatingSal')
  }

  const opertePrologue = (val: any) => {
    emit('opertePrologue', val)
    console.log('opertePrologue')
  }

  const cancelingChat = () => {
    emit('cancelingChat')
    console.log('cancelingChat')
  }
  const handleAgentClick = (key: string) => {
    emit('handleAgentClick', key)
    console.log('cancelingChat')
  }
  const goingSecItem = (val: any) => {
    emit('goingSecItem', val)
  }
  const goDelSecAssist = (val: any) => {
    emit('goDelSecAssist', val)
  }
  const addNewChat = (val: any) => {
    emit('addNewChat', val)
  }

  return {
    searchingAnswers,
    operatingSal,
    opertePrologue,
    cancelingChat,
    handleAgentClick,
    goingSecItem,
    goDelSecAssist,
    addNewChat
  }
}

const menuMap = {}
type ConfigMenuItemType = {
  name: string,
  key: KeyType,
  type: 'AiIcon' | 'AAvatar',
  params: {
    iconName: string,
    src?: string,
    size?: number
  },
  code: string,
  id: string
}

type KeyType = 'comi' | 'office' | 'qa' | 'create' | 'summary' | 'dialog' | 'security' | 'fullMenu'

export const useCustomMenu = (config: any) => {
  const currentMenu = ref('comi');
  const menuList = computed<MenuItemType[]>(() => {
    if (config && config.value && Array.isArray(config.value.menus)) {
      return config.value.menus.map(item => {
        const { key, name, type, params, code, id } = item as ConfigMenuItemType;
        const mapItem = menuMap[key] || {};
        return {
          label: name,
          key,
          icon: 'home',
          type,
          params,
          code,
          id,
          ...mapItem
        }
      })
    }
    return []
  })

  const bottomMenuList = ref<MenuItemType[]>([
    {
      label: '历史会话',
      key: 'history',
      icon: 'home',
      params: {
        iconName: 'ai-icon-a-tablishihuihualiebiao'
      },
      type: 'AiIcon',
    },
    {
      label: '智能体',
      key: 'square',
      icon: 'home',
      params: {
        iconName: 'ai-icon-a-tabzhinengti'
      },
      type: 'AiIcon',
    },
  ])
  const setCurrentMenu = (key: string) => {
    currentMenu.value = key;
  }

  return {
    menuList,
    bottomMenuList,
    currentMenu,
    setCurrentMenu
  }
}


export const useUserInfo = () => {
  const userInfo = reactive<UserType>({
    avatar: '',
    name: '',
  })

  onMounted(async () => {
    const { data } = await getUserInfo() as any;
    const avatar = await getUserAvatar(data.id);
    userInfo.avatar = avatar as string;
    userInfo.name = data.name;
  })

  return userInfo
}


// 聊天以及列表
export const useChatListAndElasticIpt = () => {
  const chatListRef = ref<any>(null)

  const searchParams = ref<{
    chatSessionId: string;
    sessionId: null | string;
    assistantId: null | string;
    assistantCode: null | string;
    input: string;
    citations: any[];
  }>({
    chatSessionId: buildUUID(),
    sessionId: null,
    assistantId: null,
    assistantCode: null,
    input: '',
    citations: [],
  });

  const getNewPro = ref<{
    finish?: number;
    messageType?: number | null;
    stepType?: string;
    chatSessionId?: string;
    sessionId?: string;
    callId?: string;
    isHaveContent?: boolean;
    code?: string;
    id?: string
  }>(); // 最新step的id和信息


  const showChatList = ref(false);
  const isCanceling = ref(false); // 是否正在取消
  const isCanceled = ref(false); // 是否已经取消
  const ckAssist = reactive<AssistInfo[]>([]); // 选中助手
  const isShowSal = ref(false); // 助手框控制
  const isShowPropolugeList = ref(false); // 开场白

  const isFinish = computed(() => {
    return getNewPro.value?.finish === 0;
  });

  const showStop = computed(() => {
    return getNewPro.value?.finish == 0 && !isCanceling.value;
  });

  // 获取进度
  const tellingProgress = (val: any) => {
    getNewPro.value = val;
    if (val.finish == 1) {
      isCanceling.value = false;
      isCanceled.value = false;
    }
  };

  // 进行提问
  const searchingAnswers = (val: ChatUserParams) => {
    showChatList.value = true;
    console.log('searchParams.value', searchParams.value, val)
    nextTick(() => {
      searchParams.value = {
        ...searchParams.value,
        ...val,
      };
    });
  };

  // 选中某一个助手
  const goingSecItem = (val: AssistInfo) => {
    console.log('goingSecItem', val);
    const { chatActions } = useChatList();
    chatActions.setDynamicData('selectedAgentInfo', val)
    chatActions.setDynamicData('sesssionId', '')
    // 渲染助手
    ckAssist.length = 0;
    ckAssist.push({ ...val });
    // 隐藏助手框
    isShowSal.value = false;
  };

  // 删除某个助手
  const goDelSecAssist = () => {
    ckAssist.length = 0;
    // 删除后重置为超级助手
    const { changeAssistInfo } = useTempRunningAssistInfo();
    const { generalAsit } = useGeneralAsit()
    const { chatActions } = useChatList();
    chatActions.setDynamicData('selectedAgentInfo', generalAsit)
    chatActions.setDynamicData('sesssionId', '')
    changeAssistInfo(generalAsit)
    ckAssist.push({
      ...generalAsit,
      type: 'general',
    });
  };

  // 展示助手列表
  const operatingSal = (val: 'show' | 'hide') => {
    // console.log('是否展示', val);
    isShowSal.value = val === 'show' ? true : false;
  };

  // 控制开场白列表
  const operatePrologue = (val: 'show' | 'hide') => {
    console.log('operatePrologue');
    isShowPropolugeList.value = val === 'show' ? true : false;
  };

  // 取消sse 传1不提示
  const cancelingChat = (val?: 1) => {
    console.log('取消sse', val);
    isCanceling.value = true;
    // TODO：暂时做成前端停止
    isCanceled.value = true;
    if (!getNewPro.value?.callId || isCanceling.value) {
      return;
    }
    // 取消sse
    cancelChat({
      chatSessionId: getNewPro.value?.chatSessionId as string,
      sessionId: getNewPro.value?.sessionId as string,
      callId: getNewPro.value?.callId as string,
    }).then((res: any) => {
      if (res && res.code == 0 && res.data) {
        if (val != 1) {
        }
      } else {
        // message.error('取消失败');
      }
    });
  };

  return {
    chatListRef,
    searchParams,
    getNewPro,
    showChatList,
    isCanceling,
    isCanceled,
    ckAssist,
    isShowSal,
    isShowPropolugeList,
    isFinish,
    showStop,
    tellingProgress,
    searchingAnswers,
    goingSecItem,
    goDelSecAssist,
    operatingSal,
    operatePrologue,
    cancelingChat
  }
}


// 配置相关

export const useConfig = (currentMenu: any, config: any) => {

  const menuCodeMap = computed(() => {
    const result: any = {}
    config.value?.menus?.forEach((menu: any) => {
      result[menu.key] = menu.code
    })
    return result
  })
  const menuIdMap = computed(() => {
    const result: any = {}
    config.value?.menus?.forEach((menu: any) => {
      result[menu.key] = menu.id
    })
    return result
  })
  const menuPhrases = computed(() => {
    const result: any = {}
    config.value?.menus?.forEach((menu: any) => {
      result[menu.key] = menu.phrases
    })
    return result
  })

  const menuLists = computed(() => {
    if (menuPhrases.value && menuPhrases.value[currentMenu.value]) {
      return menuPhrases.value[currentMenu.value].map((item: any) => {
        return {
          ...item,
          id: item.key,
          name: item.label,
        }
      })
    }
    return []
  })

  const init = async () => {
    config.value = await portalApi.getConfig()
  }

  init()
  provide('portalConfig', config)

  return {
    menuCodeMap,
    menuIdMap,
    menuPhrases,
    menuLists
  }
}
