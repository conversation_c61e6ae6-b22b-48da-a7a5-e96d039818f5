<template>
  <div class="double-screen-container">
    <div class="header">
      <BackBtn />
      <div class="header-right" v-if="isPortal">
        <Tooltip :title="toolTipTitle" v-if="dobuleScreenIsFull">
          <i id="guide-step8" class="iconfont ai-icon-a-tubiaohuizhiguifan20" @click="expandOrCollapse" />
        </Tooltip>
        <Tooltip :title="toolTipTitle" v-else>
          <i class="iconfont ai-icon-a-tubiaohuizhiguifan20" @click="expandOrCollapse" />
        </Tooltip>
        <i class="iconfont ai-icon-cha" @click="closeDobuleScreen(sdkInstance)"></i>
      </div>
      <div class="header-right" v-else>
        <i id="guide-step9" class="iconfont ai-icon-shouqi1" @click="handleClose"></i>
      </div>
    </div>
    <div class="double-screen-content">
      <component :is="componentName" :content="uChatList.dynamicData.dobuleScreenData"></component>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, defineComponent, inject, nextTick } from 'vue';
import Knowledge from '@/components/knowledgeSource/index.vue';
import Iframe from './iframe.vue';
import { useChatList } from '@/stores/chatList';
import BackBtn from '@/components/common/backBtn/index.vue';
import { useGlobal } from '@/stores/global';
import { useStateRouter } from '@/stores/stateRouter';
import { closeDobuleScreen } from '@/utils/storesUtils';
import { commonBusinessClass } from '@/utils/commonBusinessClass';
import { Tooltip } from 'ant-design-vue';

defineComponent({
  name: 'doble-screen-container',
});

const isPortal = inject('isPortal');
const uChatList = useChatList();
const uGlobal = useGlobal();
const stateRouter = useStateRouter();
const sdkInstance = inject('sdkInstance') as any;

const toolTipTitle = computed(() => {
  return dobuleScreenIsFull.value ? '分屏查看' : '全屏查看';
});

const handleClose = () => {
  closeDobuleScreen();
  uGlobal.changeState('isFullScreen', false);
  nextTick(() => {
    try {
      // 直接调用对应的功能方法，而不是通过SDK
      const businessClass = commonBusinessClass();

      if (!businessClass) {
        console.error('业务类实例不可用');
        return;
      }

      if (typeof businessClass.close === 'function') {
        businessClass.collapse();
      } else {
        console.warn(`功能方法 close 不存在或不是函数`);
      }
    } catch (error) {
      console.error(`调用功能方法 close 时出错:`, error);
    }
  });
};

const componentName = computed(() => {
  switch (uChatList.dynamicData.dobuleScreenData.type) {
    case 'knowledge':
      return Knowledge;
    case 'iframe':
      return Iframe;
  }
});

const dobuleScreenIsFull = computed(() => {
  return uChatList.dynamicData.dobuleScreenIsFull;
});

// 是否可缩放
const canExpand = computed(() => {
  return isPortal && stateRouter.currentRoute.context?.canExpand;
});

// 双屏知识源缩放
const expandOrCollapse = () => {
  uChatList.chatActions.setDynamicData('dobuleScreenIsFull', !dobuleScreenIsFull.value);
};
</script>
<style lang="less" scoped>
.double-screen-container {
  width: 100%;
  height: calc(100% - 24px);
  background-color: #fff;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  margin: 12px 0;

  .header {
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    box-sizing: border-box;

    .iconfont {
      font-size: 24px;
      color: #00000066;
      cursor: pointer;
      &:hover {
        color: @sky;
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 16px;
      height: 56px;

      .iconfont {
        font-size: 20px;
        color: #6F7686;
        &.ai-icon-cha {
          margin-top: 3px;
          font-size: 24px;
        }
      }
      .ai-icon-shouqi1 {
        color: rgba(0, 0, 0, 0.6);
        &:hover {
          color: #4379ff;
        }
      }
    }
  }

  .double-screen-content {
    height: 100%;
    width: 100%;
    overflow: auto;
  }
  .double-screen-content::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .double-screen-content::-webkit-scrollbar-thumb:hover {
    background: #91a0b5;
  }
  .double-screen-content::-webkit-scrollbar-thumb {
    height: 50px;
    background: #bec7d5;
    border-radius: 6px;
  }
  .double-screen-content::-webkit-scrollbar-track-piece {
    width: 8px;
    background-color: #f5f5f5;
    border-radius: 6px;
  }
}

.silder-left {
  .double-screen-container {
    .header {
      height: 48px;
    }
  }
}
</style>
