import fetchStream from "./fetch-stream";
import { useStreamData } from '@/stores/chatList';
import type { CardDataType, StreamFnInstance } from "@/types/api";

// 创建一个单例来管理 store
let streamStoreInstance: ReturnType<typeof useStreamData> | null = null;

// 获取 store 实例的函数
const getStreamStore = () => {
  if (!streamStoreInstance) {
    streamStoreInstance = useStreamData();
  }
  return streamStoreInstance;
};

type StreamManagerType = {
  streams: StreamFnInstance[],
  bindObj: any,
  create: (card?: CardDataType, option?: any) => any,
  stopAllStreams: () => void,
  remove: (steam: StreamFnInstance) => void,
  bind: (card: CardDataType, stream: StreamFnInstance) => void,
  runBindStream: (card: CardDataType) => void,
}

const StreamManager = {} as StreamManagerType;
StreamManager.streams = [];
StreamManager.bindObj = {};

// 创建基础stream，后续都走通用
StreamManager.create = (card) => {
  const store = getStreamStore();
  store.streamActions.setStreamIsLoading(true);
  const stream = new fetchStream(card);
  StreamManager.streams.push(stream);
  return stream;
};

// 停止
StreamManager.stopAllStreams = () => {
  const store = getStreamStore();
  store.streamActions.setStreamIsLoading(false);
  StreamManager.streams.forEach((stream: StreamFnInstance) => {
    stream.stop(false);
  })
  StreamManager.streams = [];
}

// 移除
StreamManager.remove = (steam) => {
  // 移除steam
  StreamManager.streams = StreamManager.streams.filter(el => el._id != steam._id);
  // 全部移除完时，需要设置loading为false
  if (!StreamManager.streams.length) {
    const store = getStreamStore();
    store.streamActions.setStreamIsLoading(false);
  }
}

StreamManager.bind = (card, stream) => {
  if (card._id)
    StreamManager.bindObj[card._id] = stream;
};

StreamManager.runBindStream = (card) => {
  if (card._id) {
    const stream = StreamManager.bindObj[card._id];
    if (stream) {
      stream.run();
    }
  }
};

export default StreamManager;
