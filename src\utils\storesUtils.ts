import { useGlobal, useCustomData, useMenu } from '@/stores/global';
import { commonBusinessClass } from '@/utils/commonBusinessClass';
import { useChatList } from '@/stores/chatList';
import { inject } from 'vue';
import { useStateRouter } from '@/stores/stateRouter';
import { dealOaUrl, getQueryString } from './common';
import { useTodo } from '@/stores/todoData';

// 打开双屏
export const openDobuleScreen = (data: any, type: string, options?: any) => {
    const uGlobal = useGlobal();

    // 如果type是iframe，为data添加isFromComi=true参数
    if (type === 'iframe' && typeof data === 'string') {
       data = dealOaUrl(data);
    }

    // TODO:此处临时设置了处理待办列表和待办详情的特殊逻辑，后续还需要进一步优化
    const ctxPath = window._ctxPath || '/seeyon';
    if(data === `${ctxPath}/collaboration/collaboration.do?method=listPending&isFromComi=true`){
        uGlobal.changeState('keepTodoList', true);
    }else{
        uGlobal.changeState('keepTodoList', false);
    }
    const uChatList = useChatList();
    const isPortal = getQueryString('isPortal') === 'true' ? true : false;
    const topWindow:any = window.top || window;
    const viewModeName = 'comiTargetVersion';
    const viewMode = getQueryString(viewModeName) || window.sessionStorage.getItem(viewModeName) || topWindow[viewModeName] || window.localStorage.getItem(viewModeName) || '1.0';
    const dobuleScreenData = {
        show: true,
        data,
        type,
        origin: options?.origin || 'normal',
    };

    // 侧边栏，如果是iframe，直接打开新窗口
    if ( ['1.1','1.0','A6'].includes(viewMode) && !isPortal && type == 'iframe') {
        window.open(data, '_blank')
        return;
    }
    if ( ['1.1','1.0'].includes(viewMode) || (viewMode === 'A6' && type === 'knowledge')) {
        if (!uGlobal.globalState.isFullScreen) {
            const businessClass = commonBusinessClass();
            if (businessClass && typeof businessClass.expand === 'function') {
                if (!isPortal) {
                    businessClass.expand('0', '80%');
                } else {
                    businessClass.expand('0', '450px');
                }
            }
            uGlobal.changeState('isFullScreen', true);
        } else {
            const businessClass = commonBusinessClass();
            if (businessClass && typeof businessClass.expand === 'function') {
                if (!isPortal) {
                    businessClass.expand('0', '80%');
                } else {
                    businessClass.expand('0', '450px');
                }
            }
        }
        uChatList.chatActions.setDynamicData('dobuleScreenData', dobuleScreenData);
    }
    if (type === 'iframe') {
        if ( ['1.1','1.0'].includes(viewMode)) {
            uChatList.chatActions.setDynamicData('dobuleScreenIsKnowledge', true);
            if (isPortal) {
                uChatList.chatActions.setDynamicData('dobuleScreenIsFull', true);
            }
        } else {
            const topWindow = window.top || window;
            const openCtpWindow = topWindow.openCtpWindow;
            openCtpWindow({
                url: data,
            });
        }
    } else {
        // 如果是列表，关闭知识源详情
        uChatList.chatActions.setDynamicData('dobuleScreenIsKnowledge', false);
    }
}

// 获取当前双屏 iframe 的 window 对象
export const getDobuleScreenIframeWindow = (): Window | null => {
    const topWindow = window.top || window;
    return (topWindow as any).getDobuleContainerIframe?.() || null;
}


export const closeDobuleScreen = (sdkInstance?: any) => {
    let resolve: any = null;
    //定时器，保证finally执行
    let timeout: any = null;
    const closeFun = function () {
        if (timeout) {
            clearTimeout(timeout);
            timeout = null;
        }
        const uTodo = useTodo();
        const uChatList = useChatList();
        const useCustomDataStore = useCustomData();
        const uGlobal = useGlobal();
        const uMenu = useMenu();
        // 状态路由管理
        const stateRouter = useStateRouter();
        const data = {
            show: false,
            data: null,
            type: '',
            origin: 'normal',
        };
        uGlobal.changeState('keepTodoList', false);
        uGlobal.changeState('preLinkIsTodoList', false);
        if (useCustomDataStore.customDataObj?.collApproveData) { //将协同的也还原
            if (uChatList.dynamicData?.allCardData?.length) {
                useCustomDataStore.setCustomData('needNewChat', true);
            } else {
                useCustomDataStore.setCustomData('collApproveData', null);
            }
        }
        uChatList.chatActions.setDynamicData('dobuleScreenData', data);
        uChatList.chatActions.setDynamicData('dobuleScreenIsFull', false);
        uChatList.chatActions.setDynamicData('dobuleScreenIsKnowledge', false);
        uTodo.cleanState();

        // 关闭双屏的时候，清空历史堆栈信息
        stateRouter.clearHistory();

    };
    //before close
    try {
        const contentWindow: any = getDobuleScreenIframeWindow();
        if (contentWindow && contentWindow.closePageRefreshOpener && typeof contentWindow.closePageRefreshOpener === 'function') {
            resolve = contentWindow.closePageRefreshOpener(null, "comi");
        }
    } catch (e) {
        console.log("hook closeTabIframe failed", e)
    }
    //异步回调执行，先执行业务的方法再执行关闭
    if (resolve && resolve.then) {
        timeout = setTimeout(closeFun, 500);
        resolve.then(closeFun);
    } else {
        closeFun();
    }
};
