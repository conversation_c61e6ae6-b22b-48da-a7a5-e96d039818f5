<template>
  <div class="w-full h-[48px] top_ment_wrap mb-[8px]">
    <div class="flex items-center justify-between h-full w-full">
      <div class="flex gap-2 items-center pt-[10px] pb-[10px] pl-[12px] main_logo">
        <Avatar
          :class="assistantInfo.iconUrl ? '' : 'sky-bg'"
          :src="assistantInfo.iconUrl"
          :size="24"
          v-if="assistantInfo.isAsit && !assistantInfo.isComi"
        >
          {{ assistantInfo.iconUrl ? '' : assistantInfo.name.slice(0, 2) }}
        </Avatar>
        <Avatar :src="ComiLogo" :size="24" v-if="assistantInfo.isAsit && assistantInfo.isComi">
        </Avatar>
        <h2 class="m-0 truncate">{{ assistantInfo.isComi ? 'CoMi' : assistantInfo.name }}</h2>
        <div id="guide-step0" class="portal flex items-center gap-[4px]" @click="handleOpenPortal" v-onlyV5 v-viewMode="['1.1','1.0']">
          <i class="iconfont ai-icon-zhinengmenhu"></i>
          <span> 智能门户</span>
        </div>
      </div>
      <div class="right">
        <SearchInput v-if="showSearch" :type="type" :placeholder="placeholder" />
        <div class="main-option w-[68px] flex items-center justify-center" @click="handleClose">
          <i class="iconfont ai-icon-cha"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Avatar } from 'ant-design-vue';
import { computed, nextTick } from 'vue';
import { useTempRunningAssistInfo } from '@/stores/homeSearch';
import { useGeneralAsit, useMenu, useGlobal } from '@/stores/global';
import { commonBusinessClass } from '@/utils/commonBusinessClass';
import ComiLogo from '@/assets/imgs/comi-logo.png';
import SearchInput from '@/components/common/search-input/index.vue';
import { closeDobuleScreen } from '@/utils/storesUtils';
import { useChatList } from '@/stores/chatList';

const useGlobalStore = useGlobal();


defineOptions({ name: 'TopMenu' });

const uMenu = useMenu();
const uGlobal = useGlobal();
const chatListStore = useChatList();

const uTmRunAsit = useTempRunningAssistInfo(); // store实例
const { generalAsit } = useGeneralAsit();

const assistantInfo = computed(() => {
  return {
    name: uTmRunAsit?.$state?.astInfo?.customName || uTmRunAsit?.$state?.astInfo?.name || 'CoMi',
    iconUrl: uTmRunAsit?.$state?.astInfo?.iconUrl || generalAsit?.iconUrl,
    isAsit: uTmRunAsit?.$state?.astInfo?.isAsit || generalAsit?.isAsit,
    isComi: uTmRunAsit?.$state?.astInfo?.isComi || generalAsit?.isComi,
  };
});

// 是否展示搜索框
const showSearch = computed(() => {
  return uMenu.currentMenuInfo?.id === 'square' || uMenu.currentMenuInfo?.id === 'history';
});

// 搜索框placeholder
const placeholder = computed(() => {
  let placeholder = '';
  if (uMenu.currentMenuInfo?.id === 'square') {
    placeholder = '搜索智能体';
  } else if (uMenu.currentMenuInfo?.id === 'history') {
    placeholder = '搜索会话';
  }
  return placeholder;
});

// 搜索框类型
const type = computed(() => {
  let type = '';
  if (uMenu.currentMenuInfo?.id === 'square') {
    type = 'handleSearchAssistant';
  } else if (uMenu.currentMenuInfo?.id === 'history') {
    type = 'handleSearchHistory';
  }
  return type;
});

const windowId = `comi-portal`;

const handleOpenPortal = () => {
  // TODO: v8的跳转待确定
  if(useGlobalStore.globalState.comiContainerName === 'v8'){
    return;
  }else{
    // const newWindow = window as any;
    // newWindow.portalSpaceWindows = newWindow.portalSpaceWindows || {};
    // if(newWindow.portalSpaceWindows[windowId] && !newWindow.portalSpaceWindows[windowId].closed){
    //   newWindow.portalSpaceWindows[windowId].focus();
    // } else {
    //   newWindow.portalSpaceWindows[windowId] = newWindow.open('/seeyon/apps_res/comi/html/comi-portal.html', windowId);
    // }
    (window.top || window).open('/seeyon/apps_res/comi/html/comi-portal.html', '_blank');
  }
};

const handleClose = () => {
  closeDobuleScreen();
  uGlobal.changeState('isFullScreen', false);
  nextTick(() => {
    try {
      // 直接调用对应的功能方法，而不是通过SDK
      const businessClass = commonBusinessClass();

      if (!businessClass) {
        console.error('业务类实例不可用');
        return;
      }

      if (typeof businessClass.close === 'function') {
        businessClass.close();
      } else {
        console.warn(`功能方法 close 不存在或不是函数`);
      }
    } catch (error) {
      console.error(`调用功能方法 close 时出错:`, error);
    }
  });
};
</script>

<style scoped lang="less">
.top_ment_wrap {
  background-color: rgba(255, 255, 255, 0.6);
  :deep(.main_logo) {
    cursor: pointer;
  }

  .right {
    display: flex;
  }
  :deep(.search) {
    width: 130px !important;
    margin-right: 12px;
  }
  .portal {
    cursor: pointer;
    border: 1px solid rgba(209, 224, 255, 1);
    border-radius: 8px;
    height: 28px;
    padding: 4px 8px;
    i {
      display: inline-block;
      width: 20px;
      height: 20px;
      font-size: 18px;
      color: #6f7686;
    }
    span {
      font-family: PingFang SC;
      font-weight: @font-weight-500;
      font-size: 12px;
      line-height: 21px;
      letter-spacing: 0%;
      color: #6f7686;
    }
    &:hover {
      i {
        color: rgba(37, 38, 44, 1);
      }
      span {
        color: rgba(37, 38, 44, 1);
      }
    }
  }
  .main-option {
    i {
      width: 28px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      font-size: 20px;
      color: rgba(111, 118, 134, 1);
      cursor: pointer;
    }
    &:hover {
      i {
        color: rgba(63, 67, 77, 1);
        background-color: rgba(246, 246, 248, 1);
      }
    }
  }
}
</style>
