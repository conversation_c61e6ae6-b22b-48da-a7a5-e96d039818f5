<template>
  <div class="menu-waterfall-content">
    <div
      class="menu-waterfall-columns"
      :style="{ gridTemplateColumns: `repeat(${columnCount}, 1fr)` }"
    >
      <div
        v-for="(column, index) in columns"
        :key="index"
        class="menu-waterfall-column"
      >
        <template v-for="block in column" :key="block.idKey">
          <!-- 收藏块 -->
          <MenuFavoritesBlock
            v-if="block.isFavoritesBlock"
            :favoritesList="favoritesList"
            :searchValue="searchValue"
            @select="handleSelect"
            @remove="handleRemoveFavorite"
          />
          <!-- 导航块 -->
          <MenuNavigationBlock
            v-else-if="block.isNavigationBlock"
            :searchValue="searchValue"
            :navigationList="navigationList"
            @select="handleNavigationSelect"
          />
          <!-- 搜索结果数据块 -->
          <MenuBlock
            v-else-if="block.isBlock"
            :menuBlock="block"
            :searchValue="searchValue"
            :expandAll="expandAll"
            :favoriteIds="favoriteIds"
            @select="handleSelect"
            @navigationSelect="handleNavigationSelect"
            @favorite="handleFavorite"
            @expand="handleExpand"
          />
          <!-- 普通菜单块 -->
          <MenuBlock
            v-else
            :menuBlock="block"
            :searchValue="searchValue"
            :expandAll="expandAll"
            :favoriteIds="favoriteIds"
            @select="handleSelect"
            @navigationSelect="handleNavigationSelect"
            @favorite="handleFavorite"
            @expand="handleExpand"
          />
        </template>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import MenuBlock from './MenuBlock.vue'
import MenuFavoritesBlock from './MenuFavoritesBlock.vue'
import MenuNavigationBlock from './MenuNavigationBlock.vue'
import type { MenuItem } from '../types/menu'

interface Props {
  menuList: MenuItem[]
  searchValue: string
  expandAll: boolean
  favoriteIds: Set<string>
  columnCount: number
  // 新增：收藏菜单列表
  favoritesList: MenuItem[]
  // 新增：导航菜单列表
  navigationList: MenuItem[]
}

interface Emits {
  (e: 'select', item: MenuItem): void
  (e: 'navigationSelect', item: MenuItem): void
  (e: 'favorite', item: MenuItem): void
  (e: 'expand', item: MenuItem): void
  (e: 'removeFavorite', item: MenuItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const imageError = ref(false)

// 计算菜单项高度估算（用于瀑布流布局优化）
const getMenuItemHeight = (menu: MenuItem): number => {
  // 基础高度
  let height = 50 // 标题高度

  // 计算子菜单项高度
  if (menu.children && menu.children.length > 0) {
    height += menu.children.length * 28 // 每个子菜单项约28px

    // 如果有嵌套子菜单，再加上高度
    menu.children.forEach(child => {
      if (child.children && child.children.length > 0) {
        height += child.children.length * 28
      }
    })
  }

  return height + 20 // 加上padding
}

// 计算瀑布流分列（考虑高度平衡）
const columns = computed(() => {
  const columnCount = props.columnCount
  const result: MenuItem[][] = Array.from({ length: columnCount }, () => [])
  const columnHeights = new Array(columnCount).fill(0)

  // 如果有收藏菜单且不在搜索状态，在第一列第一个位置插入收藏块
  if (!props.searchValue) {
    const favoritesBlock: MenuItem = {
      id: 'favorites-block',
      idKey: 'favorites-block',
      nameKey: '我的收藏',
      children: props.favoritesList,
      isFavoritesBlock: true // 标记为收藏块
    }
    result[0].push(favoritesBlock)
    columnHeights[0] += getMenuItemHeight(favoritesBlock)
    
    // 在第一列第二个位置插入导航块
    const navigationBlock: MenuItem = {
      id: 'navigation-block',
      idKey: 'navigation-block',
      nameKey: '导航区域',
      children: [],
      isNavigationBlock: true // 标记为导航块
    }
    result[0].push(navigationBlock)
    columnHeights[0] += getMenuItemHeight(navigationBlock)
  }

  props.menuList.forEach(menu => {
    // 找到当前高度最小的列
    let minHeightIndex = 0
    for (let i = 1; i < columnHeights.length; i++) {
      if (columnHeights[i] < columnHeights[minHeightIndex]) {
        minHeightIndex = i
      }
    }

    // 将菜单项添加到高度最小的列
    result[minHeightIndex].push(menu)
    columnHeights[minHeightIndex] += getMenuItemHeight(menu)
  })

  return result
})

// 事件处理
const handleSelect = (item: MenuItem) => {
  emit('select', item)
}

const handleNavigationSelect = (item: MenuItem) => {
  emit('navigationSelect', item)
}

const handleFavorite = (item: MenuItem) => {
  emit('favorite', item)
}

const handleExpand = (item: MenuItem) => {
  emit('expand', item)
}

const handleRemoveFavorite = (item: MenuItem) => {
  emit('removeFavorite', item)
}


// 监听菜单数据变化，重新计算布局
watch(() => props.menuList, () => {
  // 瀑布流布局会自动重新计算
}, { deep: true })
</script>

<style lang="less" scoped>
.menu-waterfall-content {
  height: 100%;
  overflow-y: auto;
  // padding: 12px 0;

  .menu-waterfall-columns {
    display: grid;
    gap: 12px;
    align-items: start;
    padding-bottom: 20px;

    .menu-waterfall-column {
      display: flex;
      flex-direction: column;
      gap: 12px;
      min-width: 0;
    }
  }

  .menu-waterfall-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;

    .empty-content {
      text-align: center;

      .empty-img {
        width: 120px;
        height: 120px;
        margin-bottom: 16px;
        opacity: 0.6;
      }

      .empty-text {
        font-size: 14px;
        color: #999;
      }
    }
  }
}

</style>
