import { ref, type Ref } from 'vue'
import { defineStore } from 'pinia'
import { storeToRefs } from 'pinia'
import { buildUUID } from '@/utils/uuid';
import { getQueryString } from '@/utils/common';
import type { CardDataType, CardContentType, AiDataCard, FileType } from '@/types/api';
import { nextTick } from 'vue';
import { useTodo } from './todoData';

// 定义 chatListStatus 的接口类型

// chatList 数据状态管理
const useChatList = defineStore('chatListData', () => {
    // 静态的非响应式数据
    const staticData = {
        lastCard: null as CardDataType | null,
        streamParams: {
            assistantId: '',
            assistantCode: '',
            chatSessionId: '',
            citations: [] as FileType[],
            input: '',
            sessionId: '',
        },
        lastTime: 0,
        assistantId: '',
        lastCardTime: null,
        callId: null,
        isInterrupt: false,
        aiSessionIdKey: '',
        weekRequestCount: 0,
        instanceLongTouch: null,
        hasVoiceKey: false,
        uploadFileInfo: null,
    }
    // 响应式数据
    const dynamicData = reactive({
        allCardData: [] as AiDataCard[], //页面上数据的data
        isAnswering: false, //是否正在回答
        isChat: false, // 是否是对话页
        isLoading: false,
        isOpenVoice: false, //是否开启语音
        requestFailure: false, //是否发送失败了
        lastRequestTime: null, //最后一次发送的时间
        chatSessionId: buildUUID(),
        sesssionId: '',
        // 判断是主 COMI 还是 单个智能体 single => true:单体对话;false:comi对话
        single: getQueryString('isSingle').toLowerCase() === '0' ? true : false,
        // historyModel => true: history模式；false:chat模式
        historyModel: getQueryString('fromHistory').toLowerCase() === 'true' ? true : false,
        historySessionId: getQueryString('historySessionId'),//历史记录的sessionId
        isNewView: getQueryString('isNewView') === '1' ? true : false,
        isScrolling: true,
        assistantInfo: null, // 助手信息
        showScrollBtn: false, // 是否显示滚动按钮
        currentTabKey: '', // 智能体页签
        selectedAgentInfo: null, //选中的agent的信息
        isHide: false, // 是否需要隐藏问题
        lockScroll: false, // 是否锁定滚动
        isDialogued: false,
        // 历史记录相关请求参数
        historyDetailsParams: {
            pageNumber: 1,
            pageSize: 10,
            needTotal: true,
            pages: 0,
        },
        dobuleScreenData: {
            show: false,
            data: null,
            type: '',
            origin: 'normal'
        },
        // 双屏数据是否全屏展示
        dobuleScreenIsFull: false,
        // 是否进入双屏知识源详情
        dobuleScreenIsKnowledge: false,
        // 是否显示开场白
        showProlog: true,
        // 人员卡片标签数据
        memberTagData: {} as Record<number, any[]>,
    });

    const chatActions = {
        // 设置静态数据的属性
        setStaticData: <K extends keyof typeof staticData>(key: K, val: any) => {
            staticData[key] = val
        },
        // 设置响应式数据的属性
        setDynamicData: <K extends keyof typeof dynamicData>(key: K, val: any) => {
            dynamicData[key] = val
        },
        // 清除数据
        clearAllData: (isRedirectAist: boolean = false) => {
            const uTodo = useTodo();
            dynamicData.allCardData = [];
            dynamicData.sesssionId = '';
            // 清除最后一个时间标记
            staticData.lastTime = 0;
            staticData.callId = null;
            staticData.weekRequestCount = 0;
            dynamicData.historyDetailsParams = {
                pageNumber: 1,
                pageSize: 10,
                needTotal: true,
                pages: 0,
            }
            // 是否重定向助手，如果不是，则清空知识源
            if (!isRedirectAist) {
                // 清除知识源数据
                dynamicData.dobuleScreenData = {
                    show: false,
                    data: null,
                    type: '',
                    origin: 'normal'
                };
                dynamicData.dobuleScreenIsKnowledge = false;
            }
            // 清除人员卡片数据
            dynamicData.memberTagData = {} as Record<number, any[]>;
            dynamicData.dobuleScreenIsFull = false;
            dynamicData.showProlog = true;
            uTodo.cleanState();
        },
        // 添加自定义卡片
        addCustomCard: (card: CardDataType) => {
            dynamicData.allCardData.push(card);
        },
        // 在头部添加卡片
        addCustomCardBefore: (card: CardDataType) => {
            dynamicData.allCardData.unshift(card);
        },
        // 在尾部添加卡片
        addCustomCardAfter: (card: CardDataType) => {
            dynamicData.allCardData.push(card);
        },
        // 清除历史参数
        clearHistoryRelParams: () => {
            dynamicData.historyModel = false;
            dynamicData.historySessionId = '';
        },
        setNewChatSessionId:()=>{
            dynamicData.chatSessionId = buildUUID();
        }
    }

    const chatListRef = ref<HTMLElement | null>(null);

    // 获取 chatListRef
    const getChatListRef = () => {
        return chatListRef.value;
    };

    // 设置 chatListRef
    const setChatListRef = (ref: HTMLElement | null) => {
        chatListRef.value = ref;
    };

    // 滚动到底部
    const scrollToBottom = async (behavior: 'smooth' | 'auto' = 'smooth') => {
        await nextTick();
        const ref = getChatListRef();
        if (ref && !dynamicData.lockScroll) {
            ref.scrollTo({
                top: ref.scrollHeight + 40,
                behavior: behavior || 'smooth',
            });
        }
    };

    return { staticData, dynamicData, chatActions, getChatListRef, scrollToBottom, setChatListRef }
})

// card 数据状态管理
const useCardData = defineStore('cardData', () => {
    const card = ref({});
    const chatListStore = useChatList();
    const { dynamicData } = storeToRefs(chatListStore);

    const cardActions = {
        add: (data: CardDataType, targetData?: any, option?: any) => {
            if (!data) return;
            const now = new Date();
            const currTime = now.getTime();
            data._id = data._id || currTime;
            data._t = currTime;
            data._index = data._index || currTime;
            // 禁止卡片响应式
            data = markRaw(data);
            if (data.data) {
                data.data = reactive(data.data);
            }
            // 清除属性
            if (chatListStore.staticData.lastCard && data.componentKey !== 'PersonalInfo') { //如果是人员卡片，不视为是最后一张卡片
                if (chatListStore.staticData.lastCard.config) {
                    chatListStore.staticData.lastCard.config.isLast = false;
                }
            }
            // 最后卡片标记
            if (['AiDataCard'].includes(data.componentKey)) {
                data.config = reactive({
                    isLast: true
                });
                chatListStore.staticData.lastCard = data;
            }
            if (targetData) {
                dynamicData.value.allCardData[dynamicData.value.allCardData.length - 1] = data;
            } else {
                // 只有新卡片才追加time标记 超过10分钟 测试代码 10秒钟
                if (chatListStore.staticData.lastTime && currTime - chatListStore.staticData.lastTime > 5 * 1000 * 60) {
                    // 追加时间卡片
                    cardActions.addTimeBox(currTime);
                } else {
                    chatListStore.staticData.lastTime = currTime;
                }
                dynamicData.value.allCardData.push(data);
            }
            // 触发存储
            // scrollToBottom();
            return data;
        },
        addTimeBox: (currTime: number) => {
            chatListStore.staticData.lastTime = currTime;
            cardActions.add({
                componentKey: 'AiShowTimeCard',
                _index: currTime,
                data: {},
                staticData: {
                    time: currTime
                }
            });
        },
        remove: (data: CardDataType) => {
            if (data) {
                dynamicData.value.allCardData = dynamicData.value.allCardData.filter((item: CardDataType) => item._id !== data._id);
                // 暂时注释掉不存在的函数调用
                // chatListStore.chatActions.removeCardFromHistory(data);
            }
        },
        find: (id: string | number, option = { reverse: true }) => {
            if (option.reverse) {
                return dynamicData.value.allCardData.reduceRight((target: CardDataType | null, item: CardDataType) => {
                    // 此处调整为字符串比较  目前接口返回的 id 值超出 parseInt 能处理的最大值
                    if (String(item._id) === String(id)) {
                        target = item;
                    }
                    return target;
                }, null);
            } else {
                return dynamicData.value.allCardData.find((item: CardDataType) => String(item._id) === String(id));
            }
        },
        getCardToText: (cardData: CardContentType[]) => {
            let text = '';
            if (cardData) {
                cardData.forEach((child: CardContentType) => {
                    if (!child.isCard && !child.isIframe) {
                        text += child.context;
                    }
                });
            }
            return text;
        }
    }

    return { card, cardActions }
})

// stream 流数据状态管理
const useStreamData = defineStore('streamData', () => {
    const streamIsLoading = ref<boolean>(false);

    const streamActions = {
        setStreamIsLoading: (val: boolean) => {
            streamIsLoading.value = val
        },
        getStreamIsLoading: () => {
            return streamIsLoading.value
        }
    }

    return { streamIsLoading, streamActions }
})

// 统一导出
export { useChatList, useCardData, useStreamData };
