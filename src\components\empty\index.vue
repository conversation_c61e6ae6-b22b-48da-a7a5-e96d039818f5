<template>
  <div class="custom_empty">
    <Empty :description="description" :image="EmptyPng" class="empty_img"></Empty>
  </div>
</template>
<script setup>
import { Empty } from 'ant-design-vue';
import EmptyPng from '@/assets/imgs/empty.png';
const props = defineProps({
  description: {
    type: String,
    default: '这里空空如也',
  },
});
</script>
<style lang="less" scoped>
.custom_empty {
  flex: 1;
  align-items: center;
  justify-content: center;
  display: flex;
  .empty_img {
    color: #00000066;

    .ant-empty-image {
      margin-bottom: 20px;
    }
  }
}
</style>
<style lang="less">
.custom_empty {
  ::v-deep .ant-empty-image {
    margin-bottom: 20px;
  }
}
</style>
