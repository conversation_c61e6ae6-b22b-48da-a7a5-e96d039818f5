# API 参考

> COMI Assistant SDK v2.0 完整 API 参考文档

本文档提供了 COMI Assistant SDK 的完整 API 参考，包含所有可用的方法、参数和返回值。

## 📋 目录

- [BusinessSDK API](#businesssdk-api)
- [CallbackExecutor API](#callbackexecutor-api)
- [CoreSDK API](#coresdk-api)
- [类型定义](#类型定义)
- [全局对象](#全局对象)
- [兼容性API](#兼容性api)

---

## BusinessSDK API

### 🚀 初始化相关

#### `initBusinessSdk(config?: BusinessConfig, coreConfig?: CoreConfig): BusinessSDK`

初始化业务SDK，返回SDK实例。

**参数：**
- `config?` - 业务配置选项
- `coreConfig?` - 核心SDK配置选项

**返回值：** `BusinessSDK` 实例

```typescript
import { initBusinessSdk } from '@/sdk/business/index.ts';

const sdk = initBusinessSdk({
  defaultAssistId: '5605278465501839487',
  hideInputBoxAssistIds: ['assist1', 'assist2'],
  autoInit: true
}, {
  timeout: 15000,
  systemId: 'my-system'
});
```

#### `waitForInitialization(): Promise<void>`

等待SDK完全初始化完成（包括所有方法绑定）。

```typescript
await sdk.waitForInitialization();
console.log('SDK已完全就绪，所有功能可用');
```

#### `isSDKInitialized(): boolean`

检查SDK是否已初始化完成。

```typescript
if (sdk.isSDKInitialized()) {
  console.log('SDK已初始化');
} else {
  console.log('SDK正在初始化中...');
}
```

### 💬 消息通信

#### `sendMsg(value: string, option?: MessageOptions): void`

发送消息到助手。

**参数：**
- `value: string` - 消息内容
- `option?: MessageOptions` - 消息选项

```typescript
// 基础用法
sdk.sendMsg('Hello COMI!');

// 带选项的消息
sdk.sendMsg('重要消息', {
  isHide: true,        // 隐藏输入框
  priority: 'high',    // 优先级
  timestamp: Date.now()
});
```

#### `setSendMsg(value: string, option?: any): void`

设置要发送的消息内容（预设消息）。

```typescript
sdk.setSendMsg('预设消息内容', {
  autoSend: false,
  placeholder: true
});
```

#### `pushCustomCard(value: string | CustomCard): void`

推送自定义卡片到聊天界面。

```typescript
// 推送简单文本卡片
sdk.pushCustomCard('业务提醒：请及时处理待办事项');

// 推送复杂对象卡片
sdk.pushCustomCard({
  title: '今日业绩报告',
  content: '销售额：¥50,000，订单：25单',
  type: 'info',
  actions: [
    { text: '查看详情', action: 'viewDetails' },
    { text: '生成报表', action: 'generateReport' }
  ],
  timestamp: Date.now()
});
```

### 🎯 助手控制

#### `redirectAssistId(id: string, showChat?: boolean, callback?: Function): void`

重定向到指定助手。

**参数：**
- `id: string` - 助手ID
- `showChat?: boolean` - 是否显示聊天界面，默认为true
- `callback?: Function` - 重定向完成后的回调函数

```typescript
// 基础重定向
sdk.redirectAssistId('5605278465501839487');

// 带回调的重定向
sdk.redirectAssistId('5605278465501839487', true, () => {
  console.log('重定向完成');
  sdk.sendMsg('您好，我是数据分析助手');
});

// 重定向后发送消息的完整示例
sdk.redirectAssistId('5605278465501839487', true, () => {
  sdk.sendMsg('请分析本月销售数据', { isHide: true });
});
```

### 🖥️ 界面控制

#### `hideInputBox(value: boolean): void`

控制输入框的显示/隐藏。

```typescript
// 隐藏输入框
sdk.hideInputBox(true);

// 显示输入框
sdk.hideInputBox(false);
```

#### `hideNextTime(): Promise<void>`

下次不再显示提示或弹窗。

```typescript
await sdk.hideNextTime();
console.log('已设置下次不再显示');
```

#### `expand(): void`

展开到全屏模式。

```typescript
sdk.expand();
```

#### `collapse(): void`

收缩到普通模式。

```typescript
sdk.collapse();
```

#### `close(): void`

关闭COMI界面。

```typescript
sdk.close();
```

#### `menuAction(item: MenuActionItem): void`

执行菜单操作（legacy方法，推荐使用具体的expand/collapse/close方法）。

```typescript
// 展开窗口
sdk.menuAction({ action: 'expand' });

// 关闭窗口
sdk.menuAction({ action: 'close' });

// 收缩窗口
sdk.menuAction({ action: 'collapse' });
```

### 🔗 业务功能

#### `getOaUrl(params: OaUrlParams): string`

生成OA系统URL。这是v2.0新增的重要功能。

**参数：**
```typescript
interface OaUrlParams {
  appType: string;      // 应用类型：'1'协同,'3'文档,'4'公文,'8'新闻等
  linkId: string;       // 业务单据ID
  clientType: string;   // 客户端类型：'1'Web,'2'Mobile,'3'Desktop
  openType?: string;    // 打开方式：'dialog','newTab','current'
  params?: object;      // 额外参数
}
```

```typescript
// 生成协同应用链接
const url = sdk.getOaUrl({
  appType: '1',           // 协同应用
  linkId: 'DOC_123456',   // 文档ID
  clientType: '1',        // Web客户端
  openType: 'dialog',     // 弹窗打开
  params: {
    readonly: true,
    version: 'latest'
  }
});

// 生成公文系统链接
const officialUrl = sdk.getOaUrl({
  appType: '4',           // 公文
  linkId: 'OFF_789012',   // 公文ID
  clientType: '1'         // Web客户端
});

console.log('生成的URL:', url);
```

#### `handleTagClick(params: any): void`

处理markdown标签点击事件。

```typescript
sdk.handleTagClick({
  tagType: 'link',
  url: 'https://example.com',
  target: '_blank'
});
```

#### `openWin(params: WindowParams): void`

打开知识源窗口或其他业务窗口。

```typescript
sdk.openWin({
  url: 'https://knowledge.company.com',
  title: '知识库',
  width: 800,
  height: 600
});
```

### 📊 状态管理

#### `getISCOMI(): boolean | null`

获取当前的嵌套关系标识。

**返回值：**
- `true` - COMI嵌入第三方系统
- `false` - 第三方系统嵌入COMI
- `null` - 未确定或未设置

```typescript
const iscomi = sdk.getISCOMI();
if (iscomi === true) {
  console.log('COMI嵌入第三方系统');
} else if (iscomi === false) {
  console.log('第三方系统嵌入COMI');
}
```

#### `getSystemStatus(): SystemStatus`

获取系统状态信息。

```typescript
const status = sdk.getSystemStatus();
console.log('系统状态:', status);
```

#### `getDebugInfo(): DebugInfo`

获取SDK调试信息。

```typescript
const debugInfo = sdk.getDebugInfo();
console.log('调试信息:', debugInfo);
```

### 🔧 绑定管理

#### `bind(key: string, value: any): void`

绑定功能对象或函数。

```typescript
// 绑定处理函数
sdk.bind('customHandler', (data) => {
  console.log('处理数据:', data);
});

// 绑定配置对象
sdk.bind('appConfig', {
  theme: 'dark',
  language: 'zh-CN',
  features: ['chat', 'analysis']
});
```

#### `getBound(key: string): any`

获取绑定的对象或函数。

```typescript
const handler = sdk.getBound('customHandler');
const config = sdk.getBound('appConfig');

if (handler) {
  handler({ message: 'test' });
}
```

#### `unbind(key: string): boolean`

移除绑定，返回是否成功移除。

```typescript
const removed = sdk.unbind('customHandler');
console.log('移除成功:', removed);
```

### 📡 事件管理

#### `addEventListener(event: string, handler: EventHandler): void`

添加事件监听器。

```typescript
// 监听消息接收
sdk.addEventListener('messageReceived', (data) => {
  console.log('收到消息:', data);
});

// 监听助手切换
sdk.addEventListener('assistantChanged', (assistantId) => {
  console.log('切换到助手:', assistantId);
});

// 监听错误事件
sdk.addEventListener('error', (error) => {
  console.error('SDK错误:', error);
});
```

#### `removeEventListener(event: string, handler?: EventHandler): void`

移除事件监听器。

```typescript
const messageHandler = (data) => console.log(data);
sdk.addEventListener('messageReceived', messageHandler);

// 移除特定监听器
sdk.removeEventListener('messageReceived', messageHandler);

// 移除所有该事件的监听器
sdk.removeEventListener('messageReceived');
```

#### `emitEvent(event: string, ...args: any[]): void`

触发事件，会同时在本地和其他窗口触发。

```typescript
// 触发自定义事件
sdk.emitEvent('customEvent', { 
  type: 'business_action',
  data: { orderId: '12345' }
});

// 触发多参数事件
sdk.emitEvent('dataProcessed', 'success', 100, { result: 'completed' });
```

### 📨 低级消息通信

#### `sendMessage(type: string, content: any): void`

发送原始消息到统一事件总线。

```typescript
// 发送自定义消息类型
sdk.sendMessage('business_data', {
  action: 'update_order',
  orderId: '12345',
  status: 'completed'
});

// 发送系统消息
sdk.sendMessage('system_event', {
  type: 'user_action',
  timestamp: Date.now()
});
```

---

## CallbackExecutor API

CallbackExecutor是v2.0新增的核心功能，提供智能回调管理。

### 构造函数

#### `new CallbackExecutor(timeout?: number)`

创建回调执行器实例。

**参数：**
- `timeout?: number` - 超时时间（毫秒），默认为10000

```typescript
const executor = new CallbackExecutor(15000); // 15秒超时
```

### 方法

#### `addCallback(callback: Function, isFromNew?: boolean): void`

添加回调函数到执行队列。

**参数：**
- `callback: Function` - 要执行的回调函数
- `isFromNew?: boolean` - 是否来自新的调用，用于防重复

```typescript
executor.addCallback(() => {
  console.log('SDK就绪后执行');
}, true);
```

#### `executeWhenReady(): void`

开始监听并在SDK就绪时执行回调。

```typescript
executor.executeWhenReady();
```

#### `checkSDKReady(): boolean`

检查SDK是否完全就绪（包括所有方法绑定）。

```typescript
if (executor.checkSDKReady()) {
  console.log('SDK完全就绪');
}
```

#### `getStatus(): ExecutorStatus`

获取执行器状态信息。

```typescript
const status = executor.getStatus();
console.log('执行器状态:', status);
```

---

## CoreSDK API

### MessageBus

#### `sendEvent(event: string, data?: any): void`

发送事件到消息总线。

```typescript
messageBus.sendEvent('custom_event', { message: 'Hello' });
```

#### `subscribe(event: string, handler: Function): void`

订阅事件。

```typescript
messageBus.subscribe('message_received', (data) => {
  console.log('收到消息:', data);
});
```

#### `unsubscribe(event: string, handler?: Function): void`

取消订阅。

```typescript
messageBus.unsubscribe('message_received', handler);
```

---

## 类型定义

### BusinessConfig

```typescript
interface BusinessConfig {
  defaultAssistId?: string;           // 默认助手ID
  hideInputBoxAssistIds?: string[];   // 需要隐藏输入框的助手ID列表
  defaultSendMsg?: boolean;           // 是否默认发送消息
  autoInit?: boolean;                 // 是否自动初始化
}
```

### CoreConfig

```typescript
interface CoreConfig {
  timeout?: number;                   // 超时时间
  systemId?: string;                  // 系统标识
  debug?: boolean;                    // 是否开启调试模式
}
```

### MessageOptions

```typescript
interface MessageOptions {
  isHide?: boolean;                   // 是否隐藏输入框
  priority?: 'low' | 'normal' | 'high'; // 消息优先级
  timestamp?: number;                 // 时间戳
  type?: 'text' | 'html' | 'markdown'; // 消息类型
}
```

### CustomCard

```typescript
interface CustomCard {
  title?: string;                     // 卡片标题
  content: string;                    // 卡片内容
  type?: 'info' | 'warning' | 'error' | 'success'; // 卡片类型
  actions?: CardAction[];             // 操作按钮
  timestamp?: number;                 // 时间戳
  metadata?: any;                     // 元数据
}

interface CardAction {
  text: string;                       // 按钮文本
  action: string;                     // 操作标识
  data?: any;                         // 操作数据
}
```

### OaUrlParams

```typescript
interface OaUrlParams {
  appType: string;                    // 应用类型
  linkId: string;                     // 业务单据ID
  clientType: string;                 // 客户端类型
  openType?: string;                  // 打开方式
  params?: Record<string, any>;       // 额外参数
}
```

### WindowParams

```typescript
interface WindowParams {
  url: string;                        // 窗口URL
  title?: string;                     // 窗口标题
  width?: number;                     // 宽度
  height?: number;                    // 高度
  target?: string;                    // 目标窗口
}
```

### SystemStatus

```typescript
interface SystemStatus {
  initialized: boolean;               // 是否已初始化
  coreSDKReady: boolean;              // 核心SDK是否就绪
  businessSDKReady: boolean;          // 业务SDK是否就绪
  methodsBound: boolean;              // 方法是否已绑定
  callbacksExecuted: boolean;         // 回调是否已执行
  lastUpdate: number;                 // 最后更新时间
}
```

### DebugInfo

```typescript
interface DebugInfo {
  version: string;                    // SDK版本
  buildTime: string;                  // 构建时间
  environment: string;                // 运行环境
  features: string[];                 // 可用功能
  statistics: {                       // 统计信息
    messagesCount: number;            // 消息数量
    eventsCount: number;              // 事件数量
    errorsCount: number;              // 错误数量
  };
}
```

---

## 全局对象

### window.COMI_ASSISTANT

全局COMI助手对象，包含所有SDK功能。

```typescript
interface COMI_ASSISTANT {
  preSdk: {
    openDrawer: (callback: Function) => void;
    callback?: Function;              // 兼容性单回调
  };
  sdk: BusinessSDK;                   // 业务SDK实例
  getOaUrl?: (params: OaUrlParams) => string; // OA URL生成函数
}
```

### window.COMI_UTIL

COMI工具函数集合。

```typescript
interface COMI_UTIL {
  handleTagClick?: (params: any) => void;
  openWin?: (params: WindowParams) => void;
  [key: string]: any;                 // 其他工具函数
}
```

### window.comiEventBus

全局事件总线，由comi-entry.js创建。

```typescript
interface ComiEventBus {
  emit: (event: string, ...args: any[]) => void;
  on: (event: string, handler: Function) => void;
  off: (event: string, handler?: Function) => void;
  once: (event: string, handler: Function) => void;
}
```

---

## 兼容性API

为了保持向后兼容，SDK保留了以下旧版本API：

### 旧版preSdk.openDrawer

```javascript
// 兼容原有调用方式
window.COMI_ASSISTANT.preSdk.openDrawer(() => {
  // 回调函数
});

// 也兼容单回调模式
window.COMI_ASSISTANT.preSdk.callback = () => {
  // 单回调函数  
};
```

### 旧版menuAction

```javascript
// 仍然支持旧的menuAction调用
sdk.menuAction({ action: 'expand' });
sdk.menuAction({ action: 'collapse' });
sdk.menuAction({ action: 'close' });
```

---

## 📝 使用注意事项

### 1. 初始化顺序

确保按正确顺序初始化：

```typescript
// 1. 引入comi-entry.js
// 2. 等待COMI_ASSISTANT就绪
// 3. 通过openDrawer获取SDK实例
window.COMI_ASSISTANT.preSdk.openDrawer(() => {
  const sdk = window.COMI_ASSISTANT.sdk;
  // 现在可以安全使用所有SDK功能
});
```

### 2. 错误处理

始终进行错误检查：

```typescript
if (typeof window.COMI_ASSISTANT === 'undefined') {
  console.error('COMI SDK未加载');
  return;
}

if (!window.COMI_ASSISTANT.sdk) {
  console.error('SDK未就绪');
  return;
}
```

### 3. 异步操作

对于异步操作，建议使用Promise或async/await：

```typescript
async function initializeAndSend() {
  await sdk.waitForInitialization();
  sdk.sendMsg('SDK已就绪');
}
```

### 4. 方法绑定

v2.0会自动绑定外部方法，无需手动配置：

```typescript
// 自动绑定后可直接使用
const url = sdk.getOaUrl(params);
```
**智能产品部-应用部-前端团队**