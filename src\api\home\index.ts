import { get, post } from '../config.js';

import type { ServiceParams } from '@/types/index.ts';

// 获取服务列表接口-分页
export const getServiceListApi = (data: ServiceParams) => {
  return post(`ai-manager/service/info/page`, data);
};

// 获取【提示词】会话基本配置接口
export const getPromptConfig = (id: string) => {
  return get(`ai-manager/prompt/info/${id}`);
};
// 获取【Agent】会话基本配置接口
export const getAgentConfig = (id: string) => {
  return get(`ai-manager/agent/info/${id}`);
};

// 提示词运行测试接口
export const getChatApi = (data: any) => {
  return post(`ai-manager/prompt/info/call`, data);
};

// 获取Agent session列表-分页
export const getAgentSessionRecordApi = (data: any) => {
  return post(`ai-manager/agent/info/session/record/page`, data);
};

// 获取Agent调用记录执行步骤详情--对话列表
export const getAgentStepDetailApi = (data: any) => {
  return post(`ai-manager/agent/info/session/runStep/page`, data);
};

// 获取一次调试的详细运行日志
export const getAgentChatLogApi = (callId: any) => {
  return get(`ai-manager/agent/info/session/call/${callId}/log`);
};

// 根据助手code 获取助手 id
export const getAssistIdByCode = (codeList: Array<string>) => {
  return post(`ai-manager/assistant/info/auth-assistant`, { codeList });
};
