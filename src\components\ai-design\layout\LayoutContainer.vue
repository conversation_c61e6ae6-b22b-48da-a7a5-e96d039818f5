<template>
  <div class="portal-layout-container">
    <!-- 关键层：缓存TodoCardColumn等卡片组件 -->
    <keep-alive :max="12">
      <component 
        v-for="(item, index) in nodes" 
        :key="getStableKey(item, index)"
        v-bind="item.props" 
        :class="item.class" 
        :nodes="item.processedChildren || item.children" 
        :style="item.style"
        :is="item.tag" 
      />
    </keep-alive>
  </div>
</template>
<script setup lang="ts">
import { parseCondition } from '../utils/conditionUtil';

const { nodes } = defineProps({
  nodes: {
    type: Array,
    default: () => [],
  },
});

// 生成稳定的缓存key - 移除动态助手信息，确保key稳定
function getStableKey(item: any, index: number): string {
  const parts = [
    'container', // 层级标识  
    item.id,
    item.name,
    item.tag || 'component',
    item.type || 'default',
    item.class,
    // 不再包含会变化的助手信息，确保key在助手切换时保持稳定
    index
  ].filter(Boolean);
  
  const key = parts.join('-');
  return key;
}


</script>

<style scoped lang="less">
.portal-layout-container {
}
</style>
