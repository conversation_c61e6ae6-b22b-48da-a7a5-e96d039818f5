<template>
  <div class="data-chart-wrap">
    <div class="top">
      <i class="iconfont ai-icon-fangda fangda" @click="previewChart"></i>
    </div>
    <ComiEchart
      :content="cardItem.renderInfo"
      :contentHeight="400"
      style="width: 100%"
      :isPreview="isPreview"
    ></ComiEchart>
    <Modal
      v-if="isFullScreenPreview"
      title="查看图表"
      v-model:open="isFullScreenPreview"
      :width="1200"
      :isFullScreen="true"
      :isPreview="true"
      :transParams="cardItem"
      centered
      :footer="null"
      wrapClassName="chart-preview-overlay"
    >
      <ChartPreview :transParams="JSON.parse(JSON.stringify(props.cardItem))" :height="650" />
    </Modal>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import type { PropType } from 'vue';
import type { CardItem } from '../../../types';
import { ComiEchart } from '@seeyon/seeyon-comi-plugins-library';
import { commonBusinessClass } from '@/utils/commonBusinessClass';
import { Modal } from 'ant-design-vue';
import ChartPreview from '@/views/chartPreview/index.vue';

const props = defineProps({
  isPreview: {
    type: Boolean,
    default: false,
  },
  cardItem: {
    type: Object as PropType<CardItem>,
    default: () => {},
  },
});

const isFullScreenPreview = ref(false);
const previewChart = () => {
  const businessClass = commonBusinessClass();
  if (businessClass?.openDialog) {
    const previewDialog = businessClass.openDialog({
      url: `/seeyon/ai-platform/ai-static/ai-copilot/chartPreview/index.html`,
      // url: `/seeyon/ai-platform/copilot#/chart-preview`,
      id: 'chartPreview',
      footer: null,
      title: '查看图表',
      width: 1200,
      height: 720,
      transParams: JSON.parse(JSON.stringify(props.cardItem)),
      onCancel() {
        previewDialog.close();
      },
    });
  } else {
    isFullScreenPreview.value = true;
  }
};
</script>
<style lang="less" scoped>
.data-chart-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;

  .top {
    width: 100%;
  }

  .fangda {
    font-size: 16px;
    float: right;
    color: #00000066;
    cursor: pointer;
  }
}
</style>
<style lang="less">
.chart-preview-overlay {
  .ant-modal-content {
    height: 770px;
    .ant-modal-title {
    }

    .preview_container {
      padding: 0;
    }
  }
}
</style>
