import type { ButtonType } from '../types';

/**
 * 纯工具函数 - 无副作用，不依赖组件状态
 */

// 常量定义
export const NEXTSTEP_TIMEOUT = 30000; // 30秒超时

/**
 * DOM 和位置相关工具函数
 */
export class DomUtils {
  /**
   * 保存元素位置信息
   */
  static saveElementPosition(element: HTMLElement) {
    const rect = element.getBoundingClientRect();
    return {
      left: rect.left,
      top: rect.top,
      width: rect.width,
      height: rect.height,
      timestamp: Date.now()
    };
  }

  /**
   * 清理列表样式
   */
  static removeListStyle(): void {
    document.querySelectorAll('.rowDiv').forEach(el => el.classList.remove('active'));
  }
}

/**
 * API 相关工具函数
 */
export class ApiUtils {
  /**
   * 获取草稿内容
   */
  static async getDrafts(affairId: string): Promise<string> {
    try {
      const ctxPath = (window as any)._ctxPath || '/seeyon';
      const response = await fetch(`${ctxPath}/rest/coll/getDraftOpinion`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ affairId })
      });
      const data = await response.json();
      return data?.content || '';
    } catch (error) {
      console.error('获取草稿失败:', error);
      return '';
    }
  }
}

/**
 * 业务逻辑判断工具函数
 */
export class BusinessLogicUtils {
  /**
   * 智迈适配触发器检查
   */
  static checkTrigger(button: ButtonType): boolean {
    const triggerParams = {
      handleType: button.handleType,
      data: button.paramMap?.paramVO || {}
    };

    // 如果有第三方触发器，需要检查
    // const sendDevelop = $.ctp.trigger?.("beforeBatchDealColl", triggerParams);
    // if (!sendDevelop) return false;

    return true; // 暂时返回 true，实际项目中需要根据具体情况实现
  }

  /**
   * 检查是否需要触发NextStep流程
   */
  static checkNeedNextStepFlow(button: ButtonType): boolean {
    const { paramMap, handleType } = button;
    
    // 检查用户提供的条件
    const hasCustomAction = !!(paramMap?.customAction && paramMap.customAction.isOptional === "1");
    const isContinueSubmit = handleType === "ContinueSubmit";
    const hasDisagreeAttitude = !!(paramMap?.attitudeKey && paramMap.attitudeKey.indexOf("disagree") !== -1);
    
    return hasCustomAction && isContinueSubmit && hasDisagreeAttitude;
  }
}

/**
 * URL 构建工具函数
 */
export class UrlUtils {
  /**
   * 构建NextStep对话框URL
   */
  static buildNextStepDialogUrl(customAction: any): string {
    const ctxPath = (window as any)._ctxPath || '/seeyon';
    return `${ctxPath}/collaboration/collaboration.do?method=disagreeDeal` +
      `&isOptional=${customAction.isOptional}` +
      `&optionalAction=${customAction.optionalAction?.replace(',SpecifiesReturn', '') || ''}` +
      `&defaultAction=${customAction.defaultAction}`;
  }

  /**
   * 根据NextStep返回值构建新的URL
   */
  static buildUpdatedUrl(returnValue: string): string {
    const ctxPath = (window as any)._ctxPath || '/seeyon';
    
    switch (returnValue) {
      case 'repeal':
        return `${ctxPath}/rest/ia/affair/quick-submit/cancel`;
      case 'stepStop':
        return `${ctxPath}/rest/ia/affair/quick-submit/terminate`;
      case 'stepBack':
        return `${ctxPath}/rest/ia/affair/quick-submit/return`;
      default:
        return '';
    }
  }

  /**
   * 根据返回值获取对应的handleType
   */
  static getHandleTypeFromReturnValue(returnValue: string): string {
    switch (returnValue) {
      case 'repeal':
        return 'Cancel';
      case 'stepStop':
        return 'Terminate';
      case 'stepBack':
        return 'Return';
      default:
        return '';
    }
  }
}

/**
 * iframe 处理工具函数
 */
export class IframeUtils {
  /**
   * 从iframe获取返回值
   */
  static getIframeReturnValue(iframe: HTMLIFrameElement): string {
    if (!iframe || !iframe.contentWindow) {
      return 'continue';
    }

    try {
      const iframeWindow = iframe.contentWindow as any;
      
      // 检查iframe是否有getReturnValue方法
      if (typeof iframeWindow.getReturnValue === 'function') {
        return iframeWindow.getReturnValue();
      }
      
      // 如果没有方法，尝试从iframe内的form或选择元素获取值
      const iframeDoc = iframeWindow.document;
      const selectedOption = iframeDoc.querySelector('input[type="radio"]:checked') ||
        iframeDoc.querySelector('select option:selected') ||
        iframeDoc.querySelector('.selected');

      if (selectedOption) {
        return selectedOption.value || selectedOption.getAttribute('data-value') || 'continue';
      }
    } catch (e) {
      console.warn('无法从iframe获取返回值:', e);
    }
    
    return 'continue';
  }
} 