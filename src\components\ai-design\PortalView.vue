<template>
  <!-- PortalView 始终存在，保持 keep-alive 结构以维持缓存 -->
  <div class="portal-view-container w-full h-full">
    <keep-alive :max="10">
      <div key="portal-content-stable" class="w-full h-full">
        <!-- Copilot 模式 -->
        <div 
          v-if="showPortal && hasComponent && isCopilot" 
          class="portal-view w-full h-full overflow-hidden relative"
        >
          <div 
            :style="{ display: children.length > 0 ? 'block' : 'none' }"
            style="position: absolute;left: 0;top: 0;right: 0;height: 440px;"
            id="guide-step7"
          >
            <LayoutCarousel :nodes="children">
            </LayoutCarousel>
          </div>
          <!-- Copilot模式的占位内容 -->
          <div 
            :style="{ display: children.length === 0 ? 'flex' : 'none' }"
            class="w-full h-full justify-center"
          >
            <PresentQuestions v-if="shouldShowPresentQuestions"/>
          </div>
        </div>
        
        <!-- Portal 工作台模式 -->
        <div 
          v-else
          class="portal-view w-full h-full"
        >
          <div 
            :style="{ display: (showPortal && actualShowState && !isCopilot && children.length > 0) ? 'flex' : 'none' }"
            class="portal-components w-full h-full items-center justify-center"
          >
            <component 
              v-for="(item, index) in children" 
              :key="getStablePortalKey(item, index)"
              v-bind="item.props" 
              :class="item.class" 
              :nodes="item.processedChildren" 
              :style="item.style"
              :is="item.tag" 
            />
          </div>
          <!-- Portal工作台模式的占位内容 -->
          <div 
            :style="{ display: (!showPortal || !actualShowState || isCopilot || children.length === 0) ? 'block' : 'none' }"
            class="w-full h-full items-center "
            :class="{'pt-[56px]': !isCopilot && isNotDoubleScreen}"
          >
            <PresentQuestions v-if="shouldShowPresentQuestions"/>
          </div>
        </div>
        
      </div>
    </keep-alive>
  </div>
</template>

<script setup lang="ts">
import { computed, inject } from 'vue';
import { parseCondition, getCopilotNode } from './utils/conditionUtil';
import { usePortalStore } from '@/stores/portal';
import PresentQuestions from '@/components/chatList/components/presentQuestions/index.vue';
import { useChatList } from '@/stores/chatList';
import { useMenu } from '@/stores/global';
import { useGlobal } from '@/stores/global';

const { view, showPortal } = defineProps({
  view: {
    type: Object,
  },
  showPortal: {
    type: Boolean,
    default: true,
  },
});

const useGlobalStore = useGlobal();
const portalStore = usePortalStore();
const chatList = useChatList();
const menu = useMenu();
const viewMode = inject('viewMode') as string;

// 缓存hasComponent状态，确保切换助手时组件结构稳定
let cachedHasComponent: any = null;
const hasComponent = computed(() => {
  const realHasComponent = portalStore.hasComponent;
  const currentResult = realHasComponent && !chatList.dynamicData.dobuleScreenData.show &&  menu.currentMenuInfo?.id === 'comi' && useGlobalStore.globalState.comiContainerName === 'v5';
  
  // 只有当实际有组件数据时才更新缓存，否则保持之前的缓存状态
  if (realHasComponent && currentResult) {
    cachedHasComponent = realHasComponent;
  }
  
  const finalResult = cachedHasComponent && !chatList.dynamicData.dobuleScreenData.show && menu.currentMenuInfo?.id === 'comi' && useGlobalStore.globalState.comiContainerName === 'v5';
  
  return finalResult;
});

const isCopilot = inject('isCopilot');

const isNotDoubleScreen = computed(() => { 
  return !chatList.dynamicData.dobuleScreenData.show
});

// 递归预处理函数 - 处理所有层级的数据
function recursivelyProcessChildren(items: any[]): any[] {
  return items.map(item => {
    const processedItem = { ...item };
    if (item.children && item.children.length > 0) {
      // 先处理当前层级
      const currentProcessed = parseCondition(item.children);
      // 递归处理下一层级
      processedItem.processedChildren = recursivelyProcessChildren(currentProcessed);
    } else {
      processedItem.processedChildren = [];
    }
    return processedItem;
  });
}

// 缓存子组件数据 - 关键修复！
let cachedChildren: any[] = [];
const children = computed(() => {
  if(useGlobalStore.globalState.comiContainerName === 'v8' || !['1.1','A6'].includes(viewMode)){
    return [];
  }
  // 只有在有效数据时才更新缓存，否则保持之前的数据
  if (view?.children) {
    let result;
    if (isCopilot) {
      result = getCopilotNode(view.children);
    } else {
      result = parseCondition(view.children);
    }
    if (result && result.length > 0) {
      // 递归预处理所有层级的数据
      cachedChildren = recursivelyProcessChildren(result);
    }
  }
  return cachedChildren;
});

// 计算实际显示状态 - 用于控制组件可见性，而不影响组件存在性
const actualShowState = computed(() => {
  // 使用实时数据确保切换助手时立即响应
  const realHasComponent = portalStore.hasComponent;
  const isCurrentMenuComi = menu.currentMenuInfo?.id === 'comi';
  const isCorrectContainer = useGlobalStore.globalState.comiContainerName === 'v5';
  
  // 必须同时满足：有组件数据、当前是comi菜单、正确的容器、非双屏模式
  const realShouldShow = realHasComponent && isCurrentMenuComi && isCorrectContainer && isNotDoubleScreen.value;
  
  return realShouldShow;
});

// 是否显示预设问题
const shouldShowPresentQuestions = computed(() => {
  return chatList.dynamicData.allCardData.length === 0 && 
         !chatList.dynamicData.historyModel && 
         chatList.dynamicData.showProlog;
});

// 生成顶层稳定的缓存key - 移除动态助手信息
function getStablePortalKey(item: any, index: number): string {
  const parts = [
    'portal', // 顶层标识
    item.id,
    item.name,
    item.tag || 'component',
    item.type || 'default', 
    item.class,
    // 不再包含会变化的助手信息，确保key稳定
    index
  ].filter(Boolean);
  
  const key = parts.join('-');
  return key;
}


</script>

<style scoped lang="less">
.portal-view {
  width: 100%;
  height: 100%;
  max-height: 100%;
  overflow-y: scroll;
  display: flex;
  :deep(.ant-carousel){
    margin-top: -31px;
    position: relative;
    z-index: 2;
  }
}
</style>
