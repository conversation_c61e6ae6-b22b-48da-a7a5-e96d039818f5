<template>
  <div class="plugin-name-desc">
    <p class="plugin-name">{{ title }}</p>
    <p class="plugin-desc">
      <slot name="desc">
        <div v-if="description">{{ description }}</div>
        <div v-else>好的，已为您查询到{{ total }}条结果</div>
      </slot>
    </p>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';

// 定义 props
const props = defineProps({
  total: {
    required: false,
    type: Number,
    default: 0,
  },
  title: {
    required: true,
    type: String,
    default: '',
  },
  description: {
    required: false,
    type: String
  },
  loading: {
    required: false,
    type: Boolean
  }
});
</script>

<style lang="less" scoped>
.plugin-name-desc {
  margin-bottom: 8px;
  .plugin-name {
    font-weight: @font-weight-400;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.4);
    line-height: 20px;
  }

  .plugin-desc {
    line-height: 22px;
    font-weight: @font-weight-400;
    font-size: 14px;
    color: #000000;
  }

  .box {
    border-radius: 8px 8px 2px 8px;
    background: #7559f8;
    max-width: 86%;
    padding: 8px 16px;
    color: #fff;
    font-size: 14px;
    line-height: 22px;
    /* 137.5% */
    letter-spacing: 0.16px;
    width: fit-content;
    overflow: hidden;
    word-break: break-all;
    white-space: pre-wrap;
  }
}
</style>
