<template>
  <div class="display-field cursor-pointer" @click="handleItem">
    <div class="field-title flex justify-between">
      <h2 class="title truncate" v-if="dataInfo?.title" :class="styles?.title?.class" :style="styles?.title?.style" :title="dataInfo.title">{{ dataInfo.title }}</h2>
      <CommonTag v-if="dataInfo.tag" :content="dataInfo.tag" :style="styles?.tag?.style" :iconClass="styles?.tag?.class" />
    </div>

    <div class="field-item" v-for="(info, index) in dataInfo?.fileds" :key="index">
      <h3 class="field-item-label" :class="styles?.label?.class" :style="styles?.label?.style">
        <span>{{ info.label }}</span
        >：
      </h3>
      <p class="field-item-value" :class="styles?.value?.class" :style="styles?.value?.style">
        <span>{{ info.value }}</span>
      </p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, type PropType } from 'vue';
import CommonTag from '../common-tag.vue';
import { CardItem } from '@/components/commonCard/types';

interface RenderInfo {
  id?: string;
  title?: string;
  fileds?: {
    label: string;
    key: string;
    value: string;
  }[];
}

const props = defineProps({
  item: {
    type: Object as PropType<CardItem>,
    default: () => {},
  },
  styles: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const dataInfo: RenderInfo = props.item.renderInfo;

const handleItem = () => {
  // todo 中间层回调函数
};
</script>

<style lang="less" scoped>
.display-field {
    margin-top: 8px;
    padding: 8px;
    border-radius: 4px;
    background: @gray-bg;
    &:hover {
      background: #EDF2FC;
      .title {
        color: #4379FF;
      }
    }
  .title {
    margin: 0;
    font-size: 14px;
    line-height: 22px;
    font-weight: @font-weight-500;
    color: #000;
  }
  .field-item {
    display: flex;

    .field-item-label {
      width: 56px;
      margin: 0;
      flex-shrink: 0;
      display: flex;
      font-size: 14px;
      line-height: 22px;
      font-weight: @font-weight-400;
      color: @gray-color;

      span {
        flex-grow: 1;
        display: inline-block;
        text-align: justify;
        text-align-last: justify;
        /*兼容ie*/
        text-justify: distribute-all-lines;
      }
    }

    .field-item-value {
      flex-grow: 1;
      font-size: 14px;
      line-height: 22px;
      font-weight: @font-weight-400;
      color: #000000;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    &:not(:last-child) {
      margin-bottom: 4px;
    }
  }
}
</style>
