<template>
  <div
    class="relative w-full h-full flex flex-col overflow-hidden"
    :class="{
      square_box: true,
      isFullScreenSquare: isFullScreen,
    }"
  >
    <div class="square_top">
      <div class="square_header mb-[10px]" v-if="isFullScreen">
        <div class="square_title">{{ title }}</div>
      </div>
      <div
        class="tool_box"
        :class="{
          'justify-end': searchValue,
          'justify-center': !searchValue,
        }"
        :style="{
          display: !isFullScreen && searchValue ? 'none' : 'flex',
        }"
        ref="tool_box"
      >
        <AiTabs
          v-if="tools.length > 0 && !searchValue"
          :tabs="tools"
          :activeTab="activeTab"
          canWhell
          @tabClick="tabClick"
          @showArrow="showArrow"
          ref="AiTabsRef"
          :notNeedSelect="true"
          :portalType="isPortal ? 'portal' : 'normal'"
        ></AiTabs>
        <div v-if="!isIntelligentCreate && isFullScreen" :class="['square_search']">
          <SearchInput type="handleSearchAssistant" placeholder="搜索智能体" />
        </div>
      </div>
      <SearchResult v-if="searchValue" :length="searchResultLength" :searchValue="searchValue" />
    </div>
    <div
      class="content-main"
      ref="contentMainWapper"
      @scroll="handleScroll"
      @mouseenter="isMouseEnter = true"
      @mouseleave="isMouseEnter = false"
    >
      <div class="content-main-wapper">
        <!-- 遍历每个分组 -->
        <AiIdentify class="loading" v-if="loading" :width="24" :loadingStatus="true"></AiIdentify>
        <template v-for="groupItem in toolsContent" :key="groupItem.key">
          <!-- 分组标题 -->
          <div
            class="group-title"
            :id="`group-${groupItem.key}`"
            :class="{
              'group-title-search': searchValue && !isFullScreen,
            }"
          >
            {{ groupItem.title }}
          </div>
          <div class="content-wrapper">
            <!-- 智能创作不展示图标 -->
            <img
              v-if="isFullScreen && !isIntelligentCreate"
              :src="groupImageMap[groupItem.title] || Other"
              alt="createCardColumnBg"
              class="absolute top-[-63px] right-0 w-[96px] h-[96px] object-cover"
            />
            <!-- 内容区  -->
            <Spin :spinning="loading" :wrapperClassName="wrapperClassName">
              <div class="tool_content" v-if="toolsContent.length > 0">
                <div class="tool_content_list">
                  <!-- 该分组下的助手 -->
                  <div
                    v-for="(item, index) in groupItem.value"
                    :key="item.id || item.code"
                    class="tool_content_item"
                    :class="{
                      'w-full': !isFullScreen,
                    }"
                    @click="() => goThisTool(item, index)"
                  >
                    <div class="tool_item_icon">
                      <Image
                        :width="44"
                        :height="44"
                        :src="item.iconUrl || Ippic"
                        :preview="false"
                      />
                    </div>
                    <div class="tool_item_info">
                      <ToolItemTitle :item="item" />
                      <div class="tool_item_desc" :title="item.introduce">{{ item.introduce }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <template v-else>
                <CustomEmpty v-if="!isFullScreen" />
                <PortalEmptyColumn v-else class="w-full h-full flex items-center justify-center" />
              </template>
            </Spin>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, inject, watch, onUnmounted, defineOptions } from 'vue';
import type { PropType } from 'vue';
import { throttle } from '@/utils/tool';
// a-b-c
import { Image, message, Spin } from 'ant-design-vue';
import Ippic from '@/assets/imgs/ai-avatar.png';
// 1-2-3
import { getAssistsByLabel } from '@/api/assistantSqare/index';
import { getAssistantList } from '@/api/common';
import type { AssistInfo, TabItem } from '@/types/index';
import CustomEmpty from '@/components/empty/index.vue';
import AiIdentify from '@/components/aiIdentify/index.vue';
import ToolItemTitle from './toolItemTitle.vue';
import { useGlobal, useMenu } from '@/stores/global';
import SearchResult from './searchResult.vue';
import AiTabs from '@/components/aiTabs/index.vue';
import PortalEmptyColumn from '@/components/portalEmptyColumn/index.vue';
// 导入数据分组工具函数
import { createAssistantLabelMap, createTabsFromLabelMap } from '@/utils/agentGroup';
import emitter from '@/utils/bus';
import SearchInput from '@/components/common/search-input/index.vue';

import AssistantSquare from '@/assets/imgs/dss.png'; // 辅助决策
import ITPng from '@/assets/imgs/it.png'; // it运维
import KnowloagePng from '@/assets/imgs/knowloage.png'; // 企业知识
import OfficeEfficiency from '@/assets/imgs/office-efficiency.png'; // 办公提效
import Other from '@/assets/imgs/other.png'; // 其他
import defaultBg from '@/assets/imgs/default.png'; // 默认背景

defineOptions({ name: 'AssistantSquare' });

/* UE要求这么加的，而且现在只能用这个groupItem.title中文来做区分
图片UE给的
 */
const groupImageMap = {
  企业知识: KnowloagePng,
  IT运维: ITPng,
  辅助决策: AssistantSquare,
  办公提效: OfficeEfficiency,
  其它: Other,
};

type uGlobalType = {
  isFullScreen: boolean;
};
const props = defineProps({
  global: {
    type: Object as PropType<uGlobalType>,
  },
  goSelAssit: {
    type: Function,
    // default: () => {},
  },
});
const uGlobal = props.global || (useGlobal() as any);
const isPortal = inject('isPortal') as any;
// nav区
const tools = ref<Array<TabItem>>([]);
// tool内容区
const toolsContent = ref<any>([]);
// 全部智能助手
const allAsiLst = ref<Array<AssistInfo>>([]);
// 根据labels id分组的助手Map
const assistantLabelMap = ref<Map<string, { key: string; value: any[]; title: string }>>(new Map());
const hasFocus = ref(false);
const searchValue = ref<string | undefined>('');
const preAsiLst = ref<Array<AssistInfo>>([]);
const loading = ref(false);
const tool_box = ref<any>(null);
const activeTab = ref<string>(''); // 当前选中的标签
const uMenu = useMenu();
const isShowArrow = ref(false);
const AiTabsRef = ref<any>(null);
const contentMainWapper = ref<any>(null);
const isMouseEnter = ref(false);
let scrollTimer: any = null;

const isFullScreen = computed(() => {
  return uGlobal.globalState.isFullScreen;
});

const wrapperClassName = computed(() => {
  let className =
    'spin_box rounded-[12px] bg-gray-100 bg-opacity-50 overflow-hidden backdrop-blur-[8px]';
  if (toolsContent.value.length === 0) {
    className += ' no_data';
  }
  return className;
});
const showArrow = (val: boolean) => {
  isShowArrow.value = val;
};

const isIntelligentCreate = computed(() => {
  return uMenu.currentMenuInfo?.id === 'create';
});

const title = computed(() => {
  return isIntelligentCreate.value ? '创作坊' : '智能体广场';
});

// 获取搜索结果长度
const searchResultLength = computed(() => {
  if (toolsContent.value.length > 0) {
    let len = 0;
    Object.keys(toolsContent.value).forEach((key) => {
      len += toolsContent.value[key].value.length;
    });
    return len;
  }
  return 0;
});

const tabClick = (key: string) => {
  activeTab.value = key;

  // 如果不是"全部"标签，滚动到对应分组
  if (key !== '' && key !== 'all') {
    const targetElement = document.getElementById(`group-${key}`);
    if (targetElement) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }
};

// 处理滚动事件，根据滚动位置自动切换 tab
const handleScrollOriginal = () => {
  // 如果鼠标在内容区域，做滚动监听
  if (!contentMainWapper.value || toolsContent.value.length === 0 || !isMouseEnter.value) return;

  const container = contentMainWapper.value;
  const scrollTop = container.scrollTop;
  const containerRect = container.getBoundingClientRect();

  // 获取所有分组标题元素
  const groupElements = toolsContent.value
    .map((group) => ({
      key: group.key,
      element: document.getElementById(`group-${group.key}`),
    }))
    .filter((item) => item.element);

  if (groupElements.length === 0) return;

  // 找到当前可见区域内最顶部的分组
  let activeGroupKey = groupElements[0].key; // 默认第一个

  for (let i = 0; i < groupElements.length; i++) {
    const { key, element } = groupElements[i];
    if (element) {
      const elementRect = element.getBoundingClientRect();
      const elementTop = elementRect.top;
      const containerTop = containerRect.top;

      // 如果分组标题在容器顶部以下（可见区域内）
      if (elementTop <= containerTop + 20) {
        activeGroupKey = key;
      } else {
        // 如果当前分组标题在容器顶部以上太远，停止检查
        break;
      }
    }
  }

  // 更新激活的 tab // 不及时更新，需要延迟一下，等动滚动完成后再更新
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }
  scrollTimer = setTimeout(() => {
    if (activeGroupKey && activeGroupKey !== activeTab.value && isMouseEnter.value) {
      activeTab.value = activeGroupKey;
      if (AiTabsRef.value) {
        AiTabsRef.value.scrollToTab(activeTab.value);
      }
    }
  }, 200);
};

// 使用节流处理滚动事件
const handleScroll = throttle(handleScrollOriginal, 100);

// 更改加载状态
const changeSpinning = (val: boolean) => {
  loading.value = val;
};
// 获取助手列表
const gettingAsiLst = async () => {
  changeSpinning(true);
  try {
    const res: any = await getAssistantList({
      pageInfo: {
        pageNumber: 1,
        pageSize: 1000,
        needTotal: true,
      },
      params: {
        keyword: '',
        labelId: '',
        assistantType: 0,
      },
    });
    if (res) {
      changeSpinning(false);
      const { content = [] } = res.data;

      // 使用工具函数创建分组Map
      const labelMap = createAssistantLabelMap(content, isIntelligentCreate.value);

      // 存储分组Map和原始数据
      assistantLabelMap.value = labelMap;
      allAsiLst.value = [...content];

      // 使用工具函数创建tabs数据
      const { tabList, squareList } = createTabsFromLabelMap(labelMap, content.length);

      tools.value = tabList;
      toolsContent.value = squareList;
      preAsiLst.value = squareList;

      // 设置默认选中第一个tab
      if (tabList.length > 0) {
        activeTab.value = tabList[0].key;
      }
    } else {
      changeSpinning(false);
      message.error('获取助手列表失败');
      toolsContent.value = [];
    }
  } catch (error) {
    changeSpinning(false);
    toolsContent.value = [];
  }
};
// 选择具体某工具
const goThisTool = (item: any, inx: number) => {
  if (props.goSelAssit) {
    props.goSelAssit(item);
  } else {
    uMenu.changeMenu(item, {
      isAsit: true,
    });
  }
};

// 输入监听
const handleInput = (data: { type: string; params: { searchValue: any } }) => {
  if (data.type === 'handleSearchAssistant') {
    const val = data.params.searchValue;
    searchValue.value = val;
    const filteredData = allAsiLst.value.filter((item: AssistInfo) => {
      if (val !== undefined) {
        return item.name.indexOf(val) !== -1 || item.introduce.indexOf(val) !== -1;
      }
      return false; // 如果val是undefined，返回false，这样就不会执行indexOf方法
    });
    setHighText(val, filteredData);

    // 无内容的话显示之前的数据
    if (!val) {
      toolsContent.value = [...preAsiLst.value];
    }
    console.log('toolsContent', toolsContent.value);
  }
};

// 聚焦搜索框
const handleFocus = () => {
  hasFocus.value = true;
  // preAsiLst.value = [...toolsContent.value];
};

// 关闭搜索
const closeSearch = () => {
  searchValue.value = undefined;
  toolsContent.value = [...preAsiLst.value];
  hasFocus.value = false;
};

// 设置高亮文本
const setHighText = (keyword: string | undefined, data: Array<AssistInfo>) => {
  const newData = data.map((item) => {
    if (!keyword) {
      return {
        ...item,
        isHighlight: false,
      };
    }
    const parts = item.name.split(new RegExp(`(${keyword})`));
    const textParts = parts.filter(Boolean).map((part: String) => ({
      text: part,
      isHighlight: part === keyword,
    }));
    let hightName = '';
    textParts.forEach((part) => {
      if (part.isHighlight) {
        hightName += `<span class="highlight">${part.text}</span>`;
      } else {
        hightName += part.text;
      }
    });
    return {
      ...item,
      hightName,
      isHighlight: true,
    };
  });
  // 使用工具函数创建分组Map
  const labelMap = createAssistantLabelMap(newData, isIntelligentCreate.value);
  // 使用工具函数创建tabs数据
  const { tabList, squareList } = createTabsFromLabelMap(labelMap, newData.length);
  toolsContent.value = squareList;
};

onMounted(async () => {
  emitter.on('emitEvent', handleInput);
  gettingAsiLst();
});
// watch(
//   () => uMenu.currentMenuInfo,
//   (newVal) => {
//     if (newVal?.id === 'create' || newVal?.id === 'square') {
//       gettingAsiLst();
//     }
//   },
// );
onUnmounted(() => {
  emitter.off('emitEvent', handleInput);
});
</script>
<style scoped lang="less">
.square_search {
  height: 32px;
  width: 240px;
  caret-color: @primary-color;
  .search {
    background-color: #ffffffe5;
    backdrop-filter: blur(30px);
  }
}
.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.square_box {
  // padding: 0 12px 12px 12px;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .square_header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .square_search_focus {
      width: auto;
      flex: 1;
    }
    .close_search {
      font-size: 20px;
      margin-left: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #00000099;
    }
  }

  .search_result {
    height: 40px;
    display: flex;
    align-items: center;
    color: #000000;
    word-break: keep-all;

    span {
      font-weight: @font-weight-500;
      font-size: 14px;
      line-height: 22px;
    }

    .search_result_text {
      flex: 1;
      max-width: fit-content;
      flex-shrink: 1;
    }
  }

  .content-main {
    height: 100%;
    overflow-y: auto;
    margin-top: 12px;
    margin-bottom: 12px;

    .content-wrapper {
      position: relative;
    }
  }

  .tool_box {
    width: 100%;
    display: flex;
    height: 40px;
    align-items: center;

    :deep(.ai-tabs-wrapper) {
      margin: 0px;
      .tab-item {
        text-align: center;
        cursor: pointer;
        word-break: keep-all;
        line-height: 22px;
        border-radius: 8px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: @font-weight-400;
        letter-spacing: 0%;
        padding: 5px 8px;
        white-space: nowrap;
        &.active {
          color: #2962f0;
          background: #d1e0ff;
          font-weight: @font-weight-500;
        }
      }
    }
  }
  .spin_box {
    flex: 1;
    min-height: 1px;
    overflow: auto;
    max-height: fit-content;
  }
  .tool_content {
    width: 100%;
    display: flex;
    justify-content: center;
    background-color: #ffffffbf;
    border-radius: 8px;
    padding: 4px 4px 0;
    .tool_content_list {
      width: 100%;
      .tool_content_item {
        padding: 12px;
        border-radius: 8px;
        display: flex;
        left: left;
        margin-bottom: 4px;
        cursor: pointer;
        .tool_item_icon {
          width: 44px;
          height: 44px;
          border-radius: 50%;
          overflow: hidden;
          background: #edf2fc;
        }
        .tool_item_info {
          flex: 1;
          margin-left: 8px;
          min-width: 1px;

          .tool_item_desc {
            font-family: PingFang SC;
            font-size: 12px;
            font-weight: @font-weight-400;
            line-height: 22px;
            text-align: left;
            word-break: keep-all;
            white-space: nowrap;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #00000066;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      .tool_content_item:hover {
        background: #edf2fc;
      }
      &:last-child {
        margin: 0px;
      }
    }
  }
}

// 分组标题样式
.group-title {
  font-weight: @font-weight-500;
  font-style: Semibold;
  font-size: 14px;
  line-height: 22px;
  padding: 16px 0;
  background: transparent;
  border-radius: 8px;
  font-family: PingFang SC;
  margin-top: 12px;
  &:first-child {
    margin-top: 0px;
  }
}
.group-title-search {
  &:first-child {
    margin-top: 0px;
    padding-top: 8px;
  }
}
</style>

<style lang="less" scoped>
// 全屏样式
.isFullScreenSquare {
  padding: 0px;
  background: transparent;
  width: 100%;

  .square_top {
    .square_header {
      align-items: flex-start;
      margin-bottom: 22px;
      .square_title {
        font-family: PingFang SC;
        font-weight: @font-weight-500;
        font-style: Semibold;
        font-size: 24px;
        line-height: 32px;
      }
    }
    .search_result {
      width: calc(100% - 260px);
    }
    .square_search_focus,
    .square_search {
      width: 173px;
      margin-left: 10px;
    }
  }
  .tool_box {
    flex: 1;
    width: 100%;

    :deep(.ai-tabs-wrapper) {
      gap: 8px;
      flex: 1;
      min-width: 0;
      .ai-tab-content {
        gap: 12px;
      }
      .tab-item {
        height: 32px;
        border-radius: 12px;
        padding: 7px 26.5px;
        background: linear-gradient(
          180deg,
          rgba(255, 255, 255, 0.528) 19.98%,
          rgba(255, 255, 255, 0.704) 79.94%
        );
        font-family: PingFang SC;
        font-weight: @font-weight-500;
        font-size: 13px;
        line-height: 18.5px;
        text-align: center;

        &.active {
          background: rgba(0, 0, 0, 0.9);
          font-weight: @font-weight-500;
          color: #ffffff;
          backdrop-filter: blur(59.30232620239258px);
        }
      }
    }
  }
  .spin_box {
    height: 100%;
    position: relative;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.528) 19.98%,
      rgba(255, 255, 255, 0.704) 79.94%
    );
    border-radius: 10px;
  }
  .tool_content {
    padding: 16px;
    background: transparent;
    justify-content: flex-start;
    .tool_content_list {
      width: 100%;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(254px, 1fr));
      gap: 12px;
      justify-items: flex-start;

      // 当只有2个卡片时，居中显示
      &:has(> *:nth-child(2):last-child) {
        max-width: 520px;
      }
      &:has(> *:nth-child(1):last-child) {
        max-width: 254px;
      }
      .tool_content_item {
        height: 82px;
        margin-bottom: 0px;
        background: #ffffff;
        border: 1px solid transparent;
        padding: 16px;
        box-sizing: border-box;
        width: 100%;
      }
      .tool_content_item:hover {
        background: #ffffff;
        box-shadow: 0px 0px 25px 0px #3e3a4a1a;
      }
      &:last-child {
        margin: 0px;
      }
    }
  }
}
</style>

<style lang="less">
.square_box {
  .square_header {
    .ant-input-affix-wrapper {
      .ant-input-prefix {
        margin-inline-end: 8px;
      }
      border-color: transparent;
      .ant-input-clear-icon {
        display: flex;
        align-items: center;
      }
    }
    .ant-input-affix-wrapper-focused {
      box-shadow: none;
      border-color: @primary-color !important;
    }
  }
  .no_data {
    max-height: 100% !important;
    .ant-spin-container {
      height: 100%;
      display: flex;
    }
  }
}
.isFullScreenSquare {
  .no_data {
    max-height: 250px !important;

    .ant-spin-container {
      height: 100%;
      display: flex;
    }
  }
}
</style>
@/utils/agentGroup
