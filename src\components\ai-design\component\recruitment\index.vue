<!--
 * @Author: 代琪 <EMAIL>
 * @Date: 2025-07-16 10:26:43
 * @LastEditors: 代琪 <EMAIL>
 * @LastEditTime: 2025-07-21 13:37:23
 * @FilePath: \ai-assistant-web\src\components\ai-design\component\recruitment\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="intelligent-recruitment-wrapper w-full">
    <div class="features-header">
      <div class="mb-[20px] title">智能招聘</div>
      <div class="subtitle">全渠道、全流程、一站式数字化招聘平台</div>
    </div>
    <!-- 智能招聘功能区域 -->
    <div class="recruitment-features">

      <div class="features-grid">
        <div
          class="feature-card"
          :class="feature.class"
          v-for="feature in features"
          :key="feature.id"
        >
          <div class="feature-content">
            <div class="feature-title">{{ feature.title }}</div>
            <div class="feature-desc">{{ feature.description }}</div>
          </div>
          <div class="feature-icon">
            <img
              :src="feature.icon"
              alt=""
              srcset=""
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 招聘流程区域 -->
    <div class="recruitment-process">
      <div class="process-header">招聘流程</div>
      <!-- <div class="step-line">
        <div
          class="step-line-item"
          v-for="step in processSteps"
          :key="step.id"
        >
          <div class="step-line-dot"></div>
        </div>
        <i class="iconfont ai-icon-you1"></i>
      </div> -->
      <div class="process-flow">
        <div class="process-steps-container">
          <div
            class="process-step"
            v-for="(step, index) in processSteps"
            :key="step.id"
          >
          <div class="step-line-item">
            <div class="step-line-dot"></div>
            <i class="iconfont ai-icon-you1" v-if="index === processSteps.length - 1"></i>
          </div>
            <div
              class="step-content"
              @click="handleStepClick(step)"
            >
              <div class="step-title">
                <div class="step-title-content">
                  <img
                    :src="step.icon"
                    class="step-icon"
                  >{{ step.title }}
                </div>
                <img
                  :src="Arrow"
                  alt=""
                  srcset=""
                  class="arrow-icon"
                >
              </div>
              <p
                class="step-desc"
                v-html="step.description"
              ></p>
            </div>
            <div
              class="step-arrow"
              v-if="index < processSteps.length - 1"
            >
              <i class="arrow-icon"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { defineOptions } from 'vue';
import RecruitmentIcon1 from '@/assets/imgs/recruitment-1.png';
import RecruitmentIcon2 from '@/assets/imgs/recruitment-2.png';
import RecruitmentIcon3 from '@/assets/imgs/recruitment-3.png';
import AiIconFabuzhiwei from '@/assets/imgs/ai-icon-fabuzhiwei.png';
import AiIconJuhejianli from '@/assets/imgs/ai-icon-juhejianli.png';
import AiIconAnpaimianshi from '@/assets/imgs/ai-icon-anpaimianshi.png';
import AiIconLuyongshenpi from '@/assets/imgs/ai-icon-luyongshenpi.png';
import AiIconAfasongyouxiang from '@/assets/imgs/ai-icon-a-fasongyouxiang.png';
import AiIconRuzhiguanli from '@/assets/imgs/ai-icon-ruzhiguanli.png';
import Arrow from '@/assets/imgs/arrow.png';
import { openDobuleScreen } from '@/utils/storesUtils';
import { Modal } from 'ant-design-vue';

defineOptions({
  name: 'IntelligentRecruitment',
});

// 智能招聘功能数据
const features = ref([
  {
    id: 1,
    title: 'AI生成职位描述',
    description: '根据职位名称生成职位描述，基于丰富的行业模型，精准洞察岗位需求，秒速输出专业文案',
    icon: RecruitmentIcon1,
    class: 'icon-bg-green'
  },
  {
    id: 2,
    title: '简历智能解析',
    description: '自动收录三方招聘网站简历并转化为结构化的数据，智能分析简历疑点、亮点，帮助HR快速了解应聘者',
    icon: RecruitmentIcon2,
    class: 'icon-bg-purple'
  },
  {
    id: 3,
    title: 'AI面试',
    description: 'AI面试官，提供超过1000道标准化面试题，全面评估候选人的胜任特质',
    icon: RecruitmentIcon3,
    class: 'icon-bg-blue'
  }
]);

// 招聘流程数据
const processSteps = ref([
  {
    id: 1,
    title: '发布职位',
    description: '将职位一键发布到三方招聘网站',
    icon: AiIconFabuzhiwei,
    url: '/ehr/recruitment/job.do?method=index'
  },
  {
    id: 2,
    title: '聚合简历',
    description: '·收录三方招聘网站简历 <br> ·手动导入简历',
    icon: AiIconJuhejianli,
    url: '/ehr/recruitment/person.do?method=index'
  },
  {
    id: 3,
    title: '安排面试',
    description: '邮件、短信通知应聘者，面试官移动端在线评价',
    icon: AiIconAnpaimianshi,
    url: '/ehr/recruitment/person.do?method=index'
  },
  {
    id: 4,
    title: '录用审批',
    description: '通过流程表单进行定编、定岗、定薪审批',
    icon: AiIconLuyongshenpi,
    url: '/ehr/recruitment/person.do?method=index'
  },
  {
    id: 5,
    title: '发送offer',
    description: '批量发送Offer，邮件、短信通知应聘者',
    icon: AiIconAfasongyouxiang,
    url: '/ehr/recruitment/person.do?method=index'
  },
  {
    id: 6,
    title: '入职管理',
    description: '·入职审批 <br> ·开通系统账号',
    icon: AiIconRuzhiguanli,
    url: '/ehr/recruitment/person.do?method=index'
  }
]);

// 当前步骤

const handleStepClick = (step: any) => {
  // const _plugins = window.top.plugins || [];
  const isRecruitmentMgr = window.top.ctpEnv?.currentUser?.userRoles?.contains('recruitmentMgr')
  if (!isRecruitmentMgr) {
    Modal.confirm({
      title: '提示',
      content: '抱歉，您暂无此功能权限。如有需要，请联系管理员',
      okText: '确认',
      cancelText: '取消',
    });
    return;
  }
  const ctxPath = window._ctxPath || '/seeyon';
  openDobuleScreen(`${ctxPath}${step.url}`, 'iframe');
};

</script>

<style scoped lang="less">
@import '@/assets/css/var.less';

.intelligent-recruitment-wrapper {
  font-family: PingFang SC;

  .title {
    font-size: 24px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 12px;
    line-height: 32px;
  }

  .subtitle {
    font-size: 13px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: 400;
  }
}

// 智能招聘功能区域
.recruitment-features {
  margin: 22px 0 12px;

  .features-header {
    text-align: center;
    margin-bottom: 32px;

  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;

    .feature-card {
      position: relative;
      border-radius: 12px;
      padding: 16px;
      height: 144px;

      &.icon-bg-blue {
        background: linear-gradient(97.41deg, rgba(255, 255, 255, 0.9) 45.53%, rgba(185, 216, 255, 0.7) 98.77%);
      }

      &.icon-bg-purple {
        background: linear-gradient(98.05deg, rgba(255, 255, 255, 0.9) 42%, rgba(213, 214, 255, 0.7) 100%);
      }

      &.icon-bg-green {
        background: linear-gradient(96.17deg, rgba(255, 255, 255, 1) 44.38%, rgba(210, 251, 255, 0.7) 99.92%);
      }

      .feature-icon {
        position: absolute;
        width: 96px;
        height: 96px;
        right: 4px;
        bottom: 8px;
      }

      .feature-content {
        position: relative;
        z-index: 1;

        .feature-title {
          font-size: 16px;
          font-weight: 600;
          color: rgba(0, 0, 0, 0.9);
          margin-bottom: 8px;
          line-height: 24px;
        }

        .feature-desc {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.6);
          line-height: 22px;
          font-weight: 400;
        }
      }
    }
  }
}

// 招聘流程区域
.recruitment-process {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 16px;

  .process-header {
    margin-bottom: 24px;
    color: rgba(28, 28, 28, 1);
    line-height: 24px;
    font-weight: 600;
    font-size: 16px;
  }

  .step-line {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  .step-line-item {
      &:before {
        position: absolute;
        content: '';
        display: block;
        width: calc(50% - 5px);
        height: 1px;
        background: rgba(28, 28, 28, 0.1);
        border-bottom: 1px dashed rgba(67, 121, 255, 1);
        left: 0;
      }

      &:after {
        position: absolute;
        content: '';
        display: block;
        right: -12px;
        width: calc(50% + 12px);
        height: 1px;
        background: rgba(28, 28, 28, 0.1);
        border-bottom: 1px dashed rgba(67, 121, 255, 1);
      }


      .step-line-dot {
        position: absolute;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 6px;
        height: 6px;
        background: rgba(67, 121, 255, 1);
        border-radius: 50%;
      }
    .ai-icon-you1 {
      position: absolute;
      right: -4px;
      top: -2px;
      color: rgba(67, 121, 255, 1);
      font-size: 12px;
    }
  }

  .process-flow {
    overflow: auto;

    @media (min-width: 1200px) {
      overflow: unset;
    }

    .process-steps-container {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      position: relative;
      gap: 12px;
      overflow: unset;
    }

    .process-step {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      z-index: 2;
      flex: 1;
      min-width: 115px;
      padding-top: 4px;

      .step-content {
        padding: 12px;
        background: rgba(255, 255, 255, 1);
        backdrop-filter: blur(60px);
        border-radius: 12px;
        margin-top: 12px;
        height: 112px;
        cursor: pointer;
        width: 100%;

        &:hover {
          backdrop-filter: blur(60px);
          box-shadow: 0px 0px 28px 0px rgba(8, 62, 221, 0.12);
        }

        .step-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          font-weight: 600;
          color: rgba(28, 28, 28, 1);
          line-height: 22px;
          margin-bottom: 6px;

          .step-title-content {
            display: flex;
            align-items: center;
            gap: 2px;

            .step-icon {
              width: 18px;
              height: 18px;
            }
          }

          .arrow-icon {
            height: 12px;
            width: 12px;
            margin-top: 2px;
          }
        }

        .step-desc {
          font-size: 12px;
          color: rgba(63, 67, 77, 1);
          line-height: 20px;
          font-weight: 400;
        }
      }
      &:last-child {
        .step-line-item {
          &:after {
            right: 2px;
            width: calc(50% - 5px);
          }
        }
      }
    }
  }
}

// 操作按钮
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;

  .btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    border: none;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;

    &.btn-primary {
      background: @primary-color;
      color: #fff;

      &:hover {
        background: darken(@primary-color, 10%);
        transform: translateY(-2px);
      }
    }

    &.btn-secondary {
      background: #f5f5f5;
      color: #333;
      border: 1px solid #e8e8e8;

      &:hover {
        background: #e8e8e8;
        transform: translateY(-2px);
      }
    }

    .btn-icon {
      font-size: 18px;
    }
  }
}
</style>
