import { post } from '@/api/config';
import { getSummaryText } from '@/api/portal';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { getMyPendingMeeting } from '@/api/pending';
import { getBatchSummary } from '@/api/portal';
// 会议项接口定义
export interface MeetingItem {
  id?: string;
  affairId: string;
  title?: string;
  startTime?: string;
  endTime?: string;
  location?: string;
  participants?: string[];
  startDate?: string;
  endDate?: string;
  createUser?: string;
  meetingName?: string;
  meetingId?: string;
  meetingTime?: string;
  createUsr?: string;
  mettingPlace?: string;
  jumpUrl?: string;
  summaryText?: string;
  loaddingSummary?: boolean;
}

// 日期格式化函数
const formatToDateCN = (date: any, format: string) => {
  return dayjs(date).locale('zh-cn').format(format);
};

// 会议列表缓存
let meetingListCache: MeetingItem[] | null = null;

// 清除会议列表缓存
export const clearMeetingListCache = () => {
  meetingListCache = null;
};

// 强制刷新会议数据（清除缓存后重新获取）
export const refreshMeetingData = async (): Promise<MeetingItem[]> => {
  clearMeetingListCache();
  return await fetchMeetingData();
};

// 获取会议数据
export const fetchMeetingData = async (): Promise<MeetingItem[]> => {
  // 如果有缓存，直接返回缓存数据
  if (meetingListCache) {
    console.log('使用缓存的会议数据');
    return meetingListCache;
  }

  try {
    console.log('从接口获取新的会议数据');
    const res: any = await getMyPendingMeeting();
    if (res?.code === '0' && res?.data?.data) {
      const processedData = res.data.data.map((item: any) => ({
        affairId: item.affairId,
        meetingName: item.title,
        meetingId: item.id,
        meetingTime: `${formatToDateCN(dayjs(item.startDate),'MM月DD日 HH:mm')} - ${formatToDateCN(dayjs(item.endDate),'HH:mm')}`,
        createUsr: item.createUser,
        mettingPlace: item.location,
        jumpUrl: `/seeyon/meeting.do?method=view&meetingId=${item.id}&showTab=true`,
        loaddingSummary: false,
        summaryText: ''
      })) || [];

      // 更新缓存
      meetingListCache = processedData;

      return processedData;
    }
    return [];
  } catch (error) {
    console.error('获取会议数据失败:', error);
    return [];
  }
};

// 获取会议摘要
export const fetchMeetingSummaries = async (meetingList: MeetingItem[]) => {
  const meetingIds = meetingList.map(item => item.meetingId);
  const response: any = await getBatchSummary({ entityIdList: meetingIds });
  if(response?.code == 0 && response?.data){
    // 将数组转换为以entityId为key的对象
    const summaryMap: Record<string, any> = {};
    response.data.forEach((item: any) => {
      if (item.entityId) {
        summaryMap[item.entityId] = item;
      }
    });

    // 更新meetingList中的摘要信息
    for(const item of meetingList){
      if (item.meetingId && summaryMap[item.meetingId]) {
        const summary = summaryMap[item.meetingId].summary;
        const status = summaryMap[item.meetingId].status;
        // 处理状态:0-待处理 5-处理中 1-处理成功 2-处理失败 null-旧数据或未同步数据
        if(status === null) {
          item.summaryText = '初始化规则历史数据不处理。';
        }else if(Number(status) === 1){
          item.summaryText = `摘要总结：${summary}`;
        } else if(Number(status) === 0 || Number(status) === 5){
          // item.summaryText = summary === null ? 'CoMi正在为你生成中，请稍后重试。' : '没有正文内容，未生成有效摘要。';
          item.summaryText = 'CoMi正在为你生成中，请稍后重试。';
        }else if(Number(status) === 2) {
          item.summaryText = '处理失败请点击详情查看。';
        }
      }
    }
  }
};

// 刷新单个会议摘要
export const refreshMeetingSummary = async (meeting: MeetingItem) => {
  if (meeting.affairId) {
    try {
      // 使用 Object.assign 确保响应式更新
      Object.assign(meeting, { loaddingSummary: true });

      const res: any = await getSummaryText(meeting.affairId);
      console.log(res)
      if (res.code === '0' && res.data.content.indexOf('error') === -1) {
        Object.assign(meeting, {
          summaryText: `摘要总结：${res.data.content as string}`,
          loaddingSummary: false
        });
      } else {
        Object.assign(meeting, { loaddingSummary: false });
      }
    } catch (error) {
      console.error('获取会议摘要失败:', error);
      Object.assign(meeting, { loaddingSummary: false });
    }
  }
};
