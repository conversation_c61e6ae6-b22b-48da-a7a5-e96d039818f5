<template>
  <!-- 附件项容器 -->
  <div
    class="attachment-item flex items-center justify-between px-[8px] py-[8px] rounded-[8px] cursor-pointer transition-colors duration-200"
    :title="attachment.name"
  >
    <!-- 左侧区域：图标和文件名 -->
    <div class="flex items-center flex-1 min-w-0 mr-4"> <!-- min-w-0 对文本截断很重要 -->
      <!-- 文件类型图标 (使用 Ant Design 图标) -->
      <FileTextOutlined class="type-icon  mr-[4px] shrink-0" />
      <!-- 文件名 (超出部分截断显示...) -->
      <span class="name text-gray-800 truncate mt-[4px]">
        {{ attachment.name }}
      </span>
    </div>

    <!-- 右侧区域：文件大小和下载图标 -->
    <div class="flex items-center shrink-0">
      <!-- 下载图标 -->
      <DownloadOutlined
        class="download-icon hover:text-blue-700"
        @click.stop="handleDownload"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// 引入 Vue 的计算属性和 PropType 工具类型
import { computed } from 'vue';
import type { PropType } from 'vue'
// 引入 Ant Design Vue 的图标组件
import { FileTextOutlined, DownloadOutlined } from '@ant-design/icons-vue';

// 定义附件数据结构接口
interface Attachment {
  id?: string;             // 附件可选的唯一 ID
  name: string;       // 文件名 (必需)
  url?: string;            // 可选的下载链接
  type?: string;           // 可选的文件类型提示
}

// 定义组件的 Props
const props = defineProps({
  // 接收一个附件对象，类型为 Attachment，且是必需的
  attachment: {
    type: Object as PropType<Attachment>,
    required: true,
  }
});

// 处理下载图标点击事件的方法
const handleDownload = () => {
  console.log('下载按钮被点击:', props.attachment.url || props.attachment.id);
  // 在这里实现实际的下载逻辑，例如：
  if (props.attachment.url) {
    // 如果有直接的 URL，在新标签页打开
    window.open(props.attachment.url, '_blank');
  } else {
    // 如果没有 URL，可能需要根据 ID 调用 API 来获取下载链接或文件流
  }
};

</script>

<style scoped lang="less">
.attachment-item {
  background: rgb(237, 242, 252);
  .type-icon {
    color: rgba(41, 127, 251, 1);
    font-size: 18px;
  }
  .name {
    font-size: 12px;
    color: rgba(0, 0, 0, 1);
  }
  .download-icon {
    font-size: 18px;
    color: rgba(41, 98, 240, 1);
  }
}

</style>
