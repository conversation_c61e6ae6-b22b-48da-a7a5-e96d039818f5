<template>
  <div
    class="knowledge-source-item"
    :class="{ 'can-download': data.category === '0' }"
    @click="handleClick(data)"
    ref="itemRef"
  >
    <div class="header">
      <div class="left">
        <i :class="iconClass(data)"></i>
        <span class="name" :title="data.title">{{ data.title }}</span>
        <span class="att" v-if="data?.accessoryName">
          <i class="iconfont ai-icon-fujian2"></i>
          <!-- <span>{{ data?.attNum || 0 }}</span> -->
        </span>
      </div>
      <div class="right">相似性：{{ similarityPercentage }}</div>
    </div>
    <span class="content">{{ data?.content }}</span>

    <div class="footer">
      <div class="left">
        <span class="label origin">来源：{{ data?.appTypeDsc }}</span>
        <span class="split-line-shu"></span>
        <span class="label creator">发起人：{{ data?.author }}</span>
        <span class="split-line-shu"></span>
        <span class="label createTime">创建时间：{{ formatDate(Number(data?.createDate)) }}</span>
      </div>
      <div class="right" v-if="data.category === '0'">
        <Tooltip title="下载">
          <i class="iconfont ai-icon-xiazai" @click="download(data)" />
        </Tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { citationJSONItem } from '@/types';
import moment from 'moment';
import { getIconByType } from '@/utils/oaDataMap';
import { Tooltip } from 'ant-design-vue';
const props = defineProps<{
  data: citationJSONItem;
}>();
// console.log('knowledgeSourceItem', props.data);
const iconMap: any = {
  bullet: 'iconfont ai-icon-danweigonggao bullet-color',
  collaboration: 'synergy collaboration-color',
  doc: 'doc doc-color',
  docx: 'docx docx-color',
  pdf: 'pdf pdf-color',
  wps: 'wps wps-color',
  xlsx: 'xls xlsx-color',
  txt: 'txt txt-color',
  news: 'news news-color',
  ppt: 'ppt ppt-color',
};
const collaborationType: any = {
  1: 'collaboration',
  7: 'bullet',
  8: 'news',
};
const emit = defineEmits(['click']);
const itemRef = ref();
const similarityPercentage = computed(() => {
  return props.data?.similarity ? props.data.similarity.toFixed(4) : '0';
});

const itemWidth = computed(() => {
  if (itemRef.value) {
    return itemRef.value.offsetWidth;
  }
});

const iconClass = (doc: any) => {
  if (doc.category === '0') {
    const splitArr = doc.title.split('.');
    const docType = splitArr[splitArr.length - 1];
    return `doc-iconfont ${iconMap[docType]}`;
  }
  return `iconfont ${getIconByType(doc.category)}`;
};

const formatDate = (date: number) => {
  if (date) {
    return moment(date).format('YYYY-MM-DD HH:mm');
  }
};

const download = (doc: any) => {
  emit('click', doc);
};
const handleClick = (item: citationJSONItem) => {
  if (item.category === '0') {
    return;
  }
  emit('click', item);
};
</script>

<style lang="less" scoped>
.knowledge-source-item {
  padding: 8px;
  font-size: 12px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  cursor: pointer;

  &.can-download {
    cursor: default;
  }

  &:hover {
    background-color: #f7f7f7;
  }
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    .left {
      color: #999999;
      font-family: PingFang SC;
      font-weight: @font-weight-500;
      font-size: 16px;
      line-height: 24px;
      color: rgba(0, 0, 0, 1);
      align-items: center;
      display: flex;
      gap: 6px;
      max-width: calc(100% - 106px);

      .iconfont {
        color: @sky;
      }
      .doc-iconfont {
        color: @sky;

        &.ppt-color {
          color: #ff9900;
        }
        &.pdf-color {
          color: #ff4141;
        }
        &.news-color,
        &.bullet-color,
        &.collaboration-color,
        &.docx-color,
        &.default-color {
          color: @sky;
        }
        &.doc-color {
          color: @sky;
        }
        &.xlsx-color {
          color: #61b109;
        }
      }

      .docIconfont {
        font-size: 16px;
        padding-top: 2px;
        box-sizing: border-box;
      }
      .name {
        flex: 1;
        min-width: 1px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: fit-content;
      }

      .att {
        display: inline-flex;
        align-items: center;
        font-family: PingFang SC;
        font-weight: @font-weight-400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(67, 121, 255, 1);
        padding: 2px 3px;
        gap: 2px;
        border-radius: 4px;
        background-color: rgba(67, 121, 255, 0.1);
        i {
          font-size: 14px;
        }
      }
    }
    .right {
      color: rgba(0, 0, 0, 0.4);
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-size: 12px;
      line-height: 20px;
    }
  }
  .content {
    font-family: PingFang SC;
    font-weight: @font-weight-400;
    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.6);
    /* 新增省略号样式 */
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 限制显示行数 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-size: 12px;
      line-height: 20px;
      color: rgba(0, 0, 0, 0.4);
      display: flex;
      align-items: center;
      gap: 8px;

      .split-line-shu {
        width: 0.5px;
        height: 12px;
        background-color: #d9d9d9;
      }
    }
    .right {
      .iconfont {
        color: #6f7686;
        cursor: pointer;

        &:hover {
          color: @sky;
        }
      }
    }
  }
}
</style>
