import { get, post } from '../config.js';

const _ctxPath = window.top?._ctxPath || '/seeyon';

export const getAboutInfo = () => {
    return get(_ctxPath + '/comi-server/api/biz/about/info', {}, true);
}

// seeyonHttpclient.get( ur: "/seeyon/rest/comi-license/info",
//     params: null,neonse = coMiHttpclient.get( ur: "/ai-manager/system/config/getSystemBuildId", 
export const getSystemBuildId = () => {
    return get('ai-manager/system/config/getSystemBuildId', {});
}
export const getComiLicenseInfo = () => {
    return get(_ctxPath + '/rest/comi-license/info', {}, true);
}
