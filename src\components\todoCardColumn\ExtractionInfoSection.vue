<template>
  <div
    v-if="extractionData"
    class="extraction-info-section"
  >
    <div class="extraction-info-content">
      <div class="extraction-info-header">
        <span class="extraction-info-title">关键信息</span>
      </div>
      <div class="extraction-info-body">
        <!-- <div class="summary-text">{{ extractionData.summary }}</div> -->
        <div
          v-if="Object.keys(extractionData.keywords || {}).length > 0"
          class="keywords-detail"
        >
          <div class="keywords-list">
            <div
              v-for="(value, key) in extractionData.keywords"
              :key="key"
              class="keyword-item"
            >
              <span class="keyword-icon">·</span>
              <span class="keyword-label">{{ key }}:</span>
              <span class="keyword-value" :title="String(value)">{{ value }}</span>
            </div>
          </div>
        </div>
        <div
          v-else
          class="keywords-detail"
        >
          <div class="keywords-list" v-if="extractionData.summary === null">CoMi正在为你生成中，请稍后重试。</div>
          <div class="keywords-list" v-else>待办没有详情内容，无法生成关键信息。</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ExtractionData {
  affairId: string;
  summary: string;
  keywords: Record<string, any>;
}

interface Props {
  extractionData?: ExtractionData | null;
}

const props = defineProps<Props>();
</script>

<style scoped lang="less">
// 信息提取区域样式
.extraction-info-section {

  .extraction-info-content {
    margin-top: 4px;

    .extraction-info-header {
      margin-bottom: 8px;

      .extraction-info-title {
        font-size: 12px;
        font-weight: @font-weight-400;
        color: #4379FF;
        line-height: 20px;
      }
    }

    .extraction-info-body {
      .summary-text {
        font-size: 13px;
        color: rgba(51, 54, 57, 1);
        line-height: 20px;
        margin-bottom: 8px;
        font-weight: @font-weight-500;
      }

      .keywords-detail {
        .keywords-list {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
          color: #25262C;
          font-size: 12px;

          .keyword-item {
            display: flex;
            width: calc(50% - 2px);
            line-height: 20px;

            &:last-child {
              margin-bottom: 0;
            }

            .keyword-icon {
              font-size: 18px;
              width: 8px;
              color: #4685FF;
              text-indent: -6px;
            }

            .keyword-label {
              font-weight: @font-weight-400;
              padding-right: 4px;
              flex-shrink: 0;
            }

            .keyword-value {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }
        }
      }
    }
  }
}
</style> 