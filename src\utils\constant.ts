export const FILE_TYPE_LIST: string[] = [
  'jpg',
  'jpeg',
  'png',
  'webp',
  'gif',
  'svg',
  'image/jpg',
  'image/jpeg',
  'image/png',
  'image/webp',
  'image/gif',
  'image/svg',
]
export const FILE_TYPE_MAP: Record<string, string> = {
  'xls': 'application/vnd.ms-excel',
  'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'csv': 'text/csv',
  'doc': 'application/msword',
  'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'wps': 'application/vnd.wps-office.doc',
  'txt': 'text/plain',
  'et': 'application/vnd.wps-office.et',
  'pdf': 'application/pdf',
  'md': 'text/markdown',
  'markdown': 'text/markdown',
}
//和快捷操作的id保持一致
export const AGENT_CODE: Record<string, string> = {
  'addressBook': 'member_agent', //通讯录agent
  'createMeeting': 'meeting_query_t17q', //会议查询 会议发起
  'createColl': 'v5_flow_summary_send_33m3', //流程发起 发起协同
  'weeklyPlan': 'affair_analysis', //周计划
  'shortcut': 'app_router_1399', //快捷磁贴
}
