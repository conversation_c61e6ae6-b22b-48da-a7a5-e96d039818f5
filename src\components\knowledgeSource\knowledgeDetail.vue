<template>
  <div class="knowledge-details-wrapper">
    <iframe
      :src="iframeUrl"
      border="0"
      width="100%"
      height="100%"
      ref="knowledgeIframe"
      @load="onIframeLoad()"
    />
  </div>
</template>
<script setup lang="ts">
import { watch, defineOptions, ref, computed, onMounted } from 'vue';
const knowledgeIframe = ref(null);
defineOptions({
  name: 'KnowledgeDetails',
});
const props = defineProps<{
  iframeUrl: any;
}>();

const emit = defineEmits<{
  (e: 'iframeLoad'): void;
}>();

const onIframeLoad = () => {
  emit('iframeLoad');
};
</script>
<style scoped lang="less">
.knowledge-details-wrapper {
  height: 100%;
  width: 100%;
  border-radius: 12px;
  padding: 8px;
  box-sizing: border-box;
  min-width: 1200px;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}
</style>
