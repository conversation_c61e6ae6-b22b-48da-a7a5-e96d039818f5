.card-header[data-v-9b08fce2] {
  position: relative;
}
.card-header .card-title[data-v-9b08fce2] {
  font-size: var(--card-font-size);
  height: 32px;
  font-size: var(--card-font-weight-bold);
  line-height: 32px;
  margin-bottom: 16px;
}
.card-header .iconScale[data-v-9b08fce2] {
  position: absolute;
  top: 0;
  right: 0;
  height: 20px;
  width: 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-header .iconScale i[data-v-9b08fce2] {
  cursor: pointer;
  font-size: var(--card-font-size);
  color: var(--card-color-tertiary);
}
.scale-tooltip[data-v-9b08fce2] {
  font-size: var(--card-font-size-small);
  color: #8e94a2;
}.card-nav[data-v-c4129c31] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-nav__select[data-v-c4129c31] {
  height: 24px;
  font-size: var(--card-font-size);
  color: rgb(0, 0, 0);
}
.card-nav__select .card-nav__select__prev[data-v-c4129c31] {
  margin-right: 12px;
  padding: 4px;
  padding: 4px;
}
.card-nav__item[data-v-c4129c31] {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
}
.card-nav__item .card-nav__item__tool[data-v-c4129c31] {
  color: rgba(0, 0, 0, 0.6);
  font-weight: var(--card-font-weight);
  font-size: var(--card-font-size-small);
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-right: 1px solid rgb(217, 217, 217);
}
.card-nav__item .card-nav__item__tool span[data-v-c4129c31] {
  margin-right: 12px;
}
.card-nav__item .card-nav__item__tool span i[data-v-c4129c31] {
  margin-right: 2px;
}
.card-nav__item .card-nav__item__Icon[data-v-c4129c31] {
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 14px;
  cursor: pointer;
  padding: 0 2px;
}
.card-nav__item .card-nav__item__Icon span[data-v-c4129c31] {
  margin-left: 3px;
}
.card-nav__item .card-nav__item__close[data-v-c4129c31] {
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 14px;
  cursor: pointer;
  padding: 0 2px;
}
.card-nav__item .card-nav__item__close i[data-v-c4129c31] {
  color: var(--card-color-tertiary);
}
.card-nav__item .card-nav__item__Icon--active[data-v-c4129c31] {
  color: var(--card-color-primary);
  background-color: #edf2fc;
}
.card-sumary__look-code[data-v-c4129c31] {
  display: flex;
  align-items: center;
  line-height: 1;
  cursor: pointer;
}
.card-sumary__look-code[data-v-c4129c31]:hover {
  color: var(--card-color-primary);
}.card-summary[data-v-05db1eb0] {
  padding-top: 12px;
}
.card-summary__top[data-v-05db1eb0] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 20px;
  margin-bottom: var(--card-space);
  line-height: 20px;
  padding-top: 6px;
}
.card-summary__top .card-summary__top--left[data-v-05db1eb0] {
  font-size: var(--card-font-size-small);
  font-weight: var(--card-font-weight);
  color: var(--card-color-font-secondary);
}
.card-summary__top .card-summary__top--right[data-v-05db1eb0] {
  font-size: var(--card-font-size-small);
  font-weight: var(--card-font-weight);
}
.card-summary__top .card-summary__top--right span[data-v-05db1eb0] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--card-color-font-secondary);
  margin-left: 8px;
  cursor: pointer;
}
.card-summary__top .card-summary__top--right span i[data-v-05db1eb0] {
  margin-right: 4px;
}
.card-summary__top .card-summary__top--right span[data-v-05db1eb0]:hover {
  color: var(--card-color-primary);
}
.card-summary__content[data-v-05db1eb0] {
  padding: 0 0 8px 0;
  box-sizing: border-box;
}
.card-summary__content .card-summary__content--item[data-v-05db1eb0] {
  box-sizing: border-box;
  width: 100%;
  border-radius: 5px;
}
.card-summary__content .card-summary__title[data-v-05db1eb0] {
  font-size: var(--card-font-size);
  margin-bottom: 10px;
  height: 24px;
}
.card-summary__content .card-summary__title .card-summary__subtitle[data-v-05db1eb0] {
  height: 24px;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  vertical-align: middle;
}
.card-summary__content .card-summary__title .card-summary__subtitle span[data-v-05db1eb0] {
  font-size: var(--card-font-size);
  color: var(--card-color-font-primary);
  font-size: var(--card-font-weight-bold);
}
.card-summary__content .card-summary__title img[data-v-05db1eb0] {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.card-summary__content .card-summary__title span[data-v-05db1eb0] {
  font-size: var(--card-font-size-small);
  color: var(--card-color-font-secondary);
  font-weight: var(--card-font-weight);
}
.card-summary__content .card-summary__content--item__content[data-v-05db1eb0] {
  font-size: var(--card-font-size);
  color: var(--card-color-font-primary);
  font-weight: var(--card-font-weight);
}:root {
    --card-color-primary: #4379ff;
    --card--color-secondary: rgba(237, 242, 252, 1);
    --card-color-tertiary: #8E94A2;
    --card-color-font-primary: #000000;
    --card-color-font-secondary: rgba(0, 0, 0, 0.4);
    --card-color-font-tertiary: rgba(0, 0, 0, 0.6);
    --card-font-size: 16px;
    --card-font-size-small: 12px;
    --card--font-weight: 400 ;
    --card-font-weight-bold: 600 ;
    --card-space: 12px;
}
.card-summary__content--item__content *{
    font-size: clamp(12px, 2.5vw, 14px)!important;
}