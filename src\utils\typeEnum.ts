const AppTypeEnum = {
    global: "全局",
    collaboration: "协同应用",
    form: "表单",
    doc: "知识管理",
    edoc: "公文",
    plan: "计划",
    meeting: "会议",
    bulletin: "公告",
    news: "新闻",
    bbs: "讨论",
    inquiry: "调查",
    calendar: "日程事件",
    mail: "邮件",
    organization: "组织模型",
    project: "项目",
    relateMember: "关联人员",
    exchange: "交换",
    hr: "人力资源",
    blog: "博客",
    edocSend: "发文",
    edocRec: "收文",
    edocSign: "签报",
    exSend: "待发送公文",
    exSign: "待签收公文",
    edocRegister: "待登记公文",
    communication: "在线交流",
    office: "综合办公",
    agent: "代理设置",
    modifyPassword: "密码修改",
    meetingroom: "会议室",
    taskManage: "任务管理",
    guestbook: "留言板",
    info: "信息报送",
    infoStat: "信息报送统计",
    edocRecDistribute: "收文分发",
    notice: "公示板",
    attendance: "签到",
    mobileAppMgrForHTML5: "移动应用接入-html5应用包",
    sapPlugin: "sap插件",
    ThirdPartyIntegration: "第三方整合",
    show: "大秀",
    wfanalysis: "流程绩效",
    behavioranalysis: "行为绩效，足迹(cmp)",
    biz: "业务生成器(cmp)",
    commons: "公共资源(cmp)",
    workflow: "工作流(cmp)",
    unflowform: "无流程表单(cmp)",
    formqueyreport: "表单查询统计(cmp)",
    cmp: "cmp",
    dee: "dee模块(cmp)",
    application: "应用模块(m3)",
    m3commons: "公共资源(m3)",
    login: "登陆(m3)",
    message: "消息模块(m3)",
    my: "我的模块(m3)",
    search: "搜索模块(m3)",
    todo: "待办模块(m3)",
    fullsearch: "全文检索(m3)",
    mycollection: "我的收藏(m3)",
    uc: "UC(m3)",
    addressbook: "通讯录(m3)",
    seeyonreport: "帆软报表",
    statusRemind: "状态提醒",
    portal: "H5门户(m3)",
    cap4Form: "cap4表单",
    cap4biz: "应用管理",
    fileReport: "文件报表",
    excelreport: "excel报表",
    vreport: "报表中心(m3)",
    memorabilia: "大事记",
    ai: "AI智能插件",
    querybtn: "cap4自定义控件-查询统计按钮",
    invoice: "cap4自定义控件电子发票",
    formcreditqueryctrl: "cap4自定义控件-企业征信查询",
    ocrbtn: "cap4自定义控件-ocr图像识别（身份证、银行卡、名片、营业执照、组织机构代码证、税务登记证）",
    formwordinjectionctrl: "表单自定义控件-word套红控件",
    formhandwritectrl: "",
    xiaoz: "小智(m3)",
    capqrcode: "二维码(cap)",
    templateApprove: "模板审批",
    cap4report: "cap4统计",
    cap4business: "cap4业务包门户",
    cap4query: "cap4查询",
    cap4unflow: "cap4无流程列表",
    cwidgetnewform: "cap4自定义控件 custom widget new form 新建表单",
    cwidgetviewform: "cap4自定义控件 custom widget view form 查看表单",
    capextend: "cap4扩展控件",
    ctripcityform: "携程控件",
    inspect: "cmp 体检",
    cap4todolist: "cap4待办",
    trustdo: "信任度",
    filemanage: "M3文件管理",
    leaderagenda: "目标管理-领导行程",
    wea: "Work efficiency analysis工作效率分析【业务效能】",
    meetingsummary: "会议纪要",
    nlpbtn: "cap4自定义控件-nlp文本提取",
    customCtrlResources: "移动端自定义控件的资源文件",
    menu: "系统菜单",
    template: "系统模板",
    workchangectrl: "表单自定义控件-工作交接 移动端展示",
    meetingComponent: "快速会议",
    formrichtextctrl: "表单自定义控件-富文本",
    ctripIntegratedPlugin: "携程集成插件",
    systemmaintenance: "系统维护",
    govdoc: "新公文枚举，专用于新公文的全文检索，其他地方请用edoc和subApp",
    govdocSend: "表单公文-发文",
    govdocRec: "表单公文-收文",
    govdocExchange: "表单公文-交换公文",
    govdocSign: "表单公文-签报",
    groupSpace: "群空间(致信)",
    simulation: "流程仿真",
    stepBackData: "协同回退数据虚拟类型",
    stepBackDataEdoc: "公文回退数据虚拟类型",
    econtract: "",
    officePlugins: "移动端office插件工程",
    leaderSchedule: "领导日程",
    conference: "",
    collaborationDoc: "文档协同",
    capformui: "cap ui 组件",
    smartCode: "智码平台",
    greetingCode: "重要信息",
    imail: "内部邮箱",
    ckeditor: "ckeditor",
    imailInbox: "内部邮箱-收件箱",
    imailSent: "内部邮箱-发件箱",
    imailDrafts: "内部邮箱-草稿箱",
    handoverSet: "交接设置",
    ia_inventory: "资产管理",
    dataFilling: "数据填报",
    gwk: "公文库",
    assistants: "协办",
    dataTable: "数据填报",
    ai_reviser: "",
    capDataPlatform: "cap 数据运营平台"
  };

  const AppTypeEnumNumber = {
    0: "全局",
    1: "协同应用",
    2: "表单",
    3: "知识管理",
    4: "公文",
    5: "计划",
    6: "会议",
    7: "公告",
    8: "新闻",
    9: "讨论",
    10: "调查",
    11: "日程事件",
    12: "邮件",
    13: "组织模型",
    14: "项目",
    15: "关联人员",
    16: "交换",
    17: "人力资源",
    18: "博客",
    19: "发文",
    20: "收文",
    21: "签报",
    22: "待发送公文",
    23: "待签收公文",
    24: "待登记公文",
    25: "在线交流",
    26: "综合办公",
    27: "代理设置",
    28: "密码修改",
    29: "会议室",
    30: "任务管理",
    31: "留言板",
    32: "信息报送",
    33: "信息报送统计",
    34: "收文分发",
    35: "公示板",
    36: "签到",
    37: "移动应用接入-html5应用包",
    38: "sap插件",
    39: "第三方整合",
    40: "大秀",
    41: "流程绩效",
    42: "行为绩效，足迹(cmp)",
    43: "业务生成器(cmp)",
    44: "公共资源(cmp)",
    45: "工作流(cmp)",
    47: "无流程表单(cmp)",
    48: "表单查询统计(cmp)",
    49: "cmp",
    51: "dee模块(cmp)",
    52: "应用模块(m3)",
    53: "公共资源(m3)",
    54: "登陆(m3)",
    55: "消息模块(m3)",
    56: "我的模块(m3)",
    57: "搜索模块(m3)",
    58: "待办模块(m3)",
    59: "全文检索(m3)",
    60: "我的收藏(m3)",
    61: "UC(m3)",
    62: "通讯录(m3)",
    63: "帆软报表",
    64: "状态提醒",
    65: "H5门户(m3)",
    66: "cap4表单",
    67: "应用管理",
    68: "文件报表",
    69: "excel报表",
    70: "报表中心(m3)",
    71: "大事记",
    72: "AI智能插件",
    73: "cap4自定义控件-查询统计按钮",
    74: "cap4自定义控件电子发票",
    75: "cap4自定义控件-企业征信查询",
    76: "cap4自定义控件-ocr图像识别（身份证、银行卡、名片、营业执照、组织机构代码证、税务登记证）",
    77: "表单自定义控件-word套红控件",
    79: "小智(m3)",
    80: "二维码(cap)",
    81: "模板审批",
    82: "cap4统计",
    83: "cap4业务包门户",
    84: "cap4查询",
    85: "cap4无流程列表",
    86: "cap4自定义控件 custom widget new form 新建表单",
    87: "cap4自定义控件 custom widget view form 查看表单",
    88: "cap4扩展控件",
    89: "携程控件",
    90: "cmp 体检",
    91: "cap4待办",
    92: "信任度",
    93: "M3文件管理",
    94: "目标管理-领导行程",
    95: "Work efficiency analysis工作效率分析【业务效能】",
    96: "会议纪要",
    97: "cap4自定义控件-nlp文本提取",
    98: "移动端自定义控件的资源文件",
    99: "系统菜单",
    100: "群空间(致信)",
    101: "系统模板",
    102: "表单自定义控件-工作交接 移动端展示",
    103: "快速会议",
    104: "表单自定义控件-富文本",
    105: "携程集成插件",
    106: "系统维护",
    111: "领导日程",
    118: "文档协同",
    119: "数据填报",
    120: "cap ui 组件",
    121: "智码平台",
    122: "重要信息",
    123: "公文库",
    125: "协办",
    127: "内部邮箱",
    128: "ckeditor",
    130: "交接设置",
    131: "数据填报",
    141: "内部邮箱-收件箱",
    142: "内部邮箱-发件箱",
    143: "内部邮箱-草稿箱",
    152: "cap 数据运营平台",
    400: "新公文枚举，专用于新公文的全文检索，其他地方请用edoc和subApp",
    401: "表单公文-发文",
    402: "表单公文-收文",
    403: "表单公文-交换公文",
    404: "表单公文-签报",
    818: "资产管理",
    994: "公文回退数据虚拟类型",
    998: "流程仿真",
    999: "协同回退数据虚拟类型"
  };
  
  
  export default {
    AppTypeEnum,
    AppTypeEnumNumber
  };
