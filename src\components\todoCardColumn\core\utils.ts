import type { ButtonType } from '../types';

/**
 * 按钮操作工具类
 */
export class ActionUtils {
  /**
   * 检查是否是特殊操作类型
   */
  static isSpecialOperation(button: ButtonType): boolean {
    return ['top', 'unTop', 'hasten', 'allocation'].includes(button.handleType);
  }

  /**
   * 检查是否是会议相关操作
   */
  static isMeetingOperation(button: ButtonType, dataType?: string): boolean {
    return dataType === '6' && ['edit', 'cancel'].includes(button.handleType);
  }

  /**
   * 检查是否是确认类操作（需要用户确认的危险操作）
   */
  static isConfirmOperation(button: ButtonType): boolean {
    return ['cancel', 'terminate', 'return'].includes(button.handleType.toLowerCase()) ||
      (button.paramMap?.attitudeKey === "disagree" &&
       button.paramMap?.customAction?.isOptional === "0" &&
       button.paramMap?.customAction?.defaultAction?.toLowerCase() === "cancel");
  }

  /**
   * 检查是否是NextStep操作
   */
  static isNextStepOperation(button: ButtonType): boolean {
    return button.paramMap?.attitudeKey === "disagree" &&
      button.paramMap?.customAction?.isOptional === "1" &&
      button.handleType === "ContinueSubmit" &&
      !(button as any).hasSelectedNextAction;
  }

  /**
   * 生成按钮的唯一标识
   */
  static getButtonKey(button: ButtonType, affairId?: string): string {
    return `${button.handleType}_${button.name}_${affairId || ''}`;
  }

  /**
   * 获取按钮样式类型
   */
  static getButtonType(handleType: string): string {
    const typeMap: Record<string, string> = {
      'Cancel': 'default',
      'Terminate': 'default',
      'ContinueSubmit': 'primary',
      'join': 'primary',
      'edit': 'primary',
      'HaveRead': 'primary',
      'default': 'default'
    };
    return typeMap[handleType] || 'default';
  }
}

/**
 * 日期格式化工具类
 */
export class DateUtils {
  /**
   * 格式化日期显示
   */
  static formatDate(date: string): string {
    if (!date) return '';
    return date.replace(/年|月/g, '-').replace(/日/g, '').replace(/-$/, '');
  }
}

/**
 * 状态标签工具类
 */
export class StatusUtils {
  /**
   * 获取状态标签文本
   */
  static getStatusLabel(affairSubStatus: number): string {
    switch (affairSubStatus) {
      case 31:
        return '参加';
      case 33:
        return '待定';
      case 7:
      case 2:
        return '被回退';
      default:
        return '';
    }
  }

  /**
   * 获取状态标签的CSS类名
   */
  static getStatusTagClass(affairSubStatus: number): string {
    switch (affairSubStatus) {
      case 31:
        return 'status-tag-meeting-join';
      case 33:
        return 'status-tag-meeting-pending';
      case 7:
      case 2:
        return 'status-tag-fallback';
      default:
        return 'status-tag-default';
    }
  }
}
