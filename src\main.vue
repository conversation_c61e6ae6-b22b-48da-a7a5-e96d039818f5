<template>
  <div class="main-container">
    <!-- 显示fullMenu组件 -->
    <FullMenu v-if="isShowFullMenu" />
    
    <!-- 显示其他组件，这里可以根据需要添加其他视图 -->
    <div v-else class="other-views">
      <div class="placeholder">
        <h2>当前菜单: {{ currentMenuName }}</h2>
        <p>这里将显示对应的内容组件</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useMenu } from '@/stores/global'
import { useMenuStore } from '@/stores/menuStore'
import FullMenu from '@/components/fullMenu/index.vue'

const uMenu = useMenu()
const menuStore = useMenuStore()

// 计算是否显示fullMenu
const isShowFullMenu = computed(() => {
  const currentMenu = uMenu.currentMenuInfo
  return currentMenu?.key === 'fullMenu' || currentMenu?.id === 'fullMenu'
})

// 当前菜单名称
const currentMenuName = computed(() => {
  const currentMenu = uMenu.currentMenuInfo
  return currentMenu?.label || currentMenu?.name || '未知菜单'
})

// 初始化SearchMenuUtils
const initSearchMenuUtils = () => {
  console.log('SearchMenuUtils will be initialized when needed')
  // SearchMenuUtils现在是内部实现，会在需要时自动初始化
}

onMounted(async () => {
  // 页面加载时预加载菜单数据
  console.log('开始预加载菜单数据...')
  await menuStore.preloadMenuData()
  console.log('菜单数据预加载完成')
  
  // 初始化SearchMenuUtils
  initSearchMenuUtils()
})
</script>

<style scoped lang="less">
.main-container {
  width: 100%;
  height: 100vh;
  
  .other-views {
    padding: 20px;
    
    .placeholder {
      text-align: center;
      margin-top: 100px;
      
      h2 {
        color: #333;
        margin-bottom: 16px;
      }
      
      p {
        color: #666;
        font-size: 14px;
      }
    }
  }
}
</style> 