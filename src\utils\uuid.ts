const hexList: string[] = []
for (let i = 0; i <= 15; i++) {
  hexList[i] = i.toString(16)
}

export function buildUUID(): string {
  let uuid = ''
  for (let i = 1; i <= 36; i++) {
    if (i === 9 || i === 14 || i === 19 || i === 24) {
      uuid += '-'
    } else if (i === 15) {
      uuid += 4
    } else if (i === 20) {
      uuid += hexList[(Math.random() * 4) | 8]
    } else {
      uuid += hexList[(Math.random() * 16) | 0]
    }
  }
  return uuid.replace(/-/g, '')
}

let unique = 0
export function buildShortUUID(prefix = ''): string {
  const time = Date.now()
  const random = Math.floor(Math.random() * 1000000000)
  unique++
  return prefix + '_' + random + unique + String(time)
}

export function generateSecure16DigitNumber({ allowNegative = false } = {}) {
  const array = new Uint32Array(4);
  window.crypto.getRandomValues(array);
  
  // 生成 16 位数字字符串
  const numStr = Array.from(array)
    .map(n => n.toString(10).padStart(8, '0').slice(-8))
    .join('')
    .slice(0, 16);

  // 如果是负数，随机决定符号
  const isNegative = allowNegative && Math.random() < 0.5;
  
  return isNegative ? `-${numStr}` : numStr;
}

