<template>
  <span class="reload_btn" @click="goGetAnewAnswer">
    <Tooltip title="重新生成">
      <i class="iconfont ans_font ai-icon-shuaxin"></i></Tooltip
  ></span>
</template>
<script setup lang="ts">
  import { onMounted,ref } from 'vue';
  import { Tooltip } from 'ant-design-vue';
  import { useChatList } from '@/stores/chatList';
  import { cateAsistantLogs } from '@/api/common/index';
  import cardInstance from '@/stores/card';

  const uChatList = useChatList();


  const props = defineProps({
    transAiInfo: {
      type: Object,
      default: () => {},
    },
  });

  // 重新生成
  const goGetAnewAnswer = () => {
    // 直接调用滚动方法
    uChatList.scrollToBottom();
    const requestParams = props.transAiInfo.staticData.requestParams;
    let inputValue = requestParams.input;
    const requestFiles = requestParams.citations;
    const needHistoryRecord = requestParams.needHistoryRecord;
    //TODO: 如果是消息助手发出去的消息隐藏了显示，重新生成时，给一个固定的显示,后面看是不是v5直接改默认的输入
    if(!needHistoryRecord && requestParams.assistantCode === "assist2714369744321397365") {
      inputValue = '查看我的消息';
    }
    // 重新发送
    cardInstance.sendMessage(inputValue.trim(),requestFiles);
    collectionLog();
  };

  const collectionLog = ()=>{
    // 采集日志
    cateAsistantLogs({
      type: 2,
      assistantId: props.transAiInfo.staticData.requestParams.assistantId,
      sessionId: props.transAiInfo.data.aiSessionId,
      citations: props.transAiInfo.staticData.requestParams.citations,
      chatSessionId: props.transAiInfo.staticData.requestParams.chatSessionId,
    });
  }

</script>
<style scoped lang="less">
  .reload_btn {
    margin-right: 8px;
    color: rgba(0, 0, 0, 0.4);
  }
  .reload_btn:hover {
    color: @sky;
    span {
      color: @sky;
    }
  }
  .iconfont:hover {
    color: @sky;
  }
</style>
