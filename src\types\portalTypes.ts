export type EmitEventData = {
  type: string
  params: any
}

// 菜单
export type MenuItemType = {
  label: string;
  key: string;
  icon: string;
  link?: string;
  type: string,
  params?: object
  id: string
};

// API 响应基础接口
export interface BaseResponse<T> {
  code: number | string;
  data: T;
  msg?: string;
}


// 栏目组件 Props 类型
export type buttonType = {
  btnIcon: string,
  btnUrl: string,
}


export interface TodoRenderInfo {
  affairId: string,
  companyName: string,
  createDate: string,
  endDate: number,
  link: string,
  templateId: string,
  title: string,
  type: string,
  typeName: string,
  userName: string,
  userPhoto: string,
  id?: string,
}

export type ConfigType = {
  desc?: string,
  icon?: string,
  maxItems: number,
  title: string,
}
export type DataType = {
  dataList: TodoRenderInfo[] | MeetingItem[],
}
export interface ColumnProps {
  btns?: buttonType[],
  config?: ConfigType,
  data?: DataType,
}


// 待办项接口
export interface TodoItem {
  app: string;
  appId: string;
  title: string;
  typeName: string;
  userName: string;
  userPhoto: string;
  createDate: string;
  endDate: string;
  affairId: string;
  templateId: string;
  businessTypeName: string;
  link: string;
  type: string;
  companyName: string;
  // 源数据字段
  senderId: string;
  businessType: string;
  senderName: string;
  startTime: string;
  deadlineDate: string;
  importantLevel: string;
  // 操作按钮
  rowButtons?: any[]; // 操作按钮数组
  handleParam?: object;
}

// 会议项接口
export interface MeetingItem {
  id?: string;
  affairId: string;
  title?: string;
  startTime?: string;
  endTime?: string;
  location?: string;
  participants?: string[];
  // 源数据字段
  startDate?: string;
  endDate?: string;
  createUser?: string;
  // 转换后的字段
  meetingName?: string;
  meetingId?: string;
  meetingTime?: string;
  createUsr?: string;
  mettingPlace?: string;
  jumpUrl?: string;
  summaryText?: string;
  loaddingSummary?: boolean;
}

// 分类数据接口 - 更新为新的数据结构
export interface ClassificationItem {
  portletParams: string;        // JSON字符串，包含各种参数
  isPortlet: string;           // "1" 表示是portlet
  predicate: boolean;          // 断言条件
  total: string;               // 总数
  appId: string;               // 应用ID
  classificationName: string;   // 分类名称
  isHide: string;              // "1" 表示隐藏
  // 可选字段
  subAppId?: string;
  isThird?: string;
  isLabel?: string;
  canDrag?: string;
}

// 组件 Props 接口
export interface TodoCardProps {
  config: {
    title: string;
    maxItems: number;
    desc?: string;
    icon?: string;
    autoLoad?: boolean;
    refreshInterval?: number;
  };
  data?: {
    dataList: TodoItem[];
  };
  classifications?: ClassificationItem[]; // 新增分类数据
  btns?: Array<{
    btnIcon: string;
    btnUrl: string;
  }>;
}

export interface MeetingCardProps {
  config: {
    title: string;
    count: number;
  };
  data: {
    dataList: MeetingItem[];
  };
}

// 组件节点接口
export interface ComponentNode {
  type: 'component';
  componentName: string;
  props: TodoCardProps | MeetingCardProps;
}

// 列节点接口
export interface ColNode {
  type: 'col';
  width?: string;
  span?: number;
  children: (RowNode | ComponentNode)[];
}

// 行节点接口
export interface RowNode {
  type: 'row';
  gutter?: number
  children: ColNode[];
}

// 用户

export type UserType = {
  avatar: string;
  name: string;
}




// --- 类型定义 (定义布局结构的基础) ---

// 布局节点的基础接口
export interface LayoutNode {
  id?: string; // 可选的唯一标识符
  type: 'row' | 'col' | 'component'; // 节点类型：行、列或组件
}

// 代表要渲染的具体组件内容的接口
export interface ComponentNode extends LayoutNode {
  type: 'component';
  componentName: string; // 要渲染的 Vue 组件的名称或标识符
  props?: Record<string, any>; // 传递给组件的 props (可选)
}

// 列（Col）节点的接口定义
export interface ColNode extends LayoutNode {
  type: 'col';
  span?: number; // 列宽，通常基于 24 栅格
  gutter?: number;
  children: (ComponentNode | RowNode)[]; // 列可以包含组件或嵌套的行
}

// 行（Row）节点的接口定义
export interface RowNode extends LayoutNode {
  type: 'row';
  span?: number; // 列宽，通常基于 24 栅格
  // 行间距 (gutter)，可以是数字或元组 [水平, 垂直]
  gutter?: number;
  children: ColNode[]; // 行只能包含列
}

// 定义 Prop 的类型，可以接受任何有效的布局节点类型
export type LayoutConfigProp = RowNode | ColNode | ComponentNode;



// 智能创作

export interface Label {
  id: string;
  name: string;
  type: number;
}

export interface AssistInfo {
  id: string;
  code: string;
  name: string;
  introduce: string;
  iconUrl: string;
  outputType: number;
  labels: Label[]; // 标签数组
  createUserId: string;
  createUserName: string | null;
  lastUseTime: number | null;
  createTime: number;
  openRecommend: number;
  fileUpload: number;
}

// 定义分类后的数据结构
export interface ClassifiedCategory {
  id: string | null;      // 分类的ID (标签ID 或 null 代表 "其他")
  title: string;          // 分类标题 ("其他" 或标签名)
  list: AssistInfo[];     // 该分类下的助手列表
}

export interface ReportItemType {
  id: string;
  views: number;
  sort: number;
  design: {
    reportId: string;
    title: string;
    type: string;
    category: string;
    categoryId: string;
    reportCategory: string;
    name: string;
    createMemberName: string;
    createTime: string;
  };
};

export interface QuestionItem {
  title: string;
  questions: {
    text: string;
  }[];
}
