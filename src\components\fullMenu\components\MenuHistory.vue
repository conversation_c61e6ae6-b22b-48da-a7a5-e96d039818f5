<template>
  <div class="menu-history-block" v-if="historyList.length > 0">
    <div class="menu-history-header">
      <div class="menu-history-title">最近访问:</div>
    </div>
    <div class="menu-history-content">
      <div class="menu-history-items">
        <template v-for="(item, index) in displayHistoryList" :key="item.idKey">
          <div
            class="menu-history-item"
            @click="handleSelect(item)"
            :title="item.nameKey"
          >
            {{ item.nameKey }}
          </div>
          <div
            v-if="index < displayHistoryList.length - 1"
            class="menu-history-split"
          >
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { MenuItem } from '../types/menu'

interface Props {
  historyList: MenuItem[]
}

interface Emits {
  (e: 'select', item: MenuItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 限制显示的历史记录数量
const displayHistoryList = computed(() => {
  return props.historyList.slice(0, 8) // 最多显示8个
})

// 处理选择
const handleSelect = (item: MenuItem) => {
  emit('select', item)
}
</script>

<style lang="less" scoped>
.menu-history-block {
  display: flex;
  font-size: 12px;
  flex: 1;
  min-width: 0;

  .menu-history-header {
    flex-shrink: 0;

    .menu-history-title {
      color: #333;
      font-weight: 600;
      line-height: 20px;
      margin-right: 12px;
    }
  }

  .menu-history-content {
    color: #333;
    overflow: hidden;
    .menu-history-items {
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      .menu-history-item {
        display: inline-block;
        cursor: pointer;
        transition: all 0.2s;
        white-space: nowrap;
        max-width: 165px;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 20px;

        &:hover {
          color: var(--theme-brand6, #4379FF);
        }
      }

      .menu-history-split {
        background-color: #BFBFBF;
        margin: 0 12px;
        user-select: none;
        line-height: 20px;
        height: 16px;
        width: 1px;
      }
    }
  }
}
</style>
