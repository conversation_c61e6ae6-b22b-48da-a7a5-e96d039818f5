import { message } from 'ant-design-vue';
export interface TypeResponse {
  code: '0' | string;
  data: any;
  message: string | null;
}
// 助手列表入参
export interface TypeCHeckAssistParams {
  params: {
    keyword: string;
    labelId?: string;
    assistantType?: 0 | 1 | null;
    defaultAssistantId?: string;
  };
  pageInfo: {
    pageNumber: number;
    pageSize: number;
    needTotal: boolean;
  };
}
// 上传文件入参
export interface TypeUploadFileParams {
  aiAgentFileInfoDto: {
    chatSessionId: 'string';
    sessionId: 'string';
  };
  file: File;
}

// 历史会话助手列表入参
export interface TypeHistoryAssistParams {
  params: {
    name: string;
  };
  pageInfo: {
    pageNumber: number;
    pageSize: number;
    needTotal: boolean;
  };
}

// 历史会话助手列表入参
export interface TypeHistoryDetailParams {
  params: {
    sessionId: string;
  };
  pageInfo: {
    pageNumber: number;
    pageSize: number;
    needTotal: boolean;
  };
}

//  点赞 、点踩
export interface TypeOperateAnswerParams {
  messageId: string;
  type: 0 | 1;
  content: string;
}

//  取消sse
export interface TypeCancelChatParams {
  chatSessionId: string;
  sessionId: string;
  callId: string | number;
}


// 卡片的内容声明
export interface CardContentType {
  isCard?: boolean;
  finish?: undefined | number,
  context: any;
  index?: number
  isIframe?: boolean,
  json?: any,
  isKnowledgeData?: any
  isDobuleScreen?: boolean
}

// 卡片的类型声明
export interface CardDataType {
  _id?: number | string;
  _index: number | string;
  _t?: number;
  componentKey: string;
  staticData?: any;
  config?: any;
  data: any;
}

// 流式请求的类型声明
export interface StreamFnInstance {
  card: any;
  isStop: boolean;
  timer: any;
  _id: number;
  startTime: number;
  option: any;
  dataHistory: any[];
  isCancel: boolean;
  messageType: Record<string, boolean>;
  retry: number;
  run: (option: any) => Promise<void>;
  stop: (isNormal?: boolean) => void;
}

export interface streamParams {
  assistantId: string,
  assistantCode: string,
  chatSessionId: string,
  citations: any[],
  input: string,
  sessionId: string,
}

export interface AiRequestCard extends CardDataType {
  data: {
    isLoading: boolean,
    isError: boolean,
    isHide: boolean,
  }
}

export interface AiLoadingCard extends CardDataType {
  data: {
    content: string,
    isLoading: boolean
  }
}

export interface AgentInfo extends CardDataType {
  data: {
    agentInfo: object
  }
}
export interface AiDataCard extends CardDataType {
  data: {
    cardData: CardContentType[]
    messageId?: string | number,
    aiSessionId?: string | number,
    illageSessionType?: string | number,
    isCompleted?: boolean,
    isPlay?: boolean,
    isStop?: boolean,
    isUnlike?: boolean,
    knowledgeData?: Array<any>,
    recommandQuestion?: Array<object>,
    citations?: Array<FileType>,
    messageType?: number,
    status: number,
    isHide?: boolean,
    processData?: any,
  }
}
export interface FileType{
  fileNumber: string,
  fileUrl: string,
  name: string,
  size: string,
}