<template>
  <!-- 菜单容器，根据折叠状态应用不同的 class -->
  <div :class="['menu-wrapper', 'h-full', 'flex', 'flex-col', state.collapsed ? 'collapsed' : 'expanded']">

    <div class="menu-header-wrapper">
      <!-- 头部区域 (按钮已移出) -->
      <div class="menu-header cursor-pointer" @click="handleLogoClick">
        <!-- Logo，始终显示 -->
        <img :src="logo_1" v-if="state.collapsed" alt="logo" class="logo h-[50px] w-[50px]">
        <img :src="logo_2"  v-else alt="logo" class="logo h-[50px] w-[155px]">
        <!-- 占位符，用于在 Logo 隐藏时辅助对齐 (现在 Logo 始终显示，可以考虑移除或保留用于其他布局目的)-->
        <!-- <span class="placeholder" v-show="state.collapsed"></span> -->
        <!-- The button is no longer here -->
      </div>
       <!-- 折叠/展开切换按钮 (移到 menu-wrapper 的直接子级) -->
       <button @click="toggleCollapsed" class="toggle-button">
        <AiIcon v-if="!state.collapsed" iconName="ai-icon-shouqi2" class="text-[22px]" />
        <AiIcon v-else iconName="ai-icon-zhankai1" class="text-[22px]" />
      </button>
    </div>

    <!-- 主要菜单区域 -->
    <div class="menu-content flex-1 overflow-auto overflow-x-hidden">
      <!-- Ant Design 菜单组件 -->
      <a-menu
        v-model:selectedKeys="state.selectedKeys"
        mode="inline"
        :inlineCollapsed="state.collapsed"
        :items="menuItems"
        @click="handleMenuClick"
        class="portal-menu"
      >
        <!-- 如果需要更复杂的渲染，可以在这里自定义，但 :items 属性更简洁 -->
      </a-menu>
    </div>


    <!-- 底部菜单区域 -->
    <div class="menu-bottom">
       <a-menu
        v-model:selectedKeys="state.selectedKeys"
        mode="inline"
        :inlineCollapsed="state.collapsed"
        :items="bottomMenuItems"
        @click="handleMenuClick"
      />
    </div>

    <!-- 用户/页脚区域 -->
    <div :class="[state.collapsed ? 'pl-[14px]' : 'pl-[16px]','pt-[15px]', 'menu-footer']">
       <!-- 用户头像，根据折叠状态调整大小 -->
       <a-avatar :size="40"  :src="userInfo.avatar" /> <!-- 替换为实际头像路径 -->
    </div>
  </div>
</template>

<script setup lang="ts">
// 引入 Vue 和 Ant Design 相关 API 及组件
import { reactive, computed, h, watch } from 'vue';
import type { PropType } from 'vue'
import { Menu as AMenu, Avatar as AAvatar, Divider as ADivider } from 'ant-design-vue';
import type { MenuProps } from 'ant-design-vue';
import type { UserType, MenuItemType } from '@/types/portalTypes'
import AiIcon from '@/components/aiIcon/index.vue'


const props = defineProps({
  userInfo: {
    type: Object as PropType<UserType>,
    default: () => ({})
  },
  topMenuList: {
    type: Array as PropType<MenuItemType[]>,
    default: () => []
  },
  bottomMenuList: {
    type: Array as PropType<MenuItemType[]>,
    default: () => []
  },
  currentMenu: {
    type: String as PropType<string>,
    default: () => ''
  }
})

const emit = defineEmits(['handleMenuClick', 'update:currentMenu'])
const logo_1 = "/seeyon/ai-platform/ai-static/logo_back.png";
const logo_2 = "/seeyon/ai-platform/ai-static/logo_expand.png";

// 组件响应式状态
const state = reactive({
  collapsed: true, // 初始状态：收起
  selectedKeys: ['comi'], // 默认选中的菜单项 key
});

// --- 菜单项定义 ---


watch(() => props.currentMenu, (newVal) => {
  state.selectedKeys = [newVal]
})

const componentMap = {
  AiIcon: AiIcon,
  AAvatar: AAvatar,
}


// 创建 Ant Design 菜单项所需数据结构的辅助函数
const createMenuItem = (label: string, key: string, icon: any, params?: any) => {
  // console.log("createMenuItem:", url);
  return {
    key, // 唯一标识
    params,
    icon: () => h(icon, params), // 渲染图标的函数
    label, // 菜单项文字
  }
};




// 上部菜单项 (使用 computed 包裹，虽然在这里不是必须，但保持一致性)
const menuItems = computed(() => {
  return props.topMenuList.map(item => {
    const { type, params, label, key } = item
    const Component = componentMap[type as keyof typeof componentMap]
    return createMenuItem(label, key, Component, params)
  })
})


// 底部菜单项
const bottomMenuItems = computed(() => {
  return props.bottomMenuList.map(item => {
    const { type, params, label, key } = item
    const Component = componentMap[type as keyof typeof componentMap]
    return createMenuItem(label, key, Component, params)
  })
})



// 切换菜单折叠状态的方法
const toggleCollapsed = () => {
  state.collapsed = !state.collapsed;
};

// 处理菜单项点击事件 (例如：用于路由导航)
const handleMenuClick: MenuProps['onClick'] = (e:any) => {
  console.log('点击的菜单项 key:', e);
  // 在这里添加导航逻辑，例如使用 Vue Router
  // 示例: router.push({ name: e.key });
  emit('handleMenuClick', e.key, e.item)
  emit('update:currentMenu', e.key)
};

const handleLogoClick = () => {
  state.selectedKeys = ['comi']
  emit('handleMenuClick', 'comi')
  emit('update:currentMenu', 'comi')
  console.log('点击了 logo');
}

</script>

<style scoped lang="less">
.menu-wrapper {
  position: relative; // 确保是定位上下文
  // background-color: #f7f9fc; // 暂时注释掉背景色，以便调试
  transition: width 0.2s cubic-bezier(0.2, 0, 0, 1) 0s;
  padding: 15px 15px 30px 10px;
  &.collapsed {
    width: 125px;
    padding-top: 20px;
  }
  &.expanded {
    width: 245px;
  }
}
.menu-header-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toggle-button {
  cursor: pointer;
  z-index: 10; // 确保在其他内容之上
  width: 24px;  // 固定按钮宽度
  height: 24px; // 固定按钮高度
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px; // 调整图标大小
  color: #6F7686; // 图标颜色
  // 平滑过渡 left 属性的变化
  transition: left 0.2s cubic-bezier(0.2, 0, 0, 1) 0s;

  &:hover {
    color: #3B425F;
    border-color: #d1d8e0; // 鼠标悬停时边框颜色稍深
  }
}

/* 展开时的按钮位置：定位按钮的中心点在 menu-wrapper 的右边缘 */
.menu-wrapper.expanded .toggle-button {
  left: calc(100% - 28px); // 100% 是父容器宽度, 减去按钮宽度的一半 (24px / 2 = 12px)
}

/* 折叠时的按钮位置：定位按钮的中心点在 menu-wrapper 的右边缘 */
.menu-wrapper.collapsed .toggle-button {
  left: calc(100%); // 同样是 100% 减去按钮宽度一半，但此时父容器宽度是 80px
                           // 这会自动将按钮定位在折叠后菜单的右侧外部
}

.menu-header {
  display: flex;
  align-items: center;
  // background-color: #f0f0f0; // 添加临时背景色以观察布局
  overflow: hidden; // 隐藏可能溢出的部分

  .logo {
    object-fit: contain;
    // 添加过渡效果，使得位置变化平滑
    transition: margin 0.2s cubic-bezier(0.2, 0, 0, 1) 0s;
  }
  // Placeholder might not be needed anymore if logo is always visible
  // .placeholder { ... }
}

/* 展开时的 Header 布局 (Logo 靠左) */
.menu-wrapper.expanded .menu-header {
  justify-content: flex-start; // Logo 靠左
}

/* 折叠时的 Header 布局 (Logo 居中) */
.menu-wrapper.collapsed .menu-header  {
  padding-left: 10px;
  .logo {
    width: 48px;
    height: 48px;
  }
}

.menu-content {
  padding-top: 20px; // 菜单内容区域距离头部的间距
}


.menu-divider-collapsed {
   margin: 10px 16px; // 折叠状态下调整左右外边距
}


.menu-bottom {
 // 底部菜单区域的特定样式 (如果需要)
}

.menu-footer {
  display: flex; // 使用 flex 布局
  align-items: center; // 垂直居中对齐

  .user-name {
    font-size: 14px; // 用户名字体大小
    color: #3B425F; // 用户名颜色
    white-space: nowrap; // 防止文字换行
    overflow: hidden; // 隐藏超出部分
    text-overflow: ellipsis; // 超出部分显示省略号 (...)
    flex: 1; // 允许在 flex 布局中缩放
  }
}

// --- Ant Design 菜单样式覆盖 (使用 :deep 穿透 scoped) ---
:deep(.ant-menu) {
  background: transparent; // 菜单背景透明，继承父容器背景
  border-inline-end: none !important; // 移除 Ant Design 菜单默认的右侧边框

  .ant-menu-item {
    margin: 0;
    padding: 0;
    height: 48px; // 菜单项高度
    line-height: 48px; // 行高与高度一致，用于垂直居中文本
    border-radius: 8px; // 菜单项圆角
    padding-left: 27px !important; // 左侧内边距 (覆盖 antd 默认)
    font-size: 14px; // 菜单项文字大小
    color: #3B425F; // 默认文字颜色
    margin-bottom: 2px;
    .ant-menu-item-icon {
      font-size: 24px; // 图标大小
      color: #6F7686; // 默认图标颜色
      min-width: auto; // 重置最小宽度 (如果需要)
    }

     // 当菜单项只有图标时 (antd 会添加此 class)
    &.ant-menu-item-only-child {
       .ant-menu-title-content {
         margin-inline-start: 8px; // 图标和文字之间的间距
       }
    }

    // 折叠状态下菜单项的特定样式
    &.ant-menu-item-collapsed {
       padding-inline: 0 !important; // 移除左右内边距
       display: flex; // 使用 flex 布局
       align-items: center; // 垂直居中图标
       justify-content: center; // 水平居中图标
       .ant-menu-item-icon {
         margin: 0; // 移除图标外边距
         font-size: 20px; // 折叠时图标稍大？(可调整)
       }
    }

    // 未选中项的鼠标悬停状态
    &:not(.ant-menu-item-selected):hover {
      background: linear-gradient(90deg, rgba(224, 237, 247, 0.5) -2.73%, rgba(226, 243, 249, 0.5) 44.01%, rgba(229, 232, 248, 0.5) 100%) !important;
      color: #3B425F; // 悬停时的文字颜色
    }
  }

  // 选中项的样式
  .ant-menu-item-selected {
    background: linear-gradient(to right, #E0EDF7, #E2F3F9, #E5E8F8); // 渐变背景
    .ant-menu-title-content {
      font-weight: @font-weight-500; // 选中项文字稍加粗
    }
  }
}
.expanded {
  :deep(.ant-menu) {
    .ant-menu-item {
      width: 220px;
    }
  }
}

// 折叠/展开时平滑隐藏/显示文字标签
.expanded :deep(.ant-menu-inline .ant-menu-item .ant-menu-title-content) {
  opacity: 1; // 展开时完全显示
  transition: opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1); // 透明度过渡效果
}
.collapsed :deep(.ant-menu-inline .ant-menu-item .ant-menu-title-content) {
  max-width: 0; // 折叠时最大宽度为 0
  display: inline-block; // 保持行内块布局
  opacity: 0; // 折叠时完全透明
}

.collapsed :deep(.ant-menu-item) {
  padding: 0 !important;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.collapsed :deep(.ant-menu-title-content) {
  width: 0;
  margin: 0 !important;
}

</style>
