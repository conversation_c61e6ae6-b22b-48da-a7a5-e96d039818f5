const tabMockData = [];

for (let i = 0; i < 15; i++) {
    tabMockData.push({
        key: 'news' + i,
        label: '新闻' + "·" + i,
        content: [
            {
                id: '1204242856935309988' + i,
                number: null,
                name: '这是有图片的新闻77' + i,
                fileSize: '',
                fileType: '',
                fileUrl: '',
                fileRelativeUrl: '',
                fileTag: '',
                source: 'user',
                sourceId: '',
                createTime: 1745482502000,
                metadata: {
                    appType: 8,
                    author: 'cs',
                    authorId: '-8603252206078505744',
                    createDate: '1745482502000',
                    entityId: '1204242856935309988',
                    title: '这是有图片的新闻',
                },
            },
            {
                id: '1204242856935309981' + i,
                number: null,
                name: '这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77这是有图片的新闻77' + i,
                fileSize: '',
                fileType: '',
                fileUrl: '',
                fileRelativeUrl: '',
                fileTag: '',
                source: 'user',
                sourceId: '',
                createTime: 1745482502000,
                metadata: {
                    appType: 8,
                    author: 'cs',
                    authorId: '-8603252206078505744',
                    createDate: '1745482502000',
                    entityId: '1204242856935309981',
                    title: '这是有图片的新闻',
                },
            },
        ]
    })
}

export const mockData = tabMockData