<template>
  <div class="personal-card-wrapper" v-for="(item, index) in personalDatas" :key="index">
    <div class="title">好的，已为您查询到“{{ item.matched_word }}”结果：</div>
    <div
      class="personal-info"
      v-for="itm in item.data.data"
      :key="itm.id"
      @click="openPersonalInfo(itm)"
    >
      <div class="header">
        <!-- TODO: 头像, 先用div占位 -->
        <img class="icon" :src="itm.avatarUrl" alt="" />
        <!-- <div class="icon"></div> -->
        <div class="name ellipsis">{{ itm.name }}</div>
        <div class="phone-number" v-if="itm.telNumber">
          <i class="iconfont ai-icon-dadianhua-xian tel"></i>
          <span class="number">{{ itm.telNumber }}</span>
        </div>
      </div>
      <div class="department ellipsis font-setting">
        <span>{{ itm.deptName }}</span>
      </div>
      <div class="post ellipsis font-setting">{{ itm.postName }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { commonBusinessClass } from '@/utils/commonBusinessClass';

const props = defineProps({
  personalDatas: {
    type: Array,
    default: () => [],
  },
});
console.log(props.personalDatas);
function openPersonalInfo(item: any) {
  const business = commonBusinessClass();
  if (business && typeof business.openPersonalInfo === 'function') {
    business.openPersonalInfo(item);
  }
}
</script>

<style lang="less" scoped>
.personal-card-wrapper {
  width: 100%;
  background: #fff;
  padding: 8px 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 16px 0;

  .title {
    font-family: PingFang SC;
    font-weight: @font-weight-400;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0px;
    color: #000;
  }

  .personal-info {
    background: #f7f7f7;
    padding: 8px;
    line-height: 22px;
    font-size: 14px;
    border-radius: 8px;
    cursor: pointer;

    .header {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      .icon {
        width: 36px;
        height: 36px;
        margin-right: 8px;
        border-radius: 50%;
      }
      .name {
        font-weight: @font-weight-500;
        flex: 1;
      }
      .phone-number {
        height: 20px;
        background: #edf2fc;
        color: #4379ff;
        font-size: 12px;
        font-size: var(--theme-font-size0, 12px);
        line-height: 20px;
        line-height: var(--theme-line-height0, 20px);
        padding: 0 4px;
        margin-left: 16px;
        line-height: 20px;
        box-sizing: border-box;
        border-radius: 4px;
        .tel {
          font-size: 14px;
        }
      }
      .number {
        margin-left: 3px;
      }
      .icon-font {
        font-size: 14px;
      }
    }
    .department {
      margin-bottom: 4px;
    }
    .department,
    .post {
      margin-left: 44px;
    }
    .font-setting {
      color: rgba(0, 0, 0, 0.4);
      color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
    }
  }
}
</style>
