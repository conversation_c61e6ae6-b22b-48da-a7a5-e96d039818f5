/**
 * COMI Assistant SDK - 核心通信层
 * 提供跨窗口通信、comiEventBus集成、ISCOMI嵌套关系管理
 */

// ==================== 类型定义 ====================

/** 消息格式 */
interface Message<T = any> {
  type: string;
  content: T;
  instanceId?: string;
  timestamp?: number;
  systemId?: string;
}

/** SDK配置选项 */
export interface SDKOptions {
  timeout?: number;
  systemId?: string; // 系统标识
}

// ==================== 传输层 ====================

/** 传输层接口 */
interface Transport {
  send(message: Message, targetWindow: Window): void;
  onMessage(callback: (message: Message, source: Window) => void): void;
}

/** PostMessage传输实现 */
class PostMessageTransport implements Transport {
  send(message: Message, targetWindow: Window): void {
    targetWindow.postMessage(message, "*");
  }

  onMessage(callback: (message: Message, source: Window) => void): void {
    window.addEventListener("message", (event) => {
      if (event.source && event.data?.type && event.data?.content !== undefined) {
        callback(event.data as Message, event.source as Window);
      }
    });
  }
}

// ==================== ISCOMI管理器 ====================

/** ISCOMI嵌套关系管理 */
class ISCOMIManager {
  private value: boolean | null = null;

  constructor() {
    this.detectISCOMI();
  }

  /** 自动检测ISCOMI标识 */
  private detectISCOMI(): void {
    const topWindow = this.getTopWindow();
    
    // 检查是否已设置
    if (typeof (topWindow as any).ISCOMI === 'boolean') {
      this.value = (topWindow as any).ISCOMI;
      return;
    }

    // 自动检测：检查是否存在特定的第三方系统标识
    const isInThirdParty = !!(topWindow as any).vPortal || 
                          !!(topWindow as any).seeyon || 
                          !!(topWindow as any).thirdPartySystem;
    
    this.value = isInThirdParty;
    (topWindow as any).ISCOMI = this.value;
  }

  private getTopWindow(): Window {
    try {
      return window.top || window;
    } catch {
      return window;
    }
  }

  get(): boolean | null {
    return this.value;
  }

  set(value: boolean): void {
    this.value = value;
    const topWindow = this.getTopWindow();
    (topWindow as any).ISCOMI = value;
  }

  getDescription(): string {
    if (this.value === null) return '未检测到嵌套关系';
    return this.value ? 'COMI嵌入第三方系统' : '第三方系统嵌入COMI';
  }
}

// ==================== comiEventBus集成器 ====================

/** comiEventBus集成管理 */
class ComiEventBusIntegrator {
  private eventBus: any = null;
  private isReady = false;
  private readyCallbacks: (() => void)[] = [];

  constructor() {
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      this.eventBus = await this.findComiEventBus();
      if (this.eventBus) {
        this.isReady = true;
        this.readyCallbacks.forEach(cb => cb());
        this.readyCallbacks = [];
      }
    } catch (error) {
      console.warn('comiEventBus初始化失败:', error);
    }
  }

  private findComiEventBus(): Promise<any> {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 30;
      const interval = 100;

      const check = () => {
        attempts++;
        let currentWindow: any = window;

        // 向上查找comiEventBus
        while (currentWindow) {
          try {
            if (currentWindow.comiEventBus?.$on && currentWindow.comiEventBus?.$emit) {
              resolve(currentWindow.comiEventBus);
              return;
            }
            if (currentWindow === currentWindow.parent) break;
            currentWindow = currentWindow.parent;
          } catch {
            break;
          }
        }

        if (attempts >= maxAttempts) {
          resolve(null);
        } else {
          setTimeout(check, interval);
        }
      };

      check();
    });
  }

  get(): any {
    return this.eventBus;
  }

  isAvailable(): boolean {
    return !!this.eventBus;
  }

  onReady(callback: () => void): void {
    if (this.isReady) {
      callback();
    } else {
      this.readyCallbacks.push(callback);
    }
  }

  emit(type: string, data: any): boolean {
    if (this.eventBus) {
      this.eventBus.$emit(type, data);
      return true;
    }
    return false;
  }

  on(type: string, handler: (data: any) => void): void {
    if (this.eventBus) {
      this.eventBus.$on(type, handler);
    }
  }

  off(type: string, handler?: (data: any) => void): void {
    if (this.eventBus) {
      this.eventBus.$off(type, handler);
    }
  }
}

// ==================== 消息队列管理器 ====================

/** 消息队列管理 */
class MessageQueue {
  private queue: Array<{ type: string; content: any; timestamp: number }> = [];
  private maxSize = 100;

  enqueue(type: string, content: any): void {
    if (this.queue.length >= this.maxSize) {
      this.queue.shift(); // 移除最旧的消息
    }
    
    this.queue.push({
      type,
      content,
      timestamp: Date.now()
    });
  }

  dequeueAll(): Array<{ type: string; content: any; timestamp: number }> {
    const messages = [...this.queue];
    this.queue = [];
    return messages;
  }

  size(): number {
    return this.queue.length;
  }

  clear(): void {
    this.queue = [];
  }
}

// ==================== 核心MessageBus ====================

/** 核心消息总线 */
export class MessageBus {
  private readonly instanceId: string;
  private readonly transport: Transport;
  private readonly iscomiManager: ISCOMIManager;
  private readonly comiIntegrator: ComiEventBusIntegrator;
  private readonly messageQueue: MessageQueue;
  
  private listeners: Map<string, Set<(content: any) => void>> = new Map();
  private isReady = false;
  private readyPromise: Promise<void>;
  
  // 简化的系统标识管理
  private systemId: string;

  constructor(options: SDKOptions = {}) {
    this.instanceId = `sdk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.transport = new PostMessageTransport();
    this.iscomiManager = new ISCOMIManager();
    this.comiIntegrator = new ComiEventBusIntegrator();
    this.messageQueue = new MessageQueue();

    // 初始化系统标识
    this.systemId = options.systemId || this.detectSystemId();

    this.setupTransport();
    this.readyPromise = this.initialize();
  }

  private detectSystemId(): string {
    // 从 comiSystemManager 获取系统标识
    const systemManager = (window as any).comiSystemManager;
    if (systemManager) {
      return systemManager.getSystemInfo().systemId;
    }
    return 'third-party-system';
  }

  private setupTransport(): void {
    this.transport.onMessage((message, source) => {
      this.handleMessage(message, source);
    });
  }

  private async initialize(): Promise<void> {
    // 等待comiEventBus就绪
    await new Promise<void>((resolve) => {
      this.comiIntegrator.onReady(() => {
        this.setupComiEventBusBridge();
        resolve();
      });
      
      // 超时处理
      setTimeout(resolve, 5000);
    });

    // 先处理队列中的消息，然后再设置为就绪状态
    this.processQueuedMessages();
    this.isReady = true;
    console.log('MessageBus 初始化完成');
  }

  private setupComiEventBusBridge(): void {
    if (!this.comiIntegrator.isAvailable()) return;

    // 监听来自comiEventBus的消息
    this.comiIntegrator.on('dispatchSdkAction', (data: any) => {
      // 修复：正确的逻辑应该是只处理外部发送的消息（没有instanceId或instanceId不同的消息）
      // 同时确保SDK已完全就绪
      if (!this.isReady) {
        console.log('SDK未就绪，消息加入队列:', data);
        this.messageQueue.enqueue(data.type, data.content);
        return;
      }

      // 如果消息有instanceId且与当前实例相同，则忽略（避免处理自己发送的消息）
      if (data.instanceId && data.instanceId === this.instanceId) {
        console.log('忽略自己发送的消息:', data);
        return;
      }

      // 处理外部消息
      console.log('处理外部dispatchSdkAction消息:', data);
      this.handleMessage({
        type: data.type,
        content: data.content,
        instanceId: data.instanceId || 'external', // 为外部消息添加标识
        timestamp: data.timestamp || Date.now()
      }, window);
    });
  }

  private processQueuedMessages(): void {
    const messages = this.messageQueue.dequeueAll();
    console.log('处理队列中的消息:', messages.length, '条');
    messages.forEach(msg => {
      console.log('处理队列消息:', msg);
      // 直接处理消息，而不是重新发送
      this.handleMessage({
        type: msg.type,
        content: msg.content,
        instanceId: 'queued', // 标识为队列消息
        timestamp: msg.timestamp
      }, window);
    });
  }

  private handleMessage(message: Message, source: Window): void {
    // 检查系统标识（同一系统的不同层级可以通信）
    if (message.systemId && message.systemId !== this.systemId) {
      return; // 忽略不同系统的消息
    }

    const handlers = this.listeners.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message.content);
        } catch (error) {
          console.error('消息处理器执行失败:', error);
        }
      });
    }
  }

  /** 发布消息 - 统一通过eventBus发送 */
  publish(type: string, content: any): void {
    const message: Message = {
      type,
      content,
      instanceId: this.instanceId,
      timestamp: Date.now(),
      systemId: this.systemId
    };

    // 优先通过comiEventBus发送
    if (this.sendViaComiEventBus(type, content)) {
      return;
    }

    // 回退到PostMessage
    this.sendViaPostMessage(message);
  }

  private sendViaComiEventBus(type: string, content: any): boolean {
    if (!this.comiIntegrator.isAvailable()) {
      if (!this.isReady) {
        this.messageQueue.enqueue(type, content);
        return true;
      }
      return false;
    }

    return this.comiIntegrator.emit('dispatchSdkAction', {
      type,
      content,
      instanceId: this.instanceId,
      timestamp: Date.now()
    });
  }

  private sendViaPostMessage(message: Message): void {
    // 简化的PostMessage发送 - 发送到父窗口
    if (window.parent !== window) {
      this.transport.send(message, window.parent);
    }
  }

  /** 订阅消息 */
  subscribe(type: string, handler: (content: any) => void): void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    this.listeners.get(type)!.add(handler);
  }

  /** 取消订阅 */
  unsubscribe(type: string, handler: (content: any) => void): void {
    const handlers = this.listeners.get(type);
    if (handlers) {
      handlers.delete(handler);
    }
  }

  /** 等待SDK就绪 */
  async waitForReady(): Promise<void> {
    return this.readyPromise;
  }

  /** 检查是否就绪 */
  isSDKReady(): boolean {
    return this.isReady;
  }

  // ==================== ISCOMI相关 ====================

  getISCOMI(): boolean | null {
    return this.iscomiManager.get();
  }

  setISCOMI(value: boolean): void {
    this.iscomiManager.set(value);
  }

  getNestingDescription(): string {
    return this.iscomiManager.getDescription();
  }

  // ==================== comiEventBus相关 ====================

  getComiEventBus(): any {
    return this.comiIntegrator.get();
  }

  hasComiEventBusIntegration(): boolean {
    return this.comiIntegrator.isAvailable();
  }

  onComiEventBusEvent(eventType: string, handler: (content: any) => void): void {
    this.comiIntegrator.on(eventType, handler);
  }

  offComiEventBusEvent(eventType: string, handler?: (content: any) => void): void {
    this.comiIntegrator.off(eventType, handler);
  }

  // ==================== 实用方法 ====================

  getInstanceId(): string {
    return this.instanceId;
  }

  getQueueStatus(): { length: number; maxSize: number; isReady: boolean } {
    return {
      length: this.messageQueue.size(),
      maxSize: 100,
      isReady: this.isReady
    };
  }

  getSystemId(): string {
    return this.systemId;
  }
}

// ==================== 类型声明 ====================

/** 扩展全局Window接口，支持COMI_ASSISTANT */
declare global {
  interface Window {
    COMI_ASSISTANT?: any;
    ISCOMI?: boolean;
    comiEventBus?: any;
  }
}
