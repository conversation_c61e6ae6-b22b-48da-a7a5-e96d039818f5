<template>
  <div class="portal-silder-wrapper">
    <Carousel>
      <div v-for="(component, index) in extractedComponents" :key="index">
        <component 
          :is="getComponentByName(component.componentName)" 
          v-bind="component.props"
          v-if="getComponentByName(component.componentName)"
        />
        <div v-else class="component-error">
          未找到组件: {{ component.componentName }}
        </div>
      </div>
      <!-- 如果没有提取到组件，显示默认内容 -->
      <div v-if="extractedComponents.length === 0">
        <div class="no-components">暂无组件数据</div>
      </div>
    </Carousel>
  </div>
</template>

<script setup lang="ts">
  import { Carousel } from 'ant-design-vue';
  import { defineAsyncComponent, computed, ref, onMounted } from 'vue';
  import { extractComponentsIterative } from '@/utils/common';
  import { buildUUID } from '@/utils/uuid';
  import { fetchAndFillPortalData } from '@/api/portal/portalDataHandler';


  const componentMap = {
    WorkInstructionCardColumn: defineAsyncComponent(() => import('@/components/workInstructionCardColumn/index.vue')),
    TodoCardColumn: defineAsyncComponent(() => import('@/components/todoCardColumn/index.vue')),
    MeetingCardColumn: defineAsyncComponent(() => import('@/components/meetingCardColumn/index.vue')),
    ComiExternalCardIframe: defineAsyncComponent(() => import('remote_app/ComiExternalCardIframe')),
    BapExternalBiCard: defineAsyncComponent(() => import('remote_app/BapExternalBiCard')),
  };


  let chatSessionId = buildUUID();
  const empty = ref(true);
  const layoutConfig = ref({});


  const comiMenuInfoHandler = async () => {
    const result = await fetchAndFillPortalData(chatSessionId, (index, summaryText) => {
      const node = layoutConfig.value as any;
      if (
        node?.children?.[1]?.children?.[0]?.children?.[0]?.children?.[0].props?.data?.dataList?.[
          index
        ]
      ) {
        node.children[1].children[0].children[0].children[0].props.data.dataList[index].summaryText =
          summaryText;
        node.children[1].children[0].children[0].children[0].props.data.dataList[
          index
        ].loaddingSummary = false;
      }
    });
    
    if (result && JSON.stringify(result.data) !== '{}') {
      empty.value = false;
      layoutConfig.value = result.data;
    } else {
      empty.value = true;
    }
  };

  // 提取所有component节点
  const extractedComponents = computed(() => {
    if (!layoutConfig.value) {
      return [];
    }
    
    const components = extractComponentsIterative(layoutConfig.value);
    console.log('提取到的组件:', components);
    return components;
  });
  
  // 根据组件名获取对应的组件
  const getComponentByName = (componentName: string) => {
    return componentMap[componentName as keyof typeof componentMap];
  };

  console.log(layoutConfig.value, 'layoutConfig');

  onMounted(()=>{
    comiMenuInfoHandler();
  });
</script>

<style scoped lang="less">
  .portal-silder-wrapper {
    padding-bottom: 32px;
    :deep(.slick-dots-bottom){
      bottom: -32px;
      margin-bottom: 0;
      height: 24px;
      li{
        width: 8px;
        height: 8px;
        border-radius: 4px;
        button{
          width: 8px;
          height: 8px;
          border-radius: 4px;
          opacity: 1;
          background: #D8DADF;
        }
      }
      li.slick-active{
        width: 16px;
        button{
          width: 16px;
          opacity: 1;
          background: #6394FF;
        }
      }
    }
  }

  .component-error {
    padding: 20px;
    text-align: center;
    color: #ff4d4f;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 4px;
  }

  .no-components {
    padding: 20px;
    text-align: center;
    color: #999;
  }
</style>
