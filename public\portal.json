{"code": 0, "data": {"type": "row", "gutter": 16, "refreshConfig": {"refreshTimeInterval": 60}, "children": [{"type": "col", "span": 16, "style": {"width": "560px"}, "id": "col-left", "children": [{"type": "component", "componentName": "TodoCardColumn", "props": {}}]}, {"type": "col", "span": 8, "style": {"width": "248px"}, "gutter": 14, "children": [{"type": "row", "gutter": 14, "children": [{"type": "col", "span": 24, "children": [{"type": "component", "componentName": "MeetingCardColumn", "props": {}}]}]}, {"type": "row", "gutter": 14, "children": [{"type": "col", "span": 24, "children": [{"type": "component", "componentName": "WorkInstructionCardColumn", "props": {"config": {"title": "工作指引"}, "data": {"dataList": [{"title": "如何走出差流程？", "workInstructionType": "1"}, {"title": "费用报销流程是什么？", "workInstructionType": "2"}, {"title": "公司请假流程是什么？", "workInstructionType": "3"}]}}}]}]}]}]}}