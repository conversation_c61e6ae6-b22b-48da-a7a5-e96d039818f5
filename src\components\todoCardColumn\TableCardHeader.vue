<template>
  <header class="table-card-header mb-[16px]">
    <!-- 左侧：待办标题和控制箭头 -->
    <div class="header-left">
      <div><i class="iconfont ai-icon-a-Frame1874"></i></div>
      <div class="table-card-title">
        {{ config.title }}
        <span
          class="pending-count"
        >·{{ formatDisplayCount(totalPendingCount, threshold) }}</span>
      </div>
      <div
        class="category-toggle-btn"
        @click="$emit('toggle-category')"
        :class="{ 'expanded': showCategories }"
      >
        <i class="iconfont ai-icon-xia"></i>
      </div>
    </div>

    <!-- 右侧：CoMi下拉菜单和更多按钮 -->
    <div class="header-right">
      <!-- <ADropdown
        trigger="hover"
        placement="bottomLeft"
        @visibleChange="handleComiDropdownVisibleChange"
      >
        <div
          class="comi-dropdown-trigger"
          :class="{ 'dropdown-open': comiDropdownVisible }"
        >
          <img
            :src="PendingStarImg"
            class="menu-icon"
            alt="智能预审"
          />
          <span class="comi-text">CoMi</span>
        </div>
        <template #overlay>
          <ul class="comi-dropdown-menu">
            <li
              class="dropdown-item"
              @click="handleDropdownClick('intelligent-review')"
            >
              <img
                :src="PendingItemStarImg"
                class="menu-icon"
                alt="智能预审"
              />
              <span>智能预审</span>
            </li>
            <li
              class="dropdown-item"
              @click="handleDropdownClick('info-extract')"
            >
              <img
                :src="PendingItemStarImg"
                class="menu-icon"
                alt="信息提取"
              />
              <span>信息提取</span>
            </li>
          </ul>
        </template>
      </ADropdown> -->

      <Tooltip title="查看更多">
        <div
          class="more-btn"
          @click="$emit('more')"
        >
          <i class="iconfont ai-icon-you"></i>
        </div>
      </Tooltip>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Dropdown as ADropdown, Tooltip } from 'ant-design-vue';
import PendingItemStarImg from '@/assets/imgs/pending-star.png';
import PendingStarImg from '@/assets/imgs/pending-star.png';

// 组件配置接口
interface TodoCardConfig {
  title: string;
  maxItems: number;
  desc?: string;
  icon?: string;
}

interface Props {
  config?: TodoCardConfig;
  totalPendingCount?: number;
  showCategories?: boolean;
  threshold?: number;
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({
    title: '待办中心',
    maxItems: 5,
    desc: '已按照智能算法规则为您筛选优先待办',
    icon: 'ai-icon-shijian'
  }),
  totalPendingCount: 0,
  showCategories: false,
  threshold: undefined
});

const emit = defineEmits<{
  'toggle-category': [];
  'dropdown-click': [key: string];
  'more': [];
  'dropdown-visible-change': [visible: boolean];
}>();

// 响应式状态
const comiDropdownVisible = ref(false);

// 格式化显示数量
const formatDisplayCount = (total: number, threshold?: number): string => {
  if (threshold && total > threshold) {
    return `${threshold}+`;
  }
  return total.toString();
};

/**
 * 处理CoMi下拉菜单可见性变化
 */
const handleComiDropdownVisibleChange = (visible: boolean) => {
  comiDropdownVisible.value = visible;
  emit('dropdown-visible-change', visible);
};

/**
 * 处理下拉菜单项点击
 */
const handleDropdownClick = (key: string) => {
  emit('dropdown-click', key);
};
</script>

<style scoped lang="less">
// 头部样式
.table-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 24px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;

    .table-card-title {
      font-size: 16px;
      font-weight: @font-weight-500;
      color: #000;
      line-height: 24px;

      .pending-count {
        font-weight: @font-weight-500;
      }
    }

    .iconfont {
      font-size: 13px;
      color: #C0C5CC;
    }

    .category-toggle-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      height: 22px;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.2s ease;

      .iconfont {
        font-size: 14px;
        color: rgba(182, 186, 195, 1);
        transition: transform 0.2s ease;
      }

      &:hover {
        background: rgba(240, 240, 240, 1);

        .iconfont {
          color: rgba(111, 118, 134, 1);
        }
      }

      &.expanded .iconfont {
        transform: rotate(180deg);
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;

    .comi-dropdown-trigger {
      display: flex;
      align-items: center;
      gap: 4px;
      line-height: 22px;
      height: 22px;
      padding: 0 8px;
      border-radius: 4px;
      font-weight: @font-weight-500;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #fff;
      }

      .comi-text {
        font-size: 14px;
        width: fit-content;
        background: linear-gradient(117.86deg, #4AEAFF -10.62%, #97F0FA -0.52%, #4FE1F7 11.26%, #566BFF 49.51%, #4379FF 80.57%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-family: PingFang SC;
      }

      .menu-icon {
        width: 16px;
      }
    }

    .more-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      height: 22px;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.2s ease;

      .iconfont {
        font-size: 14px;
        color: rgba(182, 186, 195, 1);
      }

      &:hover {
        background: rgba(237, 237, 241, 1);

        .iconfont {
          color: rgba(111, 118, 134, 1);
        }
      }
    }
  }
}
</style>

<style lang="less">
// CoMi 下拉菜单样式 - 全局样式（无scoped）
.comi-dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 140px;
  padding: 16px 16px 0;
  margin: 0;
  list-style: none;

  .dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 4px;
    line-height: 30px;
    height: 30px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: @font-weight-500;
    position: relative;
    margin-bottom: 16px;

    &:hover {
      background: #EDF2FC;
    }

    .menu-icon {
      width: 16px;
      height: 16px;
      opacity: 0.8;
    }

    span {
      flex: 1;
      font-weight: @font-weight-500;
      width: fit-content;
      background: linear-gradient(90deg, #AB7DFE 0%, #5873F6 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      font-family: PingFang SC;
    }

    // 添加星光效果
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 12px;
      width: 4px;
      height: 4px;
      background: linear-gradient(45deg, #AB7DFE, #5873F6);
      border-radius: 50%;
      opacity: 0;
      transform: translateY(-50%) scale(0);
      transition: all 0.3s ease;
    }

    &:hover::before {
      opacity: 0.6;
      transform: translateY(-50%) scale(1);
      animation: sparkle 1.5s ease-in-out infinite;
    }
  }
}

// 星光闪烁动画
@keyframes sparkle {
  0%, 100% {
    opacity: 0.6;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.2);
  }
}
</style>
