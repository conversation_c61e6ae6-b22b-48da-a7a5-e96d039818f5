(function () {
  // 设置容器的名称
  window.localStorage.removeItem('comiContainerName');
  window.localStorage.setItem('comiContainerName','v5');
  // 设置嵌套关系标识
  if (typeof window.ISCOMI === 'undefined') {
    // 判断当前窗口的嵌套层级
    let currentWindow = window;
    let nestingLevel = 0;

    while (currentWindow !== currentWindow.top) {
      nestingLevel++;
      try {
        currentWindow = currentWindow.parent;
      } catch (e) {
        // 跨域访问限制时停止
        break;
      }
    }

    // 如果是第一次加载（nestingLevel === 1），设置为 false
    // 如果是第二次加载（nestingLevel === 2），设置为 true
    window.ISCOMI = nestingLevel === 2;
    console.log('ISCOMI set to', window.ISCOMI, ': 嵌套层级为', nestingLevel);
  }

  /*** 简单事件车开始* */
  function isFunc(fn) { return typeof fn === "function" }
  function str(s) {
    if (!s) {
      return '';
    }
    s = s.replace(/^\s+|\s+$/g, "");
    return s.length > 0 ? s.toLowerCase() : '';
  }
  function handler() {
    var fns = [];
    this.fns = fns;
    this.add = function (fn) {
      fns.push(fn);
    }
    this.remove = function (fn) {
      var i = fns.indexOf(fn);
      if (i >= 0) {
        fns.splice(i, 1);
      }
    }
    this.invoke = function (args) {
      fns.forEach(function (fn) {
        try {
          fn.apply(this, args)
        } catch (error) {
          console.error(error);
        }
      });
    }
  }

  function eventBus() {
    var handers = {};
    // 历史事件
    var hisEvent = {};
    this.handers = handers;
    this.hisEvent = hisEvent;
    // 监听事件
    this.$on = function (eventName, fn) {
      eventName = str(eventName);
      if (!eventName) {
        throw new Error("事件名无效");
      }
      if (!isFunc(fn)) {
        throw new Error("必须提供事件函数");
      }
      var handle = handers[eventName];
      if (!handle) {
        handle = new handler();
        handers[eventName] = handle;
      }
      handle.add(fn);
      // 如果有历史事件发生。则马上触发。
      if (hisEvent[eventName]) {
        fn.apply(this, hisEvent[eventName])
      }
    }
    this.$off = function (eventName, fn) {
      eventName = str(eventName);
      if (!eventName) {
        return;
      }
      var handle = handers[eventName];
      if (handle) {
        if (!fn) {
          delete handers[eventName];
          // 销毁历史事件。
          delete hisEvent[eventName]
        } else {
          handle.remove(fn);
        }
      }
    }
    this.$emit = function () {
      var eventName = arguments[0];
      var args = Array.prototype.slice.apply(arguments).slice(1);
      eventName = str(eventName);
      if (!eventName) {
        return;
      }
      var handle = handers[eventName];
      // 发生历史事件
      hisEvent[eventName] = args;
      if (handle) {
        handle.invoke(args);
      }
    }
    var bus = this;
    this.bindTo = function (obj) {
      if (obj == null) {
        throw new Error("obj is null");
      }
      for (var key in bus) {
        if (bus.hasOwnProperty(key) && key !== "bindTo") {
          obj[key] = bus[key];
        }
      }
    }
  }

  // 递归查找comiEventBus，支持多层iframe嵌套
  function findOrCreateComiEventBus() {
    var currentWindow = window;
    var foundEventBus = null;

    // 向上递归查找comiEventBus
    while (currentWindow) {
      try {
        if (currentWindow.comiEventBus) {
          foundEventBus = currentWindow.comiEventBus;
          console.log('找到已存在的comiEventBus，位于:', currentWindow === window ? 'current window' : 'parent window');
          break;
        }

        // 到达顶层窗口
        if (currentWindow === currentWindow.parent || currentWindow === currentWindow.top) {
          break;
        }

        currentWindow = currentWindow.parent;
      } catch (e) {
        // 跨域访问限制时停止查找
        console.log('跨域访问限制，停止comiEventBus查找');
        break;
      }
    }

    // 如果没找到，创建新的eventBus
    if (!foundEventBus) {
      foundEventBus = new eventBus();
      console.log('未找到已存在的comiEventBus，创建新的实例');
    }

    return foundEventBus;
  }

  // comiEventBus无论ISCOMI如何都需要初始化，作为通信桥梁
  window.comiEventBus = findOrCreateComiEventBus();
  /*** 简单事件车结束* */

  // 如果ISCOMI为true，只初始化comiEventBus，不加载UI和业务逻辑
  if (window.ISCOMI === true) {
    console.log('ISCOMI is true: comi嵌入第三方系统，只初始化comiEventBus，跳过UI初始化');
    return;
  }
  //以前的代码放入闭包
  var suffix = "";
  if (window.vPortal) {
    suffix = window.vPortal.resSuffix;
  }
  if (window._staticSuffix) {
    suffix = window._staticSuffix;
  }
  var _comiAjax = {
    get: function get(url) {
      return new Promise(function (resolve, reject) {
        var xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);

        xhr.onreadystatechange = function () {
          if (xhr.readyState == 4) {
            if (xhr.status == 200 || xhr.status == 304) {
              try {
                var res = xhr.responseText ? JSON.parse(xhr.responseText) : {};
                resolve(res);
              } catch (e) {
                reject(e);
              }
            } else {
              reject(new Error('Request failed with status ' + xhr.status));
            }
          }
        };

        xhr.send();
      });
    },
    post: function post(url, data) {
      return new Promise(function (resolve, reject) {
        if (!data) return reject(new Error('No data provided'));

        if (typeof (data) == 'object') {
          data = JSON.stringify(data);
        }

        var xhr = new XMLHttpRequest();
        xhr.open("POST", url, true);

        xhr.setRequestHeader("Content-Type", "application/json;charset=utf-8");

        xhr.onreadystatechange = function () {
          if (xhr.readyState == 4) {
            if (xhr.status == 200 || xhr.status == 304) {
              try {
                var res = xhr.responseText ? JSON.parse(xhr.responseText) : {};
                resolve(res);
              } catch (e) {
                reject(e);
              }
            } else {
              reject(new Error('Request failed with status ' + xhr.status));
            }
          }
        };

        xhr.send(data);
      });
    }
  };
  window._comiEntryAjax = _comiAjax;
  function getServiceStatus() {
    return _comiAjax.get(_ctxPath + `/ai-platform/ai-manager/common/status`);
  }
  function comiLoadScript(scriptName, src, callback) {
    var url = (parent._ctxPath || '/seeyon') + src + suffix;
    if (scriptName === 'comiEntryScript') {
      var comiEntryScript = document.createElement('script');
      comiEntryScript.src = url;
      comiEntryScript.onload = callback;
      document.head.appendChild(comiEntryScript);
    }
  }

  var appendScripts = {
    comiEntryScript: '/apps_res/aiAssistantEntry/js/comi-entry-source.js',
  }
  var isComiPortal = window.top.location.href.indexOf('apps_res/comi/html/comi-portal.html') > -1;
  try {
    var scripts = window.top.document.getElementsByTagName('script');
    var comiFound = false;
    for (var i = 0; i < scripts.length; i++) {
      if (scripts[i].src && scripts[i].src.indexOf('/apps_res/aiAssistantEntry/js/comi-entry-source.js') !== -1) {
        comiFound = true;
        break;
      }
    }
    // ISCOMI已在文件开头检查，这里不需要重复判断

    if (!comiFound && !isComiPortal) {

      getServiceStatus().then(function (data) {
        if (Number(data.code) === 0) {
          window.comiEventBus.$emit('ready');
          comiLoadScript('comiEntryScript', appendScripts.comiEntryScript, function () {
            console.log('=====> comi-entry-loaded');
            window.comiEventBus.$emit('sourceReady');
          });
        }
      })
      // });
      // });

    }
  } catch (error) {
    console.log('=====> comi-entry-error', error);
  }
})();