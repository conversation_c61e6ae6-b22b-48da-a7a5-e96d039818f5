<template>
  <div class="comi-setting">
    <div v-if="isFullScreen" class="help" @click="openSetting">
      <i class="iconfont ai-icon-bangzhu" />
      <span class="help-text">使用指南</span>
    </div>
    <div v-else class="help" @click="openSetting">
      <i class="iconfont ai-icon-shezhi" />
    </div>

    <!-- 设置弹窗 -->
    <Modal
      v-if="setVisible"
      wrapClassName="setting-wrap"
      v-model:open="setVisible"
      title="CoMi设置"
      :footer="null"
      :getPopupContainer="getPopupContainer"
      centered
      :width="370"
      :maskStyle="{
        background: 'rgba(255, 255, 255, 0.5)',
        'backdrop-filter': 'blur(4px)',
      }"
    >
      <template #closeIcon>
        <span class="custom-close-icon">
          <i class="iconfont ai-icon-cha" />
        </span>
      </template>
      <ComiSettingContent />
    </Modal>
  </div>
</template>
<script lang="ts" setup>
import { useGlobal, useMenu } from '@/stores/global';
import { Modal } from 'ant-design-vue';
import ComiSettingContent from './content.vue';
import { useStateRouter } from '@/stores/stateRouter';
import { commonBusinessClass } from '@/utils/commonBusinessClass';

const uGlobal = useGlobal();
const stateRouter = useStateRouter();
const getPopupContainer = () => document.body;

const isFullScreen = computed(() => {
  return uGlobal.globalState.isFullScreen;
});

const setVisible = ref(false);

const openSetting = () => {
  uGlobal.changeState('isFullScreen', true);
  const businessClass = commonBusinessClass();
    if (businessClass && typeof businessClass.expand === 'function') {
      businessClass.expand();
    }
  useMenu().changeMenu({
    id: 'setting',
  });
  // setVisible.value = true;
};
</script>
<style lang="less" scoped>
.comi-setting {
  .help {
    width: 52px;
    height: 52px;
    margin: 0 8px;
    line-height: 52px;
    text-align: center;
    .iconfont {
      cursor: pointer;
      color: #6F7686;
      font-size: 20px;
    }
    &:hover .iconfont {
      color: rgba(67, 121, 255, 1);
    }
  }
}
</style>
