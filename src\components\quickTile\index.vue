<template>
  <div class="quick-tile" ref="quickTile" :class="quickTileCss">
    <div class="header">
      <div class="title">快捷磁贴</div>
      <i class="iconfont ai-icon-cha close-icon" @click="closeTile"></i>
    </div>
    <PortalEmptyColumn v-if="isEmpty" :image="emptyImage" :text="emptyText" :width="85" class="w-full flex-1"/>
    <div v-else class="content">
      <div class="template-item" v-for="shortcut in shortcutArr" :key="shortcut.id" @click="handleShortcut(shortcut)">
        <div class="text" :title="displayName(shortcut)">{{displayName(shortcut)}}</div>
        <span v-if="numberObject[shortcut.id]" class="number">
          {{ numberObject[shortcut.id] > 99 ? '99+' : numberObject[shortcut.id] }}
        </span>
      </div>
    </div>

  </div>
</template>

<script lang="ts" setup>
import requests from '@/api/v5/index';
import { onMounted } from 'vue';
import emptyImage from '@/assets/imgs/no-data.png';
import PortalEmptyColumn from '@/components/portalEmptyColumn/index.vue';

const _$emit = defineEmits(['closeTile', 'handleShortcut'])
const shortcutArr = ref<any[]>([]);
const dataType = ref('');
const tipNumberKey = '';
const numberObject = ref({});
const quickTile = ref(null);
const quickTileCss = ref('');
const requestEnd = ref(false);
const emptyText = ref('暂无数据哦～');
const isEmpty = computed(() => {
  return shortcutArr.value.length === 0 && requestEnd.value;
});
onMounted(()=>{
  if(quickTile.value?.offsetWidth < 422) {
    quickTileCss.value = 'small-tile';
  }
})
initShortcut();
function displayName(shortcut) {
  return shortcut.displayName || shortcut.name || shortcut.portletName;
}
function initShortcut() {
  requests.getShortcut().then((res: any) => {
    if(Number(res.code) === 0 && res.data) {
      const { data } = res;
      dataType.value = data.type;
      if(data.type === 'shortcut') {
        const shortcutData = data.data;
        shortcutData.forEach((item: any) => {
          shortcutArr.value.push(...item.portlets);
        });
        getTipNumber();
      }else if(data.type === 'menu') {
        shortcutArr.value = data.data;
      }
    }
    requestEnd.value = true;
  }, (err)=>{
    requestEnd.value = true;
  });

}

function handleShortcut(shortcut: any) {
  if(shortcut.id === 'more') {
    return;
  }
  shortcut.tileType = dataType.value;
  _$emit('handleShortcut', shortcut);
  closeTile();
}

function getTipNumber() {
  const tipNumberKey = shortcutArr.value.reduce((cur, next)=>{
    if(cur && next.needNumber > 0) {
      cur += ',' + next.id;
    }else if(next.needNumber > 0){
      cur = next.id;
    }
    return cur;
  }, '');
  if(!tipNumberKey) {
    return;
  }

  requests.getTipNumber(encodeURIComponent(tipNumberKey) ).then(res=>{
    if(!res) return;
    const domParser = new DOMParser();
    const xmlDocument = domParser.parseFromString(res,"text/xml");
    const xmlElement = xmlDocument.documentElement;
    const realValue = xmlElement?.firstChild?.nodeValue || '{}';
    const result = JSON.parse(realValue);
    numberObject.value = result;

  })
}
function closeTile() {
  _$emit('closeTile', false);
}
</script>

<style lang="less" scoped>
.quick-tile {
  position: absolute;
  bottom: calc(100% + 10px);
  left: 0;
  height: auto;
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.60);
  box-shadow: 0px 0px 48px 0px rgba(8, 62, 221, 0.12);
  border-radius: 10px;
  padding: 20px 0;
  backdrop-filter: blur(28px);
  z-index: 999;
  .header {
    display: flex;
    align-items: center;
    padding: 0 15px;
  }
  .title {
    font-weight: @font-weight-500;
    font-size: 16px;
    height: 24px;
    line-height: 24px;
    color: #000;
    flex: 1;
  }
  .close-icon {
    cursor: pointer;
    font-size: 20px;
    color: #4A4E5A;
  }
  .content {
    overflow-y: auto;
    max-height: 432px;
    display: flex;
    flex-wrap: wrap;
    padding-top: 12px;
    gap: 12px;
    padding: 12px 15px 0;
    // margin-top: 16px;
  }
  .template-item {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(30px);
    font-size: 14px;
    width: 122px;
    padding: 7px 16px;
    border-radius: 10px;

    cursor: pointer;
    text-align: center;
    // margin-top: 16px;
    border-radius: 12px;
    border: 1px solid #D1E0FF;
    background: #FFF;
    backdrop-filter: blur(15px);
    color: rgba(0, 0, 0, 0.9);
    position: relative;
    &:hover {
      background: #F6F6F8;
      color: rgba(0, 0, 0, 1);
    }
    .text {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 24px;
    }
    .number {
      position: absolute;
      background: #FF4F4B;
      color: #fff;
      font-weight: @font-weight-500;
      font-size: 10px;
      padding: 2px 3px;
      border-radius: 24px;
      min-width: 16px;
      height: 16px;
      right: -9px;
      top: -6px;
      text-align: center;
    }
  }
}
.small-tile {
  .template-item {
    width: 100px;
  }
}
</style>
