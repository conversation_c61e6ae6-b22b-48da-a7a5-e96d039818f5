<template>
  <i
    :class="['doc-iconfont', iconClass]"
    :style="{
      fontSize: `${size}px`,
    }"
  ></i>
</template>
<script lang="ts" setup>
const props = defineProps({
  iconClass: {
    type: String,
    default: '',
  },
  size: {
    type: Number,
    default: 16,
  },
});
</script>
<style scoped lang="less">
// .xls-color {
//   color: #00a65a;
// }
.doc-color {
  color: #297ffb;
}
.pdf-color {
  color: #ff4141;
}
.txt-color {
  color: #586a95;
}
.markdown-color {
  color: #4a4e5a;
}
.xls-color {
  color: #61b109;
}
.docx-color {
  color: #4379ff;
}
.csv-color {
  color: #3da6ff;
}
</style>
