import StreamManager from '@/api/stream/manager';
import { aiToast } from '../plugins/ai-toast';
import { buildUUID } from '@/utils/uuid';
import { useStreamData, useChatList, useCardData } from '@/stores/chatList';
import type { AiRequestCard, AiDataCard, FileType } from '@/types/api';
import { getAssistantId } from '@/utils/common';
import { cancelChat, getSessionId } from '@/api/common';


let useChatListObj: ReturnType<typeof useChatList> | null = null
let useCardDataObj: ReturnType<typeof useCardData> | null = null
let useStreamDataAction: any = null
function getStores() {
  if (!useChatListObj) useChatListObj = useChatList()
  if (!useCardDataObj) useCardDataObj = useCardData()
  if (!useStreamDataAction) useStreamDataAction = useStreamData().streamActions
  return { useChatListObj, useCardDataObj, useStreamDataAction }
}

// 停止上一个未完成的对话
function stopLastCard() {
  const { useChatListObj } = getStores();
  const lastCard = useChatListObj.dynamicData.allCardData[useChatListObj.dynamicData.allCardData.length - 1];
  if (lastCard && lastCard.componentKey === 'AiDataCard' && lastCard.data.status < 3) {
    // 停止上一个对话
    if (lastCard.data.messageId) {
      stopRequest(String(lastCard.data.messageId));
    }
    // 使用chatActions更新数据
    const updatedCardData = lastCard.data.cardData?.length ?
      lastCard.data.cardData.map((item: any) => ({
        ...item,
        finish: 1  // 停止时将所有数据项的finish设置为1
      })) : [];

    useChatListObj.chatActions.setDynamicData('allCardData',
      useChatListObj.dynamicData.allCardData.map((card: any) =>
        card._id === lastCard._id ? {
          ...card,
          data: {
            ...card.data,
            status: 5,
            isStop: true,
            cardData: updatedCardData
          }
        } : card
      )
    );
    useChatListObj.scrollToBottom();
  }
}
/**
 * 消息主入口
 * @param {string} message
 * @returns
 */
async function sendMessage(message: string, citations?: Array<FileType>, isHide?: boolean, options?: any) {
  interruptAction();
  const { useChatListObj, useCardDataObj, useStreamDataAction } = getStores()
  // const assistantCode = options?.assistantCode;
  if (!message) {
    return;
  }
  // 解除滚动锁止
  useChatListObj.chatActions.setDynamicData('lockScroll', false);
  useChatListObj.chatActions.setDynamicData('isAnswering', true);
  // 停止上一个未完成的对话
  stopLastCard();


  const citationsParams = citations || [];
  useChatListObj.staticData.streamParams.citations = citationsParams;
  initState();
  const aiRequestCard = createRequestCard(message, citationsParams, (isHide || false));
  useChatListObj.scrollToBottom();
  const params = initParams(message, isHide);
  // 重写code
  // if(assistantCode){
  //   params.assistantCode = assistantCode;
  // }
  let alreadyOnExecute = false;
  const aiDataCard: AiDataCard = useCardDataObj.cardActions.add({
    _index: new Date().getTime(),
    componentKey: 'AiDataCard',
    data: {
      cardData: [],
      status: 0,
      isHide: isHide || false,
      processData: [
        {
          expand: true,
          type: 'think',
          loading: true,
          title: '意图识别中',
          content: '',
        }
      ]
    },
    staticData: {
      requestParams: params,
    }
  }) as AiDataCard;
  useChatListObj.scrollToBottom();

  const url = '/seeyon/ai-platform/ai-manager/assistant/info/call/stream';
  const stream = StreamManager.create(aiRequestCard);
  let messageId = '';
  let illageSessionType: string | number;
  const currentAgentInfo = useChatListObj.dynamicData.selectedAgentInfo || useChatListObj.dynamicData.assistantInfo;
  // 是否对话过
  useChatListObj.chatActions.setDynamicData('isDialogued', true);
  /**
   * 此处直接push一个AiDataCard，状态由AiDataCard的data.status 参数控制
   * status 参数说明：
   * 0: 意图识别中
   * 1: Agent执行中
   * 2: Agent回答中
   * 3: 回答完毕
   * 4: 取消执行中
   * 5: 已停止回答
   * 6: 网络超时，请检查网络设置
   * 7: comi不小心发生错误啦，请稍后重试
   **/
  stream.run({
    url,
    params,
    agentInfo: currentAgentInfo,
    init: () => initializeStream(aiRequestCard, aiDataCard),
    eventHandel: (type: string, data: any) => {
      if (type === 'connecting') {
        if (data.data) {
          aiRequestCard.data.isLoading = false;
          alreadyOnExecute = false;
          aiDataCard.data.aiSessionId = data.data.aiSessionId;
          aiDataCard.data.processData = [
            {
              expand: true,
              type: 'think',
              loading: false,
              title: '已完成意图分析',
              content: data.content || '',
            },
            {
              expand: true,
              type: 'exec',
              loading: true,
              title: 'Agent执行中',
              content: []
            }
          ];
          aiDataCard.data.status = 1;
        } else {
          aiRequestCard.data.isLoading = false;
          aiRequestCard.data.isError = true;
          // 要重试Stream
          // StreamManager.bind(aiRequestCard, stream);
        }
      }
      // 没有错误才继续执行
      if (!aiRequestCard.data.isError) {
        if (type === 'onInit') {
          // 缓存aiSessionId, 用于连续对话使用，新开会话的时候清除
          if (data?.aiSessionId && !useChatListObj.dynamicData.historyModel) {
            useChatListObj.chatActions.setDynamicData('historyModel', false);
            useChatListObj.chatActions.setDynamicData('sesssionId', data.aiSessionId);
          }
        }
        if (type === 'onExecute') {
          const executeDataIndx = aiDataCard.data.processData?.findIndex((item: any) => item.type === 'exec');
          if (executeDataIndx !== -1) {
            const executeData = aiDataCard.data.processData[executeDataIndx];
            executeData.content.push({
              id: data.runStepId,
              complete: data.runStepStatus === 'completed' ? true : false,
              loading: false,
              title: data.stepName,
              content: data.content || ''
            })
            executeData.title = '根据您的问题，正在为您执行Agent'
            aiDataCard.data.processData[executeDataIndx] = executeData;
          }
          if (!alreadyOnExecute) {
            // aiLoadingCard.data.content = 'Agent执行中';
            aiDataCard.data.status = 1;
          }
        }
        if (type === 'onmessage') {
          dealProcessData(aiDataCard, false, true);
          aiDataCard.data.status = 2;
          const result = data;
          // 替换loaidng卡片
          // if (!aiDataCard) {
          //   aiDataCard = useCardDataObj.cardActions.add({
          //     _index: new Date().getTime(),
          //     componentKey: 'AiDataCard',
          //     data: {
          //       cardData: []
          //     },
          //     staticData: {
          //       requestParams: params
          //     }
          //   }) as AiDataCard;

          //   StreamManager.bind(aiDataCard, stream);
          // }
          if (messageId && !aiDataCard.data.messageId) {
            aiDataCard.data.messageId = messageId;
          }
          aiDataCard.data.cardData = result;
        }
        if (type === 'onclose') {
          aiDataCard.data.status = 3;
          useStreamDataAction.setStreamIsLoading(false);
        }
        if (type === 'onmessageId') {
          messageId = data;
        }
        if (type === 'onquestion') {
          aiDataCard.data.recommandQuestion = data;
        }
        if (type === 'onerror') {
          dealProcessData(aiDataCard, true, false);
          if (!navigator.onLine) {
            aiToast({
              content: '网络连接失败，请检查网络设置'
            });
            aiDataCard.data.status = 6;
          } else {
            dealStopCard(aiDataCard);
            aiToast({
              content: data.data?.message || 'comi不小心发生错误啦，请稍后重试',
            });
          }
          useChatListObj.chatActions.setDynamicData('isAnswering', false);
          const lastCard = useChatListObj.dynamicData.allCardData[useChatListObj.dynamicData.allCardData.length - 1];
          if (lastCard.componentKey === 'AiDataCard') {
            lastCard.data.status = 7;
          }
          // 初始化加载，最后一张卡片是消息卡片，添加重试
          // const lastCard = useChatListObj.dynamicData.allCardData[useChatListObj.dynamicData.allCardData.length - 1];
          // if (lastCard.componentKey === 'AiRequestCard') {
          //   aiRequestCard.data.isError = true;
          //   aiRequestCard.data.isLoading = false;
          //   StreamManager.bind(aiRequestCard, stream);
          //   // store.action.saveCardToHistory(aiRequestCard);
          // } else {

          //   if(navigator.onLine) {
          //   }
          //   // store.action.saveCardToHistory(aiDataCard);
          // }
        }
        //非法和敏感词状态
        if (type === 'illegal') {
          illageSessionType = data;
        }
        useChatListObj.scrollToBottom();
      }
    },
    stop: (isNormal: boolean) => {
      dealProcessData(aiDataCard, false, isNormal);
      useChatListObj.chatActions.setDynamicData('isAnswering', false);
      // requests.logRecord({
      //   info: "isNormal:" + isNormal + ", aiDataCard: " + JSON.stringify(aiDataCard),
      //   type: 'info'
      // });
      //看结束的时候有没有sessionId ，没有的话去后端拿一个
      // const sessionId = sessionStorage.getItem(`aiSessionId_${stream.agentInfo.id}`);
      const sessionId = useChatListObj.dynamicData.sesssionId;
      if (!sessionId) {
        const params = {
          chatSessionId: stream.option.params.chatSessionId,
          sessionId: '',
          assistantId: stream.agentInfo.id,
        }
        getSessionId(params).then((data: any) => {
          if (Number(data.code) === 0) {
            const sessionId = data.data.sessionId;
            // sessionStorage.setItem(`aiSessionId_${params.assistantId}`, sessionId);
            useChatListObj.chatActions.setDynamicData('sesssionId', sessionId);
          }
        })
      }
      // 最后的兜底
      const lastAnswer = {
        finish: 1,
        index: 0,
        context: '对不起，CoMi暂时无法回答您的问题，我会努力学习的。'
      };
      // if (!isNormal) {
      //   dealStopCard(aiDataCard)
      // } else if (isNormal && aiDataCard) {
      //   aiDataCard.data.illageSessionType = illageSessionType;
      //   aiDataCard.data.isCompleted = true;
      //   //如果没有返回数据，并且有执行过程
      //   if (!aiDataCard.data.cardData?.length) {
      //     // useCardDataObj.cardActions.remove(aiLoadingCard);
      //     aiDataCard.data.cardData.push(lastAnswer);
      //   }
      // } else if (isNormal && !aiDataCard) {
      //   aiDataCard = useCardDataObj.cardActions.add({
      //     _index: new Date().getTime(),
      //     componentKey: 'AiDataCard',
      //     data: {
      //       cardData: [lastAnswer]
      //     }
      //   }) as AiDataCard;
      // }
    },
    httpStop: (callId: string) => stopRequest(callId)
  })
  //发送后就清除
  // useChatListObj.chatActions.setDynamicData('selectedAgentInfo', null);
  useChatListObj.chatActions.setStaticData('uploadFileInfo', null);
}

function getSteamSummary(callback?: (res: any) => void, errorCallback?: (err: any) => void){
  const url = '/seeyon/ai-platform/ai-manager/knowledge/summary/stream';
  const options = {skipInit: true, isBlock: true};
  getSteamMessageHandel(url, {}, callback, errorCallback, options);
}
function getSteamMessage(message:string, currentAgentInfo: any, callback?: (res: any) => void, errorCallback?: (err: any) => void){

  // "agentCode": "message_for_portal",
  //   "chatSessionId": chatSessionId,
  //   "citations": [],
  //   "input": "查询我的消息",
  //   "sessionId": ""
  // 禁止产生历史记录
  const params = currentAgentInfo;
  const url = '/seeyon/ai-platform/ai-manager/agent/info/portal-agent/stream';
  // const url = '/seeyon/rest/comi-agent/chat/stream';
  getSteamMessageHandel(url, params, callback, errorCallback);
}
function getSteamMessageHandel(url: string, params: any, callback?: (res: any) => void, errorCallback?: (err: any) => void, options?: any){
  const stream = StreamManager.create({});
  /**
   * 此处直接push一个AiDataCard，状态由AiDataCard的data.status 参数控制
   * status 参数说明：
   * 0: 意图识别中
   * 1: Agent执行中
   * 2: Agent回答中
   * 3: 回答完毕
   * 4: 取消执行中
   * 5: 已停止回答
   * 6: 网络超时，请检查网络设置
   * 7: comi不小心发生错误啦，请稍后重试
   **/
   const aiDataCard = reactive({
    data: {
      cardData: [],
      status: 0,
      isHide: false,
      processData: [
        {
          expand: true,
          type: 'think',
          loading: true,
          title: '意图识别中',
          content: '',
        }
      ]
    },
  })
  let alreadyOnExecute = false;
  let messageId = '';
  let isError = false;
  stream.run({
    url,
    params,
    options,
    eventHandel: (type: string, data: any) => {
      if (type === 'connecting') {
        if (data.data) {
          alreadyOnExecute = false;
          aiDataCard.data.aiSessionId = data.data.aiSessionId;
          aiDataCard.data.processData = [
            {
              expand: true,
              type: 'think',
              loading: false,
              title: '已完成意图分析',
              content: data.content || '',
            },
            {
              expand: true,
              type: 'exec',
              loading: true,
              title: 'Agent执行中',
              content: []
            }
          ];
          //AgentName
          // agentInfo = {
          //   _index: new Date().getTime(),
          //   componentKey: 'AgentInfo',
          //   data: {
          //     agentInfo: stream.agentInfo,
          //   }
          // } as AgentInfo;

          // aiLoadingCard = useCardDataObj.cardActions.add({
          //   _index: new Date().getTime(),
          //   componentKey: 'AiLoadingAbort',
          //   data: {
          //     content: '意图识别中',
          //     isLoading: true
          //   }
          // }) as AiLoadingCard;
          aiDataCard.data.status = 1;
        } else {

          // 要重试Stream
          // StreamManager.bind(aiRequestCard, stream);
        }
      }
      // 没有错误才继续执行
        if (type === 'onInit') {

        }
        if (type === 'onExecute') {
          const executeDataIndx = aiDataCard.data.processData?.findIndex((item: any) => item.type === 'exec');
          if (executeDataIndx !== -1) {
            const executeData = aiDataCard.data.processData[executeDataIndx];
            executeData.content.push({
              id: data.runStepId,
              complete: data.runStepStatus === 'completed' ? true : false,
              loading: false,
              title: data.stepName,
              content: data.content || ''
            })
            executeData.title = '根据您的问题，正在为您执行Agent'
            aiDataCard.data.processData[executeDataIndx] = executeData;
          }
          if (!alreadyOnExecute) {
            // aiLoadingCard.data.content = 'Agent执行中';
            aiDataCard.data.status = 1;
          }
        }
        if (type === 'onmessage') {
          aiDataCard.data.status = 2;
          aiDataCard.data.processData = [];
          const result = data;
          // 替换loaidng卡片
          // if (!aiDataCard) {
          //   aiDataCard = useCardDataObj.cardActions.add({
          //     _index: new Date().getTime(),
          //     componentKey: 'AiDataCard',
          //     data: {
          //       cardData: []
          //     },
          //     staticData: {
          //       requestParams: params
          //     }
          //   }) as AiDataCard;

          //   StreamManager.bind(aiDataCard, stream);
          // }
          if (messageId && !aiDataCard.data.messageId) {
            aiDataCard.data.messageId = messageId;
          }
          aiDataCard.data.cardData = result;
        }
        if (type === 'onclose') {
          aiDataCard.data.status = 3;
          useStreamDataAction.setStreamIsLoading(false);
        }
        if (type === 'onmessageId') {
          messageId = data;
        }
        if (type === 'onquestion') {
          aiDataCard.data.recommandQuestion = data;
        }
        if (type === 'onerror') {
          isError = true;
          if (errorCallback) {
            errorCallback(data);
          }
          if (!navigator.onLine) {
            aiToast({
              content: '网络连接失败，请检查网络设置'
            });
            aiDataCard.data.status = 6;
          } else {
            aiToast({
              content: data.data?.message || 'comi不小心发生错误啦，请稍后重试',
            });
          }
        }
        //非法和敏感词状态
        // if (type === 'illegal') {
        //   illageSessionType = data;
        // }
        if(callback && !isError){
          callback(aiDataCard)
        }
    },
    stop: (isNormal: boolean) => {
      console.log('stop', isNormal);
      if(callback && !isError){
        callback(aiDataCard)
      }
    },
    httpStop: (callId: string) => stopRequest(callId)
  })
}

// 处理思考过程数据
function dealProcessData(aiDataCard: AiDataCard, isError: boolean = false, isNormalStop: boolean = true) {
  const thinkData = aiDataCard.data.processData?.find((item: any) => item.type === 'think');
  const executeData = aiDataCard.data.processData?.find((item: any) => item.type === 'exec');
  // 如果已经产生了数据，就不需要更改思考过程数据
  if (aiDataCard.data.cardData?.length) {
    return
  }

  let thinkDataTitle = '已完成意图分析';
  let executeDataTitle = '已执行Agent';
  // 错误时
  if (isError) {
    if (thinkData && !executeData) {
      thinkDataTitle = '未能识别意图';
    }
    if (executeData) {
      executeDataTitle = '未能成功执行Agent';
    }
  }
  // 非正常停止时
  if (!isNormalStop) {
    if (thinkData && !executeData) {
      thinkDataTitle = '已停止意图识别';
    }
    if (executeData) {
      executeDataTitle = '已停止执行Agent';
    }
  }


  if (thinkData && !executeData) {
    thinkData.loading = false;
    thinkData.title = thinkDataTitle || thinkData.title;
    thinkData.isError = isError;
    thinkData.isStop = !isNormalStop;
  }
  if (executeData) {
    executeData.loading = false;
    executeData.title = executeDataTitle || executeData.title;
    const executeDataContent = executeData.content;
    if (executeDataContent.length) {
      const lastContent = executeDataContent[executeDataContent.length - 1];
      lastContent.loading = false;
      lastContent.isError = isError;
      lastContent.isStop = !isNormalStop;
      lastContent.content = !isNormalStop ? '已停止' : lastContent.content || '';
    }
    executeData.isError = isError;
    executeData.isStop = !isNormalStop;
  }
}

//初始化一些状态
function initState() {
  const { useChatListObj } = getStores();
  useStreamDataAction.setStreamIsLoading(true);
  useChatListObj.chatActions.setDynamicData('isScrolling', true);
  if (!useChatListObj.dynamicData.isChat) {
    useChatListObj.chatActions.setDynamicData('isChat', true);
  }
  useChatListObj.scrollToBottom();
}
//处理请求参数
function initParams( message: string, isHide?: boolean) {
  const { useChatListObj } = getStores();
  const selectAssistantId = useChatListObj.dynamicData.selectedAgentInfo?.id;
  const assistantId = selectAssistantId || getAssistantId() || '';
  // const sessionIdKey = selectAssistantId ? `aiSessionId_${selectAssistantId}` : useChatListObj.staticData.aiSessionIdKey;
  // const aiChatSessionId = sessionStorage.getItem(sessionIdKey) || '';// 连续对话id
  const aiChatSessionId = useChatListObj.dynamicData.sesssionId;
  const params = useChatListObj.staticData.streamParams;
  return {
    ...params,
    assistantId,
    assistantCode: useChatListObj.dynamicData.selectedAgentInfo?.code || useChatListObj.dynamicData.assistantInfo?.code,
    sessionId: aiChatSessionId || '',
    chatSessionId: useChatListObj.dynamicData.chatSessionId,
    input: message || '',
    needHistoryRecord: !isHide, // 是否需要历史记录, 发出去的消息，被隐藏，就不需要历史记录
  };
}

function createRequestCard(message: string, citations: Array<FileType>, isHide: boolean): AiRequestCard {
  const { useCardDataObj } = getStores();
  return useCardDataObj.cardActions.add({
    _index: new Date().getTime(),
    componentKey: 'AiRequestCard',
    staticData: {
      message,
      citations: citations
    },
    data: {
      isLoading: true,
      isError: false,
      isHide
    }
  }) as AiRequestCard;
}


function initializeStream(aiRequestCard: AiRequestCard, aiDataCard: AiDataCard) {
  aiRequestCard.data.isLoading = true;
  aiRequestCard.data.isError = false;
}
//处理停止回答或异常停止共部分的逻辑
function dealStopCard(aiDataCard: AiDataCard) {
  const { useChatListObj, useCardDataObj } = getStores();
  //非正常停止并且有生成数据
  const stopCard = {
    _index: new Date().getTime(),
    componentKey: 'AiDataCard',
    data: {
      status: 5,
      content: '已停止回答',
      isLoading: false
    },
    staticData: aiDataCard.staticData
  };
  if (aiDataCard) {
    aiDataCard.data.status = 5;
    aiDataCard.data.isStop = true;
    const dataCard = aiDataCard.data?.cardData;
    if (dataCard.length) {
      // 将所有数据项的finish设置为1，表示停止时所有数据都已完成
      dataCard.forEach((item: any) => {
        item.finish = 1;
      });
    } else {
      useCardDataObj.cardActions.add(stopCard, aiDataCard);
    }
  } else {
    useCardDataObj.cardActions.add(stopCard);
  }
  useChatListObj.scrollToBottom();
}
function interruptAction() {
  StreamManager.stopAllStreams();
  stopLastCard();
}

function clean() {
  const { useChatListObj } = getStores();
  useChatListObj.chatActions.clearAllData();
  useChatListObj.chatActions.setDynamicData('isChat', false);
  useChatListObj.chatActions.setDynamicData('chatSessionId', buildUUID());
  useChatListObj.chatActions.setDynamicData('showScrollBtn', false);


  const params = {
    assistantId: getAssistantId(),
    chatSessionId: useChatListObj.dynamicData.chatSessionId,
    sessionId: ''
  };
}
function openNewConversation() {
  const { useChatListObj } = getStores();
  //如果不是对话页，就已经是最新会话了
  if (!useChatListObj.dynamicData.isChat) {
    aiToast({
      content: '已是最新对话',
      timer: 1500,
    });
    return;
  }
  // const dontAskAgain = getLocalStorage('dontAskAgain');
  // if (dontAskAgain) {
  //   clean();
  //   return;
  // }
}
function stopRequest(callId: string): void {
  const { useChatListObj } = getStores();
  useChatListObj.chatActions.setDynamicData('isAnswering', false);
  const sessionId = useChatListObj.dynamicData.sesssionId;
  const params = {
    callId: callId,
    sessionId,
    chatSessionId: useChatListObj.dynamicData.chatSessionId,
  };
  cancelChat(params).then((data: any) => {
    if (Number(data.code) === 0 && data?.data) {
      console.log('终止成功');
      // 终止成功后操作
    } else {
      console.log('终止失败');
    }
  });
};



export default {
  sendMessage,
  interruptAction,
  openNewConversation,
  getSteamMessage,
  getSteamSummary,
  clean,
};
