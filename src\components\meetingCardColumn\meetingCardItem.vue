<template>
  <!-- 单个会议项卡片 -->
  <div class="meeting-card-item bg-white px-3 py-2 rounded-md shadow-sm border border-gray-200 w-[206px]" @click="handleClick">
    <!-- 卡片头部 - 标题 -->
    <div class="item-header mb-[12px]">
      <h3 class="text-sm text-gray-800 truncate no-weight" :title="props.meeting.meetingName">
        {{ props.meeting.meetingName }}
      </h3>
    </div>

    <!-- 卡片中部 - 详细信息 -->
    <div class="item-details text-xs text-gray-500 space-y-1 mb-3">
      <div class="detail-row flex items-start">
        <span class="detail-label w-16 shrink-0">发起人:</span>
        <span class="detail-value truncate">{{ props.meeting.createUsr }}</span>
      </div>
      <div class="detail-row flex items-start">
        <span class="detail-label w-16 shrink-0">会议时间:</span>
        <span class="detail-value">{{ props.meeting.meetingTime }}</span>
      </div>
      <div class="detail-row flex items-start">
        <span class="detail-label w-16 shrink-0">会议地点:</span>
        <span class="detail-value truncate has-underline">{{ props.meeting.mettingPlace || '无' }}</span>
      </div>
    </div>

    <!-- 卡片底部 - 摘要 -->
    <div class="item-summary text-xs text-gray-600 mb-3">
      <div class="flex">
        <i class="iconfont ai-icon-xing"></i>
        <div>
          <span v-if="props.meeting.loaddingSummary" class="text-[#4379FF] font-normal line-ellipsis" :class="!isPortal ? 'six-line-ellipsis' : 'two-line-ellipsis'">正在为您生成摘要...</span>
          <span v-else class="text-[#4379FF] font-normal line-ellipsis summary-line-height" :class="!isPortal ? 'six-line-ellipsis' : 'two-line-ellipsis'" :title="props.meeting.summaryText">{{ props.meeting.summaryText }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs, inject } from 'vue';
import type { MeetingItem } from '@/types/portalTypes';

const isPortal = inject('isPortal')

// 定义 Props，接收来自父组件的单个会议数据
interface Props {
  meeting: MeetingItem; // meeting 属性是必需的
}

// 使用 defineProps 接收数据
const props = defineProps<Props>();
const { meeting } = toRefs(props);
const emit = defineEmits(['refresh-summary']);

const handleSummaryClick = (event: MouseEvent) => {
  event.stopPropagation();
  emit('refresh-summary', props.meeting);
};

const handleClick = () => {
  console.log(props.meeting);
}
</script>

<style scoped lang="less">
.meeting-card-item {
  // 可以添加额外的细微样式调整
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.594) 19.98%, rgba(255, 255, 255, 0.792) 79.94%);
  border: 1px solid #FFFFFF66;
  backdrop-filter:  blur(59px);
  -webkit-backdrop-filter:  blur(59px);
  border-radius: 12px;
  cursor: pointer;
  .no-weight {
    font-weight: normal;
    margin-bottom: 0;
    line-height: 24px;
  }
}

.hover_underline:hover{
  text-decoration: underline;
}

.detail-label {
  // 可以为标签设置固定宽度或特定样式
  color: #6b7280; // Tailwind gray-500
  width: 3.4rem;
}

.detail-value {
  color: #374151; // Tailwind gray-700
}

.has-underline{
  text-decoration: underline;
}

.detail-row{
  line-height: 20px;
}

/* 简单的 line-clamp 实现 (限制行数) */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 显示的行数 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-btn {
  // 按钮的基础样式 (如果需要覆盖 Tailwind)
  transition: background-color 0.2s ease;
}

.ai-icon-xing {
  background: linear-gradient(to right, #5D7BF8, #A082FD, #BA81FF);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-right: 4px;
}

.line-ellipsis{
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.two-line-ellipsis {
  -webkit-line-clamp: 2;
}
.six-line-ellipsis {
  -webkit-line-clamp: 6;
}
.summary-line-height {
  line-height: 20px;
}
</style>
