var GuideItem = function (keys) {
  this.keys = keys;
  this.start = function () {
    var steps = [];
    var doms = [];
    for (var key in this.keys) {
      var domKey = '#guide-' + key;
      var stepDom = document.body && document.body.querySelector(domKey);
      doms.push(stepDom);
      if (!stepDom) {
        return false;
      }
      switch (key) {
        case 'step1':
          steps.push({
            element: stepDom,
            popover: {
              title: '菜单区',
              description:`
                <div>
                  <span style="color:#4379FF;font-weight: 600;">角色化专属智能体</span><span style="color: rgba(0, 0, 0, 0.9);">，让协同人效跃升！</span>
                </div>
              `,
              side: 'right',
              align: 'center',
            },
          });
          break;
        case 'step2':
          steps.push({
            element: stepDom,
            popover: {
              title: '个人中心',
              description:`
                <div style="color: rgba(0, 0, 0, 0.9);">
                  个人偏好设置中心！
                </div>
              `,
              side: 'right',
              align: 'center',
            },
          });
          break;
        case 'step3':
          steps.push({
            element: stepDom,
            popover: {
              title: '待办中心',
              description:`
                <div>
                  <span style="font-weight:600;color:#4379FF;">审批智能体</span><span style="color: rgba(0, 0, 0, 0.9);">，助您精准审办！</span>
                </div>
              `,
              // <li>CoMi<span style="color:#4379FF">智能预审</span>辅助</li>
              side: 'bottom',
              align: 'center',
            },
          });
          break;
        case 'step4':
          steps.push({
            element: stepDom,
            popover: {
              title: '待开会议',
              description: `
                <ul>
                  <li>会议<span style="color:#4379FF">议程智能总结</span></li>
                </ul>
              `,
              side: 'left',
              align: 'center',
            },
          });
          break;
        case 'step5':
          steps.push({
            element: stepDom,
            popover: {
              title: '重要提醒',
              description: `
                <div>
                  <span style="font-weight:600;color:#4379FF;">消息智能体</span><span style="color:rgba(0,0,0,0.9)">，助您聚类整理重要消息！</span>
                </div>
              `,
              side: 'left',
              align: 'center',
            },
          });
          break;
        case 'step6':
          steps.push({
            element: stepDom,
            popover: {
              title: '快捷操作',
              description: `
                <div>
                  <span style="font-weight:600;color:#4379FF;">办公智能体</span><span style="color:rgba(0,0,0,0.9)">，高频事务一键即达！</span>
                </div>
              `,
              side: 'top',
              align: 'center',
            },
          });
          break;
        default:
          break;
      }
      localStorage.setItem(this.keys[key], true);
      var guideStartKey =
        GuideAPI.prototype.splitKeys && GuideAPI.prototype.splitKeys(this.keys[key]);
      GuideAPI.prototype.saveLocal && GuideAPI.prototype.saveLocal(guideStartKey);
    }

    if (!steps.length) {
      return true;
    }
    var driver = window.driver.js.driver;
    var driverObj = driver({
      popoverClass: 'driverjs-theme',
      showButtons: ['next', 'previous', 'close'],
      nextBtnText: '下一步',
      prevBtnText: '上一步',
      doneBtnText: '完成',
      showProgress: true,
      progressText: '{{current}}/{{total}}',
      allowClose: false,
      steps: steps,
    });
    driverObj.drive();
    return true;
  };
};
