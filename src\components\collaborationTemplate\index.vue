<template>
  <div class="collaboration-template" ref="collTemplate" :class="smallTemplateCss">
    <div class="header">
      <div class="title">我的模版</div>
      <i class="iconfont ai-icon-cha close-icon" @click="closeTemplate"></i>
    </div>
    <div class="content">
      <PortalEmptyColumn v-if="isEmpty" :image="emptyImage" :text="emptyText" :width="85" class="w-full flex-1"/>
      <div v-else class="template-content">
        <div class="template-item" v-for="template in recentTemplate" :key="template.id" @click="handleTemplateClick(template)" :title="template.subject">
          <i v-if="template.id === 'newColl'" class="iconfont ai-icon-jiahao add-icon"></i>
          <div class="item-text">{{template.subject}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import requests from '@/api/v5/index';
import {onMounted, computed } from 'vue';
import emptyImage from '@/assets/imgs/no-data.png';
import PortalEmptyColumn from '@/components/portalEmptyColumn/index.vue';
const _$emit = defineEmits(['closeTemplate', 'handleTemplateClick'])
const TEMPLATE_ITEM = 7;
const NEW_COLL = { //自由协同的信息
  subject: '新建自由协同',
  id: 'newColl',
}
const recentTemplate = ref<any[]>([]);
const requestEnd = ref(false);
const collTemplate = ref(null);
const smallTemplateCss = ref('');
const emptyText = ref('暂无数据哦～');
const isEmpty = computed(() => {
  return recentTemplate.value.length === 0 && requestEnd.value;
});

onMounted(() => {
  if(collTemplate.value?.offsetWidth < 422) {
    smallTemplateCss.value = 'small-template';
  }
});
getTemplateList();
function getTemplateList() {
  requests.getTemplate().then((res: any) => { //失败成功都要显示自由协同
    requestEnd.value = true;
    recentTemplate.value.push(NEW_COLL);
    if(Number(res.code) === 0) {
      const { data } = res;
      const { resultList } = data;
      if(!resultList.length) {
        return;
      }
      const allTemplate: Record<string, any> = {};
      const recentArr = resultList.filter(item => Number(item.categorys) === 110);
      const recentAllTpl = recentArr[0]?.allTemplateList || recentArr[0]?.templateList;
      if(recentAllTpl && recentAllTpl.length) {
        if(recentAllTpl.length >= TEMPLATE_ITEM) {
          const tempArr = recentAllTpl.slice(0, TEMPLATE_ITEM)
          recentTemplate.value.push(...tempArr);
          return;
        }else {
          recentTemplate.value.push(...recentAllTpl);
          recentAllTpl.forEach((item) => {
            allTemplate[item.id] = item;
          });
        }
      }
      for(let i = 0, len = resultList.length; i < len; i++) {
        const currentTemp = resultList[i];
        if(Number(currentTemp.categorys) !== 110) {
          let allTemplateList = currentTemp?.allTemplateList || currentTemp?.templateList || [];
          const childCategoryList = currentTemp.childCategoryList || [];
          if(childCategoryList.length) { //当前分类下面的子模版
            for(let j = 0, len = childCategoryList.length; j < len; j++) {
              const childCategory = childCategoryList[j]?.templateList || [];
              allTemplateList = allTemplateList.concat(childCategory);
            }
          }
          if(allTemplateList?.length) {
            allTemplateList.forEach(item => {
              if(!allTemplate[item.id] && recentTemplate.value.length < TEMPLATE_ITEM + 1) {
                recentTemplate.value.push(item);
                allTemplate[item.id] = item;
              }
            });
          }
        }
        if(recentTemplate.value.length > TEMPLATE_ITEM) {
          break;
        }
      }
    }
  }, (err)=> {
    requestEnd.value = true;
    recentTemplate.value.push(NEW_COLL);
  });

}

function handleTemplateClick(template: any) {
  _$emit('handleTemplateClick', template);
  closeTemplate();
}
function closeTemplate() {
  _$emit('closeTemplate', false);
}
</script>

<style lang="less" scoped>
.collaboration-template {
  position: absolute;
  bottom: calc(100% + 10px);
  left: 0;
  border-radius: 10px;
  padding: 20px 15px;
  height: auto;
  max-height: 306px;
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.60);
  box-shadow: 0px 0px 48px 0px rgba(8, 62, 221, 0.12);
  backdrop-filter: blur(28px);
  z-index: 999;
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    align-items: center;
  }
  .title {
    font-weight: @font-weight-500;
    font-size: 16px;
    height: 24px;
    line-height: 24px;
    color: #000;
    flex: 1;
  }
  .close-icon {
    cursor: pointer;
    font-size: 20px;
    color: #4A4E5A;
  }
  .content {
    display: flex;
    flex: 1;
    position: relative;
    min-height: 48px;
    overflow: auto;
    .template-content {
      width: 100%;
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-top: 12px;
      flex: 1;
    }
    .empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      padding-top: 12px;
    }
  }
  .template-item {
    display: flex;
    font-size: 14px;
    width: 189px;
    padding: 11px;
    cursor: pointer;
    border-radius: 12px;
    border: 1px solid #D1E0FF;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(30px);
    line-height: 24px;
    height: 48px;
    color: rgba(0, 0, 0, 0.9);
    &:hover {
      background: #F6F6F8;
      color: rgba(0, 0, 0, 1);
      .add-icon {
        color: rgba(0, 0, 0, 1)
      }
    }
  }
  .item-text {
    flex: 1;
    min-width: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .add-icon {
    color: rgba(111, 118, 134, 1);
    font-size: 16px;
    margin-right: 4px;
  }
}
.small-template {
  .template-item {
    width: 157px;
  }
}
</style>
