/*
 * @Author: 代琪 <EMAIL>
 * @Date: 2025-07-19 11:35:05
 * @LastEditors: 代琪 <EMAIL>
 * @LastEditTime: 2025-07-21 17:54:13
 * @FilePath: \ai-assistant-web\src\components\fullMenu\hooks\useMenuNavigation.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ref, type Ref } from 'vue'
import type { MenuItem } from '../types/menu'
import { getNavigationData } from '../../../api/portal'

export function useMenuNavigation(portalId: Ref<string>) {
  const navigationList = ref<MenuItem[]>([])
  const loading = ref(false)

  // 获取导航列表
  const fetchNavigation = async () => {
    try {
      loading.value = true

      // 调用API获取导航数据
      const result = await getNavigationData(portalId.value)

      if (result) {
        // 将导航数据转换为MenuItem格式
        navigationList.value = convertNavDataToMenuItems(result)
      }
    } catch (err) {
      console.error('获取导航数据失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 将导航数据转换为菜单项格式
  const convertNavDataToMenuItems = (navBos: any[]): MenuItem[] => {
    if (!Array.isArray(navBos)) return []

    return navBos.map((nav, index) => ({
      id: nav.id || `nav-${index}`,
      idKey: nav.id || `nav-${index}`,
      nameKey: nav.navName || nav.name || nav.text || '',
      url: nav.url || nav.href || '',
      urlKey: nav.url || nav.href || '',
      target: nav.target || '_self',
      openType: nav.openType || '',
      navType: nav.navType || '',
      spaceType: nav.spaceType || '',
      children: nav.children ? convertNavDataToMenuItems(nav.children) : []
    }))
  }

  return {
    navigationList,
    loading,
    fetchNavigation
  }
}
