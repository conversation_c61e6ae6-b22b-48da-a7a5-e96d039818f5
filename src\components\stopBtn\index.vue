<template>
  <div class="stop_btn_wrap" @click="goStop">停止生成</div>
</template>

<script setup lang="ts">
  const emit = defineEmits(['to-stop'])
  const goStop = () => {
    emit('to-stop')
  }
</script>

<style scoped lang="less">
.stop_btn_wrap {
 display: inline-block;
  align-items: center;
  height: 24px;
  padding: 0px 12px 0px 12px;
  border-radius: 12px;
  text-align: center;
  align-items: center;
  //styleName: 12px/Regular;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: @font-weight-400;
  line-height: 22px;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  border: 1px solid #BEC2FF;
  color: #7559F8;
  cursor: pointer;
}
.stop_click_btn {
    cursor: default !important;
}
</style>
