<template>
  <div class="create-card-column relative py-[16px] px-[16px] flex flex-col w-full max-h-[100%] rounded-[12px] bg-gray-100 bg-opacity-50 overflow-hidden backdrop-blur-[8px]" :class="[props.data.length ? '' : 'h-[250px]']">
    <div class="create-card-content overflow-y-auto flex-1">
      <div class="h-full">
         <div class="flex gap-x-[14px] gap-y-[14px] flex-wrap" v-if="props.data.length">
            <template v-for="assistant in props.data" :key="assistant.id">
              <CreateCardItem
                class="w-[32%] cursor-pointer rounded-[8px]"
                :id="assistant.id"
                :code="assistant.code"
                :iconUrl="assistant.iconUrl"
                :title="assistant.name"
                :introduce="assistant.introduce"
                @handleClick="handleAssistantClick"
              />
            </template>
         </div>
         <PortalEmptyColumn v-else  class="w-full h-full flex items-center justify-center"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CreateCardItem from './createCardItem.vue';
import PortalEmptyColumn from '../portalEmptyColumn/index.vue';
import emitter from '@/utils/bus';
import type { AssistInfo } from '@/types/portalTypes'


// --- Props ---
const props = defineProps<{
  data: AssistInfo[];
}>();

// --- Event Handlers ---
const handleAssistantClick = (data:any) => {
  // Emit event to parent or handle navigation/action
  emitter.emit('emitEvent', {
    type: 'agent',
    params: {
      id: data.id,
      code: data.code,
    }
  })
};

</script>

<style scoped lang="less">
.create-card-column{
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.528) 19.98%, rgba(255, 255, 255, 0.704) 79.94%);
}
/* Style the scrollbar */
.create-card-content::-webkit-scrollbar {
  width: 6px;
}

.create-card-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.create-card-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 3px;
}

.create-card-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.25);
}

/* Ensure sticky title works correctly */
.sticky {
  position: sticky;
  top: -1px; /* Offset slightly to avoid overlapping content */
  z-index: 10; /* Make sure it stays above cards */
}
</style>
