import type { ButtonType } from '../types';
import { message } from 'ant-design-vue';

/**
 * API结果接口
 */
export interface ApiResult {
  success: boolean;
  message: string;
  code?: number;
  data?: any;
}

/**
 * API处理器类 - 统一处理各种按钮的API调用
 * 直接从现有代码搬移逻辑，保持完全一致
 */
export class ApiHandlers {
  /**
   * 统一的API调用方法
   * 直接搬移 todoCardItem 中的逻辑，保持完全一致
   */
  static async handleApiCall(button: ButtonType, renderInfo: any): Promise<any> {
    // 处理URL格式，确保以/开头但不重复
    let newUrl = button.url;
    if (!newUrl.startsWith('/')) {
      newUrl = '/' + newUrl;
    }
    
    const response = await fetch(newUrl, {
      method: button.httpType,
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: JSON.stringify(button.paramMap)
    });

    return await response.json();
  }

  /**
   * 统一的结果处理方法
   * 直接搬移现有的结果处理逻辑，保持完全一致
   */
  static handleApiResult(result: any): ApiResult {
    if (result.code === 0 || result.success === "true" || result.message === "success") {
      return { 
        success: true, 
        message: '操作成功',
        code: result.code,
        data: result.data
      };
    } else {
      const errorMsg = result.message || result.error_msg || '操作失败';
      return { 
        success: false, 
        message: errorMsg,
        code: result.code,
        data: result.data
      };
    }
  }

  /**
   * 统一的消息提示处理
   */
  static showResultMessage(result: ApiResult): void {
    if (result.success) {
      message.success(result.message);
    } else {
      message.error(result.message);
    }
  }

  /**
   * 完整的API调用流程
   * 包含调用 -> 结果处理 -> 消息提示的完整流程
   */
  static async executeApiOperation(
    button: ButtonType, 
    renderInfo: any,
    emit?: any
  ): Promise<ApiResult> {
    try {
      const result = await ApiHandlers.handleApiCall(button, renderInfo);
      const apiResult = ApiHandlers.handleApiResult(result);
      
      // 显示消息提示
      ApiHandlers.showResultMessage(apiResult);
      
      // 如果成功，触发刷新事件
      if (apiResult.success && emit) {
        emit('refresh');
      }
      
      return apiResult;
    } catch (error) {
      console.error('Button click error:', error);
      const errorResult: ApiResult = {
        success: false,
        message: '网络错误，请重试'
      };
      
      ApiHandlers.showResultMessage(errorResult);
      return errorResult;
    }
  }
}

/**
 * 专门处理置顶相关操作的处理器
 * 直接搬移现有逻辑，保持完全一致
 */
export class TopActionApiHandlers {
  /**
   * 处理置顶操作
   * 直接搬移 todoCardItem 中的置顶逻辑
   */
  static async handleTopOperation(
    button: ButtonType, 
    setPendingTop: (affairId: string) => Promise<any>,
    emit: any
  ): Promise<boolean> {
    if (button.handleType === 'top') {
      try {
        const result = await setPendingTop(button.paramMap.affairId);
        if (result === 'success' || result.code === 0 || result.success === 'true' || result.message === 'success') {
          message.success('置顶成功');
          emit('refresh');
        } else {
          message.warning(result.message || '当前数据已更新，请刷新后再试');
        }
        return true;
      } catch (error) {
        console.error('置顶操作失败:', error);
        message.warning('当前数据已更新，请刷新后再试');
        return true;
      }
    }
    return false;
  }

  /**
   * 处理取消置顶操作
   * 直接搬移 todoCardItem 中的取消置顶逻辑
   */
  static async handleUnTopOperation(
    button: ButtonType, 
    cancelPendingTop: (affairId: string) => Promise<any>,
    emit: any
  ): Promise<boolean> {
    if (button.handleType === 'unTop') {
      try {
        const result = await cancelPendingTop(button.paramMap.affairId);
        if (result === 'success' || result.code === 0 || result.success === 'true' || result.message === 'success') {
          message.success('取消置顶成功');
          emit('refresh');
        } else {
          message.warning(result.message || '当前数据已更新，请刷新后再试');
        }
        return true;
      } catch (error) {
        console.error('取消置顶操作失败:', error);
        message.warning('当前数据已更新，请刷新后再试');
        return true;
      }
    }
    return false;
  }

  /**
   * 统一处理置顶相关操作
   */
  static async handleTopActions(
    button: ButtonType,
    setPendingTop: (affairId: string) => Promise<any>,
    cancelPendingTop: (affairId: string) => Promise<any>,
    emit: any
  ): Promise<boolean> {
    const handled = await TopActionApiHandlers.handleTopOperation(button, setPendingTop, emit);
    if (handled) return true;
    
    return await TopActionApiHandlers.handleUnTopOperation(button, cancelPendingTop, emit);
  }
}

/**
 * 操作类型检查工具
 */
export class ApiActionChecker {
  /**
   * 检查是否是置顶相关操作
   */
  static isTopAction(button: ButtonType): boolean {
    return ['top', 'unTop'].includes(button.handleType);
  }

  /**
   * 检查是否需要特殊处理的操作
   */
  static isSpecialAction(button: ButtonType): boolean {
    return ApiActionChecker.isTopAction(button);
  }

  /**
   * 检查是否是普通API操作
   */
  static isGeneralApiAction(button: ButtonType): boolean {
    return !ApiActionChecker.isSpecialAction(button);
  }
} 