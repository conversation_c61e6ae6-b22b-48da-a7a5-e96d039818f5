import { get, post } from '../config.js';

const _ctxPath = window.top?._ctxPath || '/seeyon';

export default {

    // V9.X 1130任务项新增
    /**
     * @description 根据名称查询应用菜单
     * @param {Object} params
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     * @param {String} params.name
     */
    getAppDataList(params: any) {
        return post(_ctxPath + `/rest/seeyon-ai/colFusion/search/appData?pageNo=${params.pageNo}&pageSize=${params.pageSize}&option.n_a_s=1`, params);

    },
    /**
     * @description 根据文档名称进行查询
     * @param {Object} params
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     * @param {String} params.name
     */
    getDocDataList(params: any) {
        return post(_ctxPath + `/rest/seeyon-ai/colFusion/search/doc?pageNo=${params.pageNo}&pageSize=${params.pageSize}&option.n_a_s=1`, params);
    },
    /**
     * @description 根据名称查询审批模板
     * @param {Object} params
     * @param {String} params.templateName
     * @param {String} params.categoryIdList //分类，默认查公共模板就传-1
     * @param {String} params.showRecent //是否显示最近使用
     * @param {String} params.fullMatch 是否完全匹配
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     */
    getTemplateTable(params: any) {
        return post(_ctxPath + `/rest/template/myTemplate/table?option.n_a_s=1`, params);
    },

    /**
     * @description 根据名称查询人员列表,带分页
     * @param {Object} params
     * @param {String} params.name
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     */
    getMemberList(params: any) {
        return post(_ctxPath + `/rest/seeyon-comi/colFusion/search/member?pageNo=${params.pageNo}&pageSize=${params.pageSize}&option.n_a_s=1`, params);
    },
    /**
     * @description 根据名称获取新闻列表,带分页
     * @param {Object} params
     * @param {String} params.name
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     * @returns
     */
    getNewsList(params: any) {
        return post(_ctxPath + `/rest/seeyon-ai/colFusion/search/news?pageNo=${params.pageNo}&pageSize=${params.pageSize}&option.n_a_s=1`, params);
    },

    /**
     * @description 根据名称获取公告列表,带分页
     * @param {Object} params
     * @param {String} params.name
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     * @returns
     */
    getBulletinList(params: any) {
        return post(_ctxPath + `/rest/seeyon-ai/colFusion/search/bulletin?pageNo=${params.pageNo}&pageSize=${params.pageSize}&option.n_a_s=1`, params);
    },
    /**
     * @description 文档中心 检查文档是否支持查看
     * @param {Object} params
     * @param {String} params.docId 文档ID
     * @param {String} params.entrance 入口类型
     * @param {String} params.userId 用户ID 非必填
     * @param {String} params.baseDocId  非必填
     * @returns
     */
    docValidInfo(params: any) {
        return post(_ctxPath + `/rest/doc/validInfo?option.n_a_s=1`, params);
    },
    //获取协同模版
    getTemplate(){
      return get(`${_ctxPath}/rest/template/myTemplate?option.n_a_s=1&categoryIds=-1,101,102,103`, {}, true);
    },
  //获取快捷磁贴
    getShortcut (){
      return get(`${_ctxPath}/rest/portal-comi/get-common-use-shortcut`, {}, true);
    },
  //全部已读
  messaegAllRead() {
    return post(`${_ctxPath}/rest/seeyon-ai/comi/message/update-category?option.n_a_s=1`, {},{}, true);
  },
  //获取未读消息的数量
  findSystemHistoryMessages() {
    const random = Math.floor(Math.random() * Math.pow(10, 5));
    const params = `managerMethod=findSystemHistoryMessages&arguments=${JSON.stringify([{"page":1,"size":1000},{"readType":"notRead"}])}`
    return post(`${_ctxPath}/ajax.do?method=ajaxAction&managerName=messageManager&rnd=${random}`, params,{}, true)
  },
  // 获取未读消息的数量以及未读消息的最后一条数据 id
  getUnreadMessageCountAndLastId() {
    return post(`${_ctxPath}/rest/seeyon-ai/comi/message/list4Category?option.n_a_s=1`, {}, {}, true);
  },
  getTipNumber(tipKey: string)  {
    return get(`${_ctxPath}/getAjaxDataServlet?S=ajaxShortCutManager&M=getPortletTipNumber&CL=true&RVT=XML&P_1_String=${tipKey}&P_2_Long=-1`, {}, true)
  }
}
