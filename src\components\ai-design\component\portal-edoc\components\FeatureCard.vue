<template>
  <div
    class="feature-card"
    @click="handleClick"
  >
    <div class="feature-card__content">
      <div class="feature-card__title"><span v-html="featureData.title"></span>
        <img
          :src="Arrow"
          alt=""
          srcset=""
          class="arrow-icon"
        >
      </div>
      <p class="feature-card__description">{{ featureData.description }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Arrow from '@/assets/imgs/arrow.png';

interface FeatureData {
  id: string
  title: string
  description: string
  icon: string
  url: string
}

interface Props {
  data: FeatureData
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: [data: FeatureData]
}>()

// 从 props 获取功能数据
const featureData = computed(() => props.data)

const handleClick = () => {
  emit('click', props.data)
}
</script>

<style scoped lang="less">
.feature-card {
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  background: url('@/assets/imgs/edoc-card-bg.png') no-repeat center center;
  border: 1px solid rgba(237, 242, 252, 1);
  background-size: cover;
  width: 100%;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #1890ff, #722ed1);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  &:hover {
    box-shadow: 0px 0px 28px 0px rgba(8, 62, 221, 0.12);
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  &__icon {
    font-size: 32px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1890ff, #722ed1);
    border-radius: 12px;
    color: white;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  }

  &__arrow {
    color: #999;
    transition: transform 0.3s ease;
  }

  &__content {
    flex: 1;
  }

  &__title {
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    span {
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
      color: rgba(67, 121, 255, 1);
    }

    .arrow-icon {
      height: 12px;
      width: 12px;
      margin-top: 2px;
    }
  }

  &__description {
    color: var(--text-3666, rgba(102, 102, 102, 1));
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .feature-card {
    padding: 20px;

    &__icon {
      font-size: 28px;
      width: 40px;
      height: 40px;
    }

    &__title {
      font-size: 16px;
    }

    &__description {
      font-size: 13px;
    }
  }
}
</style>
