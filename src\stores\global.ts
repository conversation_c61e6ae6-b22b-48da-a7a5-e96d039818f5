import { ref } from 'vue'
import { defineStore } from 'pinia'
// ts-ignore
import { aiToast } from '@/plugins/ai-toast';
import { useChatList } from './chatList';
import { useTempRunningAssistInfo } from './homeSearch';
import { getAssistIntroduce } from '@/api/common';
import { useStateRouter } from './stateRouter';
import cardInstance from '@/stores/card';
import { cancelAllRequests } from '@/api/config';
import { closeDobuleScreen } from '@/utils/storesUtils';
import { getQueryString } from '@/utils/common';

// 定义globalState的类型
interface GlobalState {
  isFullScreen: boolean
  isParsing: boolean
  isNewTopic: boolean
  showDobuleScreen: boolean
  comiContainerName: string
  roles: [string] // 角色
  [key: string]: any // 添加索引签名
  // TODO:此处临时设置了处理待办列表和待办详情的特殊逻辑，后续还需要进一步优化
  keepTodoList: boolean
  preLinkIsTodoList: boolean
}

// 全局状态
export const useGlobal = defineStore('globalStatus', () => {

  const globalState: GlobalState = reactive({
    isFullScreen: false, // 是否全屏
    isParsing: false, // 是否正在解析
    isNewTopic: false, // 是否新开话题
    showDobuleScreen: false, // 是否双屏
    comiContainerName: '', // COMI容器名称
    currAgentCode: 'comi',
    roles: ['base'], // 角色
    posts: [], //岗位
    // TODO:此处临时设置了处理待办列表和待办详情的特殊逻辑，后续还需要进一步优化
    keepTodoList: false, // 是否保持待办列表
    preLinkIsTodoList: false, // 是否是待办列表
  })
  // 测试代码
  // setTimeout(() => {
  //   globalState.currAgentCode = "sadf";
  // }, 5000)
  // 初始化comiContainerName从localStorage读取
  const initComiContainerName = () => {
    const storedName = localStorage.getItem('comiContainerName')
    if (storedName) {
      globalState.comiContainerName = storedName
    }
  }

  // 设置comiContainerName并同步到localStorage
  const setComiContainerName = (name: string) => {
    globalState.comiContainerName = name
    localStorage.setItem('comiContainerName', name)
  }

  const changeState = (key: keyof GlobalState, val: any) => {
    globalState[key] = val
  }

  // 显示解析文件提示
  const showParsingTips = (content?: string) => {
    if (globalState.isParsing) {
      aiToast({
        content: '正在解析文件，请稍后',
      })
      return true
    }

    if (content) {
      aiToast({
        content
      })
    }
    return false
  }

  // 初始化时读取localStorage中的comiContainerName
  initComiContainerName()

  return {
    globalState,
    showParsingTips,
    changeState,
    setComiContainerName,
    initComiContainerName
  }

})

// 用户信息
export const useUserInfo = defineStore('userInfo', () => {
  const userInfo = ref<any>({}) // 用户信息
  // 设置用户信息
  const setUserInfo = (val: any) => {
    userInfo.value = {
      ...userInfo.value,
      ...val
    }
  }
  return { userInfo, setUserInfo }
})

export const useGeneralAsit = defineStore('generalAsit', () => {
  const generalAsit = ref<any>(null) // 通用AI
  // 设置通用AI
  const setGeneralAsit = (val: any) => {
    generalAsit.value = {
      ...generalAsit.value,
      ...val
    }
  }
  return { generalAsit, setGeneralAsit }
})


type ChangeMenuOptions = {
  isAsit?: boolean // 是否是助手
  fromHistory?: boolean // 是否来自历史
  isRedirectAist?: boolean //  是否是重定向助手
}

/**
 * 菜单
 * 可能会有back方法，用于回退
 * 可能会有backParams参数，用于数据回填
 */
export const useMenu = defineStore('menu', () => {
  const currentMenuInfo = ref<any>({
    id: 'comi'
  })

  // 获取助手开场白，并缓存
  const getAsitPrologue = async (id: string, isAsit: boolean) => {
    const { changeAssistInfo } = useTempRunningAssistInfo();
    const { chatActions } = useChatList();
    const res: any = await getAssistIntroduce(id);
    if (res?.code == '0' && res?.data) {
      const isComi = useGeneralAsit().generalAsit?.id == res.data.id;
      const asistInfo = {
        ...res.data,
        isComi,
        isAsit
      };
      if(res.data.code === "assist4288738392844026819") { //数智仓
        asistInfo.customName = "数智仓";
      }else if(res.data.code === "assist4271406870676392934") { //数智仓
        asistInfo.customName = "服务站";
      }
      changeAssistInfo(asistInfo);
      chatActions.setDynamicData('selectedAgentInfo', asistInfo)
    }
  }
  const changeMenu = async (val: any, options?: ChangeMenuOptions) => {
    // cancelAllRequests('svc/call/rest')
    // 切换菜单的时候，清空聊天记录
    const { chatActions } = useChatList();
    const isAsit = options?.isAsit || false;
    const fromHistory = options?.fromHistory || false;
    const isRedirectAist = options?.isRedirectAist;

    // 特殊处理fullMenu菜单项
    if (val?.key === 'fullMenu' || val?.id === 'fullMenu') {
      // 设置当前菜单信息
      currentMenuInfo.value = {
        ...val,
        isAsit: false
      };

      // 设置全局状态，表明正在显示导航栏
      const uGlobal = useGlobal();
      uGlobal.changeState('currAgentCode', 'fullMenu');

      chatActions.clearAllData(isRedirectAist);
      // 可以在这里添加更多的fullMenu特定逻辑
      return;
    }

    const isPortal = getQueryString('isPortal') === 'true';
    const uGlobal = useGlobal();
    if(!isPortal){
      uGlobal.changeState('isFullScreen', false);
    }
    chatActions.setDynamicData('showProlog', true);
    // 切换助手的时候，是否会话切换为false
    chatActions.setDynamicData('isDialogued', false);
    chatActions.clearAllData(isRedirectAist);
    // 切换菜单的时候，终止回答
    cardInstance.interruptAction();
    // 切换的时候，回退一层路由，避免二级返回状态有问题
    const stateRouter = useStateRouter();
    // 如果是重定向助手，不自动回退
    if (!isRedirectAist) {
      stateRouter.back();
      closeDobuleScreen();
    }
    // 如果是助手，需要获取助手的开场白， 直接访问助手是id，菜单上绑定的助手，是assistId
    const id = val?.assistId || val.id;
    if (isAsit || val?.assistId) {
      // assistId 针对左侧菜单
      await getAsitPrologue(id, isAsit);
    } else {
      // 是超级助手
      if (id === 'comi') {
        const { changeAssistInfo } = useTempRunningAssistInfo();
        const { generalAsit } = useGeneralAsit();
        changeAssistInfo({ ...generalAsit, isAsit: true });
        chatActions.setDynamicData('selectedAgentInfo', generalAsit)
      } else {
        const { changeAssistInfo } = useTempRunningAssistInfo();
        changeAssistInfo({ name: val.label, id: id, key: val.key, isAsit });
        chatActions.setDynamicData('selectedAgentInfo', { name: val.label, id: id, key: val.key, isAsit })
      }
    }
    if (!fromHistory) {
      chatActions.clearHistoryRelParams();
    }
    // 如果id是comi，这选中comi菜单
    const isComi = useGeneralAsit().generalAsit?.id === val?.id;
    if (isComi) {
      currentMenuInfo.value = {
        id: 'comi',
        isAsit
      }
    } else {
      currentMenuInfo.value = {
        ...val,
        isAsit
      };
    }
    // 调用门户布局
    uGlobal.changeState('currAgentCode', isComi ? 'comi' : val?.code || 'comi');
  }
  return { currentMenuInfo, changeMenu }
})

//是否来自协同的辅助审批
export const useCustomData = defineStore('customData', ()=>{
  const customDataObj = reactive<any>({});
  const setCustomData = (key:string ,val:any) => {
    customDataObj[key] = val;
  }
  return {
    customDataObj,
    setCustomData
  }
});
