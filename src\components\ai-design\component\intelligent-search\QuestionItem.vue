<template>
  <div class="question-item-wrapper">
    <div class="title">{{ data.title }}：</div>
    <div class="content">
      <CommonQuestionItem
        v-for="item in data.questions"
        :item="item"
        :text="item.text"
        @click="handleClick(item)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import cardInstance from '@/stores/card';
import type { QuestionItem } from '@/types/portalTypes';
import CommonQuestionItem from '@/components/common/question-item/index.vue';

defineComponent({
  name: 'QuestionItem',
});
const props = defineProps<{
  data: QuestionItem;
}>();

// 发送消息
const handleClick = (item: { text: string }) => {
  cardInstance.sendMessage(item.text);
};
</script>
<style lang="less" scoped>
.question-item-wrapper {
  padding: 10px 12px 0;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15px);
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;

  .title {
    font-family: PingFang SC;
    font-weight: @font-weight-500;
    font-style: Semibold;
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 8px;
    padding-left: 4px;
    height: 32px;
  }

  .content {
    flex: 1;
    overflow: auto;
    // max-height: 400px;
  }

  .item {
    display: flex;
    margin-bottom: 16px;
    align-items: center;
    padding: 4px 8px;
    border-radius: 24px;
    border: 1px solid #d1e0ff;
    cursor: pointer;
    max-width: fit-content;

    &:hover {
      background: #f6f6f8;
      .text {
        color: @sky;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    .xing {
      width: 20px;
      height: 20px;
      margin-top: 1px;
    }

    .text {
      color: #000;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: @font-weight-400;
      line-height: 22px;
      margin-left: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-wrap: keep-all;
    }
  }
}
</style>
