<template>
  <div class="iframe-wrapper">
    <iframe
      ref="currentIframe"
      id="iframe-container"
      v-show="uGlobal.globalState.keepTodoList"
      :src="todoListUrl"
      border="0"
      width="100%"
      height="100%"
      class="iframe-container"
    />
    <iframe
      ref="currentIframe"
      id="iframe-container"
      v-if="!uGlobal.globalState.keepTodoList"
      :src="content?.data"
      border="0"
      width="100%"
      height="100%"
      class="iframe-container"
    />
  </div>
</template>
<script lang="ts" setup>
import { getPendingDataByCategory } from '@/api/pending';
import { useTodo } from '@/stores/todoData';
import {
  closeDobuleScreen,
  getDobuleScreenIframeWindow,
  openDobuleScreen,
} from '@/utils/storesUtils';
import { message } from 'ant-design-vue';
import { onMounted, inject, ref } from 'vue';
import { useGlobal, useMenu } from '@/stores/global';
const uMenu = useMenu();

const uGlobal = useGlobal();

const props = defineProps<{
  content: {
    data: string;
    show: boolean;
    type: string;
    origin: string;
  } | null;
}>();

// TODO: 需要进一步验证
// 判断是否为门户模式
const isPortal = inject('isPortal');
const uTodo = useTodo();
const sdkInstance = inject('sdkInstance') as any;
const currentIframe = ref<HTMLIFrameElement | null>(null);
const topWindow = window.top || window;
const _ctxPath = window._ctxPath;
let timer: any = null;
let iframeWindow: Window | null = null;
// TODO:此处临时设置了处理待办列表和待办详情的特殊逻辑，后续还需要进一步优化
const todoListUrl = `${_ctxPath}/collaboration/collaboration.do?method=listPending&isFromComi=true`;

// 处理下一条数据
const dealNextData = async () => {
  if (uMenu.currentMenuInfo?.id === 'comi' && sdkInstance) {
      sdkInstance.emitRefresh('refreshTodoData');
    }
  if (props?.content?.origin === 'portal') {
    const { searchParams, dataList, dataInx } = uTodo.todoState;
    let nextData = null;
    if (dataInx + 1 < dataList.length) {
      nextData = dataList[dataInx + 1];
      uTodo.changeState('dataInx', dataInx + 1);
    } else if (searchParams.portletParams) {
      const response: any = await getPendingDataByCategory(
        searchParams.pageNumber,
        searchParams.pageSize,
        searchParams.portletParams,
      );
      if (response && response.code === '0' && response.data) {
        const rawData = response.data?.data || [];
        const inx = 0;
        uTodo.changeState('dataInx', inx);
        uTodo.changeState('dataList', rawData);
        nextData = rawData[inx];
      } else {
        nextData = null;
      }
    } else {
      nextData = null;
    }
    if (nextData) {
      const url =
        _ctxPath + nextData?.link ||
        `${_ctxPath}/collaboration/collaboration.do?method=summary&openFrom=listPending&affairId=${nextData?.affairId}&showTab=true`;
      openDobuleScreen(url, 'iframe', { origin: 'portal' });
      timer = setInterval(() => {
        iframeWindow = getDobuleScreenIframeWindow();
        if (iframeWindow) {
          clearInterval(timer);
          return iframeWindow;
        }
      }, 500);
    } else {
      handleClose();
    }
  } else {
    handleClose();
  }
};

const handleClose = () => {
  message.success('操作成功');
  closeDobuleScreen(sdkInstance);
};

onMounted(() => {
  if (isPortal) {
    // 门户模式：设置 isMultipleTabs 为 true，并添加方法
    (topWindow as any).isMultipleTabs = true;

    // 添加空的 refreshAllTabSection 方法
    (topWindow as any).refreshAllTabSection = () => {
      // 空函数
    };

    // 添加 closeCurrentTabIframe 方法，实现我们的逻辑
    (topWindow as any).closeCurrentTabIframe = async () => {
      // TODO:此处临时设置了处理待办列表和待办详情的特殊逻辑，后续还需要进一步优化
      if (uGlobal.globalState.preLinkIsTodoList) {
        uGlobal.changeState('keepTodoList', true);
      } else {
        // 这里实现门户模式下的关闭逻辑
        dealNextData();
      }
    };
    const originalOpenCtpWindow = (topWindow as any).openCtpWindow;

    (topWindow as any).openCtpWindow = (data: any) => {
      if (data.comiOrigin === 'v5') {
        originalOpenCtpWindow(data);
        return;
      }
      if (data?.url) {
        openDobuleScreen(data.url, 'iframe');
        timer = setInterval(() => {
          iframeWindow = getDobuleScreenIframeWindow();
          if (iframeWindow) {
            clearInterval(timer);
            return iframeWindow;
          }
        }, 500);
      }
    };
    (topWindow as any).refreshTabIframe = (id: string, url: string) => {
      if (currentIframe.value && url) {
        currentIframe.value.src = url;
      } else {
        currentIframe.value?.contentWindow?.location.reload();
      }
    };
  }
  // else {
  //   // 非门户模式：拦截 closeCurrentTabIframe 方法
  //   const originalCloseCurrentTabIframe = (topWindow as any).closeCurrentTabIframe;
  //   const originalOpenCtpWindow = (topWindow as any).openCtpWindow;
  //   const isMultipleTabs = (topWindow as any).isMultipleTabs;

  //   (topWindow as any).closeCurrentTabIframe = () => {
  //     if (isMultipleTabs) {
  //       dealNextData();
  //     } else {
  //       // 然后调用原始的 closeCurrentTabIframe
  //       if (originalCloseCurrentTabIframe && typeof originalCloseCurrentTabIframe === 'function') {
  //         originalCloseCurrentTabIframe();
  //       }
  //     }
  //   };

  //   (topWindow as any).openCtpWindow = (data: any) => {
  //     if (isMultipleTabs) {
  //       if (data?.url) {
  //         openDobuleScreen(data.url, 'iframe');
  //         timer = setInterval(() => {
  //           iframeWindow = getDobuleScreenIframeWindow();
  //           if (iframeWindow) {
  //             clearInterval(timer);
  //             return iframeWindow;
  //           }
  //         }, 500);
  //       }
  //     } else {
  //       if (originalOpenCtpWindow && typeof originalOpenCtpWindow === 'function') {
  //         originalOpenCtpWindow(data);
  //       }
  //     }
  //   };
  // }
  (topWindow as any).getDobuleContainerIframe = () => {
    return currentIframe.value?.contentWindow || null;
  };
});
</script>
<style scoped lang="less">
.iframe-wrapper {
  height: 100%;
  width: 100%;
  border-radius: 12px;
  padding: 8px;
  box-sizing: border-box;
  // 最小宽度1400px，避免iframe内容过小，导致页面布局错乱， 目前是根据新建会议页面宽度来适配的，新建会议最小1366px
  min-width: 1400px;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;

  .iframe-container {
    border: none;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
  }
}
</style>
