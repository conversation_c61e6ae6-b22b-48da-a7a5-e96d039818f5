<template>
    <div class="portal_box portal_data_analysis" v-if="open" :class="uploadHoverCss">
      <div class="portal_data_analysis_title">
        <h5>数据分析</h5>
        <i class="iconfont ai-icon-cha" @click="handleClose"></i>
      </div>
      <!-- <UploadFiles :fileList="fileList" @delFile="delFileFn" v-show="fileList.length > 0"/> -->
      <!-- <div class="portal_data_analysis_box" v-if="fileList.length === 0"> -->
        <div class="portal_data_analysis_box"  @mouseover="handleMouseover" @mouseout="handleMouseout" @dragenter.prevent="handleDragEnter" @dragleave.prevent="handleDragLeave" @drop.prevent="handleDrop" @dragover.prevent>
        <Upload class="upload-content" v-if="open" ref="portalDataAnalysisRef" :chatSessionId="props.chatSessionId" uploadType="dragger" :supportFileTypes="['.xlsx']" :notShowPopover="true" @getRusultFiles="getRusultFilesFn"></Upload>
        <div class="upload-text">
          <h5>在此处拖放表格文档</h5>
          <p>数据分析功能仅支持上传表格文档，支持 xlsx，最多上传一个</p>
          <span>本地上传</span>
        </div>
      </div>
    </div>
</template>
<script setup name="DataAnalysis" lang="ts">
  import Upload from '@/components/common/upload/index.vue';
  import { ref } from 'vue';
  const fileList = ref([]);
  const portalDataAnalysisRef = ref();
  const uploadHoverCss = ref('');
  const dragCounter = ref(0);
  const props = defineProps({
    chatSessionId: {
      type: String,
      default: ''
    },
    open: {
      type: Boolean,
      default: true
    }
  });

  const emit = defineEmits(['update:open', 'getRusultFiles']);

  const handleClose = () => {
    emit('update:open', false);
  };

  const getRusultFilesFn = (files) => {
    // fileList.value = files;
    emit('getRusultFiles', files);

  }
  const delFileFn = (itm, inx) => {
    portalDataAnalysisRef.value.delFile(inx);
  }

  const getDataAnalysisFilesFn = ()=>{
    return fileList.value || [];
  }
  const handleMouseover = () => {
    uploadHoverCss.value = 'upload-hover';
  }
  const handleMouseout = () => {
    uploadHoverCss.value = '';
  }
  const handleDragEnter = () => {
    dragCounter.value++;
    if (dragCounter.value === 1) {
      uploadHoverCss.value = 'upload-hover';
    }
  };
  const handleDragLeave = () => {
    dragCounter.value--;
    if (dragCounter.value === 0) {
      uploadHoverCss.value = '';
    }
  };
  const handleDrop = () => {
    dragCounter.value = 0;
    uploadHoverCss.value = '';
  };
  defineExpose({
    getDataAnalysisFilesFn
  });

  </script>
  <style lang="less" scoped>
    .portal_data_analysis {
      border-radius: 10.47px;
      padding-top: 20px;
      padding-right: 16px;
      padding-bottom: 20px;
      padding-left: 16px;
      gap: 10px;
      border: 1px solid #FFFFFF66;
      backdrop-filter: blur(28px);
      box-shadow: 0px 0px 48px 0px #083EDD1F;
      background: #FFFFFF99;
      &_title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 24px;
        margin-bottom: 16px;
        h5{
          font-family: PingFang SC;
          font-weight: @font-weight-500;
          font-size: 16px;
          margin-bottom: 0;
          height: 24px;
          line-height: 24px;
          color: #000000;
        }
        i{
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          color: #4A4E5A;
          font-size: 20px;
          &:hover{
            cursor: pointer;
          }
        }
      }
      &_box {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 1;
        .upload-content{
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          z-index: 2;
          opacity: 0;
          display: inline-block;
        }
        .upload-text{
          position: relative;
          z-index: 1;
          width: 100%;
          text-align: center;
          h5{
            font-family: PingFang SC;
            font-weight: @font-weight-500;
            font-size: 14px;
            line-height: 24px;
            letter-spacing: 0%;
            color: #1c1c1c;
          }
          p{
            font-family: PingFang SC;
            font-weight: @font-weight-400;
            font-size: 12px;
            line-height: 20px;
            letter-spacing: 0%;
            color: #000;
            padding: 12px 0;
          }
          span{
            display: inline-block;
            width: 103px;
            height: 32px;
            border-radius: 12px;
            border: 1px solid #FFFFFF66;
            background-color: #FFFFFF;
            font-family: PingFang SC;
            font-weight: @font-weight-500;
            font-size: 13px;
            line-height: 100%;
            letter-spacing: 0px;
            color: #000;
            text-align: center;
            line-height: 32px;
            border: 1px solid #D1E0FF;
            backdrop-filter: blur(59.30232620239258px);
            &:hover{
              cursor: pointer;
            }
          }
        }
      }
    }
    .upload-hover {
      border: 1px solid #4379FF;
      .upload-text span {
        background: #F6F6F8;
        border: 1px solid #4379FF;
      }
    }
  </style>
