type StreamUtilsType = {
    setStreamString: (context: string, dataItem: any, index?: number) => any[],
    parseString: (input: string, reason_input?: string) => any[],
    formatContent: (context: string, dataList: any, isBlock?: boolean) => any[],
    formatHistoryContent: (context: string, citationsJson:string, type:number, hitKnowledgeRunSteps: any[], needMarkBlueRunSteps: any[], dataList: any) => any[],
    // 2. 动态属性签名（允许任意字符串属性）
    [key: string]: (...args: any[]) => any;
  }

  const streamUtils: StreamUtilsType = {
    setStreamString: function (context, dataItem, index) {
      const items = [dataItem];
      if (context) {
        let strBuffer = dataItem.buffer || '';
        let addStr = '';
        if(dataItem.isBlock){
          // 直接新增
          return [...items,{
            index: dataItem.index + 1,
            context,
            buffer : context.indexOf('</aicard>') >= 0 ? context : ''
          }];
        }else if (dataItem.isCard) {
          const tmpContext = (dataItem.context || '') + context;
          if (tmpContext.indexOf('</aicard>') >= 0) {
            const strBufferList = tmpContext.split('</aicard>');
            // 把前面的文本添加到context中
            const afterContext = strBufferList.shift();
            dataItem.context = afterContext || '';
            dataItem.buffer = '';
            try {
              dataItem.json = JSON.parse(afterContext || '{}');
            } catch (error) {
              dataItem.json = {};
            }
            // 结束这个item
            dataItem.finish = 1;
            // 后面的文本继续递归处理
            if (strBufferList.length > 0) {
              const afterContext = strBufferList.join('</aicard>');
              const newDataItem = {
                index: dataItem.index + 1,
                context: '',
              };
              const items = streamUtils.setStreamString(afterContext, newDataItem);
              return [dataItem, ...items];
            }
          } else {
            dataItem.context = tmpContext;
          }
        } else {
          if (strBuffer) {
            strBuffer += context;
            if (strBuffer.startsWith('<aicard>')) {
              // 终止，并且返回一个dataItem,并且形成一个新的dataItem
              strBuffer = strBuffer.slice(8);
              if (!dataItem.context) {
                dataItem.isCard = true;
                const items = streamUtils.setStreamString(strBuffer, dataItem);
                return items;
              } else {
                const newDataItem = {
                  index: dataItem.index + 1,
                  isCard: true,
                  context: '',
                };
                dataItem.buffer = '';
                dataItem.finish = 1;
                const items = streamUtils.setStreamString(strBuffer, newDataItem);
                return [dataItem, ...items];
              }
            } else {
              // 检查strBuffer
              const tmpStr8 = strBuffer.slice(0, 8);
              if ('<aicard>'.indexOf(tmpStr8) === 0) {
                dataItem.buffer = strBuffer;
              } else {
                dataItem.context = (dataItem.context || '') + strBuffer;
                dataItem.buffer = '';
              }
              return items;
            }
          }
          const strList = context.split('<');
          if (strList.length > 1) {
            addStr = strList[0];
            for (let i = 1; i < strList.length; i++) {
              const tmpStr = '<' + strList[i];
              const tmpStr8 = tmpStr.slice(0, 8);
              if ('<aicard>'.indexOf(tmpStr8) === 0) {
                strBuffer += tmpStr;
                dataItem.buffer = strBuffer;
              } else {
                addStr += tmpStr;
                dataItem.buffer = '';
              }
            }
          } else {
            addStr = context;
          }
          dataItem.context = (dataItem.context || '') + addStr;
        }
      }
      return items;
    },
    parseString: function (input, reason_input) {
      const result = [];
      const regexes = [
        { type: 'iframe', regex: /<iframe.*?<\/iframe>/gs },
        { type: 'aicard', regex: /<aicard>(.*?)<\/aicard>/gs },
      ];

      let lastIndex = 0;
      let matchFound = false;

      // 处理不同类型的标签
      for (const { type, regex } of regexes) {
        let match: any;
        while ((match = regex.exec(input)) !== null) {
          matchFound = true;

          // 如果有匹配到的部分之前的文本
          if (match.index > lastIndex) {
            result.push({
              type: 'text',
              content: input.slice(lastIndex, match.index),
              reasoning_content: reason_input,
            });
          }

          // 匹配到的标签部分
          result.push({
            type,
            content: type === 'iframe' ? match[0] : match[1],
            reasoning_content: reason_input,
          });

          // 更新 lastIndex 为当前匹配结束的位置
          lastIndex = regex.lastIndex;
        }

        // 如果匹配到某种标签，停止进一步处理
        if (matchFound) {
          // cardType.value = type;
          break;
        }
      }

      // 如果没有任何标签匹配到，只处理文本
      if (!matchFound) {
        result.push({ type: 'text', content: input, reasoning_content: reason_input });
      } else {
        // 如果最后还有剩余的文本
        if (lastIndex < input.length) {
          result.push({
            type: 'text',
            content: input.slice(lastIndex),
            reasoning_content: reason_input,
          });
        }
      }
      return result;
    },
    formatHistoryContent: function(content: string, citationsJson: string, type:number, hitKnowledgeRunSteps: Array<any>, needMarkBlueRunSteps: Array<any>, dataList: Array<any>) {
      const resultList = [...dataList];
      let needMarkBlueRunStepsArray: any[] = [];
      // 处理双屏知识源数据
      if (hitKnowledgeRunSteps.length) {
        hitKnowledgeRunSteps.forEach((item, index) => {
          let data = item.content
          if (typeof data === 'string' && data) {
            try {
              data = JSON.parse(data)
            } catch (error) {
              console.log('error', error);
            }
          }
          if (data && Object.keys(data).length) {
            resultList.push({
              index: index,
              isDobuleScreen: true,
              context: data,
            });
          }
        })
      }
      if (needMarkBlueRunSteps.length) {
        needMarkBlueRunSteps.forEach((item, index) => {
          let data = item.content
          if (typeof data === 'string' && data) {
            try {
              data = JSON.parse(data)
            } catch (error) {
              console.log('error', error);
            }
          }
          needMarkBlueRunStepsArray = needMarkBlueRunStepsArray.concat(data);
        })
      }
      if (content.startsWith("<iframe")) {
        resultList.push({
          index: resultList.length,
          isIframe: true,
          finish: 1,
          context: content
        });
      } else {
        const list = streamUtils.parseString(content);
        list.forEach((el: { type: string; content: string; citationsJson?:string }) => {
          if (el.type == "aicard" && el.content) {
            resultList.push({
              isCard: true,
              index: resultList.length,
              finish: 1,
              context: el.content,
              json: JSON.parse(el.content)
            });
          } else if (el.type == "iframe") {
            resultList.push({
              index: resultList.length,
              isIframe: true,
              finish: 1,
              context: el.content
            });
          } else {
            resultList.push({
              index: resultList.length,
              finish: 1,
              context: el.content,
              tagData: needMarkBlueRunStepsArray
            });
          }
        });
      }

      if(type === 0) {
        if(typeof citationsJson === 'string' && citationsJson) {
          try {
            citationsJson = JSON.parse(citationsJson)
          } catch (error) {
            console.log('error', error);
          }
        }
        if(citationsJson?.length) {
          resultList[resultList.length - 1].citations = citationsJson[0];
        }

      }else{
        if(typeof citationsJson === 'string' && citationsJson) {
          try {
            citationsJson = JSON.parse(citationsJson)
          } catch (error) {
            console.log('error', error);
          }
        }
        if(citationsJson?.length) {
          resultList.push({
            index: resultList.length,
            isKnowledgeData: true,
            context: citationsJson,
          });
        }
      }
      return resultList;
    },
    formatContent: function(content: string, dataList: Array<any>, isBlock?: boolean) {
      const resultList = [...dataList];
      if(isBlock){
        resultList.push({
          index: resultList.length,
          isBlock: true,
          finish: 1,
          context: content
        });
      }else if (content.startsWith("<iframe")) {
        resultList.push({
          index: resultList.length,
          isIframe: true,
          finish: 1,
          context: content
        });
      } else {
        const list = streamUtils.parseString(content);
        list.forEach((el: { type: string; content: string; citationsJson?:string }) => {
          if (el.type == "aicard" && el.content) {
            resultList.push({
              isCard: true,
              index: resultList.length,
              finish: 1,
              context: el.content,
              json: JSON.parse(el.content)
            });
          } else if (el.type == "iframe") {
            resultList.push({
              index: resultList.length,
              isIframe: true,
              finish: 1,
              context: el.content
            });
          } else {
            resultList.push({
              index: resultList.length,
              finish: 1,
              context: el.content
            });
          }
        });
      }
      return resultList;
    }
  };

  export const { setStreamString, parseString, formatContent, formatHistoryContent } = streamUtils;
