<!doctype html>
<html lang="">
  <head>
    <meta charset="UTF-8" />
    <link href="/seeyon/common/images/A8/favicon.ico" type="image/x-icon" rel="icon" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>致远A8+协同管理软件 V9.1</title>
    <script src="/seeyon/apps_res/aiAssistantEntry/js/comi-message-v5.js"></script>
    <script src="/seeyon/ai-platform/ai-static/ai-copilot/public/guide/guide-main.js"></script>
    <!-- <script src="/seeyon/rest/cap4/form/headerJs"></script>
     <script src="/seeyon/common/all-min.js"></script>
     <script src="/seeyon/apps_res/aiAssistantEntry/js/comi-entry.js"></script> -->
    <style>
      #app {
        height: 100%;
      }
      .ai-identify-component .ai_item .ai_identify_box {
        height: 38px;
        padding: 8px;
        border-radius: 2px 12px 12px 12px;
        display: flex;
        align-items: center;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
      }

      .ai-identify-component .loading_status {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
      }

      .ai-identify-component .loader_img {
        animation: ai-identify-loadcir 800ms linear 0ms infinite normal none;
        margin-right: 5px;
        margin-left: 5px;
        position: relative;
      }

      @keyframes ai-identify-loadcir {
        from {
          transform: rotateZ(0deg);
        }
        to {
          transform: rotateZ(360deg);
        }
      }
    </style>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="./main.ts"></script>
  </body>
</html>
