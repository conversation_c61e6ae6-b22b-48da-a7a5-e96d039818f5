.card-content-charts[data-v-cf373135] {
  padding-top: 12px;
  box-sizing: border-box;
  flex: 1;
  min-height: 0;
}
.cardContent[data-v-cf373135] {
  background-color: #fff;
  box-sizing: border-box;
  overflow: hidden;
}
.card-pc.cardContent[data-v-cf373135] {
  padding-bottom: 0;
  overflow: auto;
  /* min-height: 430px; */
  display: flex;
  flex-direction: column;
}
.card-pc.cardContent .card-content-charts[data-v-cf373135] {
  padding-top: 16px;
}
.card-app[data-v-cf373135] {
  padding-bottom: 16px;
}
.card-summary__top[data-v-cf373135] {
  height: 20px;
  line-height: 20px;
  display: inline-block;
}
.card-summary__top .card-summary__top--left[data-v-cf373135] {
  font-size: var(--card-font-size-small);
  font-weight: var(--card-font-weight);
  color: var(--card-color-font-secondary);
}
.card-summary__top--table[data-v-cf373135] {
  transform: translateY(-32px);
  padding-top: 6px;
  max-width: 300px;
}