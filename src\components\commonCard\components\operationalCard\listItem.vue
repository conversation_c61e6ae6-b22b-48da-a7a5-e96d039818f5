<template>
  <div class="list-item-wrap">
    <!-- 内容区 -->
    <div class="flex p-4 item_content">
      <div class="item_avatar">
        <Image class="avatar-img" :src="item.renderInfo.avatar || Avater" :preview="false" />
      </div>
      <div class="flex-1 item_info">
        <div class="flex justify-between w-full item_info_top">
          <div class="item_title ellipsis">{{ item.renderInfo.title }}</div>
          <div class="item_tag">{{ item.renderInfo.tag || '标签文字' }}</div>
        </div>
        <div class="item_user">
          <div class="item_user_name">{{ item.renderInfo.userName }}</div>
          <div class="item_desc">{{ item.renderInfo.description }}</div>
        </div>
      </div>
    </div>
    <!--按钮区 -->
    <div class="itme_action">
      <div
        v-for="(d, inx) in [...item.renderInfo.buttons]"
        :key="inx"
        :class="
          [
            'action_btn',
            d.type == 'success' ? 'action_act_btn' : null,
          ].join(' ')
        "
        @click="() => goSecAct(d.action)"
      >
        {{ d.label }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { withDefaults, onMounted } from 'vue';
import { Image } from 'ant-design-vue';
import { useTempBtnContext } from '@/stores/homeSearch';

import Avater from '@/assets/imgs/ai-avatar.png';
// 上层信息
const props = withDefaults(
  defineProps<{
    item: {
      renderInfo: any;
    };
  }>(),
  {
    item: () => {
      return {
        renderInfo: {
          title: '',
          userName: '',
          avatar: '',
          description: '',
          buttons: [],
        },
      };
    },
  },
);

const uTmBnCtxt = useTempBtnContext(); // store实例
// 按钮方法
const goSecAct = (action: string) => {
  console.log('action_____', action);
  if (action == 'agree') {
    uTmBnCtxt.setBtnActInfo({
      text: `点击同意：${props.item.renderInfo.title}`,
    });
  }
  if (action == 'reject') {
    uTmBnCtxt.setBtnActInfo({
      text: `点击拒绝：${props.item.renderInfo.title}`,
    });
  }
  console.log('item_____', props.item);
};
onMounted(() => {
  // console.log('props_____', props.item.renderInfo);
});
</script>

<style scoped lang="less">
.list-item-wrap {
  width: 100%;
  margin-bottom: 16px;
  .item_content {
    background-color: #f4f3f6;
    height: 88px;
    padding: 8px;
    border-radius: 4px;
    .item_avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      margin-right: 8px;
    }
    .item_info {
      .item_info_top {
        margin-bottom: 4px;
        .item_title {
          width: 226px;
          font-family: PingFang SC;
          font-weight: @font-weight-500;
          font-size: 14px;
          line-height: 22px;
          letter-spacing: 0px;
          color: #000000;
        }
        .item_tag {
          height: 22px;
          padding: 0px 10px;
          border: 1px solid #6394ff;
          font-family: PingFang SC;
          font-weight: @font-weight-400;
          font-size: 12px;
          letter-spacing: 0px;
          background: #e5f1ff;
          border-radius: 4px;
          color: #0077ff;
          display: flex;
          align-items: center;
          justify-items: center;
        }
      }
      .item_user {
        font-family: PingFang SC;
        font-weight: @font-weight-400;
        font-size: 14px;
        line-height: 22px;
        letter-spacing: 0px;
        color: rgba(0, 0, 0, 0.4);
        .item_user_name {
          margin-bottom: 2px;
        }
      }
    }
  }
  .item_content:hover{
    background-color: #EDF2FC;
    .item_title{
      color: #4379FF !important;
    }
  }
  .itme_action {
    display: flex;
    justify-content: space-between;
    padding-top: 16px;
    gap: 10px;
    .action_btn {
      flex: 1;
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0px;
      border-radius: 4px;
      border: 1px solid transparent;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border-color: #d5d6db;
      color: rgba(0, 0, 0, 0.9);
    }
    .action_act_btn {
      border-color: #4379ff;
      background-color: #4379ff;
      color: white;
    }
    .action_btn:hover{
      border-color: #6394FF;
      color: #6394FF;
    }
    .action_act_btn:hover{
      background-color: #6394FF;
      color: white;
    }
    .action_btn:active{
      border-color: #2962F0;
      color: #2962F0;
    }
    .action_act_btn:active{
      background-color: #2962F0;
      color: white;
    }
  }
}
</style>
