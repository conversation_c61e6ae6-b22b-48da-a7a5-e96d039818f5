import type { TabItem } from "@/types";

export const v5DataMap = {
    'all': '全部',
    '-1': '文化建设',  // 8新闻，7公告，9讨论，40享空间，10调查
    '1': '协同',
    '2': '表单',
    '3': '文档',
    '4': '公文',
    '39': '第三方待办',
    'plan': '时间安排', // 94领导人议程，6会议，30任务管理，11日历，5计划
    'application': '应用',
    '62': '通讯录',
    '70': '报表',
    '127': '协同邮箱',
    '79': '问答',
    '0': '本地知识'
}
const cultureBuildingKeys = ['7', '8', '9', '40', '10']
const timeKeys = ['94', '6', '30', '11', '5']
const applicationKeys = ['52', '101', '83', '65']
export const classifyData = (data: Record<string, any[]>) => {
    const result: Record<string, any[]> = {};

    // 初始化所有分类（包含 v5DataMap 中的 key）
    Object.keys(v5DataMap).forEach(key => {
        result[key] = [];
    });
    //对本地文件去重后的data
    const knowledgeData = dealFileData(data);
    // 合并"全部"分类
    result['all'] = Object.values(knowledgeData).flat().map(item => {
        const key = String(item.appType) as keyof typeof v5DataMap
        const category = dealDataType(key)
        return {
            ...item,
            category
        }
    });
    // 按 appType 分类数据（兼容 mockData 中的数字 key）
    Object.entries(knowledgeData).forEach(([key, items]) => {
        const appType = key as keyof typeof v5DataMap;
        const resultKey = dealDataType(appType)
        return result[resultKey].push(...items.map(a => ({ ...a, category: resultKey })));
    });

    return result;
};

export const formatKnowLedgeData = (data: any) => {
    // 按顺序输出
    const orderedKeys = ['all', '79', '62', 'application', '1', '127', '2', '4', '70', '3', '-1', 'plan', '39', '0'];
    const result: TabItem[] = [];
    orderedKeys.map(key => {
        if (data[key]?.length) {
            result.push({
                key,
                label: v5DataMap[key as keyof typeof v5DataMap] + "·" + data[key].length,
                content: data[key].sort((a: any, b: any) => b.similarity - a.similarity)
            })
        }
    });
    return result;
}
export const iconMap = {
    '-1': 'ai-icon-wenhuajianshe',  // 8新闻，7公告，9讨论，40享空间，10调查
    '1': 'ai-icon-xietong',
    '2': 'ai-icon-biaodan',
    '3': 'ai-icon-wendang',
    '4': 'ai-icon-gongwen1',
    '39': 'ai-icon-daiban',
    'plan': 'ai-icon-shijiananpai', // 94领导人议程，6会议，30任务管理，11日历，5计划
    'application': 'ai-icon-yingyong',
    '62': 'ai-icon-tongxunlu',
    '70': 'ai-icon-baobiao',
    '127': 'ai-icon-xietongyouxiang',
    '79': 'ai-icon-wenda'
}

const dealDataType = (type: string) => {
    let _type = ''
    if (timeKeys.includes(type)) {
        _type = 'plan'
    } else if (cultureBuildingKeys.includes(type)) {
        _type = '-1'
    } else if (applicationKeys.includes(type)) {
        _type = 'application'
    } else {
        _type = type
    }
    return _type
}

// 获取图标
export const getIconByType = (type: any) => {
    const _type = dealDataType(type)
    return iconMap[_type as keyof typeof iconMap]
}
export const dealFileData = (data: Record<string, any[]>) => { //对本地文件去重
  const konwledgeKeys = Object.keys(data);
  const localFilesObj: Record<string, any> = {};
  for(let i = 0 , len = konwledgeKeys.length; i < len; i++) {
    const key = konwledgeKeys[i];
    if(key === '0') {
      const localFiles = data[key];
      localFiles.forEach((item) => {
        if(!localFilesObj[item.resourceId]) {
          localFilesObj[item.resourceId] = item;
        }else { //重复文件
          if(item.similarity > localFilesObj[item.resourceId].similarity) { //取大的相似度
            localFilesObj[item.resourceId] = item;
          }
        }
      });
      data[key] = Object.values(localFilesObj);
      break;
    }
  }
  return data;
}
