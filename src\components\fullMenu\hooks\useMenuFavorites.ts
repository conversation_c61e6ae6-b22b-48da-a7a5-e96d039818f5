import { ref, reactive, computed, type Ref } from 'vue'
import type { MenuItem } from '../types/menu'
import { addFavoriteMenu, removeFavoriteMenu, getFavoriteMenus } from '../../../api/menu/index'
import { getSearchMenuUtils } from '../../../utils/searchMenuUtils'

export function useMenuFavorites(portalId: Ref<string>, spaceId: Ref<string>) {
  const favoritesList = ref<MenuItem[]>([])
  const favoriteIds = ref<Set<string>>(new Set())
  const loading = ref(false)

  // 获取收藏列表
  const fetchFavorites = async () => {
    try {
      loading.value = true

      // 调用API获取收藏菜单ID数组
      const result = await getFavoriteMenus(portalId.value, spaceId.value)

      if (result) {
        // getTopMenu返回的是menuId数组
        const menuIds = result || []

        // 根据menuId数组构建收藏列表
        const favorites: MenuItem[] = []
        const ids = new Set<string>()

        // 获取searchMenuUtils实例
        const searchMenuUtils = getSearchMenuUtils()
        const menuMap = searchMenuUtils?.menuMap || {}

        menuIds.forEach((menuId: string) => {
          // 首先尝试从searchMenuUtils.menuMap中获取完整信息
          const menuKey = `menu_${menuId}`
          const menuInfo = menuMap[menuKey]

          if (menuInfo) {
            // 从menuMap中获取到完整信息
            const menuItem: MenuItem = {
              id: menuInfo.id || menuId,
              idKey: menuInfo.idKey || menuKey,
              nameKey: menuInfo.nameKey || menuInfo.name || `菜单${menuId}`,
              urlKey: menuInfo.urlKey || menuInfo.url || '',
              resourceCode: menuInfo.resourceCode || '',
              target: menuInfo.target || '',
              children: menuInfo.children || []
            }
            favorites.push(menuItem)
            ids.add(menuId)
          }
        })

        favoritesList.value = favorites
        favoriteIds.value = ids
      }
    } catch (err) {
      console.error('获取收藏列表失败:', err)
    } finally {
      loading.value = false
    }
  }


  // 添加收藏
  const addFavorite = async (menuItem: MenuItem) => {
    try {
      // 调用API添加收藏
      const result = await addFavoriteMenu(menuItem.id, portalId.value, spaceId.value)
      if (result) {
        // API成功，添加到本地列表
        if (!favoriteIds.value.has(menuItem.id)) {
          favoritesList.value.push(menuItem)
          favoriteIds.value.add(menuItem.id)
        }
        return true
      } else {
        throw new Error('API添加收藏失败')
      }
    } catch (err) {
      console.error('添加收藏失败:', err)
      return false
    }
  }

  // 移除收藏
  const removeFavorite = async (menuId: string) => {
    try {
      // 调用API移除收藏
      const result = await removeFavoriteMenu(menuId, portalId.value, spaceId.value)
      if (result) {
        // API成功，从本地列表移除
        favoritesList.value = favoritesList.value.filter(item => item.id !== menuId)
        favoriteIds.value.delete(menuId)
        return true
      } else {
        throw new Error('API移除收藏失败')
      }
    } catch (err) {
      console.error('移除收藏失败:', err)
      return false
    }
  }

  // 切换收藏状态
  const toggleFavorite = async (menuItem: MenuItem) => {
    if (favoriteIds.value.has(menuItem.id)) {
      return await removeFavorite(menuItem.id)
    } else {
      return await addFavorite(menuItem)
    }
  }

  // 排序收藏
  const sortFavorites = async (sortedIds: string[]) => {
    try {
      // 根据sortedIds重新排序favoritesList
      const sortedList: MenuItem[] = []

      sortedIds.forEach(id => {
        const item = favoritesList.value.find(fav => fav.id === id)
        if (item) {
          sortedList.push(item)
        }
      })

      favoritesList.value = sortedList

      // TODO: 如果需要排序API，可以在这里调用

      return true
    } catch (err) {
      console.error('排序失败:', err)
      return false
    }
  }

  // 检查是否已收藏
  const isFavorited = (menuId: string) => {
    return favoriteIds.value.has(menuId)
  }

  // 处理收藏切换
  const handleToggleFavorite = async (menuItem: MenuItem) => {
    const success = await toggleFavorite(menuItem)
    if (success) {
      console.log(isFavorited(menuItem.id) ? '已添加收藏' : '已取消收藏')
    }
    return success
  }

  // 处理移除收藏
  const handleRemoveFavorite = async (menuId: string) => {
    const success = await removeFavorite(menuId)
    if (success) {
      console.log('已移除收藏')
    }
    return success
  }

  // 处理排序
  const handleSortFavorites = async (sortedIds: string[]) => {
    const success = await sortFavorites(sortedIds)
    if (success) {
      console.log('排序已保存')
    }
    return success
  }

  // 初始化时获取收藏数据
  // fetchFavorites()

  // 根据menuId获取完整菜单信息的方法
  const getMenuInfoById = (menuId: string, allMenus: MenuItem[]): MenuItem | null => {
    // 递归查找菜单
    const findMenu = (menus: MenuItem[]): MenuItem | null => {
      for (const menu of menus) {
        if (menu.id === menuId || menu.idKey === `menu_${menuId}`) {
          return menu
        }
        if (menu.children && menu.children.length > 0) {
          const found = findMenu(menu.children)
          if (found) return found
        }
      }
      return null
    }

    return findMenu(allMenus)
  }

  // 更新收藏列表（当有完整菜单数据时调用）
  const updateFavoritesWithMenuData = (allMenus: MenuItem[]) => {
    const currentFavoriteIds = Array.from(favoriteIds.value)
    const updatedFavorites: MenuItem[] = []

    // 获取searchMenuUtils实例
    const searchMenuUtils = getSearchMenuUtils()
    const menuMap = searchMenuUtils?.menuMap || {}

    currentFavoriteIds.forEach(menuId => {
      // 首先尝试从searchMenuUtils.menuMap中获取
      const menuKey = `menu_${menuId}`
      const menuInfo = menuMap[menuKey]

      if (menuInfo) {
        // 从menuMap中获取到完整信息
        const menuItem: MenuItem = {
          id: menuInfo.id || menuId,
          idKey: menuInfo.idKey || menuKey,
          nameKey: menuInfo.nameKey || menuInfo.name || `菜单${menuId}`,
          urlKey: menuInfo.urlKey || menuInfo.url || '',
          resourceCode: menuInfo.resourceCode || '',
          target: menuInfo.target || '',
          children: menuInfo.children || []
        }
        updatedFavorites.push(menuItem)
      } else {
        // 如果menuMap中没有，尝试从allMenus中查找
        const menuInfo = getMenuInfoById(menuId, allMenus)
        if (menuInfo) {
          updatedFavorites.push(menuInfo)
        }
      }
    })

    favoritesList.value = updatedFavorites
  }

  return {
    favoritesList,
    favoriteIds,
    loading,
    fetchFavorites,
    addFavorite,
    removeFavorite,
    toggleFavorite,
    sortFavorites,
    isFavorited,
    handleToggleFavorite,
    handleRemoveFavorite,
    handleSortFavorites,
    updateFavoritesWithMenuData
  }
}
