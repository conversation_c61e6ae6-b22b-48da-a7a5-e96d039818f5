<template>
  <template v-if="!isCopilotScreen">
    <div class="recommend-question">
      <div class="title-wrapper">
        <span class="title">您可能想问：</span>
        <Arrow
          v-if="columnCount - 4 > 0"
          @handleArrowClick="handleArrowClick"
          :canScrollLeft="canScrollLeft"
          :canScrollRight="canScrollRight"
        />
      </div>
      <AiIdentify class="loading" v-if="questionList.length === 0 && !isEmpty" :loadingStatus="true"
        ></AiIdentify
      >
      <div
        class="question-content"
        :style="{
          transform: `translateX(-${currentScrollNum * 206}px)`,
          transition: 'transform 0.5s cubic-bezier(0.23, 1, 0.32, 1)',
        }"
        v-else-if="questionList.length"
      >
        <CommonQuestionItem
          v-for="item in questionList"
          :item="item"
          class="question-item"
          @click="questionClick"
          :text="item.text"
        />
      </div>
      <div class="no-data" v-else>暂无数据</div>
    </div>
  </template>
  <template v-else>
    <div class="recommend-question">
      <div class="title-wrapper">
        <span class="title">您可能想问：</span>
      </div>
      <CommonQuestionItem
        v-for="item in questionList"
        :item="item"
        class="question-item"
        @click="questionClick"
        :text="item.text"
      />
    </div>
  </template>
</template>
<script lang="ts" setup>
import { defineComponent } from 'vue';
import CommonQuestionItem from '@/components/common/question-item/index.vue';
import Arrow from '@/components/common/arrow/index.vue';
import AiIdentify from '@/components/aiIdentify/index.vue';

defineComponent({
  name: 'AiRecommendQuestion',
});

const props = defineProps<{
  questionList: any[];
  isCopilotScreen?: boolean;
}>();
const emit = defineEmits(['questionClick']);

const currentScrollNum = ref(0);
const columnCount = ref(0);
const isEmpty = ref(false);

// 能否左滚动
const canScrollLeft = computed(() => {
  return columnCount.value - 4 > 0 && currentScrollNum.value > 0;
});

// 能否右滚动
const canScrollRight = computed(() => {
  return (
    columnCount.value - currentScrollNum.value > 4 && currentScrollNum.value < columnCount.value - 1
  );
});

// 滚动
const handleArrowClick = (type: string) => {
  if (type === 'pre' && canScrollLeft.value) {
    currentScrollNum.value = currentScrollNum.value > 0 ? currentScrollNum.value - 1 : 0;
  } else if (type === 'next' && canScrollRight.value) {
    currentScrollNum.value =
      currentScrollNum.value < columnCount.value - 1
        ? currentScrollNum.value + 1
        : columnCount.value - 1;
  }
};

// 问题点击
const questionClick = (item: any) => {
  emit('questionClick', item);
};
onMounted(() => {
  columnCount.value = props.questionList.length;
});
</script>
<style lang="less" scoped>
.recommend-question {
  height: 94px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15px);
  padding: 8px 16px 12px 16px;

  :deep(.ant-spin-spinning) {
    width: 100%;
  }

  .title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .title {
      font-family: PingFang SC;
      font-weight: @font-weight-500;
      font-size: 16px;
      line-height: 24px;
    }
  }

  .question-content {
    display: flex;
    width: 100%;

    .question-item {
      margin-right: 32px;
      margin-bottom: 0;
      min-width: 174px;
      max-width: 174px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
  .no-data {
    font-size: 14px;
    color: #8e94a2;
    text-align: center;
  }
}
</style>
