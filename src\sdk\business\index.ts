/**
 * COMI Assistant SDK - 业务层
 * 提供具体的业务功能接口，基于核心通信层实现
 */

import { MessageBus } from '../core/index';
import type { SDKOptions } from '../core/index';

// ==================== 类型定义 ====================

/** 业务SDK配置 */
interface BusinessConfig {
  defaultAssistId?: string;
  hideInputBoxAssistIds?: string[];
  defaultSendMsg?: boolean;
  autoInit?: boolean;
  isPortal?: boolean;
  expandDoubleContainer?: boolean;
  [key: string]: any;
}

/** 预配置接口 */
interface PreConfig {
  defaultAssistId?: string;
  hideInputBoxAssistIds?: string[];
  defaultSendMsg?: boolean;
  isPortal?: boolean;
  expandDoubleContainer?: boolean;
}

/** 事件处理器类型 */
type EventHandler = (...args: any[]) => void;

// ==================== 事件管理器 ====================

/** 事件管理器 */
class EventManager {
  private listeners: Map<string, Set<EventHandler>> = new Map();

  /** 添加事件监听器 */
  on(event: string, handler: EventHandler): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(handler);
  }

  /** 移除事件监听器 */
  off(event: string, handler?: EventHandler): void {
    if (!handler) {
      this.listeners.delete(event);
      return;
    }

    const handlers = this.listeners.get(event);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.listeners.delete(event);
      }
    }
  }

  /** 触发事件 */
  emit(event: string, ...args: any[]): void {
    const handlers = this.listeners.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args);
        } catch (error) {
          console.error(`事件处理器执行失败 [${event}]:`, error);
        }
      });
    }
  }

  /** 获取事件列表 */
  getEvents(): string[] {
    return Array.from(this.listeners.keys());
  }

  /** 清空所有事件监听器 */
  clear(): void {
    this.listeners.clear();
  }

  /** 移除所有事件监听器 */
  removeAllListeners(): void {
    this.listeners.clear();
  }

  /** 获取指定事件的监听器集合 */
  getListeners(event: string): Set<EventHandler> | undefined {
    return this.listeners.get(event);
  }
}

// ==================== 绑定对象管理器 ====================

/** 绑定对象管理器 */
class BindingManager {
  private bindings: Map<string, any> = new Map();

  /** 绑定对象 */
  bind(key: string, value: any): void {
    this.bindings.set(key, value);
  }

  /** 获取绑定对象 */
  get(key: string): any {
    return this.bindings.get(key);
  }

  /** 检查是否存在绑定 */
  has(key: string): boolean {
    return this.bindings.has(key);
  }

  /** 移除绑定 */
  unbind(key: string): boolean {
    return this.bindings.delete(key);
  }

  /** 执行绑定的方法 */
  call(key: string, ...args: any[]): any {
    const fn = this.bindings.get(key);
    if (typeof fn === 'function') {
      return fn(...args);
    }
    return undefined;
  }

  /** 获取所有绑定键 */
  getKeys(): string[] {
    return Array.from(this.bindings.keys());
  }

  /** 清空所有绑定 */
  clear(): void {
    this.bindings.clear();
  }
}

// ==================== 消息发送器 ====================

/** 消息发送器 */
class MessageSender {
  constructor(
    private messageBus: MessageBus,
    private bindingManager: BindingManager
  ) {}

  /** 发送消息（支持多种通信方式） */
  send(type: string, content: any, localHandler?: () => void): void {
    // 1. 优先通过comiEventBus发送消息到其他窗口/组件
    if (this.messageBus.hasComiEventBusIntegration()) {
      const comiEventBus = this.messageBus.getComiEventBus();
      if (comiEventBus) {
        comiEventBus.$emit(type, content);
      }
    } else {
      // 2. 回退到MessageBus发送消息
      this.messageBus.publish(type, content);
    }

    // 3. 执行本地处理逻辑（不管消息是否发送成功都要执行）
    if (localHandler) {
      localHandler();
    }
  }

  /** 发送业务消息并执行本地绑定方法 */
  sendBusinessMessage(type: string, content: any, bindingKey?: string): void {
    this.send(type, content, () => {
      if (bindingKey) {
        this.bindingManager.call(bindingKey, content);
      }
    });
  }
}

// ==================== 核心业务SDK ====================

/** 业务SDK类 */
export class BusinessSDK {
  private messageBus: MessageBus;
  private eventManager: EventManager;
  private bindingManager: BindingManager;
  private messageSender: MessageSender;
  private readonly config: BusinessConfig;
  private readonly preConfig: PreConfig;
  private pendingRefreshTimers: Map<string, NodeJS.Timeout> = new Map();

  private isInitialized = false;
  private initPromise: Promise<void>;

  constructor(config: BusinessConfig = {}, sdkOptions: SDKOptions = {}) {
    this.config = { autoInit: true, ...config };
    this.preConfig = this.loadPreConfig();
    
    this.messageBus = new MessageBus(sdkOptions);
    this.eventManager = new EventManager();
    this.bindingManager = new BindingManager();
    this.messageSender = new MessageSender(this.messageBus, this.bindingManager);

    // 异步初始化
    this.initPromise = this.initialize();
    
    this.setupMessageListeners();
    this.setupComiEventBusIntegration();
  }

  private loadPreConfig(): PreConfig {
    return (window.top as any)?.COMI_ASSISTANT?.preOption || {
      defaultAssistId: '',
      hideInputBoxAssistIds: [],
      defaultSendMsg: false,
      isPortal: undefined,
      expandDoubleContainer: undefined,
    };
  }

  /** 通用回调处理函数 */
  private executeCallback(data: any, actionName: string): void {
    if (data && typeof data.callback === 'function') {
      try {
        data.callback();
      } catch (e) {
        console.error(`执行 ${actionName} 回调时出错:`, e);
      }
    }
  }

  private setupMessageListeners(): void {
    // 监听来自其他窗口的业务事件
    this.messageBus.subscribe('businessEvent', (data: { event: string; args: any[] }) => {
      console.log('收到业务事件:', data.event);
      this.eventManager.emit(data.event, ...data.args);
    });

    // 监听通用通信消息
    this.messageBus.subscribe('communication', (data: any) => {
      console.log('收到通信消息:', data);
      this.eventManager.emit('message', data);
    });

    // 监听刷新机制消息
    this.messageBus.subscribe('refreshMechanism', (refreshData: any) => {
      console.log('BusinessSDK: 收到刷新机制消息', refreshData);
      
      // 触发特定类型的刷新事件
      if (refreshData.type) {
        this.eventManager.emit(`refresh:${refreshData.type}`, refreshData);
      }
      
      // 同时触发全局刷新事件
      this.eventManager.emit('refreshMechanism:all', refreshData);
    });

    // ==================== 监听具体业务消息（支持 comiEventBus 调用）====================
    
    // 监听发送消息请求
    this.messageBus.subscribe('sendMsg', (data: { value: string; option?: any; callback?: any }) => {
      console.log('BusinessSDK: 收到 sendMsg 消息', data);
      this.handleExternalSendMsg(data.value, data.option);
      this.executeCallback(data, 'sendMsg');
    });

    // 监听重定向助手请求
    this.messageBus.subscribe('redirectAssistId', (data: { id: string; defaultSendMsg: boolean; callback?: any }) => {
      console.log('BusinessSDK: 收到 redirectAssistId 消息', data);
      this.handleExternalRedirectAssistId(data.id, data.defaultSendMsg, data.callback);
      // 移除重复的回调执行，由 handleExternalRedirectAssistId 负责处理回调
      this.executeCallback(data, 'redirectAssistId');
    });

    // 监听隐藏输入框请求
    this.messageBus.subscribe('hideInputBox', (data: { value: boolean; callback?: any }) => {
      console.log('BusinessSDK: 收到 hideInputBox 消息', data);
      this.handleExternalHideInputBox(data.value);
      this.executeCallback(data, 'hideInputBox');
    });

    // 监听推送自定义卡片请求
    this.messageBus.subscribe('pushCustomCard', (data: { value: any; callback?: any }) => {
      console.log('BusinessSDK: 收到 pushCustomCard 消息', data);
      this.handleExternalPushCustomCard(data);
      this.executeCallback(data, 'pushCustomCard');
    });

    // 监听展开窗口请求
    this.messageBus.subscribe('expand', (data: any) => {
      console.log('BusinessSDK: 收到 expand 消息', data);
      this.handleExternalExpand();
      this.executeCallback(data, 'expand');
    });

    // 监听折叠窗口请求
    this.messageBus.subscribe('collapse', (data: any) => {
      console.log('BusinessSDK: 收到 collapse 消息', data);
      this.handleExternalCollapse();
      this.executeCallback(data, 'collapse');
    });

    // 监听关闭窗口请求
    this.messageBus.subscribe('close', (data: any) => {
      console.log('BusinessSDK: 收到 close 消息', data);
      this.handleExternalClose();
      this.executeCallback(data, 'close');
    });

    // 监听来自外部的方法请求（使用通用监听器）
    const methodNames = ['handleTagClick', 'openPersonalInfo', 'openDialog', 'openWin'];
    methodNames.forEach(methodName => {
      this.messageBus.subscribe(methodName, (data: { eventData: any; callback?: any }) => {
        console.log(`BusinessSDK: 收到外部 ${methodName} 消息`, data);
        this.handleExternalMethod(methodName, data.eventData, data.callback);
        this.executeCallback(data, methodName);
      });
    });
  }

  private setupComiEventBusIntegration(): void {
    if (this.messageBus.hasComiEventBusIntegration()) {
      console.log('BusinessSDK 已集成 comiEventBus');
      
      // 监听comiEventBus的ready事件
      this.messageBus.onComiEventBusEvent('ready', (data: any) => {
        console.log('comiEventBus ready 事件触发');
        this.eventManager.emit('comiEventBusReady');
      });
      
      // 监听comiEventBus的sourceReady事件
      this.messageBus.onComiEventBusEvent('sourceReady', (data: any) => {
        console.log('comiEventBus sourceReady 事件触发');
        this.eventManager.emit('comiEventBusSourceReady');
      });

      // 监听 comiEventBus 的业务事件
      this.messageBus.onComiEventBusEvent('pushCustomCard', (data: any) => {
        console.log('BusinessSDK: 收到 comiEventBus pushCustomCard 事件', data);
        this.handleExternalPushCustomCard(data.value || data);
      });

      this.messageBus.onComiEventBusEvent('sendMsg', (data: any) => {
        console.log('BusinessSDK: 收到 comiEventBus sendMsg 事件', data);
        this.handleExternalSendMsg(data.value || data.message || data, data.option);
      });

      this.messageBus.onComiEventBusEvent('redirectAssistId', (data: any) => {
        console.log('BusinessSDK: 收到 comiEventBus redirectAssistId 事件', data);
        this.handleExternalRedirectAssistId(data.id, data.defaultSendMsg, data.callback);
      });

      this.messageBus.onComiEventBusEvent('hideInputBox', (data: any) => {
        console.log('BusinessSDK: 收到 comiEventBus hideInputBox 事件', data);
        this.handleExternalHideInputBox(data.value || data);
      });

      this.messageBus.onComiEventBusEvent('expand', (data: any) => {
        console.log('BusinessSDK: 收到 comiEventBus expand 事件', data);
        this.handleExternalExpand();
      });

      this.messageBus.onComiEventBusEvent('collapse', (data: any) => {
        console.log('BusinessSDK: 收到 comiEventBus collapse 事件', data);
        this.handleExternalCollapse();
      });

      this.messageBus.onComiEventBusEvent('close', (data: any) => {
        console.log('BusinessSDK: 收到 comiEventBus close 事件', data);
        this.handleExternalClose();
      });

    } else {
      console.warn('BusinessSDK 未找到 comiEventBus，使用 PostMessage 通信');
    }
  }

  private async initialize(): Promise<void> {
    try {
      // 等待底层MessageBus就绪
      await this.messageBus.waitForReady();
      
      this.isInitialized = true;
      console.log('BusinessSDK 初始化完成');
      
      // 向 comiEventBus 发送 SDK 初始化完成事件
      this.notifySDKReady();
      
    } catch (error) {
      console.error('BusinessSDK 初始化失败:', error);
      this.isInitialized = true; // 标记为已初始化，避免阻塞
    }
  }

  /** 通知 comiEventBus SDK 已初始化完成 */
  private notifySDKReady(): void {
    const comiEventBus = this.messageBus.getComiEventBus();
    if (comiEventBus) {
      comiEventBus.$emit('comiReady', {
        instanceId: this.messageBus.getInstanceId(),
        timestamp: Date.now(),
        sdk: this // 可选：传递SDK实例引用
      });
      console.log('已向 comiEventBus 发送 comiReady 事件');
    }
  }

  // ==================== 公共API - 初始化相关 ====================

  /** 等待SDK初始化完成 */
  async waitForInitialization(): Promise<void> {
    return this.initPromise;
  }

  /** 检查SDK是否已初始化 */
  isSDKInitialized(): boolean {
    return this.isInitialized;
  }

  // ==================== 公共API - 绑定管理 ====================

  /** 绑定功能对象 */
  bind(key: string, value: any): void {
    this.bindingManager.bind(key, value);
  }

  /** 获取绑定对象 */
  getBound(key: string): any {
    return this.bindingManager.get(key);
  }

  /** 移除绑定 */
  unbind(key: string): boolean {
    return this.bindingManager.unbind(key);
  }

  // ==================== 公共API - 事件管理 ====================

  /** 添加事件监听器 */
  addEventListener(event: string, handler: EventHandler): void {
    this.eventManager.on(event, handler);
  }

  /** 移除事件监听器 */
  removeEventListener(event: string, handler?: EventHandler): void {
    this.eventManager.off(event, handler);
  }

  /** 触发事件 */
  emitEvent(event: string, ...args: any[]): void {
    // 本地触发
    this.eventManager.emit(event, ...args);
    
    // 通过MessageBus发送到其他窗口
    this.messageBus.publish('businessEvent', { event, args });
  }

  // ==================== 公共API - 业务功能 ====================

  /** SDK-下次不再显示 */
  async hideNextTime(): Promise<void> {
    this.messageBus.publish('hideNextTime', {});
    await this.bindingManager.call('hideNextTime');
  }

  /** 菜单操作 */
  menuAction(item: any): void {
    this.messageSender.sendBusinessMessage('menuAction', { item }, 'menuAction');
  }

  // ==================== 刷新机制 (refreshMechanism) ====================

  /** 发送刷新消息 */
  emitRefresh(refreshType: string, data?: any): void {
    // 防重复发送：清除之前的定时器
    if (this.pendingRefreshTimers.has(refreshType)) {
      clearTimeout(this.pendingRefreshTimers.get(refreshType)!);
      console.log(`BusinessSDK: 取消之前的 ${refreshType} 刷新`);
    }

    const refreshData = {
      type: refreshType,
      data: data,
      timestamp: Date.now(),
      source: 'refreshMechanism'
    };
    
    console.log('BusinessSDK: 发送刷新消息', refreshData);
    
    // 延迟发送，避免重复触发
    const timer = setTimeout(() => {
      // 直接通过EventManager发送（最简单直接的方式）
      this.eventManager.emit(`refresh:${refreshType}`, refreshData);
      
      // 通过comiEventBus发送到其他窗口（如果可用）
      if (this.messageBus.hasComiEventBusIntegration()) {
        const comiEventBus = this.messageBus.getComiEventBus();
        if (comiEventBus) {
          comiEventBus.$emit('refreshMechanism', refreshData);
        }
      }
      
      // 清理定时器记录
      this.pendingRefreshTimers.delete(refreshType);
    }, 50); // 50ms防抖
    
    // 记录定时器
    this.pendingRefreshTimers.set(refreshType, timer);
  }

  /** 监听刷新消息 */
  onRefresh(refreshType: string, handler: (data: any) => void): void {
    console.log('BusinessSDK: 注册刷新监听器', refreshType);
    
    // 检查是否已经注册过相同的监听器（防止重复注册）
    const eventType = `refresh:${refreshType}`;
    const existingListeners = this.eventManager.getListeners(eventType);
    
    if (existingListeners && existingListeners.has(handler)) {
      console.warn(`BusinessSDK: 监听器已存在，跳过重复注册 ${refreshType}`);
      return;
    }
    
    // 监听特定类型的刷新事件
    this.eventManager.on(eventType, handler);
  }

  /** 取消监听刷新消息 */
  offRefresh(refreshType: string, handler?: (data: any) => void): void {
    console.log('BusinessSDK: 取消刷新监听器', refreshType);
    
    this.eventManager.off(`refresh:${refreshType}`, handler);
  }

  /** 监听所有刷新消息 */
  onAllRefresh(handler: (refreshData: any) => void): void {
    console.log('BusinessSDK: 注册全局刷新监听器');
    
    this.eventManager.on('refreshMechanism:all', handler);
  }

  /** 取消监听所有刷新消息 */
  offAllRefresh(handler?: (data: any) => void): void {
    console.log('BusinessSDK: 取消全局刷新监听器');
    
    this.eventManager.off('refreshMechanism:all', handler);
  }

  /** 停止刷新机制 - 清理所有刷新相关的监听器 */
  stopRefreshMechanism(): void {
    console.log('BusinessSDK: 停止刷新机制，清理所有相关监听器');
    
    // 获取所有刷新相关的事件
    const allEvents = this.eventManager.getEvents();
    const refreshEvents = allEvents.filter(event => 
      event.startsWith('refresh:') || event === 'refreshMechanism:all'
    );
    
    // 清理所有刷新相关的监听器
    refreshEvents.forEach(event => {
      this.eventManager.off(event);
    });
    
    console.log(`BusinessSDK: 已清理 ${refreshEvents.length} 个刷新相关监听器`);
  }

  /** 重置刷新机制 - 清理EventManager监听器和待处理的刷新定时器 */
  resetRefreshMechanism(): void {
    console.log('BusinessSDK: 重置刷新机制');
    
    // 清理EventManager的监听器
    this.stopRefreshMechanism();
    
    // 清理所有待处理的刷新定时器
    const timerCount = this.pendingRefreshTimers.size;
    this.pendingRefreshTimers.forEach((timer, refreshType) => {
      clearTimeout(timer);
      console.log(`BusinessSDK: 清理待处理的 ${refreshType} 刷新`);
    });
    this.pendingRefreshTimers.clear();
    
    console.log(`BusinessSDK: 刷新机制重置完成，清理了 ${timerCount} 个待处理的刷新`);
  }

  /** 获取刷新机制状态 */
  getRefreshMechanismStatus(): {
    registeredTypes: string[];
    totalListeners: number;
    supportsComiEventBus: boolean;
  } {
    const events = this.eventManager.getEvents();
    const refreshEvents = events.filter(event => event.startsWith('refresh:'));
    
    return {
      registeredTypes: refreshEvents.map(event => event.replace('refresh:', '')),
      totalListeners: refreshEvents.length,
      supportsComiEventBus: this.messageBus.hasComiEventBusIntegration()
    };
  }

  /** SDK-输入框隐藏 */
  hideInputBox(value: boolean): void {
    this.messageBus.publish('hideInputBox', { value });
    this.bindingManager.call('hideInputBox', value);
  }

  /** 重定向到助手 */
  redirectAssistId(id: string, defaultSendMsg: boolean, callback?: any): void {
    console.log('BusinessSDK: redirectAssistId 被调用', { id, defaultSendMsg, hasCallback: !!callback });
    
    // 发送消息到事件总线（不包含函数）
    this.messageBus.publish('redirectAssistId', { id, defaultSendMsg });
    
    // 直接调用绑定的方法，传递完整参数
    this.bindingManager.call('redirectToAssit', id, defaultSendMsg, callback);
  }

  /** SDK-推送输入卡片 */
  pushCustomCard(value: string): void {
    const cardData = value || {};
    this.messageBus.publish('pushCustomCard', { value: cardData });
    this.bindingManager.call('pushCustomCard', cardData);
  }

  /** 设置发送消息 */
  setSendMsg(value: string, option?: any): void {
    this.messageSender.sendBusinessMessage('setSendMsg', { value, option }, 'setSendMsg');
  }

  /** 发送消息 */
  sendMsg(value: string, option?: any): void {
    this.messageSender.send('sendMsg', { value, option }, () => {
      // 本地处理逻辑：先停止当前消息，再发送新消息
      const stopMsg = this.bindingManager.get('stopMsg');
      if (typeof stopMsg === 'function') {
        stopMsg(() => {
          console.log("sendMsgHandel2:");
          this.bindingManager.call('sendMsgHandel', value, option);
        });
      }
    });
  }

  // ==================== 菜单操作方法 ====================

  /** 展开/放大窗口 */
  expand(right: string, width: string): void {
    console.log('BusinessSDK: expand 方法被调用');
    
    // 1. 发送消息到事件总线
    this.messageSender.send('expand', {});
    
    // 2. 调用外部系统的expand方法
    const external = window?.COMI_ASSISTANT || window.top?.COMI_ASSISTANT;
    if (external && typeof external.expand === 'function') {
      external.expand(right, width);
    } else {
      console.warn('BusinessSDK: 未找到外部expand方法');
    }
  }

  /** 折叠/缩小窗口 */
  collapse(): void {
    console.log('BusinessSDK: collapse 方法被调用');
    
    // 1. 发送消息到事件总线  
    this.messageSender.send('collapse', {});
    
    // 2. 调用外部系统的collapse方法
    const external = window?.COMI_ASSISTANT || window.top?.COMI_ASSISTANT;
    if (external && typeof external.collapse === 'function') {
      external.collapse();
    } else {
      console.warn('BusinessSDK: 未找到外部collapse方法');
    }
  }

  /** 关闭窗口 */
  close(): void {
    console.log('BusinessSDK: close 方法被调用');
    
    // 1. 发送消息到事件总线
    this.messageSender.send('close', {});
    
    // 2. 调用外部系统的close方法
    const external = window?.COMI_ASSISTANT || window.top?.COMI_ASSISTANT;
    if (external && typeof external.close === 'function') {
      external.close();
    } else {
      console.warn('BusinessSDK: 未找到外部close方法');
    }
  }

  // ==================== 外部消息处理方法（支持 comiEventBus 调用）====================

  /** 处理外部发送消息请求 */
  private handleExternalSendMsg(value: string, option?: any): void {
    // 本地处理逻辑：先停止当前消息，再发送新消息
    const stopMsg = this.bindingManager.get('stopMsg');
    if (typeof stopMsg === 'function') {
      stopMsg(() => {
        console.log("handleExternalSendMsg: 调用 sendMsgHandel");
        this.bindingManager.call('sendMsgHandel', value, option);
      });
    } else {
      // 如果没有 stopMsg 绑定，直接调用 sendMsgHandel
      this.bindingManager.call('sendMsgHandel', value, option);
    }
  }

  /** 处理外部重定向助手请求 */
  private handleExternalRedirectAssistId(id: string, defaultSendMsg: boolean, callback?: any): void {
    // 直接调用绑定的方法，传递完整参数
    this.bindingManager.call('redirectToAssit', id, defaultSendMsg, callback);
  }

  /** 处理外部隐藏输入框请求 */
  private handleExternalHideInputBox(value: boolean): void {
    this.bindingManager.call('hideInputBox', value);
  }

  /** 处理外部推送自定义卡片请求 */
  private handleExternalPushCustomCard(value: any): void {
    const cardData = value || {};
    this.bindingManager.call('pushCustomCard', cardData);
  }

  /** 处理外部展开窗口请求 */
  private handleExternalExpand(): void {
    // 调用外部系统的expand方法
    const external = window?.COMI_ASSISTANT || window.top?.COMI_ASSISTANT;
    if (external && typeof external.expand === 'function') {
      // 增加您需要的参数
      external.expand('0', '450px'); // 或者其他您需要的参数
    } else {
      console.warn('BusinessSDK: 未找到外部expand方法');
    }
  }

  /** 处理外部折叠窗口请求 */
  private handleExternalCollapse(): void {
    // 调用外部系统的collapse方法
    const external = window?.COMI_ASSISTANT || window.top?.COMI_ASSISTANT;
    if (external && typeof external.collapse === 'function') {
      external.collapse();
    } else {
      console.warn('BusinessSDK: 未找到外部collapse方法');
    }
  }

  /** 处理外部关闭窗口请求 */
  private handleExternalClose(): void {
    // 调用外部系统的close方法
    const external = window?.COMI_ASSISTANT || window.top?.COMI_ASSISTANT;
    if (external && typeof external.close === 'function') {
      external.close();
    } else {
      console.warn('BusinessSDK: 未找到外部close方法');
    }
  }



  // ==================== 公共API - ISCOMI相关 ====================

  /** 获取ISCOMI标识 */
  getISCOMI(): boolean | null {
    return this.messageBus.getISCOMI();
  }

  /** 设置ISCOMI标识 */
  setISCOMI(value: boolean): void {
    this.messageBus.setISCOMI(value);
  }

  /** 获取嵌套关系描述 */
  getNestingDescription(): string {
    return this.messageBus.getNestingDescription();
  }

  /** 获取系统状态 */
  getSystemStatus(): {
    systemId: string;
    iscomi: boolean | null;
  } {
    return {
      systemId: this.getSystemId(),
      iscomi: this.messageBus.getISCOMI()
    };
  }

  // ==================== 公共API - comiEventBus相关 ====================

  /** 获取comiEventBus实例 */
  getComiEventBus(): any {
    return this.messageBus.getComiEventBus();
  }

  /** 检查comiEventBus集成状态 */
  hasComiEventBusIntegration(): boolean {
    return this.messageBus.hasComiEventBusIntegration();
  }

  /** 监听comiEventBus事件 */
  onComiEventBusEvent(eventType: string, handler: (content: any) => void): void {
    this.messageBus.onComiEventBusEvent(eventType, handler);
  }

  /** 取消监听comiEventBus事件 */
  offComiEventBusEvent(eventType: string, handler?: (content: any) => void): void {
    this.messageBus.offComiEventBusEvent(eventType, handler);
  }

  /** 发送消息 */
  sendMessage(type: string, content: any): void {
    this.messageBus.publish(type, content);
  }

  // ==================== 公共API - 实用方法 ====================

  /** 获取底层MessageBus实例 */
  getMessageBus(): MessageBus {
    return this.messageBus;
  }

  /** 获取配置 */
  getConfig(): BusinessConfig {
    return this.config;
  }

  /** 获取预配置 */
  getPreConfig(): PreConfig {
    return this.preConfig;
  }

  /** 获取系统标识 */
  getSystemId(): string {
    return this.messageBus.getSystemId();
  }

  /** 获取形态参数 - 是否为门户模式 */
  getIsPortal(): boolean | null {
    // 方式1：从绑定管理器获取（推荐方式）
    const boundIsPortal = this.bindingManager.get('isPortal');
    if (typeof boundIsPortal === 'boolean') {
      console.log('BusinessSDK: 从绑定管理器获取 isPortal', boundIsPortal);
      return boundIsPortal;
    }
    
    // 方式2：从 comiEventBus 获取（如果可用）
    const topWindow = window.top || window;
    const comiEventBus = (topWindow as any).comiEventBus;
    if (comiEventBus && typeof comiEventBus.isPortal === 'boolean') {
      console.log('BusinessSDK: 从 comiEventBus 获取 isPortal', comiEventBus.isPortal);
      return comiEventBus.isPortal;
    }
    
    // 方式3：从外部 COMI_ASSISTANT 对象获取
    const externalCOMI = (topWindow as any).COMI_ASSISTANT;
    if (externalCOMI && typeof externalCOMI.isPortal === 'boolean') {
      console.log('BusinessSDK: 从外部对象获取 isPortal', externalCOMI.isPortal);
      return externalCOMI.isPortal;
    }
    
    // 方式4：从预配置中获取
    if (typeof this.preConfig.isPortal === 'boolean') {
      console.log('BusinessSDK: 从预配置获取 isPortal', this.preConfig.isPortal);
      return this.preConfig.isPortal;
    }
    
    // 方式5：从配置中获取
    if (typeof this.config.isPortal === 'boolean') {
      console.log('BusinessSDK: 从配置获取 isPortal', this.config.isPortal);
      return this.config.isPortal;
    }
    
    console.warn('BusinessSDK: 无法获取 isPortal 参数，请确保已正确绑定');
    return null;
  }

  /** 获取双屏容器展开状态参数 */
  getExpandDoubleContainer(): boolean | null {
    // 方式1：从绑定管理器获取（推荐方式）
    const boundExpandDoubleContainer = this.bindingManager.get('expandDoubleContainer');
    if (typeof boundExpandDoubleContainer === 'boolean') {
      console.log('BusinessSDK: 从绑定管理器获取 expandDoubleContainer', boundExpandDoubleContainer);
      return boundExpandDoubleContainer;
    }
    
    // 方式2：从 comiEventBus 获取（如果可用）
    const topWindow = window.top || window;
    const comiEventBus = (topWindow as any).comiEventBus;
    if (comiEventBus && typeof comiEventBus.expandDoubleContainer === 'boolean') {
      console.log('BusinessSDK: 从 comiEventBus 获取 expandDoubleContainer', comiEventBus.expandDoubleContainer);
      return comiEventBus.expandDoubleContainer;
    }
    
    // 方式3：从外部 COMI_ASSISTANT 对象获取
    const externalCOMI = (topWindow as any).COMI_ASSISTANT;
    if (externalCOMI && typeof externalCOMI.expandDoubleContainer === 'boolean') {
      console.log('BusinessSDK: 从外部对象获取 expandDoubleContainer', externalCOMI.expandDoubleContainer);
      return externalCOMI.expandDoubleContainer;
    }
    
    // 方式4：从预配置中获取
    if (typeof this.preConfig.expandDoubleContainer === 'boolean') {
      console.log('BusinessSDK: 从预配置获取 expandDoubleContainer', this.preConfig.expandDoubleContainer);
      return this.preConfig.expandDoubleContainer;
    }
    
    // 方式5：从配置中获取
    if (typeof this.config.expandDoubleContainer === 'boolean') {
      console.log('BusinessSDK: 从配置获取 expandDoubleContainer', this.config.expandDoubleContainer);
      return this.config.expandDoubleContainer;
    }
    
    console.warn('BusinessSDK: 无法获取 expandDoubleContainer 参数，请确保已正确绑定');
    return null;
  }

  /** 设置形态参数 - 是否为门户模式 */
  setIsPortal(isPortal: boolean): void {
    console.log('BusinessSDK: 设置 isPortal 参数', isPortal);
    
    // 1. 绑定到绑定管理器
    this.bindingManager.bind('isPortal', isPortal);
    
    // 2. 同步到外部 COMI_ASSISTANT 对象
    const topWindow = window.top || window;
    let externalCOMI = (topWindow as any).COMI_ASSISTANT;
    if (!externalCOMI) {
      externalCOMI = (topWindow as any).COMI_ASSISTANT = {};
    }
    externalCOMI.isPortal = isPortal;
    
    // 3. 同步到 comiEventBus 对象（如果可用）
    const comiEventBus = (topWindow as any).comiEventBus;
    if (comiEventBus) {
      comiEventBus.isPortal = isPortal;
      console.log('BusinessSDK: 已同步 isPortal 到 comiEventBus', isPortal);
    }
    
    // 4. 通过消息总线通知其他组件
    this.messageBus.publish('isPortalChanged', { isPortal });
    
    // 5. 触发事件
    this.eventManager.emit('isPortalChanged', isPortal);
  }

  /** 设置双屏容器展开状态参数 */
  setExpandDoubleContainer(expandDoubleContainer: boolean): void {
    console.log('BusinessSDK: 设置 expandDoubleContainer 参数', expandDoubleContainer);
    
    // 1. 绑定到绑定管理器
    this.bindingManager.bind('expandDoubleContainer', expandDoubleContainer);
    
    // 2. 同步到外部 COMI_ASSISTANT 对象
    const topWindow = window.top || window;
    let externalCOMI = (topWindow as any).COMI_ASSISTANT;
    if (!externalCOMI) {
      externalCOMI = (topWindow as any).COMI_ASSISTANT = {};
    }
    externalCOMI.expandDoubleContainer = expandDoubleContainer;
    
    // 3. 同步到 comiEventBus 对象（如果可用）
    const comiEventBus = (topWindow as any).comiEventBus;
    if (comiEventBus) {
      comiEventBus.expandDoubleContainer = expandDoubleContainer;
      console.log('BusinessSDK: 已同步 expandDoubleContainer 到 comiEventBus', expandDoubleContainer);
    }
    
    // 4. 通过消息总线通知其他组件
    this.messageBus.publish('expandDoubleContainerChanged', { expandDoubleContainer });
    
    // 5. 触发事件
    this.eventManager.emit('expandDoubleContainerChanged', expandDoubleContainer);
  }

  /** 获取形态描述 */
  getFormDescription(): string {
    const isPortal = this.getIsPortal();
    if (isPortal === null) {
      return '未知形态';
    }
    return isPortal ? '门户模式' : '智能助手模式';
  }

  /** 检查是否为门户模式 */
  isPortalMode(): boolean {
    return this.getIsPortal() === true;
  }

  /** 检查是否为智能助手模式 */
  isCopilotMode(): boolean {
    return this.getIsPortal() === false;
  }

  /** 检查双屏容器是否展开 */
  isExpandDoubleContainer(): boolean {
    return this.getExpandDoubleContainer() === true;
  }

  /** 检查双屏容器是否折叠 */
  isCollapseDoubleContainer(): boolean {
    return this.getExpandDoubleContainer() === false;
  }

  /** 绑定外部的isPortal参数到SDK */
  bindIsPortal(): void {
    console.log('BusinessSDK: 尝试绑定 isPortal 参数');
    
    const topWindow = window.top || window;
    
    // 1. 从 comiEventBus 获取（如果可用）
    const comiEventBus = (topWindow as any).comiEventBus;
    if (comiEventBus) {
      // 如果 comiEventBus 上已经有 isPortal 数据，直接使用
      if (typeof comiEventBus.isPortal === 'boolean') {
        this.bindingManager.bind('isPortal', comiEventBus.isPortal);
        console.log('✅ BusinessSDK: 已从 comiEventBus 获取 isPortal 参数', comiEventBus.isPortal);
      }
      
      // 监听 comiEventBus 的 isPortal 变化事件
      if (typeof comiEventBus.$on === 'function') {
        comiEventBus.$on('isPortalChanged', (data: { isPortal: boolean }) => {
          console.log('BusinessSDK: 收到 comiEventBus isPortalChanged 事件', data);
          // 直接更新绑定值，不触发新的事件
          this.bindingManager.bind('isPortal', data.isPortal);
        });
        console.log('✅ BusinessSDK: 已监听 comiEventBus 的 isPortalChanged 事件');
      }
    }
    
    // 2. 从外部对象获取
    const externalCOMI = (topWindow as any).COMI_ASSISTANT;
    if (externalCOMI && typeof externalCOMI.isPortal === 'boolean') {
      this.bindingManager.bind('isPortal', externalCOMI.isPortal);
      console.log('✅ BusinessSDK: 已从外部对象绑定 isPortal 参数');
    }
    
    // 3. 检查是否有Vue应用实例
    if ((topWindow as any).Vue || (topWindow as any).app) {
      console.log('BusinessSDK: 检测到Vue环境，等待isPortal参数绑定');
      
      // 监听isPortal参数的变化
      this.messageBus.subscribe('bindIsPortal', (data: { isPortal: boolean }) => {
        console.log('BusinessSDK: 收到 bindIsPortal 消息', data);
        this.bindingManager.bind('isPortal', data.isPortal);
      });
    }
  }

  /** 绑定外部的expandDoubleContainer参数到SDK */
  bindExpandDoubleContainer(): void {
    console.log('BusinessSDK: 尝试绑定 expandDoubleContainer 参数');
    
    const topWindow = window.top || window;
    
    // 1. 从 comiEventBus 获取（如果可用）
    const comiEventBus = (topWindow as any).comiEventBus;
    if (comiEventBus) {
      // 如果 comiEventBus 上已经有 expandDoubleContainer 数据，直接使用
      if (typeof comiEventBus.expandDoubleContainer === 'boolean') {
        this.bindingManager.bind('expandDoubleContainer', comiEventBus.expandDoubleContainer);
        console.log('✅ BusinessSDK: 已从 comiEventBus 获取 expandDoubleContainer 参数', comiEventBus.expandDoubleContainer);
      }
      
      // 监听 comiEventBus 的 expandDoubleContainer 变化事件
      if (typeof comiEventBus.$on === 'function') {
        comiEventBus.$on('expandDoubleContainerChanged', (data: { expandDoubleContainer: boolean }) => {
          console.log('BusinessSDK: 收到 comiEventBus expandDoubleContainerChanged 事件', data);
          // 直接更新绑定值，不触发新的事件
          this.bindingManager.bind('expandDoubleContainer', data.expandDoubleContainer);
        });
        console.log('✅ BusinessSDK: 已监听 comiEventBus 的 expandDoubleContainerChanged 事件');
      }
    }
    
    // 2. 从外部对象获取
    const externalCOMI = (topWindow as any).COMI_ASSISTANT;
    if (externalCOMI && typeof externalCOMI.expandDoubleContainer === 'boolean') {
      this.bindingManager.bind('expandDoubleContainer', externalCOMI.expandDoubleContainer);
      console.log('✅ BusinessSDK: 已从外部对象绑定 expandDoubleContainer 参数');
    }
    
    // 3. 检查是否有Vue应用实例
    if ((topWindow as any).Vue || (topWindow as any).app) {
      console.log('BusinessSDK: 检测到Vue环境，等待expandDoubleContainer参数绑定');
      
      // 监听expandDoubleContainer参数的变化
      this.messageBus.subscribe('bindExpandDoubleContainer', (data: { expandDoubleContainer: boolean }) => {
        console.log('BusinessSDK: 收到 bindExpandDoubleContainer 消息', data);
        this.bindingManager.bind('expandDoubleContainer', data.expandDoubleContainer);
      });
    }
  }

  /** 获取调试信息 */
  getDebugInfo() {
    return {
      isInitialized: this.isInitialized,
      config: this.config,
      preConfig: this.preConfig,
      events: this.eventManager.getEvents(),
      bindings: this.bindingManager.getKeys(),
      systemStatus: this.getSystemStatus(),
      queueStatus: this.messageBus.getQueueStatus(),
      isPortal: this.getIsPortal(),
      formDescription: this.getFormDescription(),
      expandDoubleContainer: this.getExpandDoubleContainer(),
      isExpandDoubleContainer: this.isExpandDoubleContainer()
    };
  }

  /** 销毁SDK */
  destroy(): void {
    // 清理所有待处理的刷新定时器
    this.pendingRefreshTimers.forEach((timer) => {
      clearTimeout(timer);
    });
    this.pendingRefreshTimers.clear();
    
    this.eventManager.removeAllListeners();
    this.bindingManager.clear();
    console.log('BusinessSDK 已销毁');
  }

  /** 获取OA系统URL */
  getOaUrl(params: { appType: string; linkId: string; clientType?: string }): string | undefined {
    // 方式1：从外部 COMI_ASSISTANT 对象获取
    const topWindow = window.top || window;
    const externalCOMI = (topWindow as any).COMI_ASSISTANT;
    
    if (externalCOMI && typeof externalCOMI.getOaUrl === 'function') {
      console.log('BusinessSDK: 调用外部getOaUrl方法', params);
      return externalCOMI.getOaUrl(params);
    }
    
    // 方式2：回退到绑定的方法
    const boundGetOaUrl = this.bindingManager.get('getOaUrl');
    if (typeof boundGetOaUrl === 'function') {
      console.log('BusinessSDK: 调用绑定的getOaUrl方法', params);
      return boundGetOaUrl(params);
    }
    
    console.warn('BusinessSDK: getOaUrl方法不可用');
    return undefined;
  }

  /** 绑定外部的getOaUrl方法到SDK */
  bindGetOaUrl(): void {
    this.bindExternalMethod('getOaUrl');
  }

  /** 一次性绑定所有外部方法 */
  bindAllExternalMethods(): void {
    const methodNames = ['handleTagClick', 'openPersonalInfo', 'openDialog', 'openWin', 'getOaUrl'];
    methodNames.forEach(methodName => {
      this.bindExternalMethod(methodName);
    });
  }

  /** 通用方法调用器 - 同时支持 comiEventBus 和传统回调 */
  private invokeMethod(methodName: string, data: any, callback?: (result: any) => void): any {
    console.log(`BusinessSDK: ${methodName} 被调用`, data);
    
    // 1. 通过 comiEventBus 发送消息（如果可用）
    try {
      const comiEventBus = this.getComiEventBus();
      if (comiEventBus && typeof comiEventBus.$emit === 'function') {
        console.log(`BusinessSDK: 通过 comiEventBus 发送 ${methodName} 事件`);
        comiEventBus.$emit(methodName, {
          eventData: data,
          timestamp: Date.now(),
          source: 'BusinessSDK',
          callback: callback
        });
      }
    } catch (error) {
      console.warn(`BusinessSDK: comiEventBus 发送消息失败:`, error);
    }

    // 2. 调用外部系统的方法（保持兼容性）
    const topWindow = window.top || window;
    const externalCOMI = (topWindow as any).COMI_ASSISTANT;
    
    if (externalCOMI && typeof externalCOMI[methodName] === 'function') {
      console.log(`BusinessSDK: 调用外部 ${methodName} 方法`);
      try {
        return externalCOMI[methodName](data, callback);
      } catch (error) {
        console.error(`BusinessSDK: 外部 ${methodName} 调用失败:`, error);
        // 如果外部调用失败，尝试执行回调
        if (callback && typeof callback === 'function') {
          callback(methodName === 'handleTagClick' ? data : null);
        }
      }
    } else {
      // 3. 如果没有外部方法，尝试调用绑定的方法
      const boundMethod = this.bindingManager.get(methodName);
      if (typeof boundMethod === 'function') {
        console.log(`BusinessSDK: 调用绑定的 ${methodName} 方法`);
        try {
          return boundMethod(data, callback);
        } catch (error) {
          console.error(`BusinessSDK: 绑定的 ${methodName} 调用失败:`, error);
          if (callback && typeof callback === 'function') {
            callback(methodName === 'handleTagClick' ? data : null);
          }
        }
      } else {
        console.warn(`BusinessSDK: 未找到 ${methodName} 实现，直接执行回调`);
        // 如果都没有找到实现，直接执行回调
        if (callback && typeof callback === 'function') {
          callback(methodName === 'handleTagClick' ? data : null);
        }
      }
    }
    
    return null;
  }

  /** 通用绑定方法 */
  private bindExternalMethod(methodName: string): void {
    const topWindow = window.top || window;
    const externalCOMI = (topWindow as any).COMI_ASSISTANT;
    
    if (externalCOMI && typeof externalCOMI[methodName] === 'function') {
      this.bindingManager.bind(methodName, externalCOMI[methodName]);
      console.log(`✅ BusinessSDK: 已绑定${methodName}方法`);
    } else {
      console.warn(`⚠️ BusinessSDK: 未找到外部${methodName}方法`);
    }
  }

  /** 通用外部方法处理器 */
  private handleExternalMethod(methodName: string, data: any, callback?: (result: any) => void): any {
    // 调用外部系统的方法
    const external = window?.COMI_ASSISTANT || window.top?.COMI_ASSISTANT;
    if (external && typeof external[methodName] === 'function') {
      return external[methodName](data, callback);
    } else {
      // 尝试调用绑定的方法
      const boundMethod = this.bindingManager.get(methodName);
      if (typeof boundMethod === 'function') {
        return boundMethod(data, callback);
      } else {
        console.warn(`BusinessSDK: 未找到外部${methodName}方法`);
        // 如果都没有找到实现，直接执行回调
        if (callback && typeof callback === 'function') {
          callback(methodName === 'handleTagClick' ? data : null);
        }
      }
    }
    return null;
  }

  /** 处理标签点击事件 - 同时支持 comiEventBus 和传统回调 */
  handleTagClick(data: any, callback?: (oaData: any) => void): void {
    this.invokeMethod('handleTagClick', data, callback);
  }

  /** 绑定外部的handleTagClick方法到SDK */
  bindHandleTagClick(): void {
    this.bindExternalMethod('handleTagClick');
  }

  /** 打开个人信息 - 同时支持 comiEventBus 和传统回调 */
  openPersonalInfo(data: any, callback?: (result: any) => void): void {
    this.invokeMethod('openPersonalInfo', data, callback);
  }

  /** 绑定外部的openPersonalInfo方法到SDK */
  bindOpenPersonalInfo(): void {
    this.bindExternalMethod('openPersonalInfo');
  }

  /** 打开对话框 - 同时支持 comiEventBus 和传统回调 */
  openDialog(config: any, callback?: (result: any) => void): any {
    return this.invokeMethod('openDialog', config, callback);
  }

  /** 绑定外部的openDialog方法到SDK */
  bindOpenDialog(): void {
    this.bindExternalMethod('openDialog');
  }

  /** 打开窗口 - 同时支持 comiEventBus 和传统回调 */
  openWin(config: any, callback?: (result: any) => void): any {
    return this.invokeMethod('openWin', config, callback);
  }

  /** 绑定外部的openWin方法到SDK */
  bindOpenWin(): void {
    this.bindExternalMethod('openWin');
  }
}

// ==================== 回调执行器 ====================

/** 回调执行条件配置 */
interface CallbackConditions {
  checkInterval?: number;
  maxWaitTime?: number;
  readyChecker?: () => boolean;
  waitForDOM?: boolean;
  waitForWindow?: boolean;
}

/** 当条件满足时执行回调 */
async function executeCallbackWhenReady(
  callback: (sdk: BusinessSDK) => void,
  conditions: CallbackConditions = {},
  sdk: BusinessSDK
): Promise<void> {
  const {
    checkInterval = 100,
    maxWaitTime = 5000,
    waitForDOM = true,
    waitForWindow = true,
    readyChecker
  } = conditions;

  const startTime = Date.now();

  const defaultReadyChecker = (): boolean => {
    if (waitForDOM && document.readyState !== 'complete') return false;
    if (waitForWindow && !window.document) return false;
    if (!sdk.isSDKInitialized()) return false;
    return true;
  };

  const checker = readyChecker || defaultReadyChecker;

  return new Promise((resolve, reject) => {
    const check = () => {
      try {
        if (checker()) {
          callback(sdk);
          resolve();
          return;
        }

        if (Date.now() - startTime > maxWaitTime) {
          reject(new Error('回调执行超时'));
          return;
        }

        setTimeout(check, checkInterval);
      } catch (error) {
        reject(error);
      }
    };

    check();
  });
}

// ==================== 全局实例管理 ====================

/** 全局BusinessSDK实例 */
let businessSdkInstance: BusinessSDK | null = null;

/** 获取当前版本模式 */
function getViewMode(): string {
  const topWindow = (window.top || window) as any;
  const viewModeName = 'comiTargetVersion';
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(viewModeName) || 
         window.sessionStorage.getItem(viewModeName) || 
         topWindow[viewModeName] || 
         window.localStorage.getItem(viewModeName) || 
         
         '1.0';
}

/** 初始化业务SDK */
export function initBusinessSdk(config?: BusinessConfig, sdkOptions?: SDKOptions): BusinessSDK {
  if (businessSdkInstance) {
    console.warn('BusinessSDK已经初始化，返回现有实例');
    return businessSdkInstance;

  }

  businessSdkInstance = new BusinessSDK(config, sdkOptions);
  
  // 兼容老SDK：绑定到全局对象
  const topWindow = window.top || window;
  let COMI_ASSISTANT = topWindow.COMI_ASSISTANT;
  
  // 确保 COMI_ASSISTANT 对象存在
  if (!COMI_ASSISTANT) {
    COMI_ASSISTANT = topWindow.COMI_ASSISTANT = {};
    console.log('创建 COMI_ASSISTANT 对象');
  }
  
  if (typeof COMI_ASSISTANT === 'object') {
    COMI_ASSISTANT.sdk = businessSdkInstance;
    
    // 回调执行器 - 基于状态和事件的方式
    class CallbackExecutor {
      private callbacks: (() => void)[] = [];
      private singleCallback: ((sdk: any) => void) | null = null;
      private isExecuted = false;
      private isFromNewPreSdk = false; // 标记是否来自新的preSdk调用
      
      constructor() {
        this.setupExecutionTriggers();
      }
      
      addCallback(callback: () => void, isFromNew = false): void {
        if (this.shouldExecuteImmediately()) {
          console.log('SDK已就绪，立即执行回调');
          this.executeCallback(callback);
        } else {
          console.log('SDK未就绪，添加回调到队列');
          
          // 根据来源决定保存方式，避免重复
          if (isFromNew) {
            // 来自新preSdk的调用，使用队列模式
            this.callbacks.push(callback);
            this.isFromNewPreSdk = true;
          } else {
            // 来自劫持的旧preSdk调用，使用单回调模式（兼容原始逻辑）
            this.singleCallback = callback;
          }
        }
      }
      
      setSingleCallback(callback: (sdk: any) => void): void {
        this.singleCallback = callback;
      }
      
      private shouldExecuteImmediately(): boolean {
        return !!(businessSdkInstance?.isSDKInitialized()) && this.isSDKFullyReady();
      }
      
      private isSDKFullyReady(): boolean {
        // 检查SDK的所有必要功能是否都已绑定
        return !!(
          businessSdkInstance &&
          topWindow.COMI_ASSISTANT?.sdk &&
          topWindow.COMI_ASSISTANT.sdk.redirectAssistId &&
          topWindow.COMI_ASSISTANT.sdk.sendMsg
        );
      }
      
      private setupExecutionTriggers(): void {
        console.log('CallbackExecutor: 设置执行触发器');
        
        // 方式1：监听SDK初始化完成（最重要的触发点）
        if (businessSdkInstance) {
          businessSdkInstance.waitForInitialization().then(() => {
            console.log('CallbackExecutor: SDK初始化完成，触发检查');
            this.checkAndExecute();
          }).catch(error => {
            console.error('CallbackExecutor: SDK初始化失败', error);
          });
        }
        
        // 方式2：监听绑定完成事件
        this.waitForBindingsReady().then(() => {
          console.log('CallbackExecutor: 绑定检查完成，触发检查');
          this.checkAndExecute();
        }).catch(error => {
          console.error('CallbackExecutor: 绑定检查失败', error);
        });
        
        // 方式3：监听全局对象变化（主动通知机制）
        this.setupGlobalObjectWatcher();
        
        // 方式4：使用状态观察者作为后备方案
        this.setupStateObserver();
      }
      
      private setupGlobalObjectWatcher(): void {
        console.log('CallbackExecutor: 设置全局对象监听器');
        
        // 监听 COMI_ASSISTANT 对象的变化
        const topWindow = window.top || window;
        let lastSDKState = !!topWindow.COMI_ASSISTANT?.sdk;
        let lastPreSDKState = !!topWindow.COMI_ASSISTANT?.preSdk;
        
        // 使用 Object.defineProperty 监听对象属性变化
        if (topWindow.COMI_ASSISTANT) {
          this.watchObjectProperty(topWindow.COMI_ASSISTANT, 'sdk', (newValue) => {
            if (newValue && !lastSDKState) {
              console.log('CallbackExecutor: 检测到 COMI_ASSISTANT.sdk 已设置');
              lastSDKState = true;
              this.checkAndExecute();
            }
          });
          
          this.watchObjectProperty(topWindow.COMI_ASSISTANT, 'preSdk', (newValue) => {
            if (newValue && !lastPreSDKState) {
              console.log('CallbackExecutor: 检测到 COMI_ASSISTANT.preSdk 已设置');
              lastPreSDKState = true;
              this.checkAndExecute();
            }
          });
        }
        
        // 定期检查全局对象状态（低频率）
        const checkGlobalState = () => {
          const currentSDKState = !!topWindow.COMI_ASSISTANT?.sdk;
          const currentPreSDKState = !!topWindow.COMI_ASSISTANT?.preSdk;
          
          if ((currentSDKState && !lastSDKState) || (currentPreSDKState && !lastPreSDKState)) {
            console.log('CallbackExecutor: 全局状态检查发现变化');
            lastSDKState = currentSDKState;
            lastPreSDKState = currentPreSDKState;
            this.checkAndExecute();
          }
        };
        
        // 每500ms检查一次全局状态（作为监听器的补充）
        const globalCheckInterval = setInterval(() => {
          if (this.isExecuted) {
            clearInterval(globalCheckInterval);
            return;
          }
          checkGlobalState();
        }, 500);
      }
      
      private watchObjectProperty(obj: any, prop: string, callback: (newValue: any) => void): void {
        if (!obj || typeof obj !== 'object') return;
        
        try {
          let currentValue = obj[prop];
          
          // 使用 Object.defineProperty 创建属性监听器
          Object.defineProperty(obj, prop, {
            get() {
              return currentValue;
            },
            set(newValue) {
              if (newValue !== currentValue) {
                currentValue = newValue;
                callback(newValue);
              }
            },
            configurable: true,
            enumerable: true
          });
        } catch (error) {
          console.warn(`CallbackExecutor: 无法监听属性 ${prop}:`, error);
        }
      }
      
      private async waitForBindingsReady(): Promise<void> {
        return new Promise((resolve) => {
          const checkBindings = () => {
            if (this.isSDKFullyReady()) {
              resolve();
            } else {
              // 使用 requestAnimationFrame 而不是 setTimeout
              requestAnimationFrame(checkBindings);
            }
          };
          checkBindings();
        });
      }
      
      private setupStateObserver(): void {
        // 立即检查一次
        if (this.shouldExecuteImmediately()) {
          console.log('SDK状态检查：立即就绪，执行回调');
          this.checkAndExecute();
          return;
        }
        
        // 使用事件驱动 + 快速轮询的混合方式
        let checkCount = 0;
        const maxQuickChecks = 20; // 前20次快速检查（约2秒）
        const maxTotalChecks = 100; // 总共最多检查100次（约10秒）
        
        const performCheck = () => {
          checkCount++;
          
          if (this.shouldExecuteImmediately()) {
            // console.log(`SDK状态检查：第${checkCount}次检查成功，执行回调`);
            this.checkAndExecute();
            return;
          }
          
          if (checkCount >= maxTotalChecks) {
            console.warn('SDK就绪检查最终超时，强制执行回调');
            this.executeAllCallbacks();
            return;
          }
          
          // 前20次快速检查（每100ms一次），之后慢速检查（每500ms一次）
          const nextCheckDelay = checkCount <= maxQuickChecks ? 100 : 500;
      
          
          setTimeout(performCheck, nextCheckDelay);
        };
        
        // 启动检查
        console.log('SDK状态检查：启动事件驱动检查机制');
        performCheck();
      }
      
      private checkAndExecute(): void {
        if (this.isExecuted || !this.shouldExecuteImmediately()) {
          return;
        }
        
        console.log('SDK完全就绪，开始执行所有待处理的回调');
        this.executeAllCallbacks();
      }
      
      private executeAllCallbacks(): void {
        if (this.isExecuted) {
          return;
        }
        
        this.isExecuted = true;
        
        const callbacksToExecute = [...this.callbacks];
        const singleCallbackToExecute = this.singleCallback;
        
        // 智能执行策略：避免重复执行
        if (this.isFromNewPreSdk && callbacksToExecute.length > 0) {
          // 如果有新preSdk的回调，优先执行队列回调
          console.log(`准备执行 ${callbacksToExecute.length} 个队列回调`);
          callbacksToExecute.forEach((callback, index) => {
            try {
              console.log(`执行队列回调 ${index + 1}`);
              this.executeCallbackWithVersionCheck(callback);
            } catch (error) {
              console.error(`队列回调 ${index + 1} 执行失败:`, error);
            }
          });
        } else if (singleCallbackToExecute && typeof singleCallbackToExecute === 'function') {
          // 否则执行单回调（兼容原始逻辑）
          try {
            console.log('执行单回调（兼容模式）');
            
            // v1.0 版本：传递事件驱动的SDK对象
            if (getViewMode() === '1.0' && localStorage.getItem('comiContainerName') === 'v5') {
              console.log('执行单回调（v1.0兼容模式），传入事件驱动SDK对象');
              const v1SDK = this.createV1SDK();
              singleCallbackToExecute(v1SDK);
            } else {
              // 其他版本：传递原有的SDK实例
              singleCallbackToExecute(businessSdkInstance);
            }
          } catch (error) {
            console.error('单回调执行失败:', error);
          }
        } else if (callbacksToExecute.length > 0) {
          // 后备方案：执行队列回调
          console.log(`准备执行 ${callbacksToExecute.length} 个后备回调`);
          callbacksToExecute.forEach((callback, index) => {
            try {
              console.log(`执行后备回调 ${index + 1}`);
              this.executeCallbackWithVersionCheck(callback);
            } catch (error) {
              console.error(`后备回调 ${index + 1} 执行失败:`, error);
            }
          });
        }
        
        // 清空队列
        this.callbacks = [];
        this.singleCallback = null;
      }
      
      private executeCallback(callback: () => void): void {
        // 使用 requestAnimationFrame 确保在下一个渲染帧执行
        requestAnimationFrame(() => {
          try {
            callback();
          } catch (error) {
            console.error('回调执行失败:', error);
          }
        });
      }
      
      private executeCallbackWithVersionCheck(callback: () => void): void {
        // 使用 requestAnimationFrame 确保在下一个渲染帧执行
        requestAnimationFrame(() => {
          try {
            // v1.0 版本：传递事件驱动的SDK对象
            if (getViewMode() === '1.0' && localStorage.getItem('comiContainerName') === 'v5') {
              console.log('执行队列回调（v1.0兼容模式），传入事件驱动SDK对象');
              const v1SDK = this.createV1SDK();
              (callback as any).call(null, v1SDK);
            } else {
              // 其他版本：直接调用
              callback();
            }
          } catch (error) {
            console.error('回调执行失败:', error);
          }
        });
      }
      
      private createV1SDK(): any {
        const topWindow = window.top || window;
        const preConfig = businessSdkInstance?.getPreConfig() || {};
        
        return {
          // 添加 preConfig 属性，让 v1.0 代码能通过检查
          preConfig: preConfig,
          
          sendMsg: (value: string, option?: any) => {
            console.log('v1.0 SDK sendMsg 事件:', { value, option });
            
              if (topWindow.comiEventBus && topWindow.comiEventBus.$emit) {
                console.log('v1.0 SDK: comiEventBus 可用', {
                  hasEmit: !!topWindow.comiEventBus.$emit,
                  comiEventBus: topWindow.comiEventBus
                });
                
                // 获取预配置检查是否需要自动重定向
                const preConfig = businessSdkInstance?.getPreConfig();
                if (preConfig?.defaultAssistId && preConfig?.defaultSendMsg === true) {
                  console.log('v1.0 兼容：先发送重定向事件', {
                    id: preConfig.defaultAssistId,
                    hasCallback: true
                  });
                  
                  // 先重定向，后发送消息
                  const result = topWindow.comiEventBus.$emit('redirectAssistId', {
                    id: preConfig.defaultAssistId,
                    defaultSendMsg: true,
                    callback: () => {
                      console.log('v1.0 兼容：重定向完成，发送消息');
                      topWindow.comiEventBus.$emit('sendMsg', { value, option });
                    }
                  });
                  
                  console.log('v1.0 兼容：redirectAssistId事件发送结果', result);
                  
                  // 后备方案：如果事件系统有问题，直接调用BusinessSDK方法
                  setTimeout(() => {
                    console.log('v1.0 兼容：检查事件是否被处理，如未处理则使用后备方案');
                    if (businessSdkInstance) {
                      businessSdkInstance.redirectAssistId(preConfig.defaultAssistId!, true, () => {
                        console.log('v1.0 兼容：后备方案重定向完成，发送消息');
                        businessSdkInstance!.sendMsg(value, option);
                      });
                    }
                  }, 100); // 给事件系统100ms时间处理
                } else {
                  // 直接发送消息
                  console.log('v1.0 兼容：直接发送消息事件');
                  const sendResult = topWindow.comiEventBus.$emit('sendMsg', { value, option });
                  console.log('v1.0 兼容：sendMsg事件发送结果', sendResult);
                  
                  // 后备方案
                  setTimeout(() => {
                    console.log('v1.0 兼容：后备发送消息');
                    if (businessSdkInstance) {
                      businessSdkInstance.sendMsg(value, option);
                    }
                  }, 50);
                }
              } else {
                console.warn('v1.0 SDK: comiEventBus 不可用', {
                  hasComiEventBus: !!topWindow.comiEventBus,
                  hasEmit: !!(topWindow.comiEventBus && topWindow.comiEventBus.$emit)
                });
              }
          },
          redirectAssistId: (id: string, defaultSendMsg?: boolean, callback?: any) => {
            console.log('v1.0 SDK redirectAssistId 事件:', { id, defaultSendMsg });
            
            if (topWindow.comiEventBus && topWindow.comiEventBus.$emit) {
              topWindow.comiEventBus.$emit('redirectAssistId', {
                id,
                defaultSendMsg: !!defaultSendMsg,
                callback
              });
            }
          }
        };
      }
      
      // 手动触发执行（供调试使用）
      forceExecute(): void {
        console.log('强制执行回调');
        this.executeAllCallbacks();
      }
      
      // 公开的触发检查方法
      triggerCheck(): void {
        this.checkAndExecute();
      }
      
      // 获取状态信息
      getStatus() {
        return {
          callbacksCount: this.callbacks.length,
          hasSingleCallback: !!this.singleCallback,
          isExecuted: this.isExecuted,
          sdkReady: this.shouldExecuteImmediately(),
          isFromNewPreSdk: this.isFromNewPreSdk
        };
      }
    }
    
    const callbackExecutor = new CallbackExecutor();
    
    // 劫持现有的openDrawer（如果存在）
    if (COMI_ASSISTANT.preSdk && COMI_ASSISTANT.preSdk.openDrawer) {
      const existingOpenDrawer = COMI_ASSISTANT.preSdk.openDrawer;
      console.log('立即劫持现有的openDrawer方法');
      
      // 保存已有的单个callback（兼容原始逻辑）
      if (COMI_ASSISTANT.preSdk.callback && typeof COMI_ASSISTANT.preSdk.callback === 'function') {
        callbackExecutor.setSingleCallback(COMI_ASSISTANT.preSdk.callback);
        console.log('发现已有的单个callback，已保存');
      }
      
      COMI_ASSISTANT.preSdk.openDrawer = (callback?: () => void) => {
        console.log('劫持到openDrawer调用！', { hasCallback: !!callback });
        
        // 执行原有的打开抽屉逻辑
        if (existingOpenDrawer && existingOpenDrawer !== COMI_ASSISTANT.preSdk.openDrawer) {
          try {
            existingOpenDrawer.call(COMI_ASSISTANT.preSdk, callback);
          } catch (error) {
            console.error('执行原有openDrawer失败:', error);
          }
        }
        
        // 使用回调执行器处理回调
        if (callback) {
          // 统一通过 callbackExecutor 处理，让它决定如何处理不同版本
          callbackExecutor.addCallback(callback);
        }
      };
    }
    
    // 创建新的preSdk对象
    COMI_ASSISTANT.preSdk = {
      callbackQueue: [],
      openDrawerCallbacks: [],
      callback: null,
      
      openDrawer: (callback?: () => void) => {
        console.log('新preSdk.openDrawer 被调用', { 
          hasCallback: !!callback, 
          sdkReady: businessSdkInstance && businessSdkInstance.isSDKInitialized() 
        });
        
        // 执行打开抽屉的操作 - 添加详细调试信息
        const topWindow = window.top || window;
        
        // 方案1：通过事件总线打开抽屉（避免递归调用）
        if (topWindow.comiEventBus && topWindow.comiEventBus.$emit) {
          topWindow.comiEventBus.$emit('openDrawer');
        }
        // 方案2：如果存在原生的非preSdk的openDrawer方法
        else if (topWindow.COMI_ASSISTANT && typeof topWindow.COMI_ASSISTANT.openDrawer === 'function') {
          topWindow.COMI_ASSISTANT.openDrawer();
        }
        // 方案3：检查是否有v5版本的openDrawer函数（直接调用内部函数）
        else if (typeof (topWindow as any).openDrawer === 'function') {
          (topWindow as any).openDrawer();
        }
        else {
          console.warn('无法找到有效的打开抽屉方法');
        }
        
        if (callback && typeof callback === 'function') {
          // 统一通过 callbackExecutor 处理，让它决定如何处理不同版本
          callbackExecutor.addCallback(callback);
        }
      },
      
      executeCallbacks: () => {
        console.log('手动调用executeCallbacks');
        callbackExecutor.forceExecute();
      },
      
      // 调试接口
      getCallbackStatus: () => {
        return callbackExecutor.getStatus();
      }
    };
    
    console.log('回调执行器初始化完成');
    
    // 等待SDK完全初始化后执行回调和绑定外部方法
    businessSdkInstance.waitForInitialization().then(() => {
      console.log('BusinessSDK初始化完成，开始执行待处理的回调');
      
      // 自动绑定外部方法和参数
      if (businessSdkInstance) {
        businessSdkInstance.bindGetOaUrl();
        businessSdkInstance.bindHandleTagClick();
        businessSdkInstance.bindOpenPersonalInfo();
        businessSdkInstance.bindOpenDialog();
        businessSdkInstance.bindOpenWin();
        businessSdkInstance.bindIsPortal();
        businessSdkInstance.bindExpandDoubleContainer();
      }
      
      // 主动通知：SDK已完全就绪
      console.log('主动通知：SDK已完全就绪，立即触发回调检查');
      callbackExecutor.triggerCheck();
      
      // 延迟一点再次检查，确保所有绑定都完成
      setTimeout(() => {
        console.log('延迟检查：确保所有绑定都完成');
        callbackExecutor.triggerCheck();
      }, 50);
      
    }).catch(error => {
      console.error('BusinessSDK初始化失败:', error);
    });
  }
  
  // 同时绑定到 window.sdk，确保外层系统和内部系统使用同一个实例
  (window as any).sdk = businessSdkInstance;
  
  console.log('BusinessSDK初始化完成，绑定到:', {
    'COMI_ASSISTANT.sdk': !!COMI_ASSISTANT.sdk,
    'COMI_ASSISTANT.preSdk': !!COMI_ASSISTANT.preSdk,
    'window.sdk': !!(window as any).sdk,
    'same_instance': COMI_ASSISTANT.sdk === (window as any).sdk,
    'bindings': businessSdkInstance.getDebugInfo().bindings
  });

  return businessSdkInstance;
}

/** 获取业务SDK实例 */
export function getBusinessSdk(): BusinessSDK {
  if (!businessSdkInstance) {
    throw new Error('BusinessSDK未初始化，请先调用 initBusinessSdk()');
  }
  return businessSdkInstance;
}

/** 当SDK就绪时执行回调 */
export function onSDKReady(
  callback: (sdk: BusinessSDK) => void,
  conditions?: CallbackConditions
): Promise<void> {
  return new Promise((resolve, reject) => {
    // 如果没有实例，等待初始化
    if (!businessSdkInstance) {
      console.log('SDK 实例不存在，等待初始化...');
      // 监听 SDK 初始化完成
      const checkInitialization = () => {
        if (businessSdkInstance && businessSdkInstance.isSDKInitialized()) {
          executeCallbackWhenReady(callback, conditions, businessSdkInstance)
            .then(resolve)
            .catch(reject);
        } else {
          setTimeout(checkInitialization, 100);
        }
      };
      checkInitialization();
      return;
    }

    // 如果实例存在，等待其初始化完成
    executeCallbackWhenReady(callback, conditions, businessSdkInstance)
      .then(resolve)
      .catch(reject);
  });
}

// ==================== 导出类型 ====================

export type { BusinessConfig, PreConfig, EventHandler, CallbackConditions };