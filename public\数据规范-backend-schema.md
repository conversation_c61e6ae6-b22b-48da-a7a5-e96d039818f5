### 公共卡片规范设计

抽象“**公共卡片**”为**规则化的json**，这个json是 **comi builder**的最终输出（综合需要配置工具、agent、助手），让大模型能按照用户输入回答出特定的**json**

具体的 [卡片规范梳理](https://docs.qq.com/sheet/DVU9naVhhQWp1RWVi?tab=p30uir) ，最终总结归类为以下5类

#### 1. 展示类列表A（无label）

```json
{
    "code": "0",
    "message": null,
    "data": {
        "pluginName": "日程插件",
        "pluginKey": "querySchedule",
        "cardType": 0,
        "disabled": false,
        "moreButtonType": 0, // 为0时，超过5条出现"查看更多 > " 点击后跳转到具体业务页面；为1时，超过5条出现“查看更多 v” 点击后追加5条数据； 为3时始终不展示
        // 覆盖卡片样式,需要定制化的时候设置,字段取renderInfo里的具体字段
        "cardStyles": {
            "tag": {
              "style": {
                "border": "none"
              },
              "class": "ai-icon-shijian"
            }
        },
        "pageInfo": {
            "pageNumber": 1,
            "pageSize": 10,
            "pages": 1,
            "total": 100,
            "needTotal": false
        },
        "result": [
            {
                // 原始接口返回的信息（待定，是否需要）
                "originApiInfo": {
                    "memberId" : "6208102320684287005",
                    "memberName" : "李志伟", 
                    "appId" : "-3957836230145194556",
                    "dbStartDate" : "2025-01-13 09:00",
                    "dbEndDate" : "2025-01-13 10:00",
                    "startDate" : "2025-01-13 00:00",
                    "endDate" : "2025-01-13 23:59",
                    "displayDate" : "09:00 - 10:00",
                    "tipsDate" : "01-13 09:00 - 10:00",
                    "sectionId" : null,
                    "title" : "AI技术部-周例会（09:00-10:00）",
                    "orgTitle" : null,
                    "type" : "meeting",
                    "typeName" : "会议",
                    "color" : null,
                    "textColor" : null,
                    "createMember" : "-6004484025363772863",
                    "orgentId" : "6208102320684287005"
                },
                // 卡片渲染数据结构
                "renderInfo": {
                    "id": "123456",
                    "title": "AI技术部-周例会（09:00-10:00）",
                    "userName": "李志伟",
                    "avatarUrl": "https://xxxx",
                    "description": "01-13 09:00 - 10:00",
                    "subDescripiton": "",
                    "tag": ""
                },
            }
        ]
    }
}
```





#### 2. 展示类列表B（有label）

```json
{
    "code": "0",
    "message": null,
    "data": {
        "pluginName": "会议插件",
        "pluginKey": "queryMeeting",
        "cardType": 1,
        "disabled": false,
        "moreButtonType": 0, // 为0时，超过5条出现"查看更多 > " 点击后跳转到具体业务页面；为1时，超过5条出现“查看更多 v” 点击后追加5条数据； 为2时始终不展示
        // 覆盖卡片样式,需要定制化的时候设置,字段取renderInfo里的具体字段
        "cardStyles": {
          "id": {
            "style": {
              "font-size": "12px"  
            },
          },
          "title": {
            "style": {
              "font-size": "14px"  
            },
          },
          "label": {
            "style": {
              "font-size": "12px"
            },
          },
          "value": {
            "style": {
              "font-size": "12px"
            },
          },
        },
        "pageInfo": {
            "pageNumber": 1,
            "pageSize": 10,
            "pages": 1,
            "total": 100,
            "needTotal": false
        },
        "result": [
            {
                // 原始接口返回的信息（待定，是否需要）
                "originApiInfo": {},
                // 卡片渲染数据结构
                "renderInfo": {
                    "id": "2958295471410596167",
                    "title": "变革引领 智启新程——2025年研发中心年会邀您参加！",
                    "fileds": [
                        {
                            "label": "时间",
                            "key": "showTime",
                            "value": "今天 15:30 - 18:00"
                        },
                        {
                            "label": "发起人",
                            "key": "createUserName",
                            "value": "张娟"
                        },
                        {
                            "label": "地点",
                            "key": "meetPlace",
                            "value": "北京-O座展示厅；成都-石化国际酒店（三楼多功能厅）"
                        }
                    ]
                },
            }
        ]
    }
}
```

#### 3. 操作类列表

```json
{
    "code": "0",
    "message": null,
    "data": {
        "pluginName": "流程插件",
        "pluginKey": "queryTodo",
        "cardType": 2,
        "disabled": false,
        "moreButtonType": 0, // 为0时，超过5条出现"查看更多 > " 点击后跳转到具体业务页面；为1时，超过5条出现“查看更多 v” 点击后追加5条数据； 为3时始终不展示
        // 覆盖卡片样式,需要定制化的时候设置,字段取renderInfo里的具体字段
        "cardStyles": {
          "id": {
            "style": {
              "font-size": "12px"
            },
          },
          "tag": {
            "style": {
              "font-size": "12px"
            },
            "class": "iconfont icon_phone"
          },
          "buttons": {
            "reject": {
              "style": {
                "font-size": "12px"
              },
            },
            "agree": {
              "style": {
                "font-size": "12px"
              },
            },
          }
        },
        "pageInfo": {
            "pageNumber": 1,
            "pageSize": 10,
            "pages": 1,
            "total": 100,
            "needTotal": false
        },
        "result": [
            {
                // 原始接口返回的信息（待定，是否需要）
                "originApiInfo": {
                    "affairId" : "2471688347820245750",
                    "subject" : "关于法大大电子合同签署套餐包V3.0版本不再支持新购的重要通知",
                    "startMemberName" : "徐晓丽",
                    "startDate" : "1736502599107",
                    "createDate" : "1736502599107",
                    "finishDate" : null,
                    "receiveTime" : "1736502602343",
                    "nodeDeadLineName" : "无",
                    "hastenTimes" : "0",
                    "subState" : "11",
                    "processId" : "6826437579059054445",
                    "summaryId" : "2867973994182691045",
                    "isCoverTime" : false,
                    "processIsCoverTime" : false,
                    "deadLineDate" : null,
                    "state" : "0",
                    "bodyType" : "10",
                    "hasAttsFlag" : true,
                    "superNode" : false,
                    "importantLevel" : "3",
                    "proxy" : false,
                    "subStateName" : "未读",
                    "processDeadLineName" : "2025-03-04 08:30",
                    "processDeadline" : null,
                    "isTrack" : false,
                    "trackType" : "0",
                    "showAuthorityButton" : false,
                    "startMemberId" : "882015942075629853",
                    "dealTime" : null,
                    "orgAccountId" : "***************",
                    "newflowType" : "0",
                    "advanceRemind" : null,
                    "flowFinished" : false,
                    "templeteId" : null,
                    "caseId" : "4293973700986029599",
                    "canDeleteORarchive" : false,
                    "workitemId" : "-7845804485138366112",
                    "activityId" : "***************",
                    "currentNodesInfo" : null,
                    "backFromId" : null,
                    "hasFavorite" : false,
                    "canForward" : false,
                    "replyCounts" : null,
                    "fromId" : null,
                    "expectedProcessTime" : null,
                    "nodeName" : "协同",
                    "affairState" : "3",
                    "preApproverName" : "徐晓丽",
                    "summaryState" : "0",
                    "formAppId" : null,
                    "formViewOperation" : null,
                    "formRecordid" : null,
                    "canReMove" : true,
                    "affairArchiveId" : null,
                    "print" : "0",
                    "hasPrint" : "否",
                    "readState" : "0",
                    "memberId" : "6208102320684287005",
                    "affairNodeName" : "研发中心",
                    "secretName" : null,
                    "currentNodesNumber" : null,
                    "lastestComment" : null,
                    "button" : [ {
                        "handleType" : "ContinueSubmit",
                        "name" : "提交",
                        "url" : null,
                        "needOpinion" : false,
                        "paramMap" : null,
                        "attitudeList" : null,
                        "httpType" : null,
                        "opinionPolicy" : null
                    }, {
                        "handleType" : "Terminate",
                        "name" : "终止",
                        "url" : null,
                        "needOpinion" : false,
                        "paramMap" : null,
                        "attitudeList" : null,
                        "httpType" : null,
                        "opinionPolicy" : null
                    }, {
                        "handleType" : "Cancel",
                        "name" : "撤销",
                        "url" : null,
                        "needOpinion" : false,
                        "paramMap" : null,
                        "attitudeList" : null,
                        "httpType" : null,
                        "opinionPolicy" : null
                    } ],
                    "startDateStr" : "01-10 17:49",
                    "hastenAffair" : null,
                    "parentformSummaryid" : null,
                    "cancelOpinionPolicy" : "0",
                    "disAgreeOpinionPolicy" : "0",
                    "canEdit" : false,
                    "grab" : false
                },
                // 卡片渲染数据结构
                "renderInfo": {
                    "id": "2958295471410596167",
                    "title": "变革引领 智启新程——2025年研发中心年会邀您参加！",
                    "avatar": "", // 需要拼接好http://seeyon.xxx.com/xxxx/xxxx/memberid
                    "userName": "徐晓丽",
                    "description": "01-10 17:49",
                    "buttons": [
                        {
                            "action": "reject", // 点击后按照某种格式给后端，后端进行相应的操作
                            "label": "终止",
                            "key": "reject",
                            "type": "error", // 按钮样式：error(红色)、success(紫色)、default(灰色)
                            "status": "default", // 按钮状态：disabled(禁用)、default(正常)、active(活动)
                        },
                        {
                            "action": "agree", // 点击后按照某种格式给后端，后端进行相应的操作
                            "label": "提交",
                            "key": "agree",
                            "type": "success", // 按钮样式：error(红色)、success(紫色)、default(灰色)
                            "status": "default" // 按钮状态：disabled(禁用)、default(正常)、active(活动)
                        },
                    ],
                }
            }
        ]
    }
}
```



####  4. 创建类表单

```json
{
    "code": "0",
    "message": null,
    "data": {
        "pluginName": "会议插件", // 插件名称
        "pluginDescription": "好的，正在帮您生成会议", // 插件描述
        "pluginKey": "createMeeting",
        "disabled": false,
        "cardType": 3,
        "result": {
            "renderInfo": [
              {
                  "label" : "会议主题",
                  "name" : "title",
                  "component" : "input",
                  "required" : true,
                  "hideLabel" : true,
                  "placeholder": "请输入会议主题",
                  "value": "会议主题11111",
                  "disabled": false
                }, {
                  "label" : "开始时间",
                  "name" : "startTime",
                  "component" : "dateTimePick",
                  "required" : true,
                  "hideLabel" : false,
                  "placeholder": "请选择",
                  "value" : "2025-01-20 14:14",
                  "disabled": false
                }, {
                  "label" : "结束时间",
                  "name" : "endTime",
                  "component" : "dateTimePick",
                  "required" : true,
                  "hideLabel" : false,
                  "placeholder": "请选择",
                  "value" : "2025-01-20 15:14"
                }, {
                  "label" : "会议内容",
                  "name" : "content",
                  "component" : "input",
                  "required" : true,
                  "hideLabel" : false,
                  "placeholder": "请输入",
                  "value": "会议内容123123"
                }, {
                  "label" : "主持人",
                  "name" : "host",
                  "component" : "singlePeople",
                  "required" : true,
                  "hideLabel" : false,
                  "placeholder": "请选择",
                  "fieldParam": "", // any 传入业务组件的参数，比如默认选中本人
                  "value" : [{
                    "name": "李志伟",
                    "id": "123"
                  }]
                }, {
                  "label" : "参会人",
                  "name" : "conferees",
                  "component" : "multiplePeople",
                  "required" : true,
                  "hideLabel" : false,
                  "placeholder": "请选择",
                  "fieldParam": "", // any 传入业务组件的参数，比如默认选中本人
                  "value": [
                    {
                      "name": "李志伟",
                      "id": "123"
                    },
                    {
                      "name": "李志伟2",
                      "id": "1234"
                    }
                  ]
                }, {
                  "label" : "会议室",
                  "name" : "selectedRoomApps",
                  "component" : "meetingRoom",
                  "required" : true,
                  "hideLabel" : false,
                  "placeholder": "请选择",
                  "fieldParam": "", // any 传入业务组件的参数，比如默认选中本人
                  "value": [
                    {
                      "name": "O座302",
                      "id": "123"
                    },
                  ]
                }, {
                  "label" : "会议地点",
                  "name" : "address",
                  "component" : "input",
                  "required" : true,
                  "hideLabel" : false,
                  "placeholder": "请输入会议地点",
                  "value": "O座303"
                }
            ],
            "buttons": [
                {
                    "action": "cancel", // 点击后按照某种格式给后端，后端进行相应的操作
                    "label": "取消",
                    "key": "reject",
                    "type": "error", // 按钮样式：error(红色)、success(绿色)、default(灰色)、primary(紫色)
                    "status": "default" // 按钮状态：disabled(禁用)、default(正常)、active(活动)
                },
                {
                    "action": "submit", // 点击后按照某种格式给后端，后端进行相应的操作
                    "label": "提交",
                    "key": "submit",
                    "type": "success", // 按钮样式：error(红色)、success(绿色)、default(灰色)、primary(紫色)
                    "status": "default" // 按钮状态：disabled(禁用)、default(正常)、active(活动)
                }
            ]
        }
    }
}
```



##### 4-1 创建类表单 点击“提交”按钮后传递当前表单数据给sse接口，暂定

```json
{
  "chatSessionId": "2585ccec14ca4ad79abbbaba595a7958",
  "sessionId": "",
  "assistantId": "-8922013234489046835",
  "assistantCode": "assit2627726500588113565",
  "input": "查询我的待办",
  "citations": [],
  "action": "${所点击按钮的label} + ${所点项title}", // 1. 流程插件：`${所点击按钮的label} + ${所点项title}`  2. 会议插件: `${所点击按钮的label},参数是 {id: "xxxx", ...}`
}
```



#### 5.图表卡片

```json
{
  "code": "0",
  "message": null,
  "data": {
      "pluginName": "",
      "pluginKey": "",
      "cardType": 4,
      "disabled": false,
      "moreButtonType": 0,
      "pageInfo": {
          "pageNumber": 1,
          "pageSize": 10,
          "pages": 1,
          "total": 6,
          "needTotal": false
      },
      "result": [
          {
              "originApiInfo": {},
              "renderInfo": {
                  "type": "bar_chart",
                  "column_names": [
                      "考勤日期",
                      "考勤次数"
                  ],
                  "data": [
                      [
                          "2025-02-09",
                          2
                      ],
                      [
                          "2025-02-10",
                          2
                      ],
                      [
                          "2025-02-11",
                          2
                      ],
                      [
                          "2025-02-12",
                          2
                      ],
                      [
                          "2025-02-13",
                          2
                      ],
                      [
                          "2025-02-14",
                          1
                      ]
                  ],
                  "title": "李志伟2025年2月份考勤记录统计",
                  "options": {
                      "yAxis": {
                          "axisLabel": {
                              "show": true,
                              "formatter": "{value} %"
                          }
                      },
                      "tooltip": {
                          "formatter": "{b}<br>{a}: {c}%"
                      }
                  }
              }
          }
      ]
  }
}
```



#### Copilot前端与v5/v8的通信：
v5对应工程为：ai-core-shell

1. v5/v8中间层，通过暴露到window.top.COMI_ASSISTANT对象，主要实现

   - 侧边栏iframe容器、唤起、关闭、最大化等
   - v5/v8业务组件唤起，点击业务组件后的值回传
   - 业务逻辑，如选人、选会议室、打开页面、菜单跳转、文档预览、查看更多的跳转
   - 协同融合的操作逻辑等等

2. Copilot前端

   - 通过window.top.COMI_ASSISTANT.xxx 调用相关逻辑，主要通过回调函数的方式实现业务串联

