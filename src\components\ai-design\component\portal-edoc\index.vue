<template>
  <div class="portal-edoc">
    <!-- 页面头部 -->
    <div class="portal-edoc__header">
      <div class="portal-edoc__title-section">
        <div class="portal-edoc__title">智能公文</div>
        <p class="portal-edoc__subtitle">全流程智慧办文，从发文到收文与公文场景深度融合，助力高效办文</p>
      </div>
      <div class="portal-edoc__mascot"><img :src="Mascot"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="portal-edoc__main">
      <!-- 拟文员发文区域 -->
      <SectionCard :data="draftingSection" class="portal-edoc__section1">
        <FeatureCard
          v-for="feature in draftingFeatures"
          :key="feature.id"
          :data="feature"
          @click="handleFeatureClick"
        />
      </SectionCard>

      <!-- 领导审批区域 -->
      <SectionCard :data="approvalSection" class="portal-edoc__section2">
        <FeatureCard
          v-for="feature in approvalFeatures"
          :key="feature.id"
          :data="feature"
          @click="handleFeatureClick"
        />
      </SectionCard>

      <!-- 收文登记区域 -->
      <SectionCard :data="receivingSection" class="portal-edoc__section3">
        <FeatureCard
          v-for="feature in receivingFeatures"
          :key="feature.id"
          :data="feature"
          @click="handleFeatureClick"
        />
      </SectionCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from 'vue'
import Mascot from '@/assets/imgs/create_logo.png';
import { openDobuleScreen } from '@/utils/storesUtils';
import { Modal } from 'ant-design-vue';

const SectionCard = defineAsyncComponent(() => import('./components/SectionCard.vue'))
const FeatureCard = defineAsyncComponent(() => import('./components/FeatureCard.vue'))

interface FeatureData {
  id: string
  title: string
  description: string
  icon: string
  url: string
  typeId?: string,
  plugin: string | string[]
}

interface SectionData {
  title: string
  subtitle: string
}

// 大卡片数据
const draftingSection = ref<SectionData>({
  title: '拟文员发文拟文',
  subtitle: '公文一键生成、改写、续写、润色并完成排版'
})

const approvalSection = ref<SectionData>({
  title: '领导审批',
  subtitle: '智能辅助领导办文，全面检查公文内容，辅助阅读和审批'
})

const receivingSection = ref<SectionData>({
  title: '收文登记',
  subtitle: '公文快速收文登记，提升收文效率'
})

// 拟文员发文功能数据（小卡片）
const draftingFeatures = ref<FeatureData[]>([
  {
    id: 'intelligent-drafting',
    title: '智能拟文',
    description: '快速生成符合国家标准的15种法定公文和其它事务性公文样例',
    icon: '📝',
    url: '/govdoc/govdoc.do?method=newGovdoc&fromTemplateSelect=true&sub_app=1',
    typeId: '401',
    plugin: ['F20_govdocMC-8544674753297104169', 'F20_newSend']
  },
  {
    id: 'rewriting-optimization',
    title: '改写优化',
    description: '通过改写、扩写、润色等文稿优化，助力用户提升文稿质量',
    icon: '✏️',
    url: '/govdoc/govdoc.do?method=newGovdoc&fromTemplateSelect=true&sub_app=1',
    typeId: '401',
    plugin: ['F20_govdocMC-8544674753297104169', 'F20_newSend']
  },
  {
    id: 'intelligent-formatting',
    title: '智能排版',
    description: '一键对正文进行排版，生成带红头的标准格式正文',
    icon: '📄',
    url: '/govdoc/govdoc.do?method=newGovdoc&fromTemplateSelect=true&sub_app=1',
    typeId: '401',
    plugin: ['F20_govdocMC-8544674753297104169', 'F20_newSend']
  }
])

// 领导审批功能数据（小卡片）
const approvalFeatures = ref<FeatureData[]>([
  {
    id: 'intelligent-error-correction',
    title: '智能纠错',
    description: '自动对公文要素、内容等方面进行智能纠错及敏感词检查，保障公文内容的合规性、严肃性和准确性',
    icon: '🔍',
    url: '/govdoc/govdoc.do?method=newGovdoc&fromTemplateSelect=true&sub_app=1',
    typeId: '401',
    plugin: ['F20_govdocMC-8544674753297104169', 'F20_newSend']
  },
  {
    id: 'assisted-reading-approval',
    title: '辅助阅读 <font style="color: rgba(0, 0, 0, 0.4);margin: 0 6px;">&</font> 辅助审批',
    description: '智能处理公文正文内容提供大纲、词云、摘要辅助审批人阅读，提供相关文件和相似审批文件为审批决策提供参考',
    icon: '📖',
    url: '/govdoc/govdoc.do?method=index&listType=listPendingAllRoot&_resourceCode=F20_govdocPending',
    plugin: 'F20_govdocPending'
  },
  {
    id: 'intelligent-co-handling',
    title: '智能协办',
    description: '支持您在处理公文过程中可灵活添加多人进行协办并一键引用其意见',
    icon: '👥',
    url: '/govdoc/govdoc.do?method=index&listType=listPendingAllRoot&_resourceCode=F20_govdocPending',
    plugin: 'F20_govdocPending'
  }
])

// 收文登记功能数据（小卡片）
const receivingFeatures = ref<FeatureData[]>([
  {
    id: 'intelligent-registration',
    title: '智能登记',
    description: '智能识别正文关键要素如标题、文号等，一键回填到文单',
    icon: '📋',
    url: '/govdoc/govdoc.do?method=newGovdoc&fromTemplateSelect=true&sub_app=2',
    typeId: '402',
    plugin: ['F20_govdocMC-4269480178511557817', 'F20_newDengji']
  },
  {
    id: 'intelligent-summary',
    title: '智能摘要',
    description: '自动提取公文核心内容，文单中一键生成简明扼要的摘要',
    icon: '📝',
    url: '/govdoc/govdoc.do?method=newGovdoc&fromTemplateSelect=true&sub_app=2',
    typeId: '402',
    plugin: ['F20_govdocMC-4269480178511557817', 'F20_newDengji']
  }
])

const ctxPath = (window as any)._ctxPath || '/seeyon';

const getTemplate = async (typeId: string): Promise<string> => {
  // 发文 401  收文登记 402
  try {
    const response = await fetch(`${ctxPath}/rest/template/edocTemplate?option.n_a_s=1&categoryId=${typeId}`, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    let id = '';
    const data = (await response.json())?.data || {};
    if (data?.canSelfCreateFlow) {
      // 可以自建流程
      id = ''
    } else if (data?.recentCategory?.templateList[0]) {
      // 最近使用
      id = data.recentCategory.templateList[0].id
    } else if (data?.edocCategory?.templateList[0]) {
      // 可用模版
      id = data.edocCategory.templateList[0].id
    }
    return id;
  } catch (error) {
    console.error('获取模板失败:', error);
    return '';
  }
}

const handleFeatureClick = async (featureData: FeatureData) => {
  if (featureData.plugin) {
    let hasPermission = false;
    if (Array.isArray(featureData.plugin)) {
      hasPermission = featureData.plugin.some(plugin => (window as any).top?._hasResource(plugin));
    } else {
      hasPermission = (window as any).top?._hasResource(featureData.plugin);
    }

    if (!hasPermission) {
      Modal.confirm({
          title: '提示',
          content: '抱歉，您暂无此功能权限。如有需要，请联系管理员',
          okText: '确认',
          cancelText: '取消',
        });
      return;
    }
  }
  if (featureData.typeId) {
    const id = await getTemplate(featureData.typeId);
    if (id) {
      window.open(`${ctxPath}${featureData.url}&from=template&templateId=${id}${featureData.typeId === '401' ? '&ref=comiPortal&view=4' : ''}`, '_blank');
    } else {
      window.open(`${ctxPath}${featureData.url}${featureData.typeId === '401' ? '&ref=comiPortal&view=4' : ''}`, '_blank');
    }
  } else {
    window.open(`${ctxPath}${featureData.url}`, '_blank');
  }
}
</script>

<style scoped lang="less">
.portal-edoc {
  font-family: PingFang SC;
  width: 100%;
  height: 100%;
  overflow: auto;

  &__header {
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 40px;
  }

  &__title-section {
    flex: 1;
  }

  &__title {
    font-size: 24px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 12px;
    line-height: 32px;
  }

  &__subtitle {
    font-size: 13px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: 400;
  }

  &__mascot {
    position: absolute;
    width: 102px;
    right: 0;
    bottom: -53px;
  }

  &__main {
    display: flex;
    flex-direction: column;
    gap: 28px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .portal-edoc {
    padding: 0 16px;

    &__header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    &__mascot {
      font-size: 32px;
      margin-left: 0;
    }

    &__title {
      font-size: 28px;
    }

    &__subtitle {
      font-size: 14px;
    }
  }
}
</style>
