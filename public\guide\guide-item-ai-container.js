var GuideItem = function (keys) {
  this.keys = keys;
  this.start = function () {
    var steps = [];
    var doms = [];
    for (var key in this.keys) {
      var domKey = '#guide-' + key;
      var stepDom = document.body && document.body.querySelector(domKey);
      doms.push(stepDom);
      if (!stepDom) {
        return false;
      }
      switch (key) {
        case 'step8':
          steps.push({
            element: stepDom,
            popover: {
              title: '业务容器',
              description:
                '这里可以分屏查看',
              side: 'right',
              align: 'center',
            },
          });
          break;
          case 'step9':
            steps.push({
              element: stepDom,
              popover: {
                title: '业务容器',
                description:
                  '这里可以关闭业务容器',
                side: 'right',
                align: 'center',
              },
            });
            break;
        default:
          break;
      }
      localStorage.setItem(this.keys[key], true);
      var guideStartKey =
        GuideAPI.prototype.splitKeys && GuideAPI.prototype.splitKeys(this.keys[key]);
      GuideAPI.prototype.saveLocal && GuideAPI.prototype.saveLocal(guideStartKey);
    }

    if (!steps.length) {
      return true;
    }
    var driver = window.driver.js.driver;
    var driverObj = driver({
      popoverClass: 'driverjs-theme',
      showButtons: ['next', 'previous', 'close'],
      nextBtnText: '下一步',
      prevBtnText: '上一步',
      doneBtnText: '完成',
      showProgress: true,
      progressText: '{{current}}/{{total}}',
      allowClose: false,
      steps: steps,
    });
    driverObj.drive();
    return true;
  };
};
