import { ref, computed, readonly } from 'vue'
import { defineStore } from 'pinia'

// 状态路由记录接口
export interface StateRouteRecord {
  // 页面标识
  page: string              // 页面名称，如 'newConversation'
  state: string             // 当前状态，如 'chatting', 'fileUploading', 'assistantSelecting'
  subState?: string         // 子状态，如 'typing', 'waiting', 'uploading'
  
  // 组件信息
  component?: string        // 当前显示的组件名称
  componentProps?: Record<string, any>  // 组件的 props
  
  // 上下文数据
  context?: Record<string, any>  // 状态上下文数据
  params?: Record<string, any>   // 参数
  query?: Record<string, any>    // 查询参数
  
  // 元数据
  meta?: {
    userAction?: string           // 用户操作类型
    actionData?: any             // 操作数据
    isUserAction?: boolean       // 是否为用户主动操作
    trigger?: string             // 触发来源
    duration?: number            // 停留时间（毫秒）
    sessionId?: string           // 会话ID
    [key: string]: any
  }
  
  // 时间信息
  timestamp: number         // 创建时间戳
  title?: string           // 状态标题
  description?: string     // 状态描述
}

// 用户会话接口
export interface UserSession {
  sessionId: string
  startTime: number
  endTime?: number
  page: string
  totalActions: number
  routes: StateRouteRecord[]
}

// 状态路由管理器
export const useStateRouter = defineStore('stateRouter', () => {
  // 当前状态路由信息
  const currentRoute = ref<StateRouteRecord>({
    page: 'home',
    state: 'initial',
    component: 'HomePage',
    context: {},
    params: {},
    query: {},
    meta: {},
    timestamp: Date.now()
  })

  // 状态路由历史记录
  const routeHistory = ref<StateRouteRecord[]>([])
  
  // 当前历史索引
  const currentHistoryIndex = ref(-1)
  
  // 最大历史记录数量
  const maxHistorySize = ref(100)
  
  // 是否启用持久化
  const persistEnabled = ref(true)
  
  // 当前会话ID
  const currentSessionId = ref<string>('')
  
  // 会话历史
  const sessions = ref<UserSession[]>([])

  // 本地存储key
  const STORAGE_KEY = 'state-router-history'
  const CURRENT_ROUTE_KEY = 'state-current-route'
  const SESSIONS_KEY = 'state-router-sessions'

  // 计算属性：是否可以后退
  const canGoBack = computed(() => currentHistoryIndex.value > 0)
  
  // 计算属性：是否可以前进
  const canGoForward = computed(() => 
    currentHistoryIndex.value < routeHistory.value.length - 1
  )

  // 计算属性：当前路由完整信息
  const currentRouteInfo = computed(() => ({
    ...currentRoute.value,
    canGoBack: canGoBack.value,
    canGoForward: canGoForward.value,
    historyLength: routeHistory.value.length,
    currentIndex: currentHistoryIndex.value,
    sessionId: currentSessionId.value
  }))

  // 生成会话ID
  const generateSessionId = () => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 初始化会话
  const initSession = (page: string) => {
    currentSessionId.value = generateSessionId()
    const session: UserSession = {
      sessionId: currentSessionId.value,
      startTime: Date.now(),
      page,
      totalActions: 0,
      routes: []
    }
    sessions.value.push(session)
    saveToStorage()
  }

  // 结束当前会话
  const endCurrentSession = () => {
    const currentSession = sessions.value.find(s => s.sessionId === currentSessionId.value)
    if (currentSession) {
      currentSession.endTime = Date.now()
      currentSession.routes = [...routeHistory.value]
      currentSession.totalActions = routeHistory.value.length
    }
    saveToStorage()
  }

  // 从本地存储加载数据
  const loadFromStorage = () => {
    if (!persistEnabled.value) return

    try {
      // 加载历史记录
      const historyData = localStorage.getItem(STORAGE_KEY)
      if (historyData) {
        const parsed = JSON.parse(historyData)
        routeHistory.value = parsed.records || []
        currentHistoryIndex.value = parsed.currentIndex || -1
      }
      
      // 加载当前路由
      const currentRouteData = localStorage.getItem(CURRENT_ROUTE_KEY)
      if (currentRouteData) {
        const parsedRoute = JSON.parse(currentRouteData)
        currentRoute.value = {
          ...parsedRoute,
          timestamp: parsedRoute.timestamp || Date.now()
        }
      }
      
      // 加载会话数据
      const sessionsData = localStorage.getItem(SESSIONS_KEY)
      if (sessionsData) {
        sessions.value = JSON.parse(sessionsData)
      }
    } catch (error) {
      console.warn('加载状态路由历史失败:', error)
    }
  }

  // 保存到本地存储
  const saveToStorage = () => {
    if (!persistEnabled.value) return

    try {
      const historyData = {
        records: routeHistory.value,
        currentIndex: currentHistoryIndex.value
      }
      localStorage.setItem(STORAGE_KEY, JSON.stringify(historyData))
      localStorage.setItem(CURRENT_ROUTE_KEY, JSON.stringify(currentRoute.value))
      localStorage.setItem(SESSIONS_KEY, JSON.stringify(sessions.value))
    } catch (error) {
      console.warn('保存状态路由历史失败:', error)
    }
  }

  // 推送新状态路由
  const push = (route: Partial<StateRouteRecord>) => {
    // 计算上一个状态的停留时间
    if (routeHistory.value.length > 0) {
      const lastRoute = routeHistory.value[routeHistory.value.length - 1]
      const duration = Date.now() - lastRoute.timestamp
      lastRoute.meta = {
        ...lastRoute.meta,
        duration
      }
    }

    const newRoute: StateRouteRecord = {
      page: route.page || currentRoute.value.page,
      state: route.state || 'unknown',
      subState: route.subState,
      component: route.component,
      componentProps: route.componentProps,
      context: route.context || {},
      params: route.params || {},
      query: route.query || {},
      meta: {
        sessionId: currentSessionId.value,
        ...route.meta
      },
      timestamp: Date.now(),
      title: route.title,
      description: route.description
    }

    // 更新当前路由
    currentRoute.value = newRoute

    // 如果当前不在历史记录的末尾，删除后续记录
    if (currentHistoryIndex.value < routeHistory.value.length - 1) {
      routeHistory.value = routeHistory.value.slice(0, currentHistoryIndex.value + 1)
    }

    // 添加新记录
    routeHistory.value.push(newRoute)
    currentHistoryIndex.value = routeHistory.value.length - 1

    // 限制历史记录大小
    if (routeHistory.value.length > maxHistorySize.value) {
      routeHistory.value.shift()
      currentHistoryIndex.value--
    }

    // 如果没有当前会话，初始化一个
    if (!currentSessionId.value) {
      initSession(newRoute.page)
    }

    // 保存到本地存储
    saveToStorage()

    return newRoute
  }

  // 替换当前状态路由
  const replace = (route: Partial<StateRouteRecord>) => {
    const newRoute: StateRouteRecord = {
      page: route.page || currentRoute.value.page,
      state: route.state || currentRoute.value.state,
      subState: route.subState,
      component: route.component,
      componentProps: route.componentProps,
      context: route.context || currentRoute.value.context,
      params: route.params || {},
      query: route.query || {},
      meta: {
        sessionId: currentSessionId.value,
        ...route.meta
      },
      timestamp: Date.now(),
      title: route.title,
      description: route.description
    }

    // 更新当前路由
    currentRoute.value = newRoute

    // 替换历史记录中的当前项
    if (currentHistoryIndex.value >= 0) {
      routeHistory.value[currentHistoryIndex.value] = newRoute
    } else {
      routeHistory.value.push(newRoute)
      currentHistoryIndex.value = 0
    }

    // 保存到本地存储
    saveToStorage()

    return newRoute
  }

  // 后退到上一个状态
  const back = () => {
    if (canGoBack.value) {
      currentHistoryIndex.value--
      currentRoute.value = { ...routeHistory.value[currentHistoryIndex.value] }
      saveToStorage()
      return currentRoute.value
    }
    return null
  }

  // 前进到下一个状态
  const forward = () => {
    if (canGoForward.value) {
      currentHistoryIndex.value++
      currentRoute.value = { ...routeHistory.value[currentHistoryIndex.value] }
      saveToStorage()
      return currentRoute.value
    }
    return null
  }

  // 跳转到指定历史索引
  const go = (delta: number) => {
    const targetIndex = currentHistoryIndex.value + delta
    if (targetIndex >= 0 && targetIndex < routeHistory.value.length) {
      currentHistoryIndex.value = targetIndex
      currentRoute.value = { ...routeHistory.value[targetIndex] }
      saveToStorage()
      return currentRoute.value
    }
    return null
  }

  // 获取历史记录
  const getHistory = (limit?: number) => {
    if (limit) {
      return routeHistory.value.slice(-limit)
    }
    return [...routeHistory.value]
  }

  // 获取指定页面的历史记录
  const getHistoryByPage = (page: string) => {
    return routeHistory.value.filter(record => record.page === page)
  }

  // 获取指定状态的历史记录
  const getHistoryByState = (state: string, page?: string) => {
    return routeHistory.value.filter(record => {
      const stateMatch = record.state === state
      const pageMatch = page ? record.page === page : true
      return stateMatch && pageMatch
    })
  }



  // 导出历史数据
  const exportHistory = () => {
    return {
      records: routeHistory.value,
      currentIndex: currentHistoryIndex.value,
      currentRoute: currentRoute.value,
      sessions: sessions.value,
      exportTime: new Date().toISOString()
    }
  }

  // 导入历史数据
  const importHistory = (data: any) => {
    if (data.records && Array.isArray(data.records)) {
      routeHistory.value = data.records
      currentHistoryIndex.value = data.currentIndex || -1
      if (data.currentRoute) {
        currentRoute.value = data.currentRoute
      }
      if (data.sessions && Array.isArray(data.sessions)) {
        sessions.value = data.sessions
      }
      saveToStorage()
      return true
    }
    return false
  }

  // 清空历史记录
  const clearHistory = () => {
    routeHistory.value = []
    currentHistoryIndex.value = -1
    currentRoute.value = {
      page: 'home',
      state: 'initial',
      component: 'HomePage',
      context: {},
      params: {},
      query: {},
      meta: {},
      timestamp: Date.now()
    }
    saveToStorage()
  }

  // 设置最大历史记录数量
  const setMaxHistorySize = (size: number) => {
    maxHistorySize.value = size
    if (routeHistory.value.length > size) {
      const excess = routeHistory.value.length - size
      routeHistory.value.splice(0, excess)
      currentHistoryIndex.value = Math.max(0, currentHistoryIndex.value - excess)
      saveToStorage()
    }
  }

  // 启用/禁用持久化
  const setPersistEnabled = (enabled: boolean) => {
    persistEnabled.value = enabled
    if (!enabled) {
      localStorage.removeItem(STORAGE_KEY)
      localStorage.removeItem(CURRENT_ROUTE_KEY)
      localStorage.removeItem(SESSIONS_KEY)
    } else {
      saveToStorage()
    }
  }

  // 初始化时加载数据
  loadFromStorage()

  return {
    // 状态
    currentRoute: readonly(currentRoute),
    routeHistory: readonly(routeHistory),
    currentHistoryIndex: readonly(currentHistoryIndex),
    maxHistorySize,
    persistEnabled,
    currentSessionId: readonly(currentSessionId),
    sessions: readonly(sessions),
    
    // 计算属性
    canGoBack,
    canGoForward,
    currentRouteInfo,
    
    // 基本方法
    push,
    replace,
    back,
    forward,
    go,
    
    // 历史记录管理
    getHistory,
    getHistoryByPage,
    getHistoryByState,
    clearHistory,
    
    // 会话管理
    initSession,
    endCurrentSession,
    
    // 数据管理
    exportHistory,
    importHistory,
    
    // 配置
    setMaxHistorySize,
    setPersistEnabled,
    
    // 内部方法
    loadFromStorage,
    saveToStorage
  }
})

// 路由守卫类型
export type StateRouteGuard = (
  to: StateRouteRecord,
  from: StateRouteRecord,
  next: (route?: Partial<StateRouteRecord> | boolean) => void
) => void

// 状态路由守卫管理器
export const useStateRouteGuards = defineStore('stateRouteGuards', () => {
  const beforeEachGuards = ref<StateRouteGuard[]>([])
  const afterEachGuards = ref<((to: StateRouteRecord, from: StateRouteRecord) => void)[]>([])

  // 添加前置守卫
  const beforeEach = (guard: StateRouteGuard) => {
    beforeEachGuards.value.push(guard)
    return () => {
      const index = beforeEachGuards.value.indexOf(guard)
      if (index > -1) {
        beforeEachGuards.value.splice(index, 1)
      }
    }
  }

  // 添加后置守卫
  const afterEach = (guard: (to: StateRouteRecord, from: StateRouteRecord) => void) => {
    afterEachGuards.value.push(guard)
    return () => {
      const index = afterEachGuards.value.indexOf(guard)
      if (index > -1) {
        afterEachGuards.value.splice(index, 1)
      }
    }
  }

  return {
    beforeEach,
    afterEach
  }
}) 