<template>
  <div class="arrow-wrapper-box">
    <div
      v-for="icon in icons"
      :key="icon.class"
      class="arrow-wrapper"
      :class="{
        'arrow-disabled': icon.type === 'pre' ? !props.canScrollLeft : !props.canScrollRight,
      }"
      @click="handleArrowClick(icon.type)"
    >
      <i class="iconfont" :class="icon.class"></i>
    </div>
  </div>
</template>
<script setup lang="ts">
  const emit = defineEmits<{
    (e: 'handleArrowClick', type: string): void;
  }>();

  const props = withDefaults(defineProps<{
    canScrollLeft?: boolean;
    canScrollRight?: boolean;
  }>(), {
    canScrollLeft: true,
    canScrollRight: true
  });

  // 翻页图标配置
  const icons = [
    {
      type: 'pre',
      class: 'ai-icon-zuo',
    },
    {
      type: 'next',
      class: 'ai-icon-you',
    },
  ];
  const handleArrowClick = (type: string) => {
    emit('handleArrowClick', type);
  };
</script>
<style lang="less" scoped>
  .arrow-wrapper-box{
    display: flex;
    gap: 8px;
    .arrow-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      height: 22px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.8);
      cursor: pointer;
      transition: background-color 0.2s ease;
      border: 1px solid rgba(255, 255, 255, 0.4);
      .iconfont {
        font-size: 14px;
        color: rgba(142, 148, 162, 1);
      }

      &:hover:not(.arrow-disabled) {
        background: #fff;

        .iconfont {
          color: rgba(111, 118, 134, 1);
        }
      }

      &:active:not(.arrow-disabled) {
        background: #fff;
        .iconfont {
          color: rgba(111, 118, 134, 1);
        }
      }

      &.arrow-disabled {
        cursor: not-allowed;

        .iconfont {
          color: rgba(216, 218, 223, 1);
        }
      }
    }
  }
</style>