import PdfPic from '@/assets/imgs/pdf.png';
import ExcelPic from '@/assets/imgs/excel.png';
import UrePic from '@/assets/imgs/picture.svg';
import TxtPic from '@/assets/imgs/txt.png';
import PptPic from '@/assets/imgs/ppt.png';
import WordPic from '@/assets/imgs/word.png';
import UalPic from '@/assets/imgs/usual_file.png';
import MdPic from '@/assets/imgs/markdown.png';

// 字符串如果最后一个是@ 要去掉
export const cutIt = (str: string) => {
  if (str.length > 0 && str.slice(-1) == '@') {
    return str.slice(0, -1) + '';
  }
  return '';
};

//把字节转换成正常文件大小
export const getfilesize = (size: number): string => {
  if (!size) return '';
  const num = 1024.0; //byte
  if (size < num) return size + 'B';
  if (size < Math.pow(num, 2)) return (size / num).toFixed(2) + 'KB'; //kb
  if (size < Math.pow(num, 3)) return (size / Math.pow(num, 2)).toFixed(2) + 'MB'; //M
  if (size < Math.pow(num, 4)) return (size / Math.pow(num, 3)).toFixed(2) + 'G'; //G
  return (size / Math.pow(num, 4)).toFixed(2) + 'T'; //T
};

// 获取文件类型
export const getFileType = (fileName: string) => {
  const fileType = fileName.split('.').pop();
  return fileType;
};

// 获取文件图标
export const getFileIcon = (fileType: string) => {
  if (fileType == 'doc' || fileType == 'docx') {
    return {
      type: 'icon',
      icon: 'docx docx-color',
    };
  } else if (fileType == 'xls' || fileType == 'xlsx') {
    return {
      type: 'icon',
      icon: 'xls xls-color',
    };
  } else if (fileType == 'ppt' || fileType == 'pptx') {
    return {
      type: 'img',
      icon: PptPic,
    };
  } else if (fileType == 'pdf') {
    return {
      type: 'icon',
      icon: 'pdf pdf-color',
    };
  } else if (fileType == 'txt' || fileType == 'text') {
    return {
      type: 'img',
      icon: TxtPic,
    };
  } else if (fileType == 'png' || fileType == 'jpg' || fileType == 'jpeg' || fileType == 'gif') {
    return {
      type: 'img',
      icon: UrePic,
    };
  } else if (fileType == 'md') {
    return {
      type: 'icon',
      icon: 'markdown markdown-color',
    };
  } else if (fileType == 'csv') {
    return {
      type: 'icon',
      icon: 'cvs csv-color',
    };
  } else {
    return {
      type: 'img',
      icon: UalPic,
    };
  }
};

// 下载文件
export function downloadFile(url: string, fileName: string): void {
  // 检查是否为 IE 浏览器
  const isIE = !!window.navigator.msSaveBlob;

  if (isIE) {
    // IE10+ 特殊处理
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        (window.navigator as any).msSaveBlob(blob, fileName);
      })
      .catch((error) => console.error('下载文件出错:', error));
  } else {
    // 其他现代浏览器的处理方式
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const a = document.createElement('a');
        const objectUrl = URL.createObjectURL(blob);
        a.style.display = 'none';
        a.href = objectUrl;
        a.download = fileName;
        a.target = '_blank'; // 在新标签页中打开

        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        // 释放 URL 对象
        URL.revokeObjectURL(objectUrl);
      })
      .catch((error) => console.error('下载文件出错:', error));
  }
}

// 随机产生最多含有3个元素的数组
export const sampleArray = (arr: any[], isFullscreen: boolean = false) => {
  const splitNum = isFullscreen ? 4 : 3;
  if (arr.length <= 3) {
    return arr;
  } else {
    const sampledIndices = new Set();
    const sampledArray = [];

    while (sampledIndices.size < splitNum) {
      const randomIndex = Math.floor(Math.random() * arr.length);
      if (!sampledIndices.has(randomIndex)) {
        sampledIndices.add(randomIndex);
        sampledArray.push(arr[randomIndex]);
      }
    }
    return sampledArray;
  }
};

// 根据段落分成思考部分和结论部分
export const splitThinkText = (text: string) => {
  const regex = /<think>(.*?)<\/think>/s; // 使用正则表达式匹配 <think> 标签及其内容
  const match = text.match(regex);

  if (match) {
    const thinkContent = match[1].trim(); // 获取 <think> 标签内的内容并去除首尾空格
    const remainingText = text.replace(regex, '').trim(); // 获取剩余文本并去除首尾空格
    return {
      reasoning_content: thinkContent,
      content: remainingText,
    };
  } else {
    // return [null, text.trim()]; // 如果没有找到 <think> 标签，则返回 null 和原始文本
    return {
      reasoning_content: null,
      content: text.trim(),
    };
  }
};

// 推荐问题处理
export const splitQuestionText = (text: string) => {
  if (!text) {
    return null;
  }
  const parser = new DOMParser();
  const xmlDoc = parser.parseFromString(text, 'application/xml');

  // 获取所有的 <question> 元素
  const questionElements = xmlDoc.querySelectorAll('question');

  // 提取文本内容并存储到数组中
  return Array.from(questionElements).map(element => element.textContent || '');
};


type AnyObject = { [key: string]: any };

function isObject(item: any): item is AnyObject {
  return item && typeof item === 'object' && !Array.isArray(item);
}

export function mergeDeep(target: AnyObject, source: AnyObject): AnyObject {
  if (!isObject(target) || !isObject(source)) {
    return target;
  }

  const output = { ...target };

  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          output[key] = source[key];
        } else {
          output[key] = mergeDeep({ ...target[key] }, source[key]);
        }
      } else {
        output[key] = source[key];
      }
    });
  }

  return output;
}

export const setMultipleCookies = (cookieString: string) => {
  // 将输入的 cookie 字符串按分号分割成单个 cookie 项
  const cookies = cookieString.split('; ');

  // 循环遍历每个 cookie 项并设置它们
  cookies.forEach(cookie => {
    document.cookie = cookie;
  });
}

export const throttle = <T extends (...args: any[]) => any>(func: T, delay: number): T => {
  let lastCall = 0;
  let lastResult: ReturnType<T>;
  
  return ((...args: Parameters<T>) => {
    const now = new Date().getTime();
    if (now - lastCall < delay) {
      return lastResult; // 返回上一次的结果
    }
    lastCall = now;
    lastResult = func.apply(this, args);
    return lastResult;
  }) as T;
}

export const getUrlParams = () => {
  const search = window.location.search || window.location.hash.split('?')[1] || '';
  const params = new URLSearchParams(search);
  const result: Record<string, string> = {};
  for (const [key, value] of params.entries()) {
    result[key] = value;
  }
  return result;
};

export const loadScript = (url: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const allScript = document.getElementsByTagName('script');
    for (let i = 0; i < allScript.length; i++) {
      if (allScript[i].src.indexOf(url) !== -1) {
        resolve(); // 已存在则直接返回
        return;
      }
    }

    const script = document.createElement('script');
    script.src = url;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`加载脚本失败: ${url}`));
    document.body.appendChild(script);
  });
}

