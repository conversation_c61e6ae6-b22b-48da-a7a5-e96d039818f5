<template>
  <div class="check_assist_box">
    <div class="check_assist_item" v-for="itm in ckAssist" :key="itm.id">
      <div class="item_box">
        <Avatar
          :class="itm.iconUrl ? '' : 'sky-bg'"
          :size="20"
          :src="itm.iconUrl"
          style="flex-shrink: 0"
        >
          {{ itm.iconUrl ? '' : itm.name?.slice(0, 2) }}
        </Avatar>
        <div class="assist_title truncate">{{ itm.name }}</div>
        <div class="assist-name-tips">为您回答</div>
      </div>
      <CloseOutlined class="assist_close" @click="() => goDelSecAssist()" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Avatar } from 'ant-design-vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import { defineComponent, PropType } from 'vue';
import { AssistInfo } from '@/types';

defineComponent({
  name: 'CheckAssist',
});
const props = defineProps({
  ckAssist: {
    type: Array as PropType<AssistInfo[]>,
    default: () => [],
  },
  goDelSecAssist: {
    type: Function,
    default: () => {},
  },
});
</script>
<style scoped lang="less">
.check_assist_box {
  width: 100%;
  height: 28px;
  .check_assist_item {
    height: 28px;
    display: flex;
    width: 100%;
    align-items: center;
    .item_box {
      border-radius: 8px;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex: 1;
      max-width: calc(100% - 20px);
      // background-color: #ffffff;
      // padding: 4px 8px;
      :deep(.ant-image-img) {
        border-radius: 50%;
      }
      .assist_avatar {
        width: 20px;
        height: 20px;
        flex-shrink: 0;
      }
      .assist_title {
        margin-left: 4px;
        //styleName: 14px/Semibold;
        font-size: 12px;
        font-weight: @font-weight-400;
        line-height: 22px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #000;
        max-width: calc(100% - 80px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .assist-name-tips {
        margin-left: 4px;
        color: rgba(0, 0, 0, 0.4);
        font-size: 12px;
        line-height: 22px;
      }
    }
    .assist_close {
      margin-left: 8px;
      font-size: 12px;
      font-weight: 200;
      cursor: pointer;
    }
  }
}
</style>
