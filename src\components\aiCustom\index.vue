<template>
  <div class="bg-white custom-card anb_2" :class="{hasClick: !!item.click}" @click="cardClick">
    <i v-if="item.icon" :class="item.icon" class="iconfont ai-icon-diancai-xian down_thumb"></i>
    <span>{{item.context}}</span>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted, onUnmounted, inject } from 'vue';

const props = defineProps<{
  item?: any;
}>();
const {item} = props
const cardClick = (event: Event) => {
  item?.click(item);
};
</script>
<style lang="less" scoped>
.custom-card {
    border-radius: 2px 12px 12px 12px;
    padding: 8px 16px;
    overflow: hidden;
    width: fit-content;
    max-width: 100%;
    min-height: 38px;
    margin-top: 16px;
    line-height: 22px;
    &.hasClick{
      cursor: pointer;
    }
}
</style>

