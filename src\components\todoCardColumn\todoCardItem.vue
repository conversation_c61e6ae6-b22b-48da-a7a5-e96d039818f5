<template>
  <div
    class="table-card-item-wrapper rounded-[12px] px-[12px] py-[12px]"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >

    <!-- 状态标签区域 -->
    <div
      v-if="renderInfo.affairSubStatus && getStatusLabel(Number(renderInfo.affairSubStatus))"
      class="status-tags-container"
    >
      <span :class="['status-tag', getStatusTagClass(Number(renderInfo.affairSubStatus))]">
        {{ getStatusLabel(Number(renderInfo.affairSubStatus)) }}
      </span>
    </div>
    <!-- 主要待办卡片 -->
    <div
      class="table-card-item items-center justify-between"
      :class="{ 'has-extraction': !!extractionData }"
    >
      <!-- 中间内容区域 -->
      <div class="table-card-item-content">
        <div class="table-card-item-content-title-wrapper flex items-center leading-[22px]"
        :class="{ 'pr-[36px]': renderInfo.affairSubStatus && getStatusLabel(Number(renderInfo.affairSubStatus)) }"
        >
          <div
            class="table-card-item-content-title text-[14px] truncate"
            :title="renderInfo.content || renderInfo.title"
          >
            {{ renderInfo.content || renderInfo.title }}
          </div>
          <i v-if="renderInfo.hasAttachment" class="iconfont ai-icon-fujian2 ml-[8px] flex-shrink-0"></i>
          <i
            class="iconfont ai-icon-zhiding1 text-[14px] ml-[8px] flex-shrink-0"
            v-if="renderInfo.top"
          ></i>
        </div>
        <div class="table-card-item-content-description flex text-[12px] mt-[5px]">
          <Avatar
            class="mr-[8px] shrink-0"
            :size="20"
            :src="renderInfo.senderFaceUrl || renderInfo.userPhoto || ''"
          />
          <span
            class="mr-[4px] truncate"
            :title="renderInfo.senderName || renderInfo.userName"
          >{{ renderInfo.senderName || renderInfo.userName }}</span>
          <span class="mr-[4px] shrink-0">【{{ renderInfo.categoryLabel}}】</span>
          <span class="mr-[4px] shrink-0">{{ dateFormat(renderInfo.createTime || renderInfo.createDate || '') }}</span>
        </div>
      </div>

      <!-- 右侧操作按钮区域 -->
      <ItemActionButtons
        ref="actionButtonsRef"
        :buttons="renderInfo.rowButtons"
        :visible="showButtons"
        :max-visible-count="isPortal ? 4 : 2"
        :top="renderInfo.top"
        :renderInfo="renderInfo"
        @button-click="handleButtonClick"
        @visibility-change="handleButtonVisibilityChange"
        @refresh="emit('refresh')"
      />
    </div>

    <!-- 信息提取展示区域 -->
    <ExtractionInfoSection :extraction-data="extractionData" />
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, inject } from 'vue';
import { Avatar, message } from 'ant-design-vue';
import ItemActionButtons from './itemActionButtons.vue';
import ExtractionInfoSection from './ExtractionInfoSection.vue';
import type { ButtonType, RenderInfoItemType, ConfirmActionData } from './types';
import { setPendingTop, cancelPendingTop } from '../../api/pending/index';
import { ApiHandlers, TopActionApiHandlers } from './handlers/apiHandlers';
import { DateUtils, StatusUtils } from './core/utils';

type Props = {
  renderInfo: RenderInfoItemType;
  extractionData?: {
    affairId: string;
    summary: string;
    keywords: Record<string, any>;
  } | null;
};

const props = defineProps<Props>();

const emit = defineEmits<{
  'button-click': [button: ButtonType, item: RenderInfoItemType, confirmData?: ConfirmActionData];
  'refresh': [];
}>();

// 注入全局弹窗管理器
const popoverManager = inject<any>('popoverManager');
// 注入isPortal状态
const isPortal = inject('isPortal') as boolean;

// 响应式状态
const showButtons = ref(false);
const actionButtonsRef = ref<any>();
const parentHideTimer = ref<NodeJS.Timeout>();

// 使用工具函数替换重复代码
const dateFormat = DateUtils.formatDate;
const getStatusLabel = StatusUtils.getStatusLabel;
const getStatusTagClass = StatusUtils.getStatusTagClass;

// 按钮点击处理 - 重构版：使用统一的API处理器
const handleButtonClick = async (button: ButtonType, confirmData?: ConfirmActionData) => {
  try {
    // 优先处理置顶相关操作
    const handled = await TopActionApiHandlers.handleTopActions(
      button,
      setPendingTop,
      cancelPendingTop,
      emit
    );

    if (!handled) {
      // 其他操作使用统一的API处理器
      await ApiHandlers.executeApiOperation(button, props.renderInfo, emit);
    }
  } catch (error) {
    console.error('Button click error:', error);
    message.error('网络错误，请重试');
  }

  // 传递事件给父组件（用于其他业务逻辑）
  emit('button-click', button, props.renderInfo, confirmData);
};

// 清除父组件隐藏定时器
const clearParentHideTimer = () => {
  if (parentHideTimer.value) {
    clearTimeout(parentHideTimer.value);
    parentHideTimer.value = undefined;
  }
};

// 设置父组件隐藏定时器（已移除延时，立即隐藏）
const setParentHideTimer = (delay: number = 0) => {
  clearParentHideTimer();
  if (delay === 0) {
    // 立即隐藏，不使用延时
    showButtons.value = false;
  } else {
    // 保留原有延时逻辑作为备用（但默认为0）
    parentHideTimer.value = setTimeout(() => {
      showButtons.value = false;
    }, delay);
  }
};

// 按钮显示状态变化处理
const handleButtonVisibilityChange = (visible: boolean) => {
  if (visible) {
    // 子组件要求显示时（如弹窗打开），强制显示并清除隐藏定时器
    clearParentHideTimer();
    showButtons.value = true;
  } else {
    // 弹窗关闭时，强制隐藏操作按钮
    showButtons.value = false;
  }
};

// 鼠标进入处理
const handleMouseEnter = () => {
  showButtons.value = true;
  // 清除所有隐藏定时器
  clearParentHideTimer();
  actionButtonsRef.value?.clearHideTimer();
};

// 鼠标离开处理
const handleMouseLeave = () => {
  // 立即隐藏按钮，不使用延时
  setParentHideTimer();
};

// 组件卸载时清理定时器
onUnmounted(() => {
  clearParentHideTimer();
});
</script>

<style scoped lang="less">
.table-card-item-wrapper {
  position: relative;
  cursor: pointer;
  background: #fff;
  // background: linear-gradient(to bottom, rgba(255, 255, 255, 0.66), rgba(255, 255, 255, 0.88));

  // 状态标签样式
  .status-tags-container {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 10;
    display: flex;
    gap: 4px;

    .status-tag {
      align-items: center;
      border-radius: 0 12px 0 12px;
      text-align: center;
      font-size: 12px;
      font-weight: @font-weight-500;
      line-height: 22px;
      height: 22px;
      color: #fff;
      width: 52px;

      // 默认样式
      &.status-tag-default {
        background-color: rgba(102, 102, 102, 0.8);
      }

      // 回退标签
      &.status-tag-fallback {
        color: #FF4D4F;
        background: #FFF1F0;
      }

      // 会议参加标签
      &.status-tag-meeting-join {
        background: #F3FDE7;
        color: #61B109;
      }

      // 会议待定标签
      &.status-tag-meeting-pending {
        background: #FFFBE6;
        color: #FF9900;
      }
    }
  }

  .table-card-item {
    position: relative;
    transition: all ease 0.3s;

    .ai-icon-fujian2 {
      color: #f4935c;
    }

    .ai-icon-zhiding1 {
      color: #5b6580;
    }

    .table-card-item-content-description {
      color: #6F7686;
      align-items: center;
    }
  }

  &:hover {
    // background: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
    box-shadow: 0px 0px 24px 0px rgba(8, 62, 221, 0.08);
    backdrop-filter: blur(59px);

  }



}
</style>
