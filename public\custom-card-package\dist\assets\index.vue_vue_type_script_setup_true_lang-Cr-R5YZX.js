import { importShared, __tla as __tla_0 } from "./__federation_fn_import-B47IVf2F.js";
import { useWindowSize, TableEnum, ChartTypeEnum, TypeSourceEnum, _sfc_main$2, _sfc_main$3, changeChartData, extractAiCards, __tla as __tla_1 } from "./index-DtGXkB0h.js";
import { cardNav, __tla as __tla_2 } from "./card-nav-DF7f_cgh.js";
import { _export_sfc } from "./_plugin-vue_export-helper-8ijppmbV.js";
let _sfc_main;
let __tla = Promise.all([
  (() => {
    try {
      return __tla_0;
    } catch {
    }
  })(),
  (() => {
    try {
      return __tla_1;
    } catch {
    }
  })(),
  (() => {
    try {
      return __tla_2;
    } catch {
    }
  })()
]).then(async () => {
  const { defineComponent: _defineComponent$1 } = await importShared("vue");
  const { unref: _unref, openBlock: _openBlock$1, createBlock: _createBlock$1, createCommentVNode: _createCommentVNode, createElementVNode: _createElementVNode, toDisplayString: _toDisplayString, normalizeClass: _normalizeClass, createElementBlock: _createElementBlock, normalizeStyle: _normalizeStyle } = await importShared("vue");
  const _hoisted_1 = {
    class: "card-content-charts"
  };
  const _hoisted_2 = {
    class: "card-summary__top--left"
  };
  const { reactive, ref: ref$1, inject, watch, nextTick } = await importShared("vue");
  const _sfc_main$1 = _defineComponent$1({
    __name: "card",
    props: {
      cardData: {},
      padding: {
        default: 0
      },
      height: {
        default: 400
      }
    },
    emits: [
      "changeCardOptions"
    ],
    setup(__props, { emit: __emit }) {
      var _a, _b;
      const { height: appScreenHeight } = useWindowSize();
      const props = __props;
      const { padding, height } = props;
      const realHeight = ref$1(height);
      const { column_names, data, type, legend_datas, changeColumnNames } = props.cardData.chartData;
      const { tableData, tableColumnNames } = props.cardData.tableData;
      const { title, summaryTotal, isOverMaxRows, backMaxRows } = props.cardData;
      const chartData = reactive({
        type,
        data,
        column_names,
        legend_datas,
        changeColumnNames
      });
      const tableDataInfo = reactive({
        tableData,
        tableColumnNames,
        backMaxRows,
        summaryTotal,
        isOverMaxRows,
        countNumber: 10
      });
      const changeChartSelectMap = {
        bar_chart: ChartTypeEnum.Bar,
        pie_chart: ChartTypeEnum.Pie,
        line_chart: ChartTypeEnum.Line,
        table: TableEnum.Table
      };
      const typeSelect = changeChartSelectMap[(_b = (_a = props == null ? void 0 : props.cardData) == null ? void 0 : _a.chartData) == null ? void 0 : _b.type];
      const isShowEcharts = ref$1(summaryTotal > 1 && typeSelect !== TableEnum.Table && data.length > 0);
      const chartsValue = Object.values(ChartTypeEnum);
      const selectChartType = ref$1(summaryTotal > 1 && data.length > 0 ? typeSelect ?? TableEnum.Table : TableEnum.Table);
      const changeChartType = (changeChartType2) => {
        selectChartType.value = changeChartType2;
      };
      const emit = __emit;
      const { typeSource } = inject("cardOptions");
      if (typeSource === TypeSourceEnum.PcDialog) {
        realHeight.value = appScreenHeight.value - 124;
        let numberCount = (appScreenHeight.value - 175) / 37;
        if (numberCount > 0 && numberCount) {
          tableDataInfo.countNumber = Math.floor(numberCount);
        }
      }
      watch(() => props.cardData.title, (value) => {
        nextTick(() => {
          emit("changeCardOptions", "title", value);
        });
      }, {
        immediate: true,
        deep: true
      });
      const changeChartDataEvent = (data2) => {
        const cardChartData = [];
        data2.forEach((item) => {
          cardChartData.push(Object.values(item).slice(1));
        });
        const [echartDataShowList, showColumnNames, legend_datas2] = changeChartData(changeColumnNames, cardChartData);
        chartData.data = echartDataShowList;
        chartData.legend_datas = showColumnNames;
        chartData.column_names = legend_datas2;
      };
      const lookSql = () => {
        emit("changeCardOptions", "lookSql", true);
      };
      const onPrev = () => {
        emit("changeCardOptions", "status", false);
      };
      return (_ctx, _cache) => {
        return _openBlock$1(), _createElementBlock("div", {
          ref: "el",
          class: _normalizeClass([
            "cardContent",
            _unref(typeSource) === _unref(TypeSourceEnum).AppDialog ? "card-app" : "card-pc"
          ]),
          style: _normalizeStyle({
            padding: _unref(padding) + "px"
          })
        }, [
          isShowEcharts.value ? (_openBlock$1(), _createBlock$1(_unref(cardNav), {
            key: 0,
            title: _unref(title),
            "select-chart-type": selectChartType.value,
            "is-show-echarts": isShowEcharts.value,
            onChangeChartType: changeChartType,
            onLookSql: lookSql,
            onOnPrev: onPrev
          }, null, 8, [
            "title",
            "select-chart-type",
            "is-show-echarts"
          ])) : _createCommentVNode("", true),
          _createElementVNode("div", _hoisted_1, [
            selectChartType.value === _unref(TableEnum).Table ? (_openBlock$1(), _createBlock$1(_unref(_sfc_main$2), {
              key: 0,
              "table-data-info": tableDataInfo,
              onChangeChartData: changeChartDataEvent
            }, null, 8, [
              "table-data-info"
            ])) : _createCommentVNode("", true),
            _unref(chartsValue).includes(selectChartType.value) ? (_openBlock$1(), _createBlock$1(_unref(_sfc_main$3), {
              key: chartData.type,
              "chart-data": chartData,
              "select-chart-type": selectChartType.value,
              "filed-name": {
                bar: "bar_chart",
                line: "line_chart",
                pie: "pie_chart"
              },
              height: realHeight.value
            }, null, 8, [
              "chart-data",
              "select-chart-type",
              "height"
            ])) : _createCommentVNode("", true)
          ]),
          _unref(summaryTotal) > 1 && _unref(isOverMaxRows) ? (_openBlock$1(), _createElementBlock("div", {
            key: 1,
            class: _normalizeClass([
              "card-summary__top",
              selectChartType.value === _unref(TableEnum).Table && _unref(typeSource) === _unref(TypeSourceEnum).PcDialog ? "card-summary__top--table" : ""
            ])
          }, [
            _createElementVNode("div", _hoisted_2, "\u4EC5\u5C55\u793A\u524D" + _toDisplayString(_unref(backMaxRows)) + "\u6761\u6570\u636E", 1)
          ], 2)) : _createCommentVNode("", true)
        ], 6);
      };
    }
  });
  const card = _export_sfc(_sfc_main$1, [
    [
      "__scopeId",
      "data-v-cf373135"
    ]
  ]);
  const { defineComponent: _defineComponent } = await importShared("vue");
  const { openBlock: _openBlock, createBlock: _createBlock } = await importShared("vue");
  const { ref, provide } = await importShared("vue");
  _sfc_main = _defineComponent({
    __name: "index",
    props: {
      data: {},
      cardOptions: {}
    },
    emits: [
      "update:cardOptions"
    ],
    setup(__props, { emit: __emit }) {
      const props = __props;
      provide("cardOptions", props.cardOptions);
      const cardDataResult = extractAiCards(props.data, props.cardOptions.typeSource);
      const cardData = ref(cardDataResult);
      const emit = __emit;
      const changeCardOptions = (key, value) => {
        emit("update:cardOptions", key, value);
      };
      return (_ctx, _cache) => {
        return _openBlock(), _createBlock(card, {
          "card-data": cardData.value,
          onChangeCardOptions: changeCardOptions
        }, null, 8, [
          "card-data"
        ]);
      };
    }
  });
});
export {
  __tla,
  _sfc_main
};
