/*
 * @Author: 代琪 <EMAIL>
 * @Date: 2025-06-18 13:54:16
 * @LastEditors: 代琪 <EMAIL>
 * @LastEditTime: 2025-07-21 17:52:13
 * @FilePath: \ai-assistant-web\src\api\portal\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { get, post } from '../config.js';
import portalApi from '@/api/portal/portal-api';
import { generateSecure16DigitNumber } from '@/utils/uuid';
import { getAgentResult } from '@/api/common';

const ctxPath = '/seeyon';

let mettingSummaryAgentCode = '';
// 导出一个函数，用于获取门户信息
export const getPortalInfo = (data?: any) => {
  // 发送post请求，获取门户信息
  return portalApi.getPortalInfo();
};

export const getMeetingSummaryAgentCode = async () => {
  const res: any = await portalApi.getConfig();
  if (res.menus && res.menus.length > 0) {
    return res.menus[0].mettingSummaryAgentCode;
  }
};

// getMeetingSummaryAgentCode().then(code => {
//   mettingSummaryAgentCode = code || '';
// });

// 获取用户信息
export const getUserInfo = () => {
  return get(`${ctxPath}/rest/comi-user/current?option.n_a_s=1`, {}, true);
};

// 获取用户头像
export const getUserAvatar = (userId: string) => {
  return `${ctxPath}/rest/orgMember/avatar/${userId}?t=${Date.now()}&option.n_a_s=1`
};

// 根据标签获取助手s
export const getAssistsByLabel = (labelType: string) => {
  return get(`ai-manager/label/info/type/${labelType}/used`, {}, true);
};



// 获取摘要总结信息  seeyon/ai-platform/ai-manager/assistant/info/type/general   "agentId":'4249227344427172170',
export const getSummaryText = async (input: string) => {
  if (!mettingSummaryAgentCode) {
    const code = await getMeetingSummaryAgentCode();
    mettingSummaryAgentCode = code || '';
  }

  return getAgentResult({
    "input": input,
    "chatSessionId": generateSecure16DigitNumber(),
    "agentCode": mettingSummaryAgentCode,
  });
};
// 获取门户默认设置状态
export const getPortalState = async () => {
  return get(`${ctxPath}/rest/portal-comi/get-user-status`, {}, true);
};

// 更改默认门户设置状态
export const setPortalState = async (params: any) => {
  return post(`${ctxPath}/rest/portal-comi/save-user-status`, params, {}, true);
};

//获取我的常用报表统计信息
export const getReportStatus = async () => {
  return get(`${ctxPath}/rest/comi-portal/get-mine-report-stats?option.n_a_s=1`, {}, true);
};


// 批量查询知识源摘要总结及关键信息
export const getBatchSummary = (data: any) => {
  return post(`${ctxPath}/ai-platform/ai-manager/knowledge/summary/queryKnowledgeSummary`, data, {}, true);
}

// 获取导航数据
export const getNavigationData = (portalId: string = '1') => {
  const params = new URLSearchParams();
  params.append('managerMethod', 'getCurrentUserNav');
  // params.append('arguments', JSON.stringify([{ "portalId": portalId, "containMenu": "0", "needSpaceGroup": "0", "needPortalGroup": "0", "needTileSecondMenu": "1" }]));
  params.append('arguments', JSON.stringify([ portalId,  "0",  "0",  "0", "1" ]));
  return fetch('/seeyon/ajax.do?method=ajaxAction&managerName=portalNavManager&nn=getCurrentUserNav', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: params
  }).then(res => res.json());
};
