import type { App } from 'vue';

import Row from './Row.vue';
import Col from './Col.vue';
import LayoutAside from './LayoutAside.vue';
import LayoutFooter from './LayoutFooter.vue';
import LayoutMain from './LayoutMain.vue';
import LayoutSection from './LayoutSection.vue';
import LayoutContainer from './LayoutContainer.vue';
import LayoutCarousel from './LayoutCarousel.vue';

const components = {
  "Row": Row,
  "Col": Col,
  "LayoutAside": LayoutAside,
  "LayoutFooter": LayoutFooter,
  "LayoutMain": LayoutMain,
  "LayoutSection": LayoutSection,
  "LayoutContainer": LayoutContainer,
  "LayoutCarousel": LayoutCarousel
}

export const install = function (app: App) {
  Object.keys(components).forEach(key => {
    const component = (components as Record<string, any>)[key];
    app.component(key, component);
  });
  return app;
};


export default {
  version: '1.0.0',
  install,
};
