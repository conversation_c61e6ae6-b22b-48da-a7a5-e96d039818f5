import axios from 'axios'
import { message } from 'ant-design-vue'

// 存储所有请求的 CancelToken
const pendingRequests = new Map()

// 封装原则
// 1: 添加请求和响应的拦截
// 2: 增加重复请求的取消
// 3: 对外统一接口

// 响应延迟
// axios.defaults.timeout = 59 * 1000

// 生成请求的唯一标识
const generateRequestKey = (config: any) => {
  const { method, url, params, data } = config
  // 将参数对象转换为字符串
  const paramsStr = params ? JSON.stringify(params) : ''
  const dataStr = data ? JSON.stringify(data) : ''
  const timestamp = Date.now()
  return `${method}-${url}-${paramsStr}-${dataStr}-${timestamp}`
}

axios.defaults.baseURL = '/'

// 请求拦截
axios.interceptors.request.use(
  (config) => {
    // console.log('请求拦截', config);
    config.headers.channel = `comi_copilot`

    // 为每个请求创建新的 CancelToken
    const source = axios.CancelToken.source()
    config.cancelToken = source.token

    // 使用生成的唯一标识
    const requestKey = generateRequestKey(config)
    pendingRequests.set(requestKey, source.cancel)

    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截
axios.interceptors.response.use(
  (response) => {
    // 请求完成后从 Map 中移除
    const requestKey = generateRequestKey(response.config)
    pendingRequests.delete(requestKey)
    return response
  },
  (error) => {
    if (axios.isCancel(error)) {
      console.log('请求被取消:', error.message)
    } else {
      console.log(error)
      switch (error?.response?.status) {
        case 401:
          if (window.localStorage.getItem('comiContainerName') == 'v8')  return;
          const topWindwow = window?.top || window;
          topWindwow.location.href = '/seeyon/main.do?method=logout';
          break
        case 404:
          // 去往404
          break 
        case 500:
          // 去往500
          break
        default:
        //
      }
    }
    // 请求完成后从 Map 中移除
    if (error.config) {
      const requestKey = generateRequestKey(error.config)
      pendingRequests.delete(requestKey)
    }
    return Promise.reject(error)
  },
)

// get请求
export function get(url: string, data = {}, isPortal?: boolean) {
  if(!isPortal){
    url = '/seeyon/ai-platform/' + url;
  }
  return new Promise((resolve, reject) => {
    axios.get(url, {
        params: data,
      })
      .then(function (response) {
        resolve(response.data)
      })
      .catch(function (error) {
        reject(error)
      })
  })
}
//post请求
export function post(url: string, data: object | string, options?: object, isPortal?: boolean) {
  if(!isPortal){
    if(url.indexOf('rest/comi-agent/call-agent') !== -1){
      url = '/seeyon/' + url;
    }else{
      url = '/seeyon/ai-platform/' + url;
    }
  }
  return new Promise((resolve, reject) => {
    axios.post(url, data, {
      headers: {
        ...options,
      },
    })
      .then(function (response) {
        resolve(response.data)
      })
      .catch(function (error) {
        reject(error)
      })
  })
}

//post请求 带文件 并且带获取进度
export function postUpload(url: string, data: object,config:object) {
  const newUrl = '/seeyon/ai-platform/' + url;
  return new Promise((resolve, reject) => {
    axios.post(newUrl, data,{...config})
      .then(function (response) {
        resolve(response.data)
      })
      .catch(function (error) {
        reject(error)
      })
  })
}

// 取消所有请求的方法
export function cancelAllRequests(urlStr: string) {
  pendingRequests.forEach((cancel, url) => {
    if(urlStr){
      if(url.indexOf(urlStr) !== -1){
        cancel('请求已被取消')
        pendingRequests.delete(url)
      }
    }else{
      cancel('请求已被取消')
      pendingRequests.delete(url)
    }
  })
}
