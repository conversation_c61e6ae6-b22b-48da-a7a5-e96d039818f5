<template>
  <div :class="['form-card',  disabledCard ? 'disabled-card' : '']">
    <PluginDesction :title="cardData?.data?.pluginName" :total="cardData?.data?.pageInfo?.total" />
    <div class="item-box">
      <ComponentItem
        v-for="(item, index) in cardData?.data?.result?.renderInfo"
        :key="index"
        v-bind="item"
        :styles="cardData?.data?.cardStyles"
        @change="updateItemValue"
      />
    </div>
    <div class="buttons-box">
      <CommonButton
        v-for="(item, index) in cardData?.data?.result?.buttons"
        :key="index"
        :disabled="buttonDisabled(item)"
        :active="item.status === 'active'"
        :type="item.type"
        @click="handleClickButton(item)"
      >
      {{ item.label }}
    </CommonButton>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, PropType, computed } from 'vue';
import { ChatItem } from '@/types/index';
import { CardResponse, CardItem, CardData } from '@/components/commonCard/types'
import PluginDesction from "../pluginDesction.vue"
import ComponentItem from './component-item.vue'
import CommonButton from '../common-button.vue'

const props = defineProps({
  chatData: {
    type: Object as PropType<ChatItem>,
    default: () => {},
  },
  cardData: {
    type: Object as PropType<CardResponse>,
    default: () => {},
  },
});

const disabledCard = ref(props.cardData?.disabled)

const componentData = ref()
const buttons = ref([])

const buttonDisabled = computed(() => {
  return (item: any) => {
    const status = item.status === 'disabled'
    console.log('==========> item', item)
    if (item.action !== 'submit') {
      return status
    }
    const checkValue = componentData.value.find((ele) => ele.required && !ele.value)
    return status || !!checkValue
  }
})

const handleClickButton = (item: any) => {
  console.log('====> item', item)
  if (item.action === 'cancel') {
    disabledCard.value = true
    // todo 动作逻辑
  } else if (item.action === 'submit') {
    // todo 动作逻辑
    const params = getFormData()
    console.log('===> params', params)
  }
}

const updateItemValue = (key: string, value: string | any[]) => {
  console.log('====> key', key)
  console.log('====> value', value)
  const item = componentData.value.find(item => item.name === key)
  item.value = value
}

const getFormData = () => {
  const data: any = {}
  componentData.value.forEach(item => {
    data[item.name] = item.value
  })
  return data
}

const initData = () => {
  componentData.value = props.cardData?.data?.result?.renderInfo
  buttons.value = props.cardData?.data?.result?.buttons
}

initData()
</script>
<style lang="less" scoped>
  .form-card {
    &.disabled-card {
      pointer-events: none;
      opacity: 0.7;
      cursor: not-allowed;
    }
    .item-box {
      padding: 0 14px;
      margin: 8px 0;
      border-radius: 4px;
      background: #fafafa;
      .component-item:not(:first-child) {
        border-top: 1px solid #E4E4E4;
      }
    }
    .buttons-box {
      display: flex;
    }
  }
</style>
