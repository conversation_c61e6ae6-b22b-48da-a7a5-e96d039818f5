<template>
  <div class="menu-navigation-block">
    <div class="menu-navigation-content">
      <div class="menu-navigation-header">
        <div class="menu-navigation-title">
          <span class="menu-navigation-title-text">导航</span>
        </div>
      </div>

      <div
        class="menu-navigation-items"
        v-if="props.navigationList.length > 0"
      >
        <ul class="menu-navigation-list">
          <li
            v-for="item in displayNavigation"
            :key="item.idKey"
            class="menu-navigation-item"
            @click="handleSelect(item)"
            :title="item.nameKey"
          >
            <span class="menu-navigation-item-text">{{ item.nameKey }}</span>
          </li>
        </ul>
      </div>

      <!-- 空状态 -->
      <div
        v-else
        class="menu-navigation-empty"
      >
        <span>暂无导航数据</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { MenuItem } from '../types/menu'

interface Props {
  searchValue: string
  navigationList: MenuItem[]
}

interface Emits {
  (e: 'select', item: MenuItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算显示的导航列表
const displayNavigation = computed(() => {
  if (!props.searchValue) {
    return props.navigationList
  }
  // 如果有搜索值，过滤导航项
  return props.navigationList.filter(item =>
    item.nameKey.toLowerCase().includes(props.searchValue.toLowerCase())
  )
})

// 处理选择
const handleSelect = (item: MenuItem) => {
  emit('select', item)
}
</script>

<style lang="less" scoped>
.menu-navigation-block {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 10px;
  overflow: hidden;

  .menu-navigation-content {
    padding: 16px 10px 16px 28px;

    .menu-navigation-header {
      margin-bottom: 8px;
      line-height: 18px;

      .menu-navigation-title {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: var(--theme-brand6, #4379FF);
        padding: 0 0 3px 0;

        .menu-navigation-title-text {
          flex: 1;
          font-weight: 600;
        }
      }
    }

    .menu-navigation-items {
      .menu-navigation-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .menu-navigation-item {
          display: flex;
          align-items: center;
          padding: 3px 0 3px 0;
          font-size: 12px;
          color: #333;
          line-height: 22px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &::after {
            content: '';
            position: absolute;
            width: 100%;
            bottom: 0;
            left: 0;
            border-bottom: 1px dashed transparent;
          }

          &:hover {
            color: var(--theme-brand6, #4379FF);
          }

          .menu-navigation-item-text {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .menu-navigation-empty {
      display: flex;
      flex-direction: column;
      color: #999;
      font-size: 12px;
      line-height: 20px;
      padding-top: 4px;
    }
  }
}
</style>
