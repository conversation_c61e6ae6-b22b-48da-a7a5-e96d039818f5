<template>
  <!-- 用户/页脚区域 -->
  <div class="user-avatat">
    <!-- 用户头像，根据折叠状态调整大小 -->
    <Popover
      :arrow="false"
      :offset="[10, 10]"
      :placement="showPortalChange ? 'topLeft' : 'topRight'"
      :overlayClassName="overlayClassName"
    >
      <template #content>
        <!-- <div class="content-item person-info" v-if="showPortalChange">
          <Avatar :size="28" :src="userStore.userInfo?.avatar" />
          <p>
            <strong>{{ userStore.userInfo?.name }}</strong>
            <span>{{ userStore.userInfo?.postName }}</span>
          </p>
        </div> -->
        <div class="content-item" @click="changePortal" v-if="showPortalChange">
          <i class="iconfont ai-icon-zhushouqiehuan" />
          <span>切换门户</span>
        </div>
        <div
          class="content-item"
          @click="clickMenu({ id: 'history', label: '历史对话', key: 'history' })"
        >
          <i class="iconfont ai-icon-lishiduihua" />
          <span>历史对话</span>
        </div>
        <div class="content-item" @click="clickMenu({ id: 'setting', label: 'CoMi设置' })">
          <i class="iconfont ai-icon-shezhi" />
          <span>CoMi设置</span>
        </div>
        <template v-if="isPortal">
          <div class="content-item" @click="switchToAdmin" v-if="hasBackAdminAuth">
            <i class="iconfont ai-icon-houtaiguanli" />
            <span>后台管理</span>
          </div>
          <div class="content-item" @click="logout">
            <i class="iconfont ai-icon-tuichudenglu" />
            <span>退出登录</span>
          </div>
        </template>
      </template>
      <div class="user-avatar-wrapper" id="guide-step2" v-if="showPortalChange">
        <Avatar :size="36" :src="userStore.userInfo?.avatar" />
        <div v-if="internalMenuState !== 'portalFold'">
          <p class="user-name">{{ userStore.userInfo?.name }}</p>
          <p class="user-post">{{ userStore.userInfo?.postName }}</p>
        </div>
      </div>
      <div class="user-icon" v-else>
        <i class="iconfont ai-icon-renyuan-line"></i>
      </div>
    </Popover>
  </div>
</template>
<script setup lang="ts">
import { computed, inject } from 'vue';
import { Avatar, Popover, Modal } from 'ant-design-vue';
import { useUserInfo } from '@/stores/global';
import { useMenu } from '@/stores/global';
import { setPortalState } from '@/api/portal';
import { useGlobal } from '@/stores/global';

const useGlobalStore = useGlobal();

const props = defineProps<{
  internalMenuState?: string;
}>();

const userStore = useUserInfo();
const uMenu = useMenu();
const isPortal = inject('isPortal') as boolean;
const topWindwow = window?.top || window;

const showPortalChange = computed(() => {
  return isPortal;
});

const hasBackAdminAuth = computed(() => {
  return userStore.userInfo?.hasBackAdminAuth;
});

const overlayClassName = computed(() => {
  return isPortal ? 'user-popover user-avatar-popover' : 'user-popover user-avatar-popover-silder';
});

const clickMenu = (item: any) => {
  uMenu.changeMenu(item);
};

const switchToAdmin = () => {
  topWindwow.location.href = window._ctxPath + '/main.do?method=changeLoginAccount&switchToAdmin=1';
};
const logout = () => {
  Modal.confirm({
    title: '提示',
    content: '确定要退出登录吗？',
    okText: '确定',
    cancelText: '取消',
    centered: true,
    closable: true,
    onOk() {
      topWindwow.location.href = window._ctxPath + '/main.do?method=logout';
    },
  });
};

const changePortal = () => {
  setPortalState({
    enable: false,
  }).then((res: any) => {
    if (res.code == 0) {
      const topWindwow = window?.top || window;
      if (useGlobalStore.globalState.comiContainerName === 'v8') {
        if (topWindwow.location.href.includes('/main/portal/main/portal')) {
          topWindwow.location.reload();
        } else {
          topWindwow.location.href = '/main/portal/main/portal';
        }
      } else {
        if (topWindwow.location.href.includes('/seeyon/main.do')) {
          topWindwow.location.reload();
        } else {
          topWindwow.location.href = '/seeyon/main.do?method=main';
        }
      }
    }
  });
};
</script>
<style lang="less">
.user-popover {
  width: 200px;
  // height: 56px;
  padding: 16px 12px !important;
  background-color: #fff;
  border-radius: 8px;
  line-height: 22px;
  box-shadow: 0px 6px 16px -8px rgba(0, 0, 0, 0.08);

  .ant-popover-inner {
    box-shadow: none;
    padding: 0px !important;
    .ant-popover-inner-content {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .content-item {
        height: 32px;
        font-family: PingFang SC;
        font-weight: @font-weight-400;
        font-size: 13px;
        line-height: 22px;
        color: #3f434d;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 0 8px;

        &:not(.person-info):hover {
          background-color: #f0f0f0;
          border-radius: 8px;
        }
      }
      .person-info {
        p {
          display: flex;
          flex-direction: column;
          strong {
            font-size: 13px;
            color: rgba(37, 38, 44, 1);
            font-weight: @font-weight-500;
          }
          span {
            font-size: 12px;
            color: rgba(63, 67, 77, 1);
          }
        }
      }
    }
  }
}
div.user-avatar-popover {
  left: 10px !important;
}
div.user-avatar-popover-silder {
  right: 10px !important;
}
.user-avatar-wrapper {
  // width: 52px;
  // height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px;
  gap: 8px;

  .user-name {
    font-family: PingFang SC;
    font-weight: @font-weight-500;
    font-style: Semibold;
    font-size: 13px;
    line-height: 16px;
    color: #3f434d;
    margin-bottom: 4px !important;
  }
  .user-post {
    font-family: PingFang SC;
    font-weight: @font-weight-400;
    font-style: Regular;
    font-size: 10px;
    line-height: 12px;
    color: #3f434d;
  }
}
.user-icon {
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  i {
    font-size: 18px;
    color: rgba(111, 118, 134, 1);
  }
}
</style>
