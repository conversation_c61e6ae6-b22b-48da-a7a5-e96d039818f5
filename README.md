# copilot 项目简介


## 快速开始
|步骤|命令|
|----|----|
|安装|yarn install |
|启动|yarn dev|
|构建|yarn build|


## 相关文档地址
[***AI智能助手***](http://10.101.129.2:8080/frontend#/)

[*原型*](https://7rh0r3.axshare.com/#id=fe7ru4&p=%E5%8A%A9%E6%89%8B%E9%97%A8%E6%88%B7&g=1)

[设计图](https://www.figma.com/design/mjJf94140aauqU34HrWFLb/AI-%E9%97%A8%E6%88%B7?node-id=0-1&node-type=canvas&t=t8iZzRxz91aBkQYG-0)

![logo.png](./src/assets/imgs/logo.png)

## 构建说明
本项目采用vite 构建，构建模式为多页应用，同时采用了federation模块联邦的方式快速集成第三方组件库的组件，为微前端打下基础。
## 使用技术
- vite
- vue3.0
- typescript
- pinia
- vue-router
- ant-design-vue
- axios

## 编码必要规范

### 原则
  1. 要用最少得代码完成最多的功能
  2. 封装好组件
  3. 封装好接口
  4. 封装好store
  5. 封装好路由
  6. 封装好工具类
  7. 精简、干净、准确的注释
  8. 每个文件最好不要超过400行，最大容忍度500行
  9. 持续优化项目中的代码

### 命名规范
#### 概述
目前工程的命名还存在一定的不规范现象，因此对命名进行相应的规范制定

#### 组件类
对系统组件的命名涉及到两个方面：一个是组件文件夹的命名，另外一个是组件文件的命名

##### 组件文件夹
对组件文件夹采用英文小写字母和‘-’的组合方式进行命名

``` html
  // 推荐使用方式
  ai-answer   ai-custom   ai-footer   ai-answer-title

  // 不推荐使用方式
  aiAnswer  ai-answerTitle AIANSWER
```


## 核心工具说明
### 路由
#### 概述
本项目由最初的 vite 构建的单页应用调整为 vite 构建的多页应用，随着应用方式的转变原有的 router 不再适用于现有系统，因此基于 vue router 的核心思想开发了一套路由机制。

#### 特征
##### 🔄 状态驱动管理
- **页面状态管理**: 可以通过状态（state）控制组件显示，state 原本定义为当前页面的状态枚举值，举个例子：一个页面由多个组件构成，其中每几个组件可以构成一个页面的独特状态，一个页面由几个这种状态构成，因此可以通过 state 来控制页面的状态。
- **子状态支持**: 支持嵌套的子状态（subState），一个页面的某个状态下，可能还会有其它的状态，subState 就是用来解决这个问题的。
- **上下文数据**: 丰富的状态上下文信息，可以记录上下文的数据变化信息。
- **组件映射**: 状态与组件的自动映射，此处做了一些组件的映射，可以不考虑用到此处，此处是冗余的设计。

##### 📝 操作记录
- **操作路径记录**: 完整记录用户的操作序列
- **会话管理**: 用户会话的生命周期管理

##### 💾 数据持久化
- **LocalStorage 存储**: 自动保存状态历史
- **会话恢复**: 页面刷新后恢复用户状态
- **数据导出/导入**: 支持数据的备份和恢复
- **可配置存储**: 灵活的存储策略配置

##### 🛡️ 状态守卫
- **前置守卫**: 状态切换前的验证和拦截
- **后置守卫**: 状态切换后的回调处理
- **异步支持**: 支持异步状态验证

#### 📁 文件结构
```
src/
├── stores/
   └── stateRouter.ts          # 🔥 核心状态路由管理器
```
#### API说明文档

|序号|参数|说明|参数数据类型|是否必须|默认值|备注说明|
|----|----|----|----|----|----|----|
|1|page|页面名称|string|是|无|初始化时需要设置初始值|
|2|state|当前状态|string|是|无|初始化时需要设置初始值|
|3|subState|子状态|string|否|无|无|
|4|params|参数|string|否|无|无|
|5|query|查询参数|string|否|无|无|
|6|meta|元数据|string|否|无|无|
|7|component|组件名称|sring|否|无|无|
|8|componentProps|组件的 props|string|否|无|无|
|9|context|上下文数据|string|否|无|无|
|10|timestamp|创建的时间戳|number|是|Date.now()|无|
|11|title|标题|string|否|无|无|
|12|description|描述|string|否|无|无|

#### 🚀 快速开始

##### 1. 初始化路由
```typescript
import { useStateRouter, useStateRouteGuards } from '@/stores/stateRouter'

// 状态路由管理
const stateRouter = useStateRouter()
// 状态路由守卫管理器
const routeGuards = useStateRouteGuards()

onMounted(()=>{
  // 初始化会话
  if (!stateRouter.currentSessionId) {
    // 传入页面名称
    stateRouter.initSession('newConversation')
  }
});

// 页面卸载时结束会话
onUnmounted(() => {
  stateRouter.endCurrentSession()
})
```


##### 2. 路由的数据以及方法
```typescript
// 路由数据
// 当前路由
const currentState = computed(() => stateRouter.currentRoute)
// 是否可以回退
const canGoBack = computed(() => stateRouter.canGoBack)
// 路由长度
const historyCount = computed(() => stateRouter.routeHistory.length)
// 当前路由的索引值
const currentIndex = computed(() => stateRouter.currentHistoryIndex)
// 最新的 10 条历史记录
const recentHistory = computed(() => stateRouter.getHistory(10).reverse())

// 路由 push
stateRouter.push({
  page: 'newConversation',
  state: 'chatting',
  component: 'ChatComponent',
  context: { chatStarted: true },
  meta: {
    userAction: '开始聊天',
    isUserAction: true
  },
  title: '聊天界面'
})

// 路由回退
stateRouter.back()

// 跳转到具体的路由的第几条数据
stateRouter.go(index)

// 路由前置守卫
routeGuards.beforeEach((to, from, next) => {
  console.log('状态切换:', { from: from.state, to: to.state })
  
  // 可以添加状态切换的验证逻辑
  if (to.state === 'error' && !to.context?.errorMessage) {
    to.context = { ...to.context, errorMessage: '发生了未知错误' }
  }
  
  next()
})

// 路由后置守卫
routeGuards.afterEach((to, from) => {
  console.log('状态切换完成:', { from: from.state, to: to.state })
  
  // 记录状态切换的分析数据
  if (to.meta?.userAction) {
    console.log('用户操作记录:', to.meta.userAction, to.meta.actionData)
  }
})

```
### CoMi sdk 

#### 概述
CoMi sdk是针对外层嵌套 CoMi 的业务系统与 CoMi 进行通信的一套机制，可以通过 sdk 向 CoMi 发送消息对其进行操作

##### 引入 SDK

引入 comi-entry.js 文件

---
##### 使用方式

###### 监听 comiReady
```javascript
window.comiEventBus.$on('comiReady',(data)=>{
    console.log(data);
});
```

###### 界面控制

```javascript

// 打开侧边栏
window.comiEventBus.$emit('openDrawer');

// 隐藏 comi
window.comiEventBus.$emit('hideComi');

// 展示 comi
window.comiEventBus.$emit('showComi');

// 隐藏输入框 content.value 为 true 时显示输入框，为 false 时隐藏输入框
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'hideInputBox',
    content: { value: true },
});

// 展开窗口
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'expand',
});

// 折叠窗口
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'collapse',
});

// 关闭窗口
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'close',
});
```
###### 发送消息
```javascript
// 基础消息发送  option.isHide 为 true 时会隐藏问题，直接显示答案。为 false 时会显示问题和答案
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'sendMsg',
    content: { 
        value: 'Hello from EventBus!', 
        option: { isHide: false } 
    },
});

```

###### 重定向助手
```javascript
// 重定向到指定助手
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'redirectAssistId',
    content: { 
        id: '-170804761875430614', 
    },
});
```


###### 连续调用示例
```javascript
// 打开侧边栏并且定位到Id 为-170804761875430614的助手，同时给助手发消息
window.comiEventBus.$emit('openDrawer',{
  callback:function(){
    console.log('openDrawer');
    window.comiEventBus.$emit('dispatchSdkAction', {
        type: 'redirectAssistId',
        content: { 
            id: '-170804761875430614',
            callback:function(){
              window.comiEventBus.$emit('dispatchSdkAction', {
                type: 'sendMsg',
                content: { 
                    value: 'Hello from EventBus!', 
                    option: { isHide: false } 
                },
              });
            }
        },
    });
  }
});
```

---

##### 📚 API参考

###### window.comiEventBus

| 方法 | 参数 | 说明 |
|------|------|------|
| `$emit(event, data)` | `event: string, data: object` | 发送事件 |
| `$on(event, handler)` | `event: string, handler: function` | 监听事件 |
| `$off(event, handler?)` | `event: string, handler?: function` | 取消监听 |
