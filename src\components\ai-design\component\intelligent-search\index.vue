<template>
  <div class="intelligent-search-page-wrapper flex-1 flex">
    <IntelligentSearch />
    <!-- <AiFooter :showNewTopicBtn="false"/> -->
  </div>
</template>

<script setup lang="ts">
import AiFooter from '@/components/aiFooter/index.vue';
import { defineOptions } from 'vue';
import IntelligentSearch from './IntelligentSearch.vue';
import { useChatList } from '@/stores/chatList';

defineOptions({
  name: 'IntelligentSearch',
});

const isPortal = inject('isPortal');
const uChatList = useChatList();
// 如果不是全屏或者双屏模式下，采用小页面模式显示
const isCopilotScreen = computed(() => {
  return !isPortal || uChatList.dynamicData.dobuleScreenData.show;
});
</script>

<style scoped lang="less">
.intelligent-search-page-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  justify-content: space-between;
}
</style>
