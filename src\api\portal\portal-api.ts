import axios from 'axios'
import portalConfigJson from '#/portal-config.json'
import portalDataJson from '#/portal.json'
import searchRecommendQuestionJson from '#/search-recommend-question.json'
import { getQueryString } from '@/utils/common';

const topWindow:any = window.top || window;
const viewModeName = 'comiTargetVersion';
const viewMode = getQueryString(viewModeName) || window.sessionStorage.getItem(viewModeName) || topWindow[viewModeName] || window.localStorage.getItem(viewModeName) || '1.0';

console.log(viewMode);
function getRootUrl() {
  if (import.meta.env.DEV) {
    // 使用本地数据
    return '';
  } else {
    // 使用线上数据
    if(viewMode === 'A6'){
      return '/seeyon/ai-platform/ai-static/ai-copilot/public/A6';
    }else{
      return '/seeyon/ai-platform/ai-static/ai-copilot/public';
    }
  }
}
const saveStore: any = {
  config: null,
  isRunning: false,
  callBackList: []
};
// 定义 SDK 类
class PortalApi {
  // 构造函数，初始化 SDK
  constructor() {

  }
  async getConfig(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      if (!saveStore.config) {
        if (!saveStore.isRunning) {
          saveStore.isRunning = true;
          let config: any = null
          if (!getRootUrl()) {
            config = portalConfigJson;
          } else {
            const url = `${getRootUrl()}/portal-config.json?t=${new Date().getTime()}`
            const response = await axios.get(url);
            config = response.data;
          }
          // 如果url中不包含iform配置，则过滤掉iform相关的菜单
          if (location.href.indexOf('iFormCompanyld') == -1 && location.href.indexOf('secretkey') == -1) {
            config.menus = config.menus.filter((item: any) => {
              return !item.key.includes('iform')
            });
          }
          const codeList: any = config.menus.filter((item: any) => !!item.code).map((item: any) => item.code);
          const response2 = await axios.post('/seeyon/ai-platform/ai-manager/assistant/info/auth-assistant', { codeList });
          const dataArray = response2.data.data || [];
          const codeObj = dataArray.reduce((acc: any, item: any) => {
            acc[item.code] = item.id;
            return acc;
          }, {});
          config.menus = config.menus.filter((item: any) => {
            return !item.code || !!codeObj[item.code];
          })

          const isPortal = getQueryString('isPortal') === 'true';
          if (!isPortal) {
            config.menus = config.menus.filter((item: any) => {
              return item.key !== 'fullMenu';
            });
          }
          config.menus.forEach((item: any) => {
            item.id = codeObj[item.code];
          });
          saveStore.config = config;
          saveStore.callBackList.forEach((item: any) => {
            item();
          });
          saveStore.callBackList = [];
          saveStore.isRunning = false;
          resolve(saveStore.config);
        } else {
          saveStore.callBackList.push(async () => {
            resolve(saveStore.config);
          });
        }
      } else {
        resolve(saveStore.config);
      }
    });
  }
  // sdk-下次不再显示
  async getPortalInfo(): Promise<any> {
    // 如果是本地启动，则使用本地数据
    if (!getRootUrl()) {
      return portalDataJson;
    }
    const url = `${getRootUrl()}/portal.json?t=${new Date().getTime()}`
    const response = await axios.get(url);
    // 组装各种数据，沟通过接口

    return response.data
  }

  // 获取智能搜索推荐问题
  async getSearchRecommendQuestion(): Promise<any> {
    if (!getRootUrl()) {
      return searchRecommendQuestionJson;
    }
    const url = `${getRootUrl()}/search-recommend-question.json?t=${new Date().getTime()}`
    const response = await axios.get(url);
    return response.data
  }
}

// 导出 SDK 类
export default new PortalApi();
