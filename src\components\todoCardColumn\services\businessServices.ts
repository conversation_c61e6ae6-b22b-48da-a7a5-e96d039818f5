import { Modal, message } from 'ant-design-vue';
import { BusinessLogicUtils, DomUtils } from '../utils/pureUtils';
import type { ButtonType, ConfirmActionData } from '../types';

/**
 * 按钮状态计算服务
 */
export class ButtonStateService {
  /**
   * 计算按钮状态对象（纯函数版本）
   */
  static calculateButtonState(button: ButtonType, renderInfo?: any) {
    const { handleType, opinionPolicy, paramMap } = button;

    return {
      dataType: renderInfo?.appId || paramMap?.appId,
      submitData: button,
      hasOpinionHandle: opinionPolicy?.opinionPolicy === "1",
      hasCancelOrTerminateOpinion: opinionPolicy?.opinionPolicy === "1" ||
        (opinionPolicy?.cancelOpinionPolicy === "1" && ['Cancel', 'Terminate', 'Return'].includes(handleType)),
      hasDisagreeOpinion: opinionPolicy?.opinionPolicy === "1" ||
        (opinionPolicy?.disAgreeOpinionPolicy === "1" && paramMap?.attitudeKey === "disagree")
    };
  }

  /**
   * 判断是否满足意见策略条件（纯函数版本）
   */
  static needOpinionPolicyAction(button: ButtonType, buttonState: any): boolean {
    const { handleType, opinionPolicy, paramMap } = button;

    // 智迈适配触发器检查
    if (!BusinessLogicUtils.checkTrigger(button)) {
      return false;
    }

    // 复杂的不同意操作判断
    const hasDisagreeAndCancelOrTerminate =
      paramMap?.attitudeKey === "disagree" &&
      (paramMap?.customAction?.defaultAction === "Terminate" ||
        paramMap?.customAction?.defaultAction === "Cancel") &&
      paramMap?.customAction?.isOptional === "0" &&
      opinionPolicy?.cancelOpinionPolicy === "1";

    // 只有满足意见策略条件才返回 true
    return buttonState.hasOpinionHandle ||
      buttonState.hasCancelOrTerminateOpinion ||
      buttonState.hasDisagreeOpinion ||
      hasDisagreeAndCancelOrTerminate;
  }
}

/**
 * 对话框服务
 */
export class DialogService {
  /**
   * 显示确认对话框
   */
  static showConfirmDialog(title: string, content: string): Promise<boolean> {
    return new Promise((resolve) => {
      Modal.confirm({
        title,
        content,
        okText: '确定',
        cancelText: '取消',
        onOk: () => resolve(true),
        onCancel: () => {
          DomUtils.removeListStyle();
          resolve(false);
        }
      });
    });
  }
}

/**
 * 操作处理服务
 */
export class ActionProcessingService {
  /**
   * 处理催办逻辑
   */
  static handleHasten(button: ButtonType): void {
    // 这里应该实现催办逻辑
    console.log('处理催办:', button);
    message.info('催办功能');
  }

  /**
   * 处理分配逻辑
   */
  static handleAllocation(button: ButtonType): void {
    // 这里应该实现分配逻辑
    console.log('处理分配:', button);
    message.info('分配功能');
  }
}

/**
 * 会议服务
 */
export class MeetingService {
  /**
   * 打开个人会议编辑弹窗
   */
  static openPersonalDialog(
    meetingId: string,
    setMeetingEditId: (id: string) => void,
    setMeetingEditVisible: (visible: boolean) => void
  ): void {
    if (!meetingId) {
      console.error('meetingId 不能为空');
      return;
    }

    console.log('打开个人会议编辑弹窗，meetingId:', meetingId);

    // 设置会议ID并显示弹窗
    setMeetingEditId(meetingId);
    setMeetingEditVisible(true);
  }

  /**
   * 处理会议编辑成功
   */
  static handleMeetingEditSuccess(
    setMeetingEditVisible: (visible: boolean) => void,
    setMeetingEditId: (id: string) => void
  ): void {
    console.log('会议编辑成功');
    setMeetingEditVisible(false);
    setMeetingEditId('');
  }

  /**
   * 处理会议编辑取消
   */
  static handleMeetingEditCancel(
    setMeetingEditVisible: (visible: boolean) => void,
    setMeetingEditId: (id: string) => void
  ): void {
    console.log('取消会议编辑');
    setMeetingEditVisible(false);
    setMeetingEditId('');
  }
}

/**
 * NextStep流程服务
 */
export class NextStepService {
  /**
   * 处理NextStep流程（简单包装）
   */
  static handleNextStepFlow(
    button: ButtonType, 
    confirmData: ConfirmActionData,
    handleNextStep: (button: ButtonType) => void
  ): void {
    console.log('触发NextStep流程，条件满足:', {
      customAction: button.paramMap?.customAction,
      handleType: button.handleType,
      attitudeKey: button.paramMap?.attitudeKey
    });
    
    // 调用现有的 handleNextStep 方法（传递button和空事件）
    handleNextStep(button);
  }
} 