<template>
  <div class="intelligent-question-count-page-wrapper flex-1 flex">
    <IntelligentQuestionCount />
    <!-- <AiFooter :showNewTopicBtn="false" /> -->
  </div>
</template>

<script setup lang="ts">
import AiFooter from '@/components/aiFooter/index.vue';
import { defineOptions, computed } from 'vue';
import IntelligentQuestionCount from './IntelligentQuestionCount.vue';
import { useChatList } from '@/stores/chatList';
defineOptions({
  name: 'IntelligentQuestionCount',
});

const isPortal = inject('isPortal');
const uChatList = useChatList();
// 如果不是全屏或者双屏模式下，采用小页面模式显示
const isCopilotScreen = computed(() => {
  return !isPortal || uChatList.dynamicData.dobuleScreenData.show;
});
</script>

<style scoped lang="less">
.intelligent-question-count-page-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  justify-content: space-between;
}
</style>
