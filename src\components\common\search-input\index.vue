<template>
  <Input
    allow-clear
    :placeholder="placeholder"
    class="search"
    @input="handleInput($event.target.value)"
    @focus="handleFocus"
    :value="searchValue"
    :bordered="false"
    style="width: 100%"
    @pressEnter="handlePressEnter"
  >
    <template #prefix>
      <i class="iconfont ai-icon-m3-search search_icon" @click="handleClick"></i>
    </template>
  </Input>
</template>
<script lang="ts" setup>
import emitter from '@/utils/bus';
import { Input } from 'ant-design-vue';
import { useMenu } from '@/stores/global';

defineOptions({
  name: 'SearchInput',
});

const props = defineProps<{
  placeholder: string;
  type: string;
  pressEnter?: () => void;
  click?: () => void;
}>();

const uMenu = useMenu();

const searchValue = ref<string | undefined>('');
const hasFocus = ref(false);

// 输入
const handleInput = (val: string | undefined) => {
  searchValue.value = val;
  emitter.emit('emitEvent', {
    type: props.type,
    params: {
      searchValue: val,
    },
  });
};

// 回车
const handlePressEnter = () => {
  emitter.emit('emitEvent', {
    type: 'pressEnter',
    params: {
      searchValue: searchValue.value,
    },
  });
};

// 点击
const handleClick = () => {
  emitter.emit('emitEvent', {
    type: 'click',
    params: {
      searchValue: searchValue.value,
    },
  });
};
// 聚焦搜索框
const handleFocus = () => {
  hasFocus.value = true;
};
</script>
<style scoped lang="less">
.search {
  border-radius: 4px;
  border: 1px solid rgba(216, 218, 223, 1) !important;
  &:hover {
    border-color: @primary-color !important;
  }
  :deep(.ant-input-suffix) {
    line-height: 5px;
  }
}

.search_icon {
  font-size: 14px;
  color: #00000066;
}
</style>
