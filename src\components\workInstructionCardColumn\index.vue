<template>
  <!-- 工作指引卡片主容器 -->
  <div
    class="work-instruction-card px-[16px] py-[20px] rounded-[12px] w-full h-[160px] leading-[20px]"
  >
    <!-- 卡片标题 -->
    <header class="card-header mb-[12px]">
      <h2 class="text-[16px] font-semibold text-gray-800">工作指引</h2>
    </header>

    <!-- 指引项列表 -->
    <div class="instruction-list space-y-3">
      <!-- 使用 v-for 循环渲染列表项 -->
      <div
        v-for="item in dataList"
        :key="item.title"
        class="instruction-item flex items-center justify-between mb-[12px]"
        @click="handleItemClick(item)"
      >
        <!-- 指引项文本 -->
        <span class="item-text text-[12px] text-gray-700">{{ item.title }}</span>
        <!-- 指引项图标 (右上箭头) -->
        <AImage :src="Arrow" :width="14" :height="14" :preview="false" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { toRefs } from 'vue';
// 引入 Ant Design Vue 的图标
import type { ColumnProps, TodoRenderInfo, MeetingItem } from '@/types/portalTypes';
import { Image as AImage } from 'ant-design-vue';
import Arrow from '@/assets/imgs/arrow.png';
import cardInstance from '@/stores/card';

const props = withDefaults(defineProps<ColumnProps>(), {
  config: () => ({
    maxItems: 5,
    title: '',
  }),
  data: () => ({
    dataList: [],
  }),
});
const { data, config } = toRefs(props);
const { title } = config.value;
const { dataList } = data.value;

// 处理列表项点击事件的方法
const handleItemClick = (item: TodoRenderInfo | MeetingItem) => {
  cardInstance.sendMessage(item.title || '');
};
</script>

<style scoped lang="less">
.work-instruction-card {
  // --- 毛玻璃效果 ---
  // 设置半透明背景色（浅蓝色调）
  background-color: rgba(230, 240, 255, 0.5);
  // 应用背景模糊效果
  backdrop-filter: blur(8px);
  // 兼容 Webkit 内核浏览器
  -webkit-backdrop-filter: blur(8px);
  // 设置边框（浅蓝色调，部分透明）
  user-select: none;
  // --- 可选样式 ---
  // 添加细微阴影增加层次感
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.card-header h2 {
  // 标题样式 (Tailwind 已应用部分)
  color: #1f2937; // 更深的颜色 (类似 Tailwind gray-800)
}

.instruction-list {
  // 列表样式 (Tailwind 已应用部分)
}

.instruction-item {
  cursor: pointer;
  // 单个列表项样式
  .item-text {
    color: #374151; // 文本颜色 (类似 Tailwind gray-700)
    transition: color 0.2s ease; // 平滑颜色过渡
  }

  .item-icon {
    color: #3b82f6; // 图标颜色 (类似 Tailwind blue-500)
    transition: color 0.2s ease; // 平滑颜色过渡
    // 如果需要，可以在这里添加 transform: rotate(45deg);
    // 但 Ant Design 图标组件通常接受 rotate prop
  }

  // 鼠标悬停效果
  &:hover {
    .item-text {
      color: #2563eb; // 悬停时文本颜色 (类似 Tailwind blue-600)
    }
    .item-icon {
      color: #1d4ed8; // 悬停时图标颜色 (类似 Tailwind blue-700)
    }
  }
  .ai-icon-xing {
    background: linear-gradient(45deg, #46ffff, #5061ff, #2a69fe);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
}
</style>
