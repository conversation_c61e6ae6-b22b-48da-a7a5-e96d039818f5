import { getBusinessSdk } from '@/sdk/business/index';

declare global {
  interface Window {
    COMI_ASSISTANT?: any;
    $?: any
    parentDialogObj?: any
    _ctxPath: string
    openCtpWindow: (params: any) => void,
    getCtpTop: () => void;
    openDocument: any;
    messageLinkConstants: any;
    getA8Top?: any;
  }
}



/** 创建降级方法的通用函数 */
function createFallbackMethod(methodName: string, hasReturn = false) {
  return (...args: any[]) => {
    console.warn(`BusinessSDK不可用，${methodName}方法无效`, args);
    
    // 查找回调函数（通常是最后一个参数且为函数）
    const callback = args.find(arg => typeof arg === 'function');
    
    if (callback) {
      // 对于不同方法，回调参数不同
      if (methodName === 'handleTagClick') {
        callback(args[0]); // 返回原始数据
      } else if (['openDialog', 'openWin'].includes(methodName)) {
        callback(null); // 返回 null
      } else {
        callback(args[0]); // 默认返回第一个参数
      }
    }
    
    return hasReturn ? null : undefined;
  };
}

// 现在只使用BusinessSDK
export function commonBusinessClass(): any {
  console.log('commonBusinessClass: 使用BusinessSDK');
  
  try {
    const sdk = getBusinessSdk();
    if (sdk) {
      console.log('commonBusinessClass: 找到BusinessSDK实例');
      return sdk;
    }
  } catch (error) {
    console.error('commonBusinessClass: BusinessSDK获取失败', error);
  }
  
  // 降级：返回空方法实现
  console.warn('commonBusinessClass: BusinessSDK不可用，返回空实现');
  
  // 定义方法列表
  const simpleMethods = ['expand', 'collapse', 'close', 'sendMsg', 'menuAction'];
  const callbackMethods = ['handleTagClick', 'openPersonalInfo'];
  const returnMethods = ['openDialog', 'openWin'];
  
  const fallbackObject: any = {};
  
  // 生成简单方法
  simpleMethods.forEach(methodName => {
    fallbackObject[methodName] = createFallbackMethod(methodName);
  });
  
  // 生成回调方法
  callbackMethods.forEach(methodName => {
    fallbackObject[methodName] = createFallbackMethod(methodName);
  });
  
  // 生成有返回值的方法
  returnMethods.forEach(methodName => {
    fallbackObject[methodName] = createFallbackMethod(methodName, true);
  });
  
  return fallbackObject;
}


// // 代理顶层窗口属性到当前window，避免内嵌的iframe使用window.parent出现找不到对象问题
export const proxyWindow = () => {
  window.$ = window.parent.$ || window.top?.$;
  window.getCtpTop = window.parent.getCtpTop || window.top?.getCtpTop;
  window.getA8Top = window.parent.getA8Top || window.top?.getA8Top;
  window._ctxPath = window.parent._ctxPath || window.top?._ctxPath || '/seeyon';
}
