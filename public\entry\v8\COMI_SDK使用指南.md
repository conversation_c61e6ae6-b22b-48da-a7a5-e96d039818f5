# COMI SDK 完整使用指南

> 🚀  COMI Assistant SDK 完整使用文档，包含所有调用方式和最佳实践

## 📋 目录

- [快速开始](#快速开始)
- [使用方式：window.comiEventBus]
- [API参考](#api参考)

---

## 🚀 快速开始

### 引入 SDK

引入 comi-entry.js 文件

---
## 使用方式

### 监听 comiReady
```javascript
window.comiEventBus.$on('comiReady',(data)=>{
    console.log(data);
});
```

### 监听事件
```javascript
// 监听展开侧边栏
window.comiEventBus.$on('openDrawer',(data)=>{
    console.log(data);
});
// 监听隐藏 comi
window.comiEventBus.$on('hideComi',(data)=>{
    console.log(data);
});
// 监听展示 comi
window.comiEventBus.$on('showComi',(data)=>{
    console.log(data);
});
// 监听点击事件的穿透
window.comiEventBus.$on('handleTagClick',(data)=>{
    console.log(data);
});
// 监听打开人员信息
window.comiEventBus.$on('openPersonalInfo',(data)=>{
    console.log(data);
});
// 监听打开弹窗
window.comiEventBus.$on('openDialog',(data)=>{
    console.log(data);
});
// 监听新窗口
window.comiEventBus.$on('openWin',(data)=>{
    console.log(data);
});

// 通过dispatchSdkAction发送消息的都通过dispatchSdkAction监听即可
window.comiEventBus.$on('dispatchSdkAction', (data)=>{
    console.log(data);
});
```

### 界面控制

```javascript

// 打开侧边栏
window.comiEventBus.$emit('openDrawer');

// 隐藏 comi
window.comiEventBus.$emit('hideComi');

// 展示 comi
window.comiEventBus.$emit('showComi');


// 隐藏输入框 content.value 为 true 时显示输入框，为 false 时隐藏输入框
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'hideInputBox',
    content: { value: true },
});

// 展开窗口
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'expand',
});

// 折叠窗口
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'collapse',
});

// 关闭窗口
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'close',
});
```
### 发送消息
```javascript
// 基础消息发送  option.isHide 为 true 时会隐藏问题，直接显示答案。为 false 时会显示问题和答案
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'sendMsg',
    content: { 
        value: 'Hello from EventBus!', 
        option: { isHide: false } 
    },
});
window.comiEventBus.$on('dispatchSdkAction', (data)=>{console.log(data);});
```

### 重定向助手
```javascript
// 重定向到指定助手
window.comiEventBus.$emit('dispatchSdkAction', {
    type: 'redirectAssistId',
    content: { 
        id: '-170804761875430614', 
    },
});
```


### 连续调用示例
```javascript
// 打开侧边栏并且定位到Id 为-170804761875430614的助手，同时给助手发消息
window.comiEventBus.$emit('openDrawer',{
  callback:function(){
    console.log('openDrawer');
    window.comiEventBus.$emit('dispatchSdkAction', {
        type: 'redirectAssistId',
        content: { 
            id: '-170804761875430614',
            callback:function(){
              window.comiEventBus.$emit('dispatchSdkAction', {
                type: 'sendMsg',
                content: { 
                    value: 'Hello from EventBus!', 
                    option: { isHide: false } 
                },
              });
            }
        },
    });
  }
});


```

---

## 📚 API参考

### window.comiEventBus

| 方法 | 参数 | 说明 |
|------|------|------|
| `$emit(event, data)` | `event: string, data: object` | 发送事件 |
| `$on(event, handler)` | `event: string, handler: function` | 监听事件 |
| `$off(event, handler?)` | `event: string, handler?: function` | 取消监听 |


 