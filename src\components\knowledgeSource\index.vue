<template>
  <div class="knowledge-source-wrapper">
    <template v-if="!iframeUrl">
      <div class="knowledge-source-content">
        <div class="header">
          <span class="title">参考知识源</span>
          <div class="btn-wrapper">
            <!-- <span class="btn">生成方案</span> -->
          </div>
        </div>
        <AiTabs
          v-if="tabs.length > 0"
          ref="AiTabsRef"
          :activeTab="activeTab"
          :tabs="tabs"
          canWhell
          @tabClick="tabClick"
          :notNeedSelect="true"
        />
        <div class="content" ref="scrollContent">
          <template v-for="(item, index) in contentData" :key="item.id">
            <KnowledgeSourceItem :data="item" @click="handleClick" />
            <span v-if="index !== contentData?.length - 1" class="split-line-heng"></span
          ></template>
        </div>
      </div>
    </template>
    <template v-else>
      <Spin :spinning="iframeLoading" wrapperClassName="iframe-spin">
        <KnowledgeDetail :iframeUrl="iframeUrl" @iframeLoad="iframeLoad" />
      </Spin>
    </template>
  </div>
</template>

<script setup lang="ts">
import { downloadFile } from '@/utils/tool';
import type { citationJSONItem, TabItem } from '@/types';
import { ref, computed, onMounted, nextTick, onUnmounted, watch } from 'vue';
import { commonBusinessClass } from '@/utils/commonBusinessClass';
import { useChatList } from '@/stores/chatList';
import AiTabs from '@/components/aiTabs/index.vue';
import KnowledgeDetail from './knowledgeDetail.vue';
import { useStateRouter } from '@/stores/stateRouter';
import KnowledgeSourceItem from './knowledgeSourceItem.vue';
import { classifyData, formatKnowLedgeData } from '@/utils/oaDataMap';
import { useGlobal, useMenu } from '@/stores/global';
import { Spin } from 'ant-design-vue';

const uGloabal = useGlobal();
const uChatList = useChatList();
const uMenu = useMenu();
// 状态路由管理
const stateRouter = useStateRouter();
// 当前点击知识源
const iframeUrl = ref('');
const AiTabsRef = ref();
const tabs = ref<TabItem[]>([]);
const contentData = computed(() => {
  return tabs.value.find((tab) => tab.key === activeTab.value)?.content || [];
});
const activeTab = ref('all');
const iframeLoading = ref(false);
const scrollPos = ref(0);
const scrollContent = ref();
const isPortal = inject('isPortal');

const iframeLoad = () => {
  iframeLoading.value = false;
};

const tabClick = (activeKey: string) => {
  activeTab.value = activeKey;
};
const handleClick = (item: citationJSONItem) => {
  if (item.appType == '0') {
    const url = `/seeyon/ai-platform/ai-manager/repository/export/download/file/${item.resourceId}`;
    downloadFile(url, item.title);
  } else {
    if (scrollContent.value) {
      scrollPos.value = scrollContent.value.scrollTop;
    }
    const businessClass = commonBusinessClass();

    const { appType, entityId } = item;
    const params = {
      appType: appType,
      linkId: entityId,
      clientType: '',
    };
    if (businessClass && typeof businessClass.getOaUrl === 'function') {
      const oaUrl = businessClass.getOaUrl(params)
      // ||
      // 'http://127.0.0.1:8088/seeyon/bulData.do?method=bulView&bulId=-4031050576498965345';
      // 'http://127.0.0.1:8088/seeyon/collaboration/collaboration.do?method=summary&openFrom=listPending&affairId=1903322102843263426&showTab=false';
      if (oaUrl) {
        // 如果是侧边栏，直接通过window.open打开
        if (!isPortal) {
          window.open(oaUrl, '_blank');
          return;
        }
        stateRouter.clearHistory();
        nextTick(() => {
          stateRouter.push({
            page: 'knowledgeSource',
            state: 'list',
            context: {
              activeTab,
              list: tabs.value,
              backBtnPos: 0,
              allCardData: uChatList.dynamicData.allCardData,
              menuInfo: useMenu().currentMenuInfo,
              selectInfo: uChatList.dynamicData.selectedAgentInfo,
            },
          });
          stateRouter.push({
            page: 'knowledgeDetail',
            state: 'knowledgeDetail',
            context: {
              canBack: true,
              canExpand: true,
            },
          });
        });
        uChatList.chatActions.setDynamicData('dobuleScreenIsKnowledge', true);
        iframeUrl.value = oaUrl;
        iframeLoading.value = true;
      }
    }
  }
};

const getKnowledgeData = (data: any) => {
  const classifiedData = classifyData(data);
  tabs.value = formatKnowLedgeData(classifiedData);
  activeTab.value = tabs.value[0].key;
  // 重新定位到第一个
  if (AiTabsRef.value) {
    AiTabsRef.value.scrollToTab(activeTab.value);
  }
  iframeUrl.value = '';
};

onMounted(() => {
  nextTick(() => {
    getKnowledgeData(uChatList.dynamicData.dobuleScreenData.data);
  });
});

onUnmounted(() => {
  stateRouter.clearHistory();
});
// 回填知识源列表
watch(
  () => stateRouter.currentRoute,
  (newRoute) => {
    if (newRoute && newRoute.page === 'knowledgeSource') {
      const context = newRoute.context;
      if (context) {
        tabs.value = context.list;
        activeTab.value = context.activeTab;
        const selectedAgentInfoId = (uChatList.dynamicData.selectedAgentInfo as any)?.id;
        if (selectedAgentInfoId !== context.selectInfo?.id) {
          uMenu.changeMenu(context.menuInfo, { isAsit: true, isRedirectAist: true });
          nextTick(() => {
            uChatList.chatActions.setDynamicData('allCardData', context.allCardData);
            uChatList.chatActions.setDynamicData('selectedAgentInfo', context.selectInfo);
          });
        }
        iframeUrl.value = '';
        uChatList.chatActions.setDynamicData('dobuleScreenIsKnowledge', false);
      }
      nextTick(() => {
        if (scrollContent.value && scrollPos.value) {
          scrollContent.value.scrollTop = scrollPos.value;
        }
      });
    }
  },
);

watch(
  () => uChatList.dynamicData.dobuleScreenData,
  (newVal) => {
    if (newVal.show && newVal.data) {
      getKnowledgeData(newVal.data);
    }
  },
);
</script>

<style lang="less" scoped>
.knowledge-source-wrapper {
  height: 100%;
  width: 100%;
  overflow: auto;

  // margin-top: 58px;

  .knowledge-source-content {
    height: 100%;
    padding: 0 8px 16px;
    width: 100%;
    // min-width: 1080px;
    margin: 0 auto;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 8px;
      box-sizing: border-box;
      height: 24px;

      .title {
        font-family: PingFang SC;
        font-weight: @font-weight-500;
        font-size: 16px;
        line-height: 24px;
        color: #000;
      }

      .btn-wrapper {
        display: flex;
        gap: 12px;
        height: 100%;
      }
      .btn {
        border: 1px solid @sky;
        color: @sky;
        height: 24px;
        border-radius: 4px;
        font-size: 14px;
        line-height: 22px;
        padding: 1px 8px;
        cursor: pointer;
      }
    }

    :deep(.ai-tabs-wrapper) {
      margin: 12px 0;
      .ai-tab-content {
        .tab-item {
          padding: 3px 8px;
        }
      }
    }

    .content {
      display: flex;
      flex-direction: column;
      gap: 8px;
      max-height: calc(100% - 90px);
      overflow-y: auto;
      .split-line-heng {
        display: block;
        width: 100%;
        height: 0.5px;
        background-color: #d8dadf;
      }
    }
  }
  .iframe-spin {
    width: 100%;
    height: 100%;

    :deep(.ant-spin-container) {
      width: 100%;
      height: 100%;
    }
  }
}
.knowledge-source-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.knowledge-source-wrapper::-webkit-scrollbar-thumb:hover {
  background: #91a0b5;
}
.knowledge-source-wrapper::-webkit-scrollbar-thumb {
  height: 50px;
  background: #bec7d5;
  border-radius: 6px;
}
.knowledge-source-wrapper::-webkit-scrollbar-track-piece {
  width: 8px;
  background-color: #f5f5f5;
  border-radius: 6px;
}
</style>
