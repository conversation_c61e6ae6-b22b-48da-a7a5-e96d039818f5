<template>
  <div class="portal-collaboration h-full w-full">
    <!-- 页面头部 -->
    <div class="mb-[22px]">
      <h1 class="portal-collaboration__title">协同流程</h1>
      <p class="portal-collaboration__subtitle">全新智能协同办公模式</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="portal-collaboration__main">
      <!-- 左侧内容 -->
      <div class="portal-collaboration__left">
        <!-- 智能办公应用卡片 -->
        <div class="mb-[12px]">
          <SmartOfficeCard></SmartOfficeCard>
        </div>

        <!-- 最近使用 -->
        <div class="flex-1">
          <RecentUsage
            :data="recentUsageData"
            @item-click="handleRecentItemClick"
          />
        </div>
      </div>

      <!-- 右侧功能卡片 -->
      <div class="ml-[12px] h-[412px] 2xl:w-[248px] portal-collaboration__right">
        <div class="portal-collaboration__feature-cards">
          <FeatureCard
            v-for="feature in featureCardsData"
            :key="feature.id"
            :data="feature"
            @click="handleFeatureClick"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onActivated, inject } from 'vue'
import RecentUsage from './components/RecentUsage.vue'
import SmartOfficeCard from './components/SmartOfficeCard.vue'
import FeatureCard from './components/FeatureCard.vue'
import { openDobuleScreen } from '@/utils/storesUtils';
import EmailIcon from '@/assets/imgs/email.png';
import AbstractIcon from '@/assets/imgs/abstract.png';
import WorkflowIcon from '@/assets/imgs/wrokflow.png';
import { useRedirectAssistant } from './hooks';
import { Modal } from 'ant-design-vue';

const isPortal = inject('isPortal');
const sdkInstance = inject('sdkInstance') as any;

defineOptions({
  name: 'PortalCollaboration',
});

const { redirectToAssit, getAssistId, redirectToAssistantByCode } = useRedirectAssistant();
// 临时使用模拟数据，避免API导入问题
interface RecentUsageItem {
  id: string
  subject: string
  belongOrg: string
}

interface FeatureData {
  id: string
  title: string
  description: string
  icon: string
  url: string,
  plugin: string,
  agentCode: string,
  openDouble?: boolean
}

// 响应式数据
const recentUsageData = ref<RecentUsageItem[]>([])

// 功能卡片数据
const featureCardsData = ref<FeatureData[]>([
  {
    id: 'mailbox',
    title: '协同邮箱',
    description: '全新企业内部信息沟通模式，实现信息即时同步',
    icon: EmailIcon,
    url: '/common/internalmail/index.html?renderType=1&showModel=false&menuSummary=add&_resourceCode=imail_compose',
    plugin: 'imail_compose',
    agentCode: ''
  },
  {
    id: 'approval',
    title: '待办中心',
    description: '待办流程处理，包含辅助审批、流程预审等智能能力。',
    icon: AbstractIcon,
    url: '/collaboration/collaboration.do?method=listPending&isFromComi=true',
    plugin: '',
    agentCode: '',
    openDouble: true
  },
  {
    id: 'analysis',
    title: '流程效能分析',
    description: '精准掌握流程运营数据，助力企业流程优化',
    icon: WorkflowIcon,
    url: '',
    plugin: '',
    agentCode: 'assist-6113044676125769061'  // TODO 这个code是流程效能开发提供的，类生产环境中已同步
  }
])

const ctxPath = window._ctxPath || '/seeyon';

// 方法
const loadRecentUsageData = async () => {
  recentUsageData.value = await getTemplateList();
}

const getTemplateList = async (): Promise<any> => {
  const params = new URLSearchParams();
  params.append('managerMethod', 'getRecentTemplateVos');
  params.append('arguments', JSON.stringify(['1', 12]));
  const res = await fetch('/seeyon/ajax.do?method=ajaxAction&managerName=templateManager', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: params
  });
  return res.json();
}

const handleRecentItemClick = (item: RecentUsageItem) => {
  window.open(`${ctxPath}/collaboration/collaboration.do?method=newColl&fromNewItem=true&showTab=true&templateId=${item.id}&menuSummary=add`, '_blank');
  // openDobuleScreen(`${ctxPath}/collaboration/collaboration.do?method=newColl&fromNewItem=true&showTab=true&templateId=${item.id}&menuSummary=add`, 'iframe');
}

const handleFeatureClick = async (featureData: FeatureData) => {
  if (featureData.agentCode) {
    // 跳转智能助手
    await redirectToAssistantByCode(featureData.agentCode, true);
  } else if (featureData.url) {
    // 处理外部链接跳转
    if (featureData.plugin) {
      if (!(window as any).top?._hasResource(featureData.plugin)) {
        Modal.confirm({
          title: '提示',
          content: '抱歉，您暂无此功能权限。如有需要，请联系管理员',
          okText: '确认',
          cancelText: '取消',
        });
        return;
      }
      window.open(`${ctxPath}${featureData.url}`, '_blank');
    } else if(featureData.openDouble){
      openDobuleScreen(`${ctxPath}${featureData.url}`, 'iframe');
    }else {
      window.open(`${ctxPath}${featureData.url}`, '_blank');
    }
  }
}

const initInput = () =>{
  setTimeout(() => { //外面组件的显示用了nextTick，这里用setTimeout，确保组件显示了才传值
    // 设置输入框默认值
    sdkInstance.sendMsg('', {
      inputTemplate: [
        {
          id: '1',
          text: '帮我新建',
        },
        {
          id: '2',
          type: 'input',
          placeholder: '流程模版名称',
          focus: true,
        },
      ],
    });
  });
}

// 生命周期
onMounted(() => {
  loadRecentUsageData()
  if(!isPortal) { //侧边栏目前没缓存，在mounted里面初始化
    initInput();
  }
})
onActivated(()=>{
  initInput();
})
</script>

<style scoped lang="less">
.portal-collaboration {
  overflow: auto;
  &__title {
    font-size: 24px;
    font-weight: 600;
    line-height: 32px;
    margin-bottom: 12px;
  }

  &__subtitle {
    font-size: 13px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.9);
  }

  &__main {
    display: grid;
    grid-template-columns: 902fr 248fr;
    align-items: start;

    @media (max-width: 1440px) {
      grid-template-columns: 592fr 220fr;
    }
  }

  &__left {
    display: flex;
    flex-direction: column;
  }

  &__feature-cards {
    display: flex;
    flex-direction: column;
    gap: 9px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(20px);
    align-items: stretch;
  }
}
</style>
