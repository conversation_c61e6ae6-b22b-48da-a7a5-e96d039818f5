<template>
  <div class="preview_container" v-if="dialogData">
    <ComiExternalBiCardDialog
      :data="dialogData"
      ref="biCardRef"
      :card-options="biCardOptions"
    ></ComiExternalBiCardDialog>
  </div>
  <div v-else>暂无数据</div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue';

const props = defineProps({
  transParams: {
    type: Object,
    default: () => ({})
  },
  height: {
    type: Number,
  }
})

const dialogData = (window?.frameElement as any)?.transParams || window?.parentDialogObj?.BiPreview?.getTransParams() || props.transParams;

const biCardOptions = ref({
  status: false,
  lookSql: false,
  canLookSql: true,
  typeSource: 'pcDialog'
})


</script>
<style lang="less" scoped>
.preview_container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px 24px 24px;
  background-color: #fff;
  .chart_container {
    flex: 1;
    margin-top: 12px;

    :deep(.data-chart) {
      height: 100%;
    }
  }
}
</style>
