<template>
  <div
    class="action-buttons-wrapper"
    v-if="hasButtons"
    ref="actionButtonsWrapperRef"
  >
    <div
      v-show="shouldShowButtons"
      class="action-buttons-container"
    >
      <!-- 半透明遮罩层 -->
      <!-- <div class="action-buttons-mask"></div> -->
      <!-- 按钮组 -->
      <div class="action-buttons flex items-center gap-[8px]">
        <!-- 前几个按钮直接显示 -->
         <config-provider :autoInsertSpaceInButton="false" v-for="button in visibleButtons"
         :key="getButtonKey(button)">
        <Button
          
          :type="getButtonType(button.handleType)"
          :loading="isButtonLoading(button)"
          :class="['action-btn', { 'compact-btn': button.handleType === 'top' || button.handleType === 'unTop' }]"
          size="middle"
          @click.stop="handleButtonClick(button, $event)"
        >
          <template v-if="button.handleType === 'top'">
            <i class="iconfont ai-icon-zhiding1"></i>
          </template>
          <template v-else-if="button.handleType === 'unTop'">
            <i class="iconfont ai-icon-quxiaozhiding"></i>
          </template>
          <template v-else>
            {{ button.name }}
          </template>
        </Button>
      </config-provider>
        <!-- 更多按钮下拉菜单 -->
        <Dropdown
          v-if="hiddenButtons.length > 0"
          :trigger="['hover']"
          placement="bottomRight"
          @click.stop
          @openChange="handleDropdownVisibleChange"
        >
          <Button
            size="middle"
            type="default"
            class="action-btn compact-btn"
            ref="moreButtonRef"
          >
            <i class="iconfont ai-icon-vertical-more"></i>
          </Button>
          <template #overlay>
            <Menu
              @click="handleMoreMenuClick"
              @mouseenter="handleDropdownMouseEnter"
              @mouseleave="handleDropdownMouseLeave"
            >
              <MenuItem
                v-for="button in hiddenButtons"
                :key="getButtonKey(button)"
              >
              {{ button.name }}
              </MenuItem>
            </Menu>
          </template>
        </Dropdown>
      </div>
    </div>
  </div>

  <!-- 会议编辑弹窗 -->
  <MeetingEditDialog
    v-model:open="meetingEditVisible"
    :meeting-id="meetingEditId"
    @refresh="() => emit('refresh')"
    @ok="handleMeetingEditSuccess"
    @cancel="handleMeetingEditCancel"
    ref="meetingEditDialogRef"
  />
</template>

<script setup lang="ts">
import { ref, computed, inject, h, nextTick, onUnmounted, watch } from 'vue';
import {
  Button,
  Dropdown,
  Menu,
  MenuItem,
  Modal,
  ConfigProvider
} from 'ant-design-vue';
import MeetingEditDialog from './MeetingEditDialog.vue';
import type {
  ButtonType,
  ConfirmActionData
} from './types';
import { ActionHandlers, ActionTypeChecker } from './handlers/actionHandlers';
import { ActionUtils } from './core/utils';
import { message } from 'ant-design-vue';
import {
  DomUtils,
  ApiUtils,
  BusinessLogicUtils,
  UrlUtils,
  IframeUtils,
  NEXTSTEP_TIMEOUT
} from './utils/pureUtils';
import {
  ButtonStateService,
  DialogService,
  ActionProcessingService,
  MeetingService,
  NextStepService
} from './services/businessServices';
import { NextStepStateService, UITimerService, NextStepProcessingService, OpinionPopoverService } from './services/advancedServices';

interface Props {
  buttons?: ButtonType[];
  visible?: boolean;
  maxVisibleCount?: number;
  top?: boolean;
  affairId?: string;
  renderInfo?: {
    appId?: string; // 应用ID，6为会议
    affairId?: string;
    [key: string]: any;
  };
}

const props = withDefaults(defineProps<Props>(), {
  buttons: () => [],
  visible: false,
  maxVisibleCount: 4,
  top: false,
  affairId: '',
  renderInfo: () => ({})
});

const emit = defineEmits<{
  'button-click': [button: ButtonType, confirmData?: ConfirmActionData];
  'visibility-change': [visible: boolean];
  'refresh': [refreshData?: any];
}>();

// 注入全局弹窗管理器
const popoverManager = inject<any>('popoverManager');

// 注入NextStep状态管理器
const nextStepStateManager = inject<any>('nextStepStateManager');

// 响应式状态
const isDropdownVisible = ref(false);
const loadingButtons = ref<Set<string>>(new Set());
const recentlyClicked = ref(false); // 最近是否有按钮被点击

// Template refs
const actionButtonsWrapperRef = ref<HTMLElement>();
const moreButtonRef = ref<any>(); // Button 组件实例，需要通过 $el 获取 DOM 元素

// 当前组件是否是弹窗的触发者
const isCurrentPopoverTrigger = ref(false);

// NextStep状态管理
const nextStepState = ref({
  isProcessing: false,
  buttonWithPosition: null as any
});

// 监听nextStepState变化，同步到父组件
watch(() => nextStepState.value.isProcessing, (newValue) => {
  if (nextStepStateManager) {
    nextStepStateManager.setNextStepActive(newValue);
  }
}, { immediate: true });

// 按钮状态管理
const buttonState = ref<{
  dataType?: string;
  submitData?: ButtonType;
  hasOpinionHandle?: boolean;
  hasCancelOrTerminateOpinion?: boolean;
  hasDisagreeOpinion?: boolean;
  btnEventTarget?: HTMLElement;
}>({});

// 生成带置顶按钮的按钮列表
const computedButtons = computed(() => {
  const affairId = props.affairId || props.renderInfo?.affairId || '';
  const topButton: ButtonType = props.top
    ? {
      handleType: 'unTop',
      name: '取消置顶',
      paramMap: { affairId },
      opinionPolicy: {},
    } as ButtonType
    : {
      handleType: 'top',
      name: '置顶',
      paramMap: { affairId },
      opinionPolicy: {},
    } as ButtonType;
  return [topButton, ...(props.buttons || [])];
});

// 计算属性
const hasButtons = computed(() => {
  return computedButtons.value && computedButtons.value.length > 0;
});

// 是否应该显示按钮（综合考虑各种状态）
const shouldShowButtons = computed(() => {
  return props.visible ||
    isDropdownVisible.value ||
    isCurrentPopoverTrigger.value ||  // 只有当前组件触发的弹窗才保持显示
    recentlyClicked.value;
});

// 前几个显示的按钮
const visibleButtons = computed(() => {
  return computedButtons.value.slice(0, props.maxVisibleCount) || [];
});

// 超过限制的隐藏按钮
const hiddenButtons = computed(() => {
  return computedButtons.value.slice(props.maxVisibleCount) || [];
});

// 使用工具函数替换重复代码
const getButtonKey = (button: ButtonType): string => {
  return ActionUtils.getButtonKey(button, props.renderInfo?.affairId);
};

const getButtonType = ActionUtils.getButtonType;

// 检查按钮是否正在加载
const isButtonLoading = (button: ButtonType): boolean => {
  return loadingButtons.value.has(getButtonKey(button));
};

// 获取草稿内容（使用工具类）
const getDrafts = ApiUtils.getDrafts;

// 占位符函数已删除，使用下面的完整实现

// 处理催办逻辑（使用业务服务）
const handleHasten = ActionProcessingService.handleHasten;

// 处理分配逻辑（使用业务服务）
const handleAllocation = ActionProcessingService.handleAllocation;

// 保存元素位置信息（使用工具类）
// NextStep定时器引用
const nextStepTimer = { current: null as NodeJS.Timeout | null };

// 清理NextStep状态（使用高级服务）
const cleanupNextStepState = (button?: ButtonType) => {
  NextStepStateService.cleanupNextStepState(button, nextStepState);
  // 通知父组件NextStep状态变化
  if (nextStepStateManager) {
    nextStepStateManager.setNextStepActive(false);
  }
};

// 启动NextStep定时器（使用高级服务）
const startNextStepTimer = () => {
  // NextStepStateService.startNextStepTimer(nextStepTimer, nextStepState, cleanupNextStepState);
};

// 清理NextStep定时器（使用高级服务）
const clearNextStepTimer = () => {
  NextStepStateService.clearNextStepTimer(nextStepTimer);
};

// 处理 nextStep 流程（使用高级服务）
const handleNextStep = (button: ButtonType, event?: Event) => {
  NextStepProcessingService.handleNextStep(
    button,
    event,
    nextStepState,
    startNextStepTimer,
    clearNextStepTimer,
    cleanupNextStepState,
    handleNextStepResult,
    emit as any,
    popoverManager // 传入弹窗管理器
  );
};

// 处理nextStep结果（使用高级服务）
const handleNextStepResult = async (button: ButtonType, returnValue: string) => {
  return NextStepProcessingService.handleNextStepResult(
    button,
    returnValue,
    nextStepState,
    clearNextStepTimer,
    openOpinionPopoverWithPosition,
    emit as any,
    cleanupNextStepState
  );
};

// 会议编辑弹窗状态
const meetingEditVisible = ref(false);
const meetingEditId = ref('');
const meetingEditDialogRef = ref();

// 打开个人会议编辑弹窗（使用业务服务）
const openPersonalDialog = (meetingId: string) => {
  MeetingService.openPersonalDialog(
    meetingId,
    (id: string) => { meetingEditId.value = id; },
    (visible: boolean) => { meetingEditVisible.value = visible; }
  );
};

// 处理会议编辑成功（使用业务服务）
const handleMeetingEditSuccess = () => {
  MeetingService.handleMeetingEditSuccess(
    (visible: boolean) => { meetingEditVisible.value = visible; },
    (id: string) => { meetingEditId.value = id; }
  );
};

// 处理会议编辑取消（使用业务服务）
const handleMeetingEditCancel = () => {
  MeetingService.handleMeetingEditCancel(
    (visible: boolean) => { meetingEditVisible.value = visible; },
    (id: string) => { meetingEditId.value = id; }
  );
};

// 使用保存的位置信息打开意见弹窗（用于NextStep后续操作，使用高级服务）
const openOpinionPopoverWithPosition = async (button: ButtonType, hasPlaceholder: boolean = false, needDraft: boolean = true) => {
  return OpinionPopoverService.openOpinionPopoverWithPosition(
    button,
    hasPlaceholder,
    needDraft,
    popoverManager,
    actionButtonsWrapperRef,
    isCurrentPopoverTrigger,
    buttonState,
    props,
    checkNeedNextStepFlow,
    handleNextStepFlow,
    cleanupNextStepState,
    clearNextStepTimer,
    nextStepState,
    emit as any
  );
};

// 打开意见填写弹窗（使用高级服务）
const openOpinionPopover = async (button: ButtonType, event?: Event, isFromDropdown: boolean = false, hasPlaceholder: boolean = false, needDraft: boolean = true) => {
  return OpinionPopoverService.openOpinionPopover(
    button,
    event,
    isFromDropdown,
    hasPlaceholder,
    needDraft,
    popoverManager,
    moreButtonRef,
    actionButtonsWrapperRef,
    isCurrentPopoverTrigger,
    buttonState,
    props,
    checkNeedNextStepFlow,
    handleNextStepFlow,
    emit as any
  );
};

// 清理列表样式（使用工具类）
const removeListStyle = DomUtils.removeListStyle;

// 显示确认对话框（使用业务服务）
const showConfirmDialog = DialogService.showConfirmDialog;

// 智迈适配触发器检查（使用工具类）
const checkTrigger = BusinessLogicUtils.checkTrigger;

// 设置按钮处理状态（使用业务服务）
const setButtonState = (button: ButtonType) => {
  buttonState.value = ButtonStateService.calculateButtonState(button, props.renderInfo);
};

// 检查是否需要触发NextStep流程（使用工具类）
const checkNeedNextStepFlow = BusinessLogicUtils.checkNeedNextStepFlow;

// 处理NextStep流程（使用业务服务）
const handleNextStepFlow = (button: ButtonType, confirmData: ConfirmActionData) => {
  NextStepService.handleNextStepFlow(button, confirmData, handleNextStep);
};

// 判断是否满足意见策略条件（使用业务服务）
const needOpinionPolicyAction = (button: ButtonType): boolean => {
  return ButtonStateService.needOpinionPolicyAction(button, buttonState.value);
};

// 按钮点击处理 - 重构版：使用处理器分发逻辑
const handleButtonClick = async (button: ButtonType, event?: Event, isFromDropdown: boolean = false) => {
  // 🔥 关键修复：在处理新按钮之前，先关闭当前已打开的弹窗
  const manager = popoverManager?.value || popoverManager;
  if (manager && typeof manager.closePopover === 'function' && manager.isVisible) {
    console.log('检测到已有弹窗打开，先关闭当前弹窗');
    manager.closePopover();
    // 重置弹窗触发状态
    isCurrentPopoverTrigger.value = false;
  }

  // 标记最近有按钮被点击
  recentlyClicked.value = true;
  setTimeout(() => {
    recentlyClicked.value = false;
  }, 1000);

  // 设置按钮处理状态（必须在开始时设置）
  setButtonState(button);

  // 创建处理器实例
  const handlers = new ActionHandlers(
    emit,
    props,
    buttonState,
    showConfirmDialog,
    openOpinionPopover,
    handleHasten,
    handleAllocation,
    handleNextStep,
    openPersonalDialog
  );

  // 检查是否满足意见策略条件（对应原始的第一个 if 条件）
  if (ActionTypeChecker.needOpinionPolicyAction(button, buttonState)) {
    // 满足意见策略条件，直接打开意见弹窗，需要获取草稿
    await openOpinionPopover(button, event, isFromDropdown, false, true);
    return;
  }

  // 按类型分发处理，保持原有逻辑完全不变
  if (await handlers.handleTopActions(button)) return;
  if (await handlers.handleMeetingActions(button, event, isFromDropdown)) return;

  if (await handlers.handleConfirmActions(button)) return;
  if (await handlers.handleOtherActions(button)) return;

  // 6. 其他情况
  // 意见填写设置设置“撤销/回退/终止时必须填写意见”，节点态度设置“不同意”，不同意操作设置“是”---则在选择之后还要弹意见填写弹窗
  // 意见填写设置设置“撤销/回退/终止时必须填写意见”，节点态度设置“不同意”，不同意操作设置“否”---则直接提交
  // 意见填写设置设置“撤销/回退/终止时必须填写意见、不同意时必须填写意见”，节点态度设置“不同意”，不同意操作设置“是”---则打开继续选择弹窗
  // nextStep 逻辑检查 - 添加hasSelectedNextAction检查避免重复弹窗
  if (await handlers.handleNextStepActions(button, event)) return;

  // 默认情况：直接提交
  handlers.handleDefaultAction(button);
};

// 定时器引用
const hideTimer = ref<NodeJS.Timeout>();

// 下拉菜单显示状态变化
const handleDropdownVisibleChange = (visible: boolean) => {
  isDropdownVisible.value = visible;

  if (visible) {
    clearHideTimer();
  } else {
    // 下拉菜单关闭时，立即隐藏，不使用延时
    if (!isCurrentPopoverTrigger.value) {
      isDropdownVisible.value = false;
    }
  }
};

// 下拉菜单鼠标进入
const handleDropdownMouseEnter = () => {
  clearHideTimer();
};

// 下拉菜单鼠标离开
const handleDropdownMouseLeave = () => {
  // 如果当前组件触发了弹窗，不隐藏
  if (isCurrentPopoverTrigger.value) {
    return;
  }
  // 立即隐藏，不使用延时
  isDropdownVisible.value = false;
};

// 更多菜单点击处理
const handleMoreMenuClick = (info: any) => {
  const key = String(info.key);
  const button = hiddenButtons.value.find(btn => getButtonKey(btn) === key);

  if (button) {
    // 🔥 关键修复：在处理新按钮之前，先关闭当前已打开的弹窗
    const manager = popoverManager?.value || popoverManager;
    if (manager && typeof manager.closePopover === 'function' && manager.isVisible) {
      console.log('下拉菜单点击：检测到已有弹窗打开，先关闭当前弹窗');
      manager.closePopover();
      // 重置弹窗触发状态
      isCurrentPopoverTrigger.value = false;
    }

    // 立即关闭下拉菜单（当弹窗出现时下拉菜单应该关闭）
    isDropdownVisible.value = false;
    // 清除所有隐藏定时器，确保按钮在点击后仍然可见
    clearHideTimer();

    // 如果需要确认，标记当前组件为弹窗触发者
    if (ActionTypeChecker.needOpinionPolicyAction(button, buttonState)) {
      isCurrentPopoverTrigger.value = true;
    }

    // 对于更多菜单中的按钮，使用当前组件的"更多"按钮作为触发元素
    const moreButton = moreButtonRef.value;

    if (moreButton) {
      const mockEvent = { target: moreButton, currentTarget: moreButton } as unknown as Event;
      handleButtonClick(button, mockEvent, true);
    } else {
      // 如果找不到更多按钮，直接调用处理函数，但不传event
      console.warn('找不到更多按钮，使用默认处理');
      handleButtonClick(button, undefined, true);
    }
  }
};

// UI定时器工具方法（使用高级服务）
const clearHideTimer = () => {
  UITimerService.clearHideTimer(hideTimer);
};

const setHideTimer = (delay: number = 200) => {
  UITimerService.setHideTimer(hideTimer, isCurrentPopoverTrigger, isDropdownVisible, delay);
};

// 组件销毁时清理资源
onUnmounted(() => {
  clearNextStepTimer();
  cleanupNextStepState();
});

// 暴露方法给父组件
defineExpose({
  clearHideTimer,
  setHideTimer,
  // 暴露隐藏按钮的方法给父组件
  hideButtons: () => {
    emit('visibility-change', false);
  }
});
</script>

<style scoped lang="less">
.action-buttons-wrapper {
  position: absolute;
  right: -12px;
  top: -12px;
  bottom: -12px;
  min-width: 120px;
  display: flex;
  justify-content: flex-end;

  .action-buttons-container {
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
    background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 8.7%);
    border-radius: 0 12px 12px 0;
    padding-left: 36px;

    .action-buttons-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(4px);
      -webkit-backdrop-filter: blur(4px);
      border-radius: 6px;
      z-index: 1;
    }

    .action-buttons {
      position: relative;
      z-index: 2;
      padding: 4px 12px 4px 8px;
    }
  }
}

// 移除了按钮显示动画相关样式

// 按钮样式
:deep(.action-btn) {
  height: 28px !important;
  padding-top: 2px;
  padding-bottom: 2px;
  box-shadow: none;

  &.compact-btn {
    padding: 2px 5px !important;
    line-height: 1;
  }
}

// 下拉菜单样式
:deep(.ant-dropdown-menu) {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.ant-dropdown-menu-item) {
  font-size: 12px;
  padding: 6px 12px;
}

// 会议编辑弹窗样式
:deep(.meeting-edit-modal) {
  .ant-modal {
    top: 50px;
  }

  .ant-modal-body {
    padding: 0;
  }

  .meeting-edit-content {
    border-radius: 6px;
    overflow: hidden;

    iframe {
      display: block;
      background: #fff;
    }
  }
}

:deep(.ant-dropdown-menu-item) {
  &:hover {
    background-color: #f5f5f5;
  }
}
</style>
