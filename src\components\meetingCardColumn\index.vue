<template>
  <!-- 会议卡片列的主容器 -->
  <div id="guide-step4"
    class="meeting-card-column flex flex-col w-full pl-[16px] pt-[16px] pb-[12px] rounded-[12px] relative"
  >
    <!-- 头部区域 -->
    <header class="column-header mb-[10px]">
      <!-- 列标题 -->
      <h2 class="text-base font-semibold text-gray-700 m-0">
        {{ title }}<span class="pl-[6px]">·{{ dataList.length }}</span>
      </h2>
    </header>

    <!-- 内容区域 - 添加 ref 和事件监听器 -->
    <div class="w-full relative flex-1 content-wrapper min-h-[0]">
      <div
        v-if="dataList.length"
        :class="[
          'column-content w-full  flex flex-nowrap overflow-x-auto h-full',
          dataList.length === 1 ? 'justify-start ' : '',
        ]"
        ref="scrollContainerRef"
      >
        <!-- 使用 v-for 循环遍历会议数据 -->
        <template v-for="(meeting, index) in dataList" :key="meeting.id">
          <!-- 动态绑定类：如果不是第一个元素 (index > 0)，则添加左边距 -->
          <MeetingCardItem
            :meeting="meeting"
            :class="{
              'ml-3': index > 0,
              'w-[214px]': dataList.length === 1,
            }"
            @click="handleClick(meeting)"
            @refresh-summary="handleRefreshSummary"
          />
        </template>
      </div>
      <PortalEmptyColumn v-else :image="EmptyPendingImg" :text="emptyMessage" :width="80" class="w-full flex-1 empty-meeting"/>

      <Arrow
        @handleArrowClick="handleArrowClick"
        :canScrollLeft="dataList.length > 1 && scrollDistance > 10"
        :canScrollRight="canScrollRight"
        class="arrow-wrapper"
        v-if="dataList.length > 1"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, onMounted, onActivated, onDeactivated, reactive, computed, nextTick, watch, inject } from 'vue';
import MeetingCardItem from './meetingCardItem.vue';
import PortalEmptyColumn from '../portalEmptyColumn/index.vue';
import LeftSvg from '@/assets/imgs/left.svg';
import RightSvg from '@/assets/imgs/right.svg';
import { Image as AImage } from 'ant-design-vue';
import Arrow from '@/components/common/arrow/index.vue';
import {
  type MeetingItem,
  fetchMeetingData,
  fetchMeetingSummaries,
  refreshMeetingData,
  refreshMeetingSummary
} from './meetingService';
// 导入会议刷新 composable
import { useMeetingRefresh } from '../../hooks/portal/useMeetingRefresh';
import EmptyPendingImg from '@/assets/imgs/empty_pending.png';
import { openDobuleScreen } from '@/utils/storesUtils';

const sdkInstance = inject('sdkInstance') as any;
// 定义组件内部的数据状态
const meetingData = reactive({
  title: '待开会议',
  dataList: [] as MeetingItem[]
});
const emptyMessage = ref('暂无数据哦～')

// 使用会议刷新 composable
const { meetingState, updateMeetingData, setLoading } = useMeetingRefresh();

// 计算属性
const title = computed(() => meetingData.title);
const dataList = computed(() => meetingData.dataList);

// 计算是否可以向右滚动
const canScrollRight = computed(() => {
  if (!scrollContainerRef.value || dataList.value.length <= 1) return false;
  const maxScroll = getMaxScrollLeft();
  return scrollDistance.value < maxScroll - 10; // 留10px的误差范围
});

// --- 鼠标拖动状态 ---
const scrollContainerRef = ref<HTMLElement | null>(null);
const isDragging = ref(false);
const startX = ref(0);
const startScrollLeft = ref(0);
const dragDistance = ref(0);

// --- 鼠标按下事件处理 ---
const handleMouseDown = (event: MouseEvent) => {
  if (!scrollContainerRef.value) return;
  isDragging.value = false;
  dragDistance.value = 0;
  startX.value = event.pageX - scrollContainerRef.value.offsetLeft;
  startScrollLeft.value = scrollContainerRef.value.scrollLeft;
  scrollContainerRef.value.style.cursor = 'grabbing';
  scrollContainerRef.value.style.userSelect = 'none';

  window.addEventListener('mousemove', handleMouseMove);
  window.addEventListener('mouseup', handleMouseUpGlobal);
};

const handleArrowClick = (direction: 'pre' | 'next') => {
  if (direction === 'pre') {
    handlePrev();
  } else {
    handleNext();
  }
};

const handleClick = (item: MeetingItem) => {
  openDobuleScreen(item.jumpUrl, 'iframe');
};

// --- 鼠标移动事件处理 ---
const handleMouseMove = (event: MouseEvent) => {
  if (!scrollContainerRef.value) return;

  const x = event.pageX - scrollContainerRef.value.offsetLeft;
  const walk = (x - startX.value) * 1.5;

  dragDistance.value = walk;

  if (Math.abs(walk) > 5) {
    isDragging.value = true;
  }

  event.preventDefault();
  scrollContainerRef.value.scrollLeft = startScrollLeft.value - walk;
};

// --- 鼠标松开事件处理 (全局，确保能捕捉到) ---
const handleMouseUpGlobal = () => {
  if (scrollContainerRef.value) {
    scrollContainerRef.value.style.cursor = 'grab';
    scrollContainerRef.value.style.userSelect = 'auto';
  }
  isDragging.value = false;
  dragDistance.value = 0;
  window.removeEventListener('mousemove', handleMouseMove);
  window.removeEventListener('mouseup', handleMouseUpGlobal);
};

// --- 鼠标移出容器事件处理 ---
const handleMouseLeave = () => {
  // handleMouseUpGlobal();
};

// --- 鼠标在容器内松开事件处理 ---
const handleMouseUp = () => {
  if (scrollContainerRef.value) {
    scrollContainerRef.value.style.cursor = 'grab';
    scrollContainerRef.value.style.userSelect = '';
  }
};

let scrollDistance = ref(0);

// 计算最大滚动距离
const getMaxScrollLeft = () => {
  if (scrollContainerRef.value) {
    return scrollContainerRef.value.scrollWidth - scrollContainerRef.value.clientWidth;
  }
  return 0;
};

// 滚动事件监听器，实时更新 scrollDistance
const handleScroll = () => {
  if (scrollContainerRef.value) {
    scrollDistance.value = scrollContainerRef.value.scrollLeft;
  }
};

const animationScroll = (direction: 'prev' | 'next') => {
  if (scrollContainerRef.value) {
    const scrollAmount = 218;
    const currentScroll = scrollContainerRef.value.scrollLeft;
    const maxScrollLeft = scrollContainerRef.value.scrollWidth - scrollContainerRef.value.clientWidth;

    // 计算目标滚动位置
    let targetScroll = direction === 'prev' ? currentScroll - scrollAmount : currentScroll + scrollAmount;

    // 边界检查
    targetScroll = Math.max(0, Math.min(targetScroll, maxScrollLeft));

    scrollContainerRef.value.scrollTo({
      left: targetScroll,
      behavior: 'smooth',
    });
  }
};

const handlePrev = () => {
  animationScroll('prev');
};

const handleNext = () => {
  animationScroll('next');
};

// 初始化滚动状态
const initializeScrollState = () => {
  nextTick(() => {
    if (scrollContainerRef.value) {
      // 重置滚动位置到开始
      scrollContainerRef.value.scrollLeft = 0;
      scrollDistance.value = 0;

      // 确保滚动事件监听器已添加
      scrollContainerRef.value.removeEventListener('scroll', handleScroll);
      scrollContainerRef.value.addEventListener('scroll', handleScroll);
    }
  });
};

// 获取并设置会议数据
const loadMeetingData = async (forceRefresh: boolean = false) => {
  setLoading(true);
  try {
    // 根据 forceRefresh 参数决定是否强制刷新
    const data = forceRefresh ? await refreshMeetingData() : await fetchMeetingData();
    meetingData.dataList = data;

    // 同步到 composable 状态
    updateMeetingData(data);

    // 数据加载完成后重新初始化滚动状态
    initializeScrollState();

    // 在后台获取会议摘要 - 使用响应式的 dataList
    if (meetingData.dataList.length > 0) {
      fetchMeetingSummaries(meetingData.dataList);
    }
  } catch (error) {
    console.error('加载会议数据失败:', error);
  } finally {
    setLoading(false);
  }
};

// 刷新摘要处理
const handleRefreshSummary = async (meeting: MeetingItem) => {
  await refreshMeetingSummary(meeting);
};

// 监听刷新信号
watch(
  () => meetingState.refreshTrigger,
  (newValue) => {
    if (newValue > 0) {
      loadMeetingData(true);
    }
  }
);

// 监听数据变化，重置滚动状态
watch(
  () => dataList.value.length,
  () => {
    // 数据变化时重新初始化滚动状态
    initializeScrollState();
  }
);

const getMeetingData = ()=>{
  loadMeetingData(true);
}

// 生命周期 - 支持 keep-alive 缓存
onMounted(() => {
  loadMeetingData();
  sdkInstance.onRefresh('refreshData',getMeetingData);
  sdkInstance.onRefresh('refreshTodoData',getMeetingData);
});

onActivated(() => {

});

onDeactivated(() => {

});

// --- 组件卸载时清理监听器 ---
onUnmounted(() => {
  window.removeEventListener('mousemove', handleMouseMove);
  window.removeEventListener('mouseup', handleMouseUpGlobal);

  // 清理滚动事件监听器
  if (scrollContainerRef.value) {
    scrollContainerRef.value.removeEventListener('scroll', handleScroll);
  }

  sdkInstance.offRefresh('refreshData',getMeetingData);
  sdkInstance.offRefresh('refreshTodoData',getMeetingData);

});
</script>

<style scoped lang="less">
.meeting-card-column {
  // --- 毛玻璃效果 ---
  // 设置半透明背景色（浅蓝色调）
  background: rgba(255, 255, 255, 0.65);

  border: 1.5px solid rgba(255, 255, 255, 0.8);

  // 应用背景模糊效果
  backdrop-filter: blur(29px);

  // 设置边框（浅蓝色调，部分透明）
  user-select: none;
  // --- 可选样式 ---
  // 添加细微阴影增加层次感

  .column-header h2 {
    // 头部标题的样式 (Tailwind 类已应用部分样式)
    color: #000; // 设置更深的文字颜色
    font-size: 16px;
  }
  .button-common {
    width: 22px;
    height: 22px;
    background: #fff;
    cursor: pointer;
    transform: translateY(-10px);
    z-index: 2;
    border-radius: 50%;
    color: #8d97a6;
    top: 50%;
    display: none;
    line-height: 22px;
  }
  .button-common-icon {
    font-size: 13px;
    color: rgba(142, 148, 162, 1);
  }
.arrow-wrapper{
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding: 0 3px 0;
  gap: 8px;
  bottom: 17px;
}



  .content-wrapper:hover {
    .pre,
    .next {
      display: flex;
    }
  }

  .column-content {
    // cursor: grab; // 设置默认的可抓取鼠标样式

    // 添加右边距，确保在 Safari 中也能正确显示
    padding-right: 16px; // 相当于 mr-6 的 24px

    > * {
      flex-shrink: 0;
      // user-select: none;
      /* 防止子元素文本被选中 */
      // -webkit-user-drag: none;
      /* 禁止元素的默认拖拽 */
      // pointer-events: none;
      /* 使子元素不响应鼠标事件，让父级容器捕获 */
    }

    // 保持 overflow-x: auto (或 scroll) 来启用滚动能力
    overflow-x: auto; // 或者 overflow-x: scroll;

    // --- 隐藏滚动条 ---
    // 1. 针对 Webkit 内核浏览器 (Chrome, Safari, Edge 新版等)
    &::-webkit-scrollbar {
      display: none;
      /* 完全隐藏滚动条 */
      // 或者可以只设置 width/height 为 0:
      // width: 0;
      // height: 0;
    }

    // 2. 针对 Firefox
    scrollbar-width: none;
    /* 隐藏滚动条，但保留滚动功能 */

    // 3. 针对 IE / Edge 旧版
    -ms-overflow-style: none;
    /* 隐藏滚动条 */

    scroll-behavior: smooth; // 添加平滑滚动效果
    transition: scroll-left 0.3s ease-in-out; // 添加过渡效果
  }
  .empty-meeting {
    width: calc(100% - 16px);//减16是因为左边有padding,右边没有
  }
}
</style>
