
<template>
  <div
    class="intelligent-question-count-wrapper w-full relative"
    :class="{
      'intelligent-question-count-small': isCopilotScreen,
    }"
  >
    <div class="top">
      <div class="mb-[25px] title">数智仓</div>
      <template v-if="reportDataSource.length > 0">
        <div v-for="(item, index) in childrenData" :key="index" class="content-wrapper">
          <div class="content-inner-title">{{ item.label }}（{{ item?.children.length }}）</div>
          <ReportItem :data="item.children" @openReport="openReport" :isCopilotScreen="true"/>
        </div>
        <!-- <AiTabs
          v-if="reportDataSource.length > 0"
          class="w-full mb-[24px] tabs"
          :tabs="reportDataSource"
          :activeTab="currentLabel"
          canWhell
          @tabClick="handleClick"
          :notNeedSelect="true"
          :portalType="isPortal ? 'portal' : 'normal'"
        ></AiTabs>

        <img
          src="@/assets/imgs/create_logo.png"
          alt="createCardColumnBg"
          class="absolute top-[25px] right-0 w-[100px] h-[100px] object-cover"
          v-if="reportDataSource.length"
        />
        <div v-if="isCopilotScreen" class="content-small">
          <Carousel
            v-if="currentLabel === 'all'"
            arrows
            :dots="false"
            class="custom-carousel"
            :after-change="handleCarouselChange"
            :infinite="false"
          >
            <div v-for="(item, index) in childrenData" :key="index" class="content-wrapper">
              <div class="content-inner-title">{{ item.label }}（{{ item?.children.length }}）</div>
              <ReportItem :data="item.children" @openReport="openReport" :isCopilotScreen="true"/>
            </div>
            <template #prevArrow>
              <div
                class="custom-slick-arrow"
                :class="{
                  disabled: carouselCount === 0,
                }"
              >
                <i class="iconfont ai-icon-zuo"></i>
              </div>
            </template>
            <template #nextArrow>
              <div
                class="custom-slick-arrow"
                :class="{
                  disabled: carouselCount === childrenData.length - 1,
                }"
              >
                <i class="iconfont ai-icon-you"></i>
              </div>
            </template>
          </Carousel>
          <div v-else class="content-wrapper">
            <ReportItem :data="childrenData" @openReport="openReport" :isCopilotScreen="true"/>
          </div>
        </div>
        <div class="content" v-else>
          <template v-if="currentLabel === 'all'">
            <div v-for="(item, index) in childrenData" :key="index" class="content-wrapper">
              <div class="content-inner-title">{{ item.label }}（{{ item?.children.length }}）</div>
              <ReportItem :data="item.children" @openReport="openReport" :isCopilotScreen="true"/>
            </div>
          </template>
          <div v-else class="content-wrapper">
            <ReportItem :data="childrenData" @openReport="openReport" :isCopilotScreen="true"/>
          </div>
        </div> -->
      </template>
      <Empty v-else description="暂无数据"></Empty>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, computed, inject } from 'vue';
import AiTabs from '@/components/aiTabs/index.vue';
import { getReportStatus } from '@/api/portal';
import ReportItem from './ReportItem.vue';
import { openDobuleScreen } from '@/utils/storesUtils';
import { getAgentResult } from '@/api/common';
import { generateSecure16DigitNumber } from '@/utils/uuid';
import CommonQuestionItem from '@/components/common/question-item/index.vue';
import type { TypeResponse } from '@/types/api';
import { isJSON } from '@/utils/is';
import Arrow from '@/components/common/arrow/index.vue';
import { Carousel } from 'ant-design-vue';
import Empty from '@/components/empty/index.vue';
import AiIdentify from '@/components/aiIdentify/index.vue';

export default defineComponent({
  name: 'IntelligentQuestionCountSmall',
});
</script>
<script setup lang="ts">
import type { ReportItemType } from '@/types/portalTypes';
import { useChatList } from '@/stores/chatList';
import { useGlobal } from '@/stores/global';
import cardInstance from '@/stores/card';

const props = defineProps<{
  reportDataSource: any[];
}>();

type QuestionItemType = {
  name: string;
  question: string;
};

const isPortal = inject('isPortal');
const uGlobal = useGlobal();
const uChatList = useChatList();
const sdkInstance = inject('sdkInstance') as any;

const currentLabel = ref<string>('all');

const recommendQuestions = ref<QuestionItemType[]>([]);
const currentScrollNum = ref(0);
const columnCount = ref(0);
const carouselCount = ref(0);
const isEmpty = ref(false);

const canScrollLeft = computed(() => {
  return columnCount.value - 4 > 0 && currentScrollNum.value > 0;
});
const canScrollRight = computed(() => {
  return (
    columnCount.value - currentScrollNum.value > 4 && currentScrollNum.value < columnCount.value - 1
  );
});

const childrenData = computed(() => {
  const currentLabelData = props.reportDataSource.find((item) => item.key === currentLabel.value);
  // TODO: 临时暴力的数据处理
  let resultData = [{
    label: '常用报表',
    key: 'all',
    children: [],
  }];
  currentLabelData.children.forEach((item: any) => {
    resultData[0].children = resultData[0].children.concat(item.children);
  });
  resultData[0].children = resultData[0].children.slice(0,6)
  return resultData;
});

// 如果不是全屏或者双屏模式下，采用小页面模式显示
const isCopilotScreen = computed(() => {
  return !isPortal || uChatList.dynamicData.dobuleScreenData.show;
});

// 标签点击
const handleClick = (key: string) => {
  currentLabel.value = key;
  carouselCount.value = 0;
};

const handleCarouselChange = (current: number) => {
  carouselCount.value = current;
};

// 打开报表
const openReport = (item: ReportItemType) => {
  const { categoryId, reportId } = item.design;
  const ctxPath = window._ctxPath || '/seeyon';
  const url = `${ctxPath}/report4Result.do?method=showResult&designId=${reportId}`;
  openDobuleScreen(url, 'iframe');
};

const handleArrowClick = (type: string) => {
  if (type === 'pre' && canScrollLeft.value) {
    currentScrollNum.value = currentScrollNum.value > 0 ? currentScrollNum.value - 1 : 0;
  } else if (type === 'next' && canScrollRight.value) {
    currentScrollNum.value =
      currentScrollNum.value < columnCount.value - 1
        ? currentScrollNum.value + 1
        : columnCount.value - 1;
  }
};

const getRecommendQuestions = async () => {
  const chatSessionId = generateSecure16DigitNumber();
  const data = {
    input: '推荐问题',
    chatSessionId: chatSessionId,
    agentCode: 'smartQuestionList',
  };
  const res = (await getAgentResult(data)) as TypeResponse;
  if (res.code == '0' && res.data?.content) {
    if (isJSON(res.data.content)) {
      const data = JSON.parse(res.data.content);
      recommendQuestions.value = data;
      columnCount.value = recommendQuestions.value.length;
    }
  }
};

const questionClick = (item: QuestionItemType) => {
  cardInstance.sendMessage(item.question);
};

// 格式化数据
const formatData = (data: any) => {
  const result: any = {};
  data.forEach((it: any) => {
    const reportCategory = it.design.reportCategory;
    if (reportCategory) {
      if (!result[reportCategory]) {
        result[reportCategory] = {
          label: it.design.name,
          key: reportCategory,
          children: [it],
        };
      } else {
        result[reportCategory].children.push(it);
      }
    }
  });
  const newData = Object.values(result);
  if (newData.length) {
    const allReportData: any[] = [];
    newData.forEach((it: any) => {
      allReportData.push(...it.children);
    });
    const allReportDataResult: any = {};
    allReportData
      .sort((a, b) => a.views - b.sort.views)
      .slice(0, 10)
      .forEach((it: any) => {
        const reportCategory = it.design.reportCategory;
        if (!allReportDataResult[reportCategory]) {
          allReportDataResult[reportCategory] = {
            label: it.design.name,
            key: reportCategory,
            children: [it],
          };
        } else {
          allReportDataResult[reportCategory].children.push(it);
        }
      });
    newData.unshift({
      label: '常用报表',
      key: 'all',
      children: Object.values(allReportDataResult),
      // .concat(Object.values(allReportDataResult))
      // .concat(Object.values(allReportDataResult)),
    });
  }
  return newData;
};

const initInput = () =>{
  setTimeout(() => { //外面组件的显示用了nextTick，这里用setTimeout，确保组件显示了才传值
    // 设置输入框默认值
    sdkInstance.sendMsg('', {
      inputTemplate: [
        {
          id: '1',
          text: '帮我查询',
        },
        {
          id: '2',
          type: 'input',
          placeholder: '报表名称',
          focus: true,
        },
        {
          id: '3',
          text: '报表，',
        },
        {
          id: '4',
          type: 'input',
          placeholder: '希望查询的问题或数据',
        },
      ],
    });
  });
}
onMounted(() => {
  // getReportData();
  // getRecommendQuestions();
  if(!isPortal) { //侧边栏目前没缓存，在mounted里面初始化
    initInput();
  }

});
onActivated(()=>{
  initInput();
})
</script>
<style scoped lang="less">
.intelligent-question-count-wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  width: 100%;
  justify-content: space-between;

  .custom_empty {
    height: 100%;
  }

  .top {
    height: 100%;
    overflow: hidden;

    .title {
      font-family: PingFang SC;
      font-weight: @font-weight-500;
      font-style: Semibold;
      font-size: 24px;
      line-height: 32px;
    }

    .content {
      margin-top: 15px;
      max-height: calc(100% - 110px);
      overflow: auto;
    }
  }

  .tabs {
    width: calc(100% - 116px);
  }

  :deep(.ai-tabs-wrapper) {
    margin: 0px;
    gap: 8px;
    height: auto;
    .ai-tab-content {
      gap: 12px;
    }
    .tab-item {
      height: 32px;
      border-radius: 12px;
      padding: 7px 26.5px;
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.528) 19.98%,
        rgba(255, 255, 255, 0.704) 79.94%
      );
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-size: 13px;
      line-height: 19px;

      &.active {
        background: rgba(0, 0, 0, 0.9);
        font-weight: @font-weight-500;
        color: #ffffff;
        backdrop-filter: blur(59.30232620239258px);
      }
    }
  }

  .content-wrapper {
    flex: 1;
    min-height: 1px;
    overflow-y: auto;
    background-color: #ffffff80;
    border-radius: 12px 12px 0px 0px;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(15px);
    padding: 8px 0px;
    border-radius: 12px;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .content-inner-title {
      //   backdrop-filter: blur(15px);
      padding-left: 16px;
      color: #000;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: @font-weight-500;
      line-height: 24px; /* 150% */
    }

    :deep(.content-inner-wrapper) {
      .content-item {
        width: 33.33%;
      }
    }
  }
  .recommend-question {
    height: 94px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    padding: 8px 16px 12px 16px;

    :deep(.ant-spin-spinning) {
      width: 100%;
    }

    .title-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .title {
        font-family: PingFang SC;
        font-weight: @font-weight-500;
        font-size: 16px;
        line-height: 24px;
      }
    }

    .question-content {
      display: flex;
      width: 100%;

      .question-item {
        margin-right: 32px;
        margin-bottom: 0;
        min-width: 174px;
        max-width: 174px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
    .no-data {
      font-size: 14px;
      color: #8e94a2;
      text-align: center;
    }
  }
}
.intelligent-question-count-small {
  .top {
    height: 100%;
    .title,
    img {
      display: none;
    }
    .tabs {
      width: calc(100% - 5px);
    }

    :deep(.ai-tabs-wrapper) {
      .ai-tab-content {
        gap: 12px;
      }
      .tab-item {
        line-height: 22px;
        border-radius: 8px;
        padding: 5px 8px;
        background: transparent;
        font-weight: @font-weight-500;

        &.active {
          background: #d1e0ff;
          font-weight: @font-weight-500;
          color: #2962f0;
        }
      }
    }
    .content-small {
      margin-top: 12px;
      .content-wrapper {
        overflow: auto;
        background: #ffffff80;
        height: 316px;
        backdrop-filter: blur(1000px);
      }

      :deep(.content-inner-wrapper) {
        flex-direction: column;
        .content-item {
          width: 100%;
        }
      }
      :deep(.custom-slick-arrow) {
        z-index: 10;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        backdrop-filter: blur(59.30232620239258px);
        background: #ffffffb2;
        position: absolute;
        border: 1px solid #ffffff66;
        color: #8e94a2;

        &.slick-prev {
          top: 94%;
          left: 83%;
        }
        &.slick-next {
          top: 94%;
          right: 2%;
        }
        .iconfont {
          font-size: 14px;
          position: absolute;
          top: 11px;
          left: 4px;
        }
        &.disabled {
          cursor: not-allowed;
          color: #d8dadf;
        }
      }
    }
    .recommend-question-small {
      margin-top: 12px;
    }
  }
}
</style>
