<template>
  <div class="portal-layout-col">
      <component :key="index" v-for="(item, index) in nodes" v-bind="item.props" :class="item.class" :nodes="parseCondition(item.children)" :style="item.style"
      :is="item.tag" />
  </div>
</template>
<script setup lang="ts">
import { parseCondition } from '../utils/conditionUtil';
const { nodes } = defineProps({
  nodes: {
    type: Array,
    default: () => [],
  },
});

</script>

<style scoped lang="less">
.portal-layout-col {

}

</style>
