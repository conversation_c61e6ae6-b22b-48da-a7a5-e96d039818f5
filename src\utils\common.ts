// 字符计算函数
export const calculateCharLength = (str: string): number => {
  let length = 0;
  for (let i = 0; i < str.length; i++) {
    const char: string = str.charAt(i);
    if (/[\u4e00-\u9fa5]/.test(char)) {
      length += 1; // 汉字算1个字符
    } else {
      length += 0.5; // 其他字符2个算1个字符
    }
  }
  return length;
};

/**
 * 获取url上指定名称的参数值
 * @param {string} name
 * @returns
 */
export const getQueryString = (name: string) => {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
  const r = window.location.search.substr(1).match(reg);
  if (r != null) return decodeURIComponent(r[2]);
  return '';
};

/**
 * 获取各个agent的ID
 * @param
 * @returns
 */
export const getAssistantId = function () {
  return getQueryString('assistantId') || sessionStorage.getItem('assistantId'); // 2048555557967186661 超级助理CoMi
};


/**
 * @param layoutConfig 布局配置对象
 * @returns 包含所有component节点的数组
 */
export function extractComponentsIterative(layoutConfig: any): any[] {
  if (!layoutConfig || typeof layoutConfig !== 'object') {
    return [];
  }

  const components: any[] = [];
  const stack: any[] = [layoutConfig]; // 使用栈进行深度优先搜索

  while (stack.length > 0) {
    const current = stack.pop()!;
    
    // 如果当前节点是component，添加到结果数组并停止遍历其子节点
    if (current.type === 'component') {
      components.push(current);
      continue; // 跳过子节点遍历
    }
    
    // 如果有children属性且是数组，将子节点加入栈（逆序添加保持遍历顺序）
    if (Array.isArray(current.children)) {
      for (let i = current.children.length - 1; i >= 0; i--) {
        stack.push(current.children[i]);
      }
    }
  }

  return components;
}

/**
 * 通过tag快速查找节点数据
 * @param layoutConfig 布局配置对象
 * @param tag 要查找的tag名称
 * @param options 查找选项
 * @returns 找到的节点数据，如果未找到返回null
 */
export function findNodeByTag(
  layoutConfig: any, 
  tag: string, 
  options: {
    /** 是否查找第一个匹配的节点，默认为true */
    findFirst?: boolean;
    /** 是否查找所有匹配的节点，默认为false */
    findAll?: boolean;
    /** 是否包含完整的节点路径，默认为false */
    includePath?: boolean;
  } = {}
): any {
  const { findFirst = true, findAll = false, includePath = false } = options;
  
  if (!layoutConfig || typeof layoutConfig !== 'object' || !tag) {
    return findAll ? [] : null;
  }

  const results: any[] = [];
  const stack: Array<{ node: any; path: string[] }> = [{ node: layoutConfig, path: [] }];

  while (stack.length > 0) {
    const { node: current, path } = stack.pop()!;
    
    // 检查当前节点是否匹配
    if (current.tag === tag) {
      const result = includePath 
        ? { node: current, path: [...path, current.tag || 'root'] }
        : current;
      
      if (findFirst && !findAll) {
        return result;
      }
      
      if (findAll) {
        results.push(result);
      }
    }
    
    // 如果有children属性且是数组，将子节点加入栈（逆序添加保持遍历顺序）
    if (Array.isArray(current.children)) {
      for (let i = current.children.length - 1; i >= 0; i--) {
        const child = current.children[i];
        const childPath = [...path, current.tag || 'root'];
        stack.push({ node: child, path: childPath });
      }
    }
  }

  return findAll ? results : null;
}

/**
 * 通过tag快速查找多个节点数据
 * @param layoutConfig 布局配置对象
 * @param tags 要查找的tag名称数组
 * @param includePath 是否包含完整的节点路径，默认为false
 * @returns 找到的节点数据映射表
 */
export function findNodesByTags(
  layoutConfig: any, 
  tags: string[], 
  includePath: boolean = false
): Record<string, any[]> {
  if (!layoutConfig || typeof layoutConfig !== 'object' || !Array.isArray(tags) || tags.length === 0) {
    return {};
  }

  const tagSet = new Set(tags);
  const results: Record<string, any[]> = {};
  
  // 初始化结果对象
  tags.forEach(tag => {
    results[tag] = [];
  });

  const stack: Array<{ node: any; path: string[] }> = [{ node: layoutConfig, path: [] }];

  while (stack.length > 0) {
    const { node: current, path } = stack.pop()!;
    
    // 检查当前节点是否匹配任何tag
    if (current.tag && tagSet.has(current.tag)) {
      const result = includePath 
        ? { node: current, path: [...path, current.tag] }
        : current;
      
      results[current.tag].push(result);
    }
    
    // 如果有children属性且是数组，将子节点加入栈（逆序添加保持遍历顺序）
    if (Array.isArray(current.children)) {
      for (let i = current.children.length - 1; i >= 0; i--) {
        const child = current.children[i];
        const childPath = [...path, current.tag || 'root'];
        stack.push({ node: child, path: childPath });
      }
    }
  }

  return results;
}

/**
 * 通过tag更新节点数据
 * @param layoutConfig 布局配置对象
 * @param tag 要更新的tag名称
 * @param updateData 要更新的数据或更新函数
 * @param updateAll 是否更新所有匹配的节点，默认为false（只更新第一个）
 * @returns 更新是否成功
 */
export function updateNodeByTag(
  layoutConfig: any, 
  tag: string, 
  updateData: any | ((node: any) => any),
  updateAll: boolean = false
): boolean {
  if (!layoutConfig || typeof layoutConfig !== 'object' || !tag) {
    return false;
  }

  let updated = false;
  const stack: any[] = [layoutConfig];

  while (stack.length > 0) {
    const current = stack.pop()!;
    
    // 检查当前节点是否匹配
    if (current.tag === tag) {
      if (typeof updateData === 'function') {
        const newData = updateData(current);
        Object.assign(current, newData);
      } else {
        Object.assign(current, updateData);
      }
      
      updated = true;
      
      // 如果只更新第一个匹配的节点，则返回
      if (!updateAll) {
        return true;
      }
    }
    
    // 如果有children属性且是数组，将子节点加入栈
    if (Array.isArray(current.children)) {
      for (let i = current.children.length - 1; i >= 0; i--) {
        stack.push(current.children[i]);
      }
    }
  }

  return updated;
}

// 处理oaUrl
export const dealOaUrl = (url: string) => { 
  if (url && typeof url === 'string') {
      if (url.includes('?')) {
          url = url + '&isFromComi=true';
      } else {
          url = url + '?isFromComi=true';
      }
  }
  return url;
}