<template>
  <div class="flex agent">
    <span :class="[
      'agent-item border border-gray-300 rounded-[12px] px-[26px] py-[8px] mr-[12px] text-[13px] cursor-pointer',
      activeAgentId === item.id ? 'active-agent-item': ''
    ]"
      v-for="item in agentList"
      :key="item.id"
      @click="handleAgentClick(item.id)"
    >{{ item.name }}</span>
  </div>
</template>

<script setup lang="ts">
import { type PropType, toRefs } from 'vue'


type agentListType = {
  id: string
  name: string
}
const props = defineProps({
  activeAgentId: {
    type: String || null,
    default: ''
  },
  agentList: {
    type: Array as PropType<agentListType[]>,
    default: () => []
  }
})
console.log(props.activeAgentId)
const emit = defineEmits(['handleAgentClick'])
const { agentList } = toRefs(props)


const handleAgentClick = (id: string) => {
  emit('handleAgentClick', id)
}


</script>

<style scoped lang="less">
.agent {
  .agent-item {
    background-color: rgba(255, 255, 255, 0.5);
    backdrop-filter:  blur(59px);
    -webkit-backdrop-filter:  blur(59px);
    word-break: keep-all;
  }
  .active-agent-item {
    background-color: rgb(20, 24, 25);
    color: #fff;
  }
}

</style>
