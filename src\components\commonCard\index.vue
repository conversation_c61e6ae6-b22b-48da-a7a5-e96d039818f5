<template>
  <div class="common-card">
    <CardWithoutLabel
      v-if="cardData?.data?.cardType == 0"
      :cardData="cardData"
      :chatData="chatData"
    ></CardWithoutLabel>
    <CardWithLabel
      v-else-if="cardData?.data?.cardType == 1"
      :cardData="cardData"
      :chatData="chatData"
    ></CardWithLabel>
    <OperationalCard
      v-else-if="cardData?.data?.cardType == 2"
      :cardData="cardData"
      :chatData="chatData"
    ></OperationalCard>
    <FormCard
      v-else-if="cardData?.data?.cardType == 3"
      :cardData="cardData"
      :chatData="chatData"
    ></FormCard>
    <DataChartTable
      v-else-if="cardData?.data?.cardType == 4"
      :cardData="cardData"
      :chatData="chatData"
    ></DataChartTable>
    <DataAttachment
      v-else-if="cardData?.data?.cardType == 5"
      :cardData="cardData"
      :chatData="chatData"
      />
    <CardBi
      v-else-if="cardData?.data?.cardType == 6"
      :cardData="cardData"
    />
    <div v-else>{{ cardData }}</div>
    <!-- <FormCard :cardData="cardData" :chatData="chatData" /> -->
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, provide } from 'vue';
import type { PropType } from 'vue';
import type { ChatItem } from '@/types/index';
import CardWithLabel from './components/cardWithLabel/index.vue';
import CardWithoutLabel from './components/cardWithoutLabel/index.vue';
import OperationalCard from './components/operationalCard/index.vue';
import FormCard from './components/formCard/index.vue';
import DataChartTable from './components/dataChartTable/index.vue';
import DataAttachment from './components/dataAttachment/index.vue';
import CardBi from './components/cardBi/index.vue';
import type { CardResponse } from './types';
import { get,post } from '@/api/config';

const props = defineProps({
  chatData: {
    type: Object as PropType<ChatItem>,
    default: () => {},
  },
  content: {
    type: String,
    default: '',
  },
});

provide('axiosInstance', {
  get,
  post
});

const cardData = ref<CardResponse>();

// const testData = {
//     "code": "0",
//     "message": null,
//     "data": {
//         "pluginName": "",
//         "pluginKey": "",
//         "cardType": 4,
//         "disabled": false,
//         "moreButtonType": 0,
//         "pageInfo": {
//             "pageNumber": 1,
//             "pageSize": 10,
//             "pages": 1,
//             "total": 6,
//             "needTotal": false
//         },
//         "result": [
//             {
//                 "originApiInfo": {},
//                 "renderInfo": {
//                     "type": "bar_chart",
//                     "column_names": [
//                         "考勤日期",
//                         "考勤次数"
//                     ],
//                     "data": [
//                         [
//                             "2025-02-09",
//                             2
//                         ],
//                         [
//                             "2025-02-10",
//                             2
//                         ],
//                         [
//                             "2025-02-11",
//                             2
//                         ],
//                         [
//                             "2025-02-12",
//                             2
//                         ],
//                         [
//                             "2025-02-13",
//                             2
//                         ],
//                         [
//                             "2025-02-14",
//                             1
//                         ]
//                     ],
//                     "title": "王维2025年2月份考勤记录统计"
//                 }
//             }
//         ]
//     }
// }

onMounted(() => {
  try {
    cardData.value = JSON.parse(props.content);
    console.log('cardData.value', cardData.value);
  } catch (error) {
    console.log(error);
  }
});
</script>
<style lang="less" scoped></style>
