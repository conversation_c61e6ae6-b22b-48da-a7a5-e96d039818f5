<template>
  <div
    ref="elsaticIptRef"
    class="input-container"
    :class="{
      search_box_wrap: true,
      search_asist_box_wrap: transCkAssist.length > 0 && transCkAssist[0].type !== 'general',
      search_box_wrap_active: isFocus,
      filelist_box_wrap: fileList.length > 0,
      chatting_wrap: isAnswering,
    }"
    @click="handleContainerClick"
  >
    <div class="input_box_top" v-if="isShowInputBoxTop">
      <!-- 上方【选中助手】插槽 -->
      <slot name="checkAssist"></slot>
      <!-- 文件列表 -->
      <UploadFiles
        v-if="fileList.length > 0"
        :fileList="fileList"
        :showBackground="true"
        @delFile="delFileFn"
      />
    </div>
    <!-- 主输入区 -->
    <div class="search_box_shirt">
      <CoMiTextarea
        ref="ownRefTextArea"
        :placeholder="defaultPlaceholder"
        :inputTemplate="inputTemplate"
        v-model="computedKeyValue"
        @input="autoAdjustReviewInput"
        @focus="focus"
        @blur="blur"
        @keydown="sendMsg"
      ></CoMiTextarea>
      <!-- <textarea
        ref="ownRefTextArea"
        v-model="computedKeyValue"
        :placeholder="defaultPlaceholder"
        class="ele_txtara"
        @input="autoAdjustReviewInput"
        @focus="focus"
        @blur="blur"
        @keydown="sendMsg"
      ></textarea> -->

      <div class="w-full btn_box">
        <div class="btn_box_left">
          <Tooltip arrow-point-at-center title="快捷指令" placement="topLeft">
            <i
              class="iconfont ai-icon-kuaijiezhiling quick_btn"
              @click="toOpertePrologue"
              data-prologue="prologue"
            ></i>
          </Tooltip>
          <!-- 上传按钮 -->
          <Upload
            ref="commonUploadRef"
            :chatSessionId="chatSessionId"
            uploadType="btn"
            @getRusultFiles="getRusultFilesFn"
          ></Upload>
        </div>

        <div class="btn_box_right">
          <!-- 发送按钮 -->
          <div
            v-if="!isAnswering"
            :class="{
              send_btn: true,
              send_btn_active: computedKeyValue && computedKeyValue.trim().length > 0,
            }"
            @click="sendMsg"
          >
            <i class="iconfont ai-icon-fasong"></i>
          </div>
          <!-- 停止按钮 -->
          <div v-else class="stop-btn" @click="stopChatFn">
            <img :src="StopPic" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  watch,
  nextTick,
  onMounted,
  onUpdated,
  inject,
  defineExpose,
  computed,
  onBeforeUnmount,
} from 'vue';
import type { AssistInfo, ChatUserParams } from '@/types/index';
import Upload from '@/components/common/upload/index.vue';
import CoMiTextarea from '@/components/elasticIpt/CoMiTextarea.vue';
import { cutIt, getFileType } from '@/utils/tool';
import StopPic from '@/assets/imgs/stop-btn.png';
import UploadFiles from '@/components/common/uploadFiles/index.vue';
import { useCheckAssistantParams } from '@/stores/homeSearch';
import { useGlobal, useMenu } from '@/stores/global';
import { FILE_TYPE_MAP } from '@/utils/constant';
import cardInstance from '@/stores/card';
import { useChatList } from '@/stores/chatList';
import { Tooltip } from 'ant-design-vue';
import { useChatListAndElasticIpt } from '@/hooks/portal';

// props
const props = defineProps<{
  stopWtachAt?: boolean; // 暂时没用到
  transShowSal?: boolean;
  transCkAssist: AssistInfo[];
  dataAnalysisFiles?: {
    fileList?: any[];
    confileList?: any[];
  };
  quickMsg?: string | undefined;
  clearQuikcMsg?: () => void;
  toOperteSal: (val: 'show' | 'hide') => void;
  toOpertePrologue: (val: 'show' | 'hide') => void;
  isGeneralAssist?: any;
}>();

// store实例
const uGlobal = useGlobal();
const useChatListObj = useChatList();
const uCkAstPms = useCheckAssistantParams();
const chatSessionId = useChatListObj.dynamicData.chatSessionId;

// 获取sdk实例
const sdkInstance = inject('sdkInstance') as any;
const elemo = document.getElementsByClassName('main_opr');

// 使用 computed 来动态获取快速消息
const computedKeyValue = computed({
  get: () => {
    return props.quickMsg || keyValue.value;
  },
  set: (val) => {
    keyValue.value = val;
  },
});

// 输入框对象
const elsaticIptRef = ref();
// 输入框高度
const elsIptRefHeight = ref(0);
// 输入框内容
const keyValue = ref('');
// 是否聚焦
const isFocus = ref(false);
// 输入框ref
const ownRefTextArea = ref();

// 临时文件列表
const fileList = ref<any[]>([]);
// 真实上传文件列表
const confileList = ref<any[]>([]);
const inputTemplate = ref<any[]>([]);

const { goingSecItem } = useChatListAndElasticIpt();
// inputTemplate.value.push({
//   id: '1',
//   text: '数据测试'
// });
// inputTemplate.value.push({
//   id: '2',
//   type: "input",
//   placeholder: '课程主题'
// });
// inputTemplate.value.push({
//   id: '3',
//   text: '测试数据111'
// });

// 提示语
const placeholder1 = '输入您的问题，也可以通过@选择希望对话的助理';
const placeholder2 = '请输入您的问题';
const placeholder3 = '你可以问我文档、新闻、公告、表单、协同、公文等协同知识';
// 根据是否是超级助手展示对应的提示语
const defaultPlaceholder = computed(() => {
  if (useMenu().currentMenuInfo?.id == 'office') {
    return placeholder3;
  }
  return props.isGeneralAssist ? placeholder1 : placeholder2;
});

// 有非超级助手或者有文件时，显示上方区域
const isShowInputBoxTop = computed(() => {
  return (
    (props.transCkAssist.length > 0 && props.transCkAssist[0].type !== 'general') ||
    fileList.value.length > 0
  );
});

// 是否可以返回输入
let canBackInput = ref(false);

// 用户是否主动失焦（用于避免自动重新聚焦）
const userManuallyBlurred = ref(false);

// 是否正在回答中
const isAnswering = computed(() => {
  return useChatListObj.dynamicData.isAnswering;
});

// 停止会话
const stopChatFn = () => {
  cardInstance.interruptAction();
};

// 上传组件ref
const commonUploadRef = ref();

// 聚焦
const focus = () => {
  isFocus.value = true;
  // 清除用户主动失焦标志
  userManuallyBlurred.value = false;
};

const delFileFn = (itm: any, inx: number) => {
  if (commonUploadRef.value && commonUploadRef.value.delFile) {
    commonUploadRef.value.delFile(itm, inx);
  }
};

// 自动调整文本框大小
const autoAdjustReviewInput = () => {
  if (ownRefTextArea.value.$el.scrollHeight >= 118) {
    ownRefTextArea.value.$el.style.overflowY = 'auto';
  }
  ownRefTextArea.value.$el.style.height = 'auto';
  ownRefTextArea.value.$el.style.height = `${ownRefTextArea.value.$el.scrollHeight}px`;
  adjustIptTop();
};

// 失去焦点
const blur = () => {
  console.log('失去焦点');
  isFocus.value = false;
  // 标记用户主动失焦
  userManuallyBlurred.value = true;
  // 隐藏助手表
  props.toOperteSal('hide');
};

// 处理容器点击事件
const handleContainerClick = (e: Event) => {
  // 阻止事件冒泡，避免触发全局点击事件
  e.stopPropagation();

  // 检查点击的目标是否是输入框或其子元素
  const target = e.target as HTMLElement;
  const textareaElement = ownRefTextArea.value?.$el;

  // 如果点击的是输入框或其子元素，不执行失焦
  if (textareaElement && (textareaElement === target || textareaElement.contains(target))) {
    return;
  }

  // 如果点击的是上传按钮、发送按钮等功能按钮，不执行失焦
  if (target.closest('.btn_box') || target.closest('.upload_btn') || target.closest('.send_btn') || target.closest('.stop-btn')) {
    return;
  }

  // 其他情况下让输入框失去焦点
  if (textareaElement) {
    textareaElement.blur();
  }
};

// 打开开场白
const toOpertePrologue = () => {
  if (uGlobal.showParsingTips()) {
    return;
  }
  props.toOpertePrologue('show');
};

// 获取上传的文件
const getRusultFilesFn = (obj: any) => {
  fileList.value = obj.fileList;
  confileList.value = obj.confileList;
  // 文件列表变化时调整位置
  nextTick(() => {
    adjustIptTop();
  });
};
// let assistantCode = "";
// 发送消息
const sendMsgHandel = (inputValue: string, option: any = {}) => {
  inputTemplate.value = [];
  // assistantCode = "";
  if (option.assistant) {
    goingSecItem(option.assistant);
  }
  if (option.inputTemplate) {
    computedKeyValue.value = '';
    nextTick(() => {
      inputTemplate.value = option.inputTemplate;
    });
    return;
  }
  if (uGlobal.showParsingTips()) {
    return;
  }
  if (!inputValue || !inputValue?.trim()) {
    return;
  }
  let requestFiles =
    confileList.value.length > 0
      ? confileList.value.map((d: any) => {
          const fileTypeKey = getFileType(d.name) as string;
          const mappedFileType = FILE_TYPE_MAP[fileTypeKey] || 'unknown'; // 添加默认值处理
          return {
            fileNumber: d.number,
            name: d.name,
            id: d.id,
            fileUrl: d.fileUrl,
            fileType: mappedFileType,
            chatSessionId,
            size: d.fileSize,
            fileRelativeUrl: d.fileRelativeUrl,
          };
        })
      : [];

  cardInstance.sendMessage(inputValue.trim(), requestFiles, option?.isHide);

  // 关框
  props.toOperteSal('hide');
  // 置空
  computedKeyValue.value = '';
  ownRefTextArea.value.$el.blur();
  fileList.value = [];
  confileList.value = [];
  if (props.clearQuikcMsg && typeof props.clearQuikcMsg === 'function') {
    props.clearQuikcMsg();
  }

  // 重置用户主动失焦标志，允许发送消息后自动聚焦
  userManuallyBlurred.value = false;

  // 发送完后输入框恢复原来高度
  ownRefTextArea.value.$el.style.height = 'auto';
  // 发送消息后调整位置
  nextTick(() => {
    adjustIptTop();
  });
};
// 绑定信息发送
sdkInstance.bind('sendMsgHandel', sendMsgHandel);
sdkInstance.bind('stopMsg', (callback: any) => {
  if (isAnswering.value) {
    cardInstance.interruptAction();
    setTimeout(() => {
      callback();
    }, 500);
  } else {
    callback();
  }
});
sdkInstance.bind('setSendMsg', (value: string, option: any) => {
  if (option.isAdd) {
    computedKeyValue.value = computedKeyValue.value + value;
  } else {
    computedKeyValue.value = value;
  }
  ownRefTextArea.value.$el.focus();
  nextTick(() => {
    autoAdjustReviewInput();
  });
});
// 发送消息
const sendMsg = (e: any) => {
  //是否回车  ||  是否发送
  if (e.type === 'keydown' && e.keyCode === 13) {
    // Enter键
    if (e.shiftKey) {
      // Shift + Enter  阻止默认行为（阻止提交）
      e.preventDefault();
      const textarea = ownRefTextArea.value.$el as HTMLTextAreaElement;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;

      // 在光标位置插入换行符
      computedKeyValue.value =
        computedKeyValue.value.substring(0, start) + '\n' + computedKeyValue.value.substring(end);
      // 移动光标到新换行的位置后
      nextTick(() => {
        textarea.focus();
        textarea.selectionStart = textarea.selectionEnd = start + 1;
      });
    } else {
      // 仅 Enter
      // 执行发送逻辑
      if (!props.transShowSal) {
        if (computedKeyValue.value.length > 0) {
          canBackInput.value = true;
        } else {
          canBackInput.value = false;
        }
        sendMsgHandel(computedKeyValue.value);
      }
    }
  } else if (e.type == 'click') {
    if (computedKeyValue.value.length > 0) {
      canBackInput.value = true;
    } else {
      canBackInput.value = false;
    }
    sendMsgHandel(computedKeyValue.value);
  }
};
// 动态调整上距离值
const adjustIptTop = () => {
  nextTick(() => {
    const currentHeight = elsaticIptRef.value?.getBoundingClientRect().height;
    elsIptRefHeight.value = currentHeight;
    const outHeight = (document.body.offsetHeight - currentHeight) * 0.52 - 164;
    if (elemo && elemo.length > 0) {
      (elemo[0] as HTMLElement).style.top = `${outHeight}px`;
    }
  });
};

// 监听助手的变化
watch(
  () => props.transCkAssist,
  () => {
    // fileList.value = [];
    // confileList.value = [];
    computedKeyValue.value = cutIt(computedKeyValue.value);
    // 助手变化时调整位置
    nextTick(() => {
      adjustIptTop();
    });
  },
  { deep: true, immediate: false },
);

// 监听输入框内容变化
watch(computedKeyValue, (newValue: string) => {
  // 停止@的访问 常用助手列表
  if (
    props.stopWtachAt ||
    (typeof props.isGeneralAssist !== 'undefined' && !props.isGeneralAssist)
  ) {
    return;
  }

  //设置【助手搜索】关键字
  if (computedKeyValue.value.indexOf('@') > -1) {
    const atList = computedKeyValue.value.split('@');
    const lastAtStr = atList[atList.length - 1].trim();
    uCkAstPms.setCkAstStr(lastAtStr);
  }
  // 显示
  if (newValue.length > 0 && newValue.slice(-1) == '@') {
    props.toOperteSal('show');
  }
  // 关闭
  if (newValue.length == 0) {
    props.toOperteSal('hide');
    uCkAstPms.setCkAstStr('');
  }
});
// 监听数据分析文件列表
watch(
  () => props.dataAnalysisFiles,
  (newVal: any) => {
    if (newVal) {
      fileList.value = newVal.fileList || [];
      confileList.value = newVal.confileList || [];
    }
  },
  {
    deep: true,
  },
);

// 处理停止按钮状态变化的函数
const handleStopStateChange = () => {
  // 只有在回答完成且用户之前有输入内容且用户没有主动失焦时才自动聚焦
  if (!isAnswering.value && canBackInput.value && !userManuallyBlurred.value) {
    nextTick(() => {
      const textarea = ownRefTextArea.value.$el as HTMLTextAreaElement;
      textarea?.focus();
    });
  }
};

// 处理新对话的清空函数
const handleNewTopicClear = () => {
  fileList.value = [];
  confileList.value = [];
  computedKeyValue.value = '';
};

onMounted(() => {
  adjustIptTop();
  window.addEventListener('resize', () => {
    adjustIptTop();
  });
  if (ownRefTextArea.value?.$el) {
    ownRefTextArea.value.$el.addEventListener('focus', focus);
    ownRefTextArea.value.$el.addEventListener('blur', blur);
  }
});

onBeforeUnmount(() => {
  // 清理事件监听器
  if (ownRefTextArea.value?.$el) {
    ownRefTextArea.value.$el.removeEventListener('focus', focus);
    ownRefTextArea.value.$el.removeEventListener('blur', blur);
  }
  window.removeEventListener('resize', adjustIptTop);
});

// 使用 onUpdated 替代部分 watch 功能
onUpdated(() => {
  adjustIptTop();
  // 处理停止按钮状态变化
  handleStopStateChange();
  // 处理新对话清空  不允许再update中更新状态，或者会死循环
  // handleNewTopicClear();
});
// 将函数暴露出去
defineExpose({
  handleNewTopicClear,
});
</script>
<style scoped lang="less">
.chatting_wrap {
  pointer-events: none;
  .quick_btn,
  .upload_btn {
    opacity: 0.45;
  }
}
.search_box_wrap {
  width: 100%;
  border-radius: 12px;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 32px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  background: linear-gradient(
    99.3deg,
    rgba(42, 105, 254, 0.05) 0%,
    rgba(134, 146, 255, 0.05) 52.29%,
    rgba(161, 232, 255, 0.05) 99.29%
  );
  border: 1px solid #f6f6f8;

  .no_radius {
    border-radius: 0px;
  }

  .search_box_shirt {
    padding: 8px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 0;
    .ele_txtara {
      width: 100%;
      min-height: 34px;
      max-height: 118px;
      // margin-bottom: 8px;
      margin-bottom: 4px;
      overflow-y: hidden;
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: @font-weight-400;
      line-height: 22px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      resize: none;
      border-image-source: none;
      color: #000;
      caret-color: @sky;
      background-color: transparent;

      scrollbar-color: #e1dfe4 transparent;
      scrollbar-width: thin;
      .textarea {
        border: none;
      }
    }
    .ele_txtara::placeholder {
      color: #cccccc;
    }
    .ele_txtara:focus {
      box-shadow: none;
    }

    .btn_box {
      // background-color: rgb(210, 250, 234);
      height: 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .btn_box_left {
        display: flex;
        align-items: center;
      }

      .btn_box_right {
        display: flex;
      }
      .quick_btn {
        font-size: 16px;
        cursor: pointer;
        color: #00000099;
        &:hover {
          color: @primary-color;
        }
      }
      .send_btn {
        width: 32px;
        height: 32px;
        line-height: 32px;
        cursor: default;
        background: linear-gradient(99.3deg, #2a69fe 0%, #8692ff 52.29%, #a1e8ff 99.29%);
        text-align: center;
        // padding: 10px;
        border-radius: 999px;
        opacity: 0.45;
        color: white;
      }
      .send_btn_active {
        opacity: 1;
        cursor: pointer;
      }
      .stop-btn {
        pointer-events: auto;
        cursor: pointer;
        width: 32px;
        height: 32px;
        img {
          width: 100%;
        }
      }
    }
  }
}

.search_box_wrap:hover,
.search_box_wrap_active {
  background: linear-gradient(
    99.3deg,
    rgba(42, 105, 254, 0.05) 0%,
    rgba(134, 146, 255, 0.05) 52.29%,
    rgba(161, 232, 255, 0.05) 99.29%
  );
  border: 1px solid #2a69fe;
}
.search_asist_box_wrap,
.filelist_box_wrap {
  background-color: #f4f2ff;
  .search_box_shirt {
    border-radius: 0px 0px 8px 8px;
    padding: 7px;
  }
}
.input-container.filelist_box_wrap {
  .check_assist_box {
    padding-bottom: 0;
    height: 36px;
  }
}
.input-container {
  .input_box_top {
    background: #fff;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
  }
}
</style>
