<template>
  <section class="portal-layout-section">
    <!-- 添加keep-alive缓存LayoutContainer组件 -->
    <keep-alive :max="8">
      <component 
        v-for="(item, index) in stableNodes" 
        :key="getStableKey(item, index)"
        v-bind="item.props" 
        :class="item.class" 
        :nodes="item.processedChildren || item.children" 
        :style="item.style"
        :is="item.tag" 
      />
    </keep-alive>
  </section>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { parseCondition } from '../utils/conditionUtil';

const { nodes } = defineProps({
  nodes: {
    type: Array,
    default: () => [],
  },
});

// 缓存节点数据，确保稳定性
let cachedNodes: any[] = [];
const stableNodes = computed(() => {
  // 只有当有有效数据时才更新缓存
  if (nodes && nodes.length > 0) {
    cachedNodes = [...nodes];
  }
  return cachedNodes;
});

// 生成稳定的缓存key
function getStableKey(item: any, index: number): string {
  const parts = [
    'section', // 层级标识
    item.id,
    item.name,
    item.tag || 'component',
    item.type || 'default',
    item.class,
    index
  ].filter(Boolean);
  
  const key = parts.join('-');
  return key;
}


</script>

<style scoped lang="less">
.portal-layout-section {
  width: 100%;
  margin: auto;
}
</style>
