<template>
  <div class="top-back" v-if="canBack">
    <span class="back-wrapper" @click="handleBack">
      <i class="iconfont ai-icon-zuo" />
      <span>返回</span>
    </span>
  </div>
  <div class="top-back" v-if="canBackTodoList && !canBack">
    <span class="back-wrapper" @click="handleTodoListBack">
      <i class="iconfont ai-icon-zuo" />
      <span>返回</span>
    </span>
  </div>
  <div v-if="!canBack && !canBackTodoList"></div>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { useStateRouter } from '@/stores/stateRouter';
import { useGlobal } from '@/stores/global';
import { useCustomData } from '@/stores/global';
// TODO:此处临时设置了处理待办列表和待办详情的特殊逻辑，后续还需要进一步优化
const uGlobal = useGlobal();
const useCustomDataStore = useCustomData();

const stateRouter = useStateRouter();
const handleBack = () => {
  stateRouter.back();
};
const handleTodoListBack = () => {
  uGlobal.changeState('keepTodoList', true);
  useCustomDataStore.setCustomData('collApproveData', null);
};
// 是否显示返回按钮
const canBack = computed(() => {
  return stateRouter.currentRoute.context?.canBack;
});
// 是否显示返回按钮
const canBackTodoList = computed(() => {
  return uGlobal.globalState.preLinkIsTodoList && !uGlobal.globalState.keepTodoList;
});
</script>
<style lang="less" scoped>
.top-back {
  font-family: PingFang SC;
  font-weight: @font-weight-400;
  font-size: 16px;
  line-height: 22px;
  height: 56px;
  display: flex;
  align-items: center;
  width: 100%;
  color: #000000;

  .back-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;

    &:hover,
    &:hover .iconfont {
      color: @sky;
    }
    span{
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }
  }

  .iconfont {
    font-size: 14px;
    margin-right: 4px;
    cursor: pointer;
    color: #000000E5;
  }
}
</style>
