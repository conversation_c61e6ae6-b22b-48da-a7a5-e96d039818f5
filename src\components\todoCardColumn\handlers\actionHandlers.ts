import type { ButtonType, ConfirmActionData } from '../types';
import { message, Modal } from 'ant-design-vue';

/**
 * 动作处理器类 - 负责处理各种按钮操作的业务逻辑
 * 直接从 itemActionButtons.vue 搬移逻辑，保持完全一致
 */
export class ActionHandlers {
  constructor(
    private emit: any,
    private props: any,
    private buttonState: any,
    private showConfirmDialog: any,
    private openOpinionPopover: any,
    private handleHasten: any,
    private handleAllocation: any,
    private handleNextStep: any,
    private openPersonalDialog: any
  ) {}

  /**
   * 处理置顶相关操作
   * 直接搬移现有逻辑，保持完全一致
   */
  async handleTopActions(button: ButtonType): Promise<boolean> {
    if (button.handleType === 'top' || button.handleType === 'unTop') {
      this.emit('button-click', button);
      return true;
    }
    return false;
  }

  /**
   * 处理会议相关操作
   * 直接搬移现有逻辑，保持完全一致
   */
  async handleMeetingActions(button: ButtonType, event?: Event, isFromDropdown: boolean = false): Promise<boolean> {
    const dataType = this.buttonState?.value?.dataType;
    
    // 会议编辑操作
    if (dataType === '6' && button.handleType === 'edit') {
      if (button.paramMap?.providerCode === 'personal') {
        this.openPersonalDialog(button.paramMap.meetingId);
      } else {
        window.open(`${(window as any)._ctxPath || '/seeyon'}${button.url}`, '_blank');
      }
      return true;
    }

    // 会议取消操作 - 特殊处理，即使不满足意见策略也要打开意见弹窗，但不需要获取草稿
    if (dataType === '6' && button.handleType.toLowerCase() === 'cancel') {
      await this.openOpinionPopover(button, event, isFromDropdown, true, false); // hasPlaceholder = true, needDraft = false
      return true;
    }

    return false;
  }

  /**
   * 处理需要确认的操作（撤销/终止/回退等）
   * 直接搬移现有逻辑，保持完全一致
   */
  async handleConfirmActions(button: ButtonType): Promise<boolean> {
    // 普通撤销/终止/回退操作 - 显示确认对话框
    if (['cancel', 'terminate', 'return'].includes(button.handleType.toLowerCase()) ||
        (button.paramMap?.attitudeKey === "disagree" &&
         button.paramMap?.customAction?.isOptional === "0" &&
         button.paramMap?.customAction?.defaultAction?.toLowerCase() === "cancel")) {
      
      const confirmed = await this.showConfirmDialog(
        '提示',
        `当前执行操作【"${button.name}"】不可恢复，确认继续吗？`
      );
      
      if (confirmed) {
        // 设置态度（如果需要）
        if (button.attitudeList?.[button.attitudeList.length - 1]?.attitudeKey === "disagree") {
          button.paramMap.attitude = "common.disagree.label";
          button.paramMap.attitudeKey = "disagree";
        }
        this.emit('button-click', button);
      }
      return true;
    }
    return false;
  }

  /**
   * 处理其他特殊操作（催办、分配）
   * 直接搬移现有逻辑，保持完全一致
   */
  async handleOtherActions(button: ButtonType): Promise<boolean> {
    // 催办操作
    if (button.handleType.toLowerCase() === 'hasten') {
      this.handleHasten(button);
      return true;
    }

    // 分配操作
    if (button.handleType.toLowerCase() === 'allocation') {
      this.handleAllocation(button);
      return true;
    }

    return false;
  }

  /**
   * 处理NextStep相关逻辑
   * 直接搬移现有逻辑，保持完全一致
   */
  async handleNextStepActions(button: ButtonType, event?: Event): Promise<boolean> {
    // nextStep 逻辑检查 - 添加hasSelectedNextAction检查避免重复弹窗
    if (button.paramMap?.attitudeKey === "disagree" &&
        button.paramMap?.customAction?.isOptional === "1" &&
        button.handleType === "ContinueSubmit" &&
        !(button as any).hasSelectedNextAction) {  // 检查是否已选择下一步操作
      
      this.handleNextStep(button, event);  // 传递事件参数
      return true;
    }

    return false;
  }

  /**
   * 处理默认操作
   * 直接搬移现有逻辑，保持完全一致
   */
  handleDefaultAction(button: ButtonType): void {
    // 默认情况：直接提交
    this.emit('button-click', button);
  }
}

/**
 * 操作类型判断工具类
 */
export class ActionTypeChecker {
  /**
   * 检查是否需要意见策略处理
   * 直接搬移现有逻辑，保持完全一致
   */
  static needOpinionPolicyAction(button: ButtonType, buttonState: any): boolean {
    if (!buttonState?.value) return false;

    // 检查特殊组合情况
    const hasDisagreeAndCancelOrTerminate = button.paramMap?.attitudeKey === "disagree" &&
      button.paramMap?.customAction?.isOptional === "0" &&
      ['Terminate', 'Cancel'].includes(button.paramMap?.customAction?.defaultAction || '') &&
      buttonState.value.hasCancelOrTerminateOpinion;

    // 只有满足意见策略条件才返回 true
    return buttonState.value.hasOpinionHandle ||
      buttonState.value.hasCancelOrTerminateOpinion ||
      buttonState.value.hasDisagreeOpinion ||
      hasDisagreeAndCancelOrTerminate;
  }

  /**
   * 检查是否是特殊操作
   */
  static isSpecialOperation(button: ButtonType): boolean {
    return ['top', 'unTop', 'hasten', 'allocation'].includes(button.handleType);
  }

  /**
   * 检查是否是会议操作
   */
  static isMeetingOperation(button: ButtonType, dataType?: string): boolean {
    return dataType === '6' && ['edit', 'cancel'].includes(button.handleType);
  }

  /**
   * 检查是否是确认类操作
   */
  static isConfirmOperation(button: ButtonType): boolean {
    return ['cancel', 'terminate', 'return'].includes(button.handleType.toLowerCase()) ||
      (button.paramMap?.attitudeKey === "disagree" &&
       button.paramMap?.customAction?.isOptional === "0" &&
       button.paramMap?.customAction?.defaultAction?.toLowerCase() === "cancel");
  }

  /**
   * 检查是否是NextStep操作
   */
  static isNextStepOperation(button: ButtonType): boolean {
    return button.paramMap?.attitudeKey === "disagree" &&
      button.paramMap?.customAction?.isOptional === "1" &&
      button.handleType === "ContinueSubmit" &&
      !(button as any).hasSelectedNextAction;
  }
} 