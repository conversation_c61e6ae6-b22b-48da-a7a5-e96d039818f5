<template>
  <div class="question_wrap" ref="qr">
    <div
      class="question_item"
      v-for="item in transAssistAndQuestion.prologuePreQuestions"
      :key="item.id"
      @click="() => goSelectThisQuestion(item)"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
// 1-2-3
import { useHomeSearch } from '@/stores/homeSearch';
import { useRouter } from 'vue-router';
const uHmSh = useHomeSearch(); // store实例
const qr = ref();
defineExpose({ qr });

const props = defineProps<{
  transAssistAndQuestion: any;
}>();

// 点击
const goSelectThisQuestion = (itm: any) => {
  const { asitInfo, prologuePreQuestions } = props.transAssistAndQuestion;

  const params = {
    input: itm.name,
    citations: [],
    assistantId:asitInfo.id,
    assistantCode:asitInfo.code,
  };
  // 远存
  uHmSh.changeParams({
    ...params,
  });
  // 发送
  localStorage.setItem('menu_type_inx', JSON.stringify({ type: 'top', inx: 1 }));
  // uRtr.push({
  //   name: 'NewConversation',
  // });
};
</script>

<style scoped lang="less">
.question_wrap {
  width: 100%;
  padding: 0px 53px;
  //   background-color: lightgoldenrodyellow;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  margin-top: 32px;
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-around;
  justify-content: center;
  // justify-content: space-between;
  .question_item {
    padding: 5px 11px;
    border-radius: 4px;
    border: 1px solid #ffffff;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(16px);
    //styleName: 14px/Regular;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: @font-weight-400;
    line-height: 22px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #000000;
    margin-bottom: 16px;
    cursor: pointer;
    margin: 0px 8px 16px;
    // margin-right: 16px;
  }
  .question_item:hover {
    color: @violet;
  }
}
</style>
