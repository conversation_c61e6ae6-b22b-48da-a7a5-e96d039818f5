<template>
  <div class="ai-tabs-wrapper" ref="aiTabsWrapper">
    <div
      class="ai-tab-content"
      id="guide-step6"
      ref="tabContent"
      :class="{ 'can-wheel': canWhell, hasArrow: showArrow }"
      v-if="tabs.length > 0"
    >
      <div
        v-for="tab in tabs"
        :key="tab.key"
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        @click="tabIconClick(tab.key, tab)"
        ref="tabItem"
        :style="getStyle(tab.label)"
        :title="tab.label"
      >
      <i v-if="tab.className" class="base-icon-setting" :class="tab.className"></i>
      <i v-else-if="!tab.className && tab.from === 'quickOperation'" class="base-icon-setting iconfont ai-icon-jiahao"></i>
        <span class="text">{{ tab.label }}</span>
      </div>
    </div>
    <template v-if="showArrow">
      <div
        v-for="icon in icons"
        :key="icon.class"
        class="i-wrapper"
        :class="{
          'icon-disabled':
            (leftDisabled && icon.type === 'pre') || (rightDisabled && icon.type === 'next'),
        }"
        @click="handleArrowClick(icon.type)"
      >
        <i class="iconfont" :class="icon.class"></i>
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { useGlobal } from '@/stores/global';
import type { TabItem } from '@/types';
import {
  defineComponent,
  defineEmits,
  ref,
  computed,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
} from 'vue';
import { useChatList } from '@/stores/chatList';
defineComponent({
  name: 'AiTabs',
});
const { tabs, activeTab, canWhell, notNeedSelect, portalType } = defineProps<{
  tabs: Array<TabItem>;
  activeTab: string;
  canWhell?: boolean;
  notNeedSelect?: boolean;
  portalType?: 'portal' | 'dobuleScreen' | 'normal';
}>();
const isPortal = inject('isPortal');

const chatListStore = useChatList();
const tabContent = ref<any>(null);
const aiTabsWrapper = ref<any>(null);
const showArrow = ref<boolean>(false);
//左右按钮禁用
const leftDisabled = ref<boolean>(true);
const rightDisabled = ref<boolean>(false);
const icons = [
  {
    type: 'pre',
    class: 'ai-icon-zuo',
  },
  {
    type: 'next',
    class: 'ai-icon-you',
  },
];

const emit = defineEmits(['tab-click', 'showArrow']);
const tabItem = ref<any>(null);
const getHiddenWidth = () => {
  return new Promise((resolve) => {
    const element = tabContent.value;
    if (!element) resolve(0);
    // 克隆节点
    const clone = element.cloneNode(true);
    clone.style.cssText = `
    position: absolute;
    visibility: hidden;
    max-width: fit-content;
  `;
    // 添加到DOM
    aiTabsWrapper.value.appendChild(clone);
    nextTick(() => {
      const width = clone.offsetWidth;
      // 移除克隆节点
      aiTabsWrapper.value.removeChild(clone);
      resolve(width);
    });
  });
};
const getStyle = (tabLabel: string) => {
  let width = '0';
  if (portalType === 'normal') {
    width = tabLabel.length > 6 ? '100px' : '0';
  } else if (portalType === 'portal') {
    width = tabLabel.length > 6 ? '145px' : '0';
  }

  if (width !== '0') {
    return {
      minWidth: width,
      width: width,
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
    };
  }
};

// 箭头点击处理 - 改为滚动控制
const handleArrowClick = (type: string) => {
  if (!tabContent.value) return;
  if (!notNeedSelect) {
    //需要边滚动边选中就走以前的逻辑
    tabIconClick(type);
    return;
  }
  const tabContainer = tabContent.value;
  const tabClientWidth = tabContainer.clientWidth;
  const scrollAmount = tabClientWidth * 0.8; // 每次滚动容器宽度的80%
  const scrollLeft = tabContainer.scrollLeft;
  const scrollWidth = tabContainer.scrollWidth;
  const toRightDistance = scrollWidth - scrollLeft - tabClientWidth; // 剩余可滚动距离
  const canScrollRight = toRightDistance > 1; //兼容小数
  const maxScrollLeft = scrollWidth - tabClientWidth;

  let targetScrollLeft = scrollLeft;

  if (type === 'next' && canScrollRight) {
    // 向右滚动
    targetScrollLeft = Math.min(scrollLeft + scrollAmount, maxScrollLeft);
    tabContent.value.scrollBy({
      left: scrollAmount,
      behavior: 'smooth',
    });
  } else if (type === 'pre' && scrollLeft > 0) {
    // 向左滚动
    targetScrollLeft = Math.max(scrollLeft - scrollAmount, 0);
    tabContent.value.scrollBy({
      left: -scrollAmount,
      behavior: 'smooth',
    });
  }
};
// icon点击
const tabIconClick = (type: string, item?: any) => {
  const activeTabIndex = tabs.findIndex((tab) => tab.key === activeTab);
  let newTabKey = '';
  if (type == 'next') {
    if (activeTabIndex < tabs.length - 1) {
      newTabKey = tabs[activeTabIndex + 1].key;
    } else {
      return;
    }
  } else if (type == 'pre') {
    if (activeTabIndex > 0) {
      newTabKey = tabs[activeTabIndex - 1].key;
    } else {
      return;
    }
  } else {
    newTabKey = type;
  }
  emit('tab-click', newTabKey, item);
  scrollToTab(newTabKey);
};

// 滚动到指定tab
const scrollToTab = (key: string) => {
  const inx = tabs.findIndex((tab) => tab.key === key);
  if (inx !== -1) {
    nextTick(() => {
      if (tabItem.value && tabItem.value[inx]) {
        tabItem.value[inx].scrollIntoView({
          behavior: 'smooth',
          block: 'center', // 居中对齐
          inline: 'center', // 水平居中对齐
        });
      }
    });
  }
};

defineExpose({
  scrollToTab,
});

const handleWheel = (e: WheelEvent) => {
  if (tabContent.value) {
    tabContent.value.scrollLeft += e.deltaY; // 根据滚轮方向调整 scrollLeft
  }
};

const checkContentWidth = async () => {
  getHiddenWidth().then((res: any) => {
    const aiTabsWrapperWidth = aiTabsWrapper.value.offsetWidth;
    if (res > aiTabsWrapperWidth) {
      showArrow.value = true;
    } else {
      showArrow.value = false;
    }

    // 特殊处理
    if (res + 100 < aiTabsWrapperWidth) {
      emit('showArrow', false);
    } else {
      emit('showArrow', true);
    }
  });
};
const handleDisabled = (e: Event) => {
  const tabContentDom = tabContent.value;
  const scrollLeft = tabContentDom.scrollLeft;
  const scrollDistance = tabContentDom.scrollWidth - scrollLeft;

  if (scrollLeft === 0) {
    leftDisabled.value = true;
    rightDisabled.value = false;
  } else if (scrollDistance - tabContentDom.clientWidth < 1) {
    rightDisabled.value = true;
    leftDisabled.value = false;
  } else if (scrollLeft > 0 && scrollDistance > tabContentDom.clientWidth) {
    rightDisabled.value = false;
    leftDisabled.value = false;
  }
};

// 添加防抖的 checkContentWidth
const debouncedCheckContentWidth = (() => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;
  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      checkContentWidth();
    }, 100);
  };
})();

// 监听tabs变化
// watch(() => tabs, checkContentWidth, { deep: true });

onMounted(() => {
  tabIconClick(activeTab);
  checkContentWidth();
  if (canWhell) {
    tabContent.value?.addEventListener('wheel', handleWheel);
  }
  window.addEventListener('resize', checkContentWidth);
  tabContent.value?.addEventListener('scroll', handleDisabled);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkContentWidth);
  if (canWhell) {
    tabContent.value?.removeEventListener('wheel', handleWheel);
  }
  tabContent.value?.removeEventListener('scroll', handleDisabled);
});
watch(
  [
    () => useGlobal().globalState.isFullScreen,
    () => chatListStore.dynamicData.dobuleScreenData.show,
    () => chatListStore.dynamicData.dobuleScreenIsFull,
  ],
  () => {
    nextTick(() => {
      debouncedCheckContentWidth();
    });
  },
);
watch(()=> chatListStore.dynamicData.allCardData.length, (newValue, oldValue)=>{
  if(newValue > oldValue && oldValue === 0) {
    nextTick(() => {
      debouncedCheckContentWidth();
    });
  }else if(newValue < oldValue && newValue === 0) {
    nextTick(() => {
      debouncedCheckContentWidth();
    });
  }
});
</script>
<style lang="less">
.ai-tabs-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  margin: 12px 0;
  width: 100%;
  height: 32px;

  .hide-tab-content {
    display: none;
  }

  .i-wrapper {
    width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    cursor: pointer;
    &.icon-disabled {
      cursor: not-allowed;

      .iconfont {
        color: rgba(0, 0, 0, 0.1);
      }
    }
  }

  .iconfont {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.4);
  }

  .ai-tab-content {
    display: flex;
    overflow: hidden;
    // min-width: 1px;
    gap: 4px;
    max-width: 100%;

    &.hasArrow {
      max-width: calc(100% - 68px);
    }

    &.hide {
      display: none;
    }

    &.can-wheel {
      overflow-x: auto;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, and Opera */
      }
    }

    .tab-item {
      padding: 1px 8px;
      cursor: pointer;
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-size: 14px;
      line-height: 22px;
      white-space: nowrap;
      border-radius: 4px;
      text-align: center;
      .base-icon-setting {
        color: rgba(63, 67, 77, 1);
        margin-right: 4px;
      }
      &.active {
        background-color: #edf2fc;
        color: @sky;
      }
      &:hover {
        // background-color: @sky;
        color: @sky;
      }
    }
    .tool_line {
      background-color: #4379ff;
      width: 100%;
      height: 4px;
      border-radius: 2px;
    }
  }
}
</style>
