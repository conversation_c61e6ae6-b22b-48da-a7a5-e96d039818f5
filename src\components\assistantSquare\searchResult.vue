<template>
  <div class="search_result">
    <span> 与“ </span>
    <span class="search_result_text ellipsis">{{ searchValue }}</span>
    <span>”相关的智能体&nbsp;·&nbsp;</span>
    <span> {{ length }}</span>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  length: number;
  searchValue: string | undefined;
}>();
</script>
<style scoped lang="less">
.search_result {
  height: 40px;
  display: flex;
  align-items: center;
  color: #000000;
  word-break: keep-all;

  span {
    font-weight: @font-weight-500;
    font-size: 14px;
    line-height: 22px;
  }

  .search_result_text {
    flex: 1;
    max-width: fit-content;
    flex-shrink: 1;
  }
}
</style>
