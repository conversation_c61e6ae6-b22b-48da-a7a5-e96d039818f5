.menu-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100%;
  
  // 搜索区域
  .menu-search-section {
    // padding: 20px;
    position: relative;
    z-index: 100;
  }
  
  // 历史记录区域
  .menu-history-section {
    // padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    position: relative;
    z-index: 99;
    align-items: baseline;
  }
  
  // 我的收藏区域
  .menu-favorites-section {
    position: relative;
    z-index: 98;
  }
  
  // 菜单瀑布流区域
  .menu-waterfall-section {
    flex: 1;
    overflow: hidden;
    position: relative;
    margin-top: 12px;
  }
  
  // 操作按钮区域
  .menu-actions {
    flex-shrink: 0;
    
    .expand-button {
      padding: 0;
      cursor: pointer;
      font-size: 12px;
      color: #666;
      
      &:hover {
        border-color: var(--theme-brand6, #4379FF);
        color: var(--theme-brand6, #4379FF);
      }
      
      i {
        font-size: 12px;
        margin-right: 4px;
      }
    }
  }
  .loading {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .loading .loader_img {
    width: 20px;
    height: 20px;
    animation: ai-identify-loadcir 800ms linear 0ms infinite normal none;
    position: relative;
  }
  
  @keyframes ai-identify-loadcir {
    from {
      transform: rotateZ(0deg);
    }
    to {
      transform: rotateZ(360deg);
    }
  }
}

// 主题色变量
:root {
  --theme-brand6: #4379FF;
}