import type { ButtonType, ConfirmActionData, RenderInfoItemType } from '../types';

/**
 * 事件来源类型
 */
export type EventSource = 'todoCardItem' | 'itemActionButtons' | 'actionPopoverManager';

/**
 * 事件类型
 */
export type EventType = 'button-click' | 'refresh' | 'visibility-change' | 'action-completed' | 'action-failed';

/**
 * 统一的事件数据结构
 */
export interface ActionEvent {
  type: EventType;
  button: ButtonType;
  renderInfo?: RenderInfoItemType | any;
  confirmData?: ConfirmActionData;
  source: EventSource;
  timestamp?: number;
  error?: any;
  result?: any;
}

/**
 * 按钮点击事件数据
 */
export interface ButtonClickEvent extends ActionEvent {
  type: 'button-click';
  button: ButtonType;
  renderInfo: RenderInfoItemType | any;
  confirmData?: ConfirmActionData;
  source: EventSource;
}

/**
 * 刷新事件数据
 */
export interface RefreshEvent extends ActionEvent {
  type: 'refresh';
  source: EventSource;
  reason?: string;
}

/**
 * 显示状态变化事件数据
 */
export interface VisibilityChangeEvent extends ActionEvent {
  type: 'visibility-change';
  visible: boolean;
  source: EventSource;
}

/**
 * 操作完成事件数据
 */
export interface ActionCompletedEvent extends ActionEvent {
  type: 'action-completed';
  button: ButtonType;
  result: any;
  source: EventSource;
}

/**
 * 操作失败事件数据
 */
export interface ActionFailedEvent extends ActionEvent {
  type: 'action-failed';
  button: ButtonType;
  error: any;
  source: EventSource;
}

/**
 * 事件规范化工具类
 */
export class EventNormalizer {
  /**
   * 创建按钮点击事件
   */
  static createButtonClickEvent(
    button: ButtonType, 
    renderInfo?: RenderInfoItemType | any, 
    confirmData?: ConfirmActionData,
    source: EventSource = 'todoCardItem'
  ): ButtonClickEvent {
    return {
      type: 'button-click',
      button,
      renderInfo,
      confirmData,
      source,
      timestamp: Date.now()
    };
  }

  /**
   * 创建刷新事件
   */
  static createRefreshEvent(
    source: EventSource,
    reason?: string
  ): RefreshEvent {
    return {
      type: 'refresh',
      button: {} as ButtonType, // 刷新事件不需要button，但接口要求有
      source,
      reason,
      timestamp: Date.now()
    };
  }

  /**
   * 创建显示状态变化事件
   */
  static createVisibilityChangeEvent(
    visible: boolean,
    source: EventSource
  ): VisibilityChangeEvent {
    return {
      type: 'visibility-change',
      button: {} as ButtonType, // 显示状态变化事件不需要button，但接口要求有
      visible,
      source,
      timestamp: Date.now()
    };
  }

  /**
   * 创建操作完成事件
   */
  static createActionCompletedEvent(
    button: ButtonType,
    result: any,
    source: EventSource
  ): ActionCompletedEvent {
    return {
      type: 'action-completed',
      button,
      result,
      source,
      timestamp: Date.now()
    };
  }

  /**
   * 创建操作失败事件
   */
  static createActionFailedEvent(
    button: ButtonType,
    error: any,
    source: EventSource
  ): ActionFailedEvent {
    return {
      type: 'action-failed',
      button,
      error,
      source,
      timestamp: Date.now()
    };
  }

  /**
   * 规范化事件数据
   * 将不同组件的事件数据转换为统一格式
   */
  static normalize(
    type: EventType,
    button: ButtonType, 
    renderInfo?: RenderInfoItemType | any, 
    confirmData?: ConfirmActionData,
    source: EventSource = 'todoCardItem',
    additionalData?: any
  ): ActionEvent {
    const baseEvent: ActionEvent = {
      type,
      button,
      renderInfo,
      confirmData,
      source,
      timestamp: Date.now(),
      ...additionalData
    };

    return baseEvent;
  }

  /**
   * 检查事件是否有效
   */
  static isValidEvent(event: any): event is ActionEvent {
    return event && 
           typeof event === 'object' && 
           typeof event.type === 'string' &&
           typeof event.source === 'string' &&
           event.button !== undefined;
  }

  /**
   * 获取事件的描述信息
   */
  static getEventDescription(event: ActionEvent): string {
    switch (event.type) {
      case 'button-click':
        return `按钮点击: ${event.button.name} (来源: ${event.source})`;
      case 'refresh':
        return `刷新事件 (来源: ${event.source})`;
      case 'visibility-change':
        return `显示状态变化 (来源: ${event.source})`;
      case 'action-completed':
        return `操作完成: ${event.button.name} (来源: ${event.source})`;
      case 'action-failed':
        return `操作失败: ${event.button.name} (来源: ${event.source})`;
      default:
        return `未知事件: ${event.type} (来源: ${event.source})`;
    }
  }
}

/**
 * 事件处理器接口
 */
export interface EventHandler<T extends ActionEvent = ActionEvent> {
  canHandle(event: ActionEvent): boolean;
  handle(event: T): Promise<void> | void;
}

/**
 * 事件调试工具
 */
export class EventDebugger {
  private static enabled = false;

  /**
   * 启用事件调试
   */
  static enable(): void {
    EventDebugger.enabled = true;
  }

  /**
   * 禁用事件调试
   */
  static disable(): void {
    EventDebugger.enabled = false;
  }

  /**
   * 记录事件
   */
  static log(event: ActionEvent): void {
    if (!EventDebugger.enabled) return;

    console.group(`🎯 ${EventNormalizer.getEventDescription(event)}`);
    console.log('事件详情:', event);
    console.log('时间戳:', new Date(event.timestamp || Date.now()).toLocaleString());
    console.groupEnd();
  }

  /**
   * 记录事件错误
   */
  static logError(event: ActionEvent, error: any): void {
    if (!EventDebugger.enabled) return;

    console.group(`❌ 事件处理失败: ${EventNormalizer.getEventDescription(event)}`);
    console.log('事件详情:', event);
    console.error('错误信息:', error);
    console.groupEnd();
  }
} 