<template>
  <div class="coll-approval">
    <div class="text">为你提供智能辅助能力：</div>
    <div class="button" @click="collApproval">
      <img class="image" :src="questionXing" />
      <div class="button-text">辅助审批</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import questionXing from '@/assets/imgs/question-xing.png';
const _$emit = defineEmits(['collApproval']);
const props = defineProps<{
  collApproveData: any;
}>();
function collApproval() {
  _$emit('collApproval', props.collApproveData);
}
</script>

<style lang="less" scoped>
.coll-approval {
  margin-bottom: 32px;
  .text {
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
  }
  .button {
    margin-top: 15px;
    display: flex;
    align-items: center;;
    border: 1px solid rgba(209, 224, 255, 1);
    width: 104px;
    height: 34px;
    padding: 2px 8px;
    opacity: 1;
    border-radius: 24px;
    cursor: pointer;
    .image {
      height: 20px;
    }
    .button-text {
      margin-left: 4px;
      width: fit-content;
      flex: 1;
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
      background: linear-gradient(90deg, #AB7DFE 0%, #5873F6 100%);
      background-clip: text;
      color: transparent;
    }
  }
}

</style>
