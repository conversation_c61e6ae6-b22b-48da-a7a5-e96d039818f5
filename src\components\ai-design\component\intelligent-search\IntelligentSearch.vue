<template>
  <div
    class="intelligent-search-wrapper w-full relative"
    :class="{ 'intelligent-search-small': isCopilotScreen }"
  >
    <div class="top-content">
      <div class="header">
        <div class="mb-[20px] title">服务站</div>
        <div class="desc">帮您查询企业知识</div>
        <img
          v-if="questionList.length > 0"
          src="@/assets/imgs/create_logo.png"
          alt="createCardColumnBg"
          class="absolute top-[5px] right-0 w-[100px] h-[100px] object-cover"
        />
      </div>
      <div class="content-wrapper" v-if="isCopilotScreen">
        <div
          class="content"
          ref="contentRef"
          :style="{
            transform: `translateX(-${currentScrollNum * cardWidth}px)`,
            transition: 'transform 0.5s cubic-bezier(0.23, 1, 0.32, 1)',
          }"
        >
          <QuestionItem v-for="(item, index) in questionList" :data="item" :key="index" />
        </div>

        <div class="bottom-blur" v-if="false"></div>
      </div>
      <div v-else class="content" ref="contentRef">
        <div
          class="question-content-wrapper"
          :style="{
            transform: `translateX(-${currentScrollNum * cardWidth}px)`,
            transition: 'transform 0.5s cubic-bezier(0.23, 1, 0.32, 1)',
          }"
        >
          <QuestionItem v-for="(item, index) in questionList" :data="item" :key="index" />
        </div>
      </div>
      <Arrow
        @handleArrowClick="handleArrowClick"
        :canScrollLeft="canScrollLeft"
        :canScrollRight="canScrollRight"
        class="arrow-wrapper"
        v-if="isCopilotScreen || questionList.length > 3"
      />
    </div>
    <AiRecommendQuestion
      v-if="false"
      :questionList="recentQuestionList"
      @questionClick="questionClick"
      class="search-recommend-question"
      :isCopilotScreen="isCopilotScreen"
    />
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, computed, inject } from 'vue';
import QuestionItem from './QuestionItem.vue';
import portalApi from '@/api/portal/portal-api';
import cardInstance from '@/stores/card';
import AiRecommendQuestion from '@/components/ai-recommennd-question/index.vue';
import Arrow from '@/components/common/arrow/index.vue';
</script>
<script setup lang="ts">
import { useChatList } from '@/stores/chatList';

const isPortal = inject('isPortal');

const uChatList = useChatList();
const currentScrollNum = ref(0);
const columnCount = ref(0);
const recentQuestionList = ref<any[]>([]);
const allQuestionList = ref<any[]>([]);

const isCopilotScreen = computed(() => {
  return !isPortal || uChatList.dynamicData.dobuleScreenData.show;
});

const questionList = computed(() => {
  if (isCopilotScreen.value) {
    columnCount.value = allQuestionList.value.length;
    return allQuestionList.value;
  }
  const fileterData = allQuestionList.value.filter((item: any) => item.id !== 'recent');
  columnCount.value = fileterData.length;
  return fileterData;
});

const questionClick = (item: any) => {
  cardInstance.sendMessage(item.text);
};

const canScrollLeft = computed(() => {
  return columnCount.value > 1 && currentScrollNum.value > 0;
});

const canScrollRight = computed(() => {
  if (isCopilotScreen.value) {
    return columnCount.value > 1 && currentScrollNum.value < columnCount.value - 1;
  }
  return columnCount.value > 1 && currentScrollNum.value < columnCount.value - 3;
});

const contentRef = ref<any>(null);
const cardWidth = computed(() => {
  if (contentRef.value) {
    if (isCopilotScreen.value) {
      return contentRef.value.clientWidth;
    }
    return (contentRef.value?.clientWidth - 24) / 3 + 12;
  }
  return 0;
});

const handleArrowClick = (type: string) => {
  if (type === 'pre' && canScrollLeft.value) {
    currentScrollNum.value = currentScrollNum.value > 0 ? currentScrollNum.value - 1 : 0;
  } else if (type === 'next' && canScrollRight.value) {
    currentScrollNum.value =
      currentScrollNum.value < columnCount.value - 1
        ? currentScrollNum.value + 1
        : columnCount.value - 1;
  }
};

onMounted(async () => {
  const res = await portalApi.getSearchRecommendQuestion();
  if (res && res.data?.length) {
    allQuestionList.value = res.data;
    recentQuestionList.value = res.data.find((item: any) => item.id === 'recent')?.questions || [];
  }
});
</script>
<style scoped lang="less">
.intelligent-search-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  height: calc(100% - 200px);
  justify-content: space-between;
  margin-bottom: 10px;

  .top-content {
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .question-item-wrapper {
      padding-bottom: 16px;
    }
  }

  .header {
    .desc {
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-style: Regular;
      font-size: 13px;
      line-height: 20px;
      color: #000000e5;
      margin-bottom: 12px;
    }
    .title {
      font-family: PingFang SC;
      font-weight: @font-weight-500;
      font-style: Semibold;
      font-size: 24px;
      line-height: 32px;
    }
  }

  .content {
    overflow: hidden;
    height: 344px;

    .question-content-wrapper {
      height: 100%;
      display: flex;
      gap: 12px;
      width: 100%;
    }
    :deep(.question-item-wrapper) {
      width: calc((100% / 3) - 12px);
      flex: none;
      padding: 16px 12px;

      .content {
        max-height: 312px;
        overflow: auto;
        .content::-webkit-scrollbar {
          width: 8px;
          background: #f0f0f0;
        }
        .content::-webkit-scrollbar-thumb {
          background: #d1e0ff;
          border-radius: 4px;
        }
        .content::-webkit-scrollbar-track {
          background: #f0f0f0;
        }
      }
    }
  }
  .arrow-wrapper {
    z-index: 10;
    margin-top: 3px;
    text-align: right;
    width: 100%;
    justify-content: flex-end;
    height: 32px;
    align-items: center;
    margin-bottom: 4px;
  }
  .search-recommend-question {
    margin-top: 9px;
  }
}
.intelligent-search-small {
  .top-content {
    backdrop-filter: blur(30px);
    border: 1.5px solid #ffffffcc;
    background: #ffffff80;
    width: 100%;
    border-radius: 12px;
    padding: 16px 16px 0 16px;
    box-sizing: border-box;
  }

  .title,
  img {
    display: none;
  }

  .desc {
    font-size: 13px;
    color: #00000099;
  }
  .content-wrapper {
    position: relative;
    overflow: hidden;
  }
  .content {
    overflow: unset;
    padding-bottom: 0;
    width: 100%;
    gap: 0;
    display: flex;
    :deep(.question-item-wrapper) {
      width: 100%;
      flex: none;
      overflow: auto;
    }
  }
  .arrow-wrapper {
    margin-top: 16px;
  }
  .bottom-blur {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 33px;
    background: linear-gradient(
      0deg,
      #ffffff 0%,
      rgba(255, 255, 255, 0.8) 25.48%,
      rgba(255, 255, 255, 0) 100%
    );
    border-bottom-right-radius: 12px;
    border-bottom-left-radius: 12px;
  }
}
</style>
