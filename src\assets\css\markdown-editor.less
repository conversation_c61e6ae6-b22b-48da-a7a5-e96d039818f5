.md-editor .md-editor-preview {
  --md-theme-color: rgba(0, 0, 0, 1) !important;
  --md-theme-color-reverse: rgba(0, 0, 0, 0.6) !important;
  --md-theme-color-hover: #eee;
  --md-theme-color-hover-inset: #ddd;
  --md-theme-link-color: @sky !important;
  --md-theme-link-hover-color: @sky !important;
  --md-theme-border-color: rgba(0, 0, 0, 0.6);
  --md-theme-border-color-reverse: #bebebe;
  --md-theme-border-color-inset: #d6d6d6;
  --md-theme-bg-color: #fff;
  --md-theme-bg-color-inset: #ececec;
  --md-theme-bg-color-scrollbar-track: #e2e2e2;
  --md-theme-bg-color-scrollbar-thumb: rgba(0, 0, 0, 0.3019607843);
  --md-theme-bg-color-scrollbar-thumb-hover: rgba(0, 0, 0, 0.3490196078);
  --md-theme-bg-color-scrollbar-thumb-active: rgba(0, 0, 0, 0.3803921569);
  --md-theme-code-copy-tips-color: inherit;
  --md-theme-code-copy-tips-bg-color: #fff;
  --md-theme-code-active-color: #61aeee;
}


div.md-editor-preview ol, div.md-editor-preview ul {
    padding-left: 1.5em;
    li {
      &::marker {
        display: inline-block;
        unicode-bidi: isolate;
        font-variant-numeric: tabular-nums;
        text-transform: none;
        text-indent: 16px !important;
        text-align: start !important;
        text-align-last: auto !important;
      }
  }
}

// md-editor-v3 bug fix
.md-editor-preview {
    font-size: 14px !important;
    // 有序列表
    ul li {
      list-style-type: disc !important;
    }
    ol > li {
      list-style-type: decimal !important;
    }
    ul li li {
      list-style-type: circle !important;
    }
    ul li li li {
      list-style-type: square !important;
    }

    // 表格样式
    .table {
      border-collapse: collapse;
      /* 合并边框 */
    }
  
    .table,
    .table th,
    .table td {
      border: 1px solid #bdc5d6;
      white-space: normal;
    }
    .table {
      width: 100%;
      .column-name {
        background-color: #f3f6ff;
        color: rgba(0, 0, 0, 0.6);
      }
      th,
      td {
        min-width: 68px;
        /* 设置边线颜色为蓝色 */
        padding: 8px 12px;
        height: 39px;
        box-sizing: border-box;
        text-align: center;
        span {
          word-break: keep-all;
          white-space: nowrap;
        }
      }
      tbody {
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%) !important;
      }
    }

    // 文字样式
    h1 {
      font-weight: 600 !important;
      font-size: 24px !important;
      line-height: 32px !important;
      letter-spacing: 0% !important;
      margin: 0 0 12px !important;
    }
    h2 {
      font-weight: 600 !important;
      font-size: 20px !important;
      line-height: 28px !important;
      letter-spacing: 0% !important;
      margin: 0 0 10px !important;
    }
    h3 {
      font-weight: 600 !important;
      font-size: 18px !important;
      line-height: 26px !important;
      letter-spacing: 0% !important;
      margin: 0 0 9px !important;
    }
    h4 {
      font-weight: 600 !important;
      font-size: 16px !important;
      line-height: 24px !important;
      letter-spacing: 0% !important;
      margin: 0 0 8px !important;
    }
    h5, h6 {
      font-weight: 600 !important;
      font-size: 14px !important;
      line-height: 22px !important;
      letter-spacing: 0% !important;
      margin: 0 0 7px !important;
    }
    p, li {
      font-weight: 400 !important;
      font-size: 14px !important;
      line-height: 22px !important;
      letter-spacing: 0% !important;
      margin: 0 0 0.5em !important;
    }
    blockquote {
      font-weight: 400 !important;
      font-size: 14px !important;
      line-height: 22px !important;
      letter-spacing: 0% !important;
      padding-left: 1em !important;
      border-left: 2px solid #d5d6db !important;
      margin-left: 0 !important;
    }
}