import { ref, reactive } from 'vue';

// 会议项接口定义
export interface MeetingItem {
  id?: string;
  affairId: string;
  title?: string;
  startTime?: string;
  endTime?: string;
  location?: string;
  participants?: string[];
  startDate?: string;
  endDate?: string;
  createUser?: string;
  meetingName?: string;
  meetingId?: string;
  meetingTime?: string;
  createUsr?: string;
  mettingPlace?: string;
  jumpUrl?: string;
  summaryText?: string;
  loaddingSummary?: boolean;
}

// 全局会议状态
const meetingState = reactive({
  meetings: [] as MeetingItem[],
  isLoading: false,
  lastUpdateTime: 0,
  refreshTrigger: 0 // 用于触发刷新
});

export const useMeetingRefresh = () => {
  // 触发会议数据刷新
  const triggerMeetingRefresh = () => {
    meetingState.refreshTrigger = Date.now();
  };

  // 更新会议数据
  const updateMeetingData = (meetings: MeetingItem[]) => {
    meetingState.meetings = meetings;
    meetingState.lastUpdateTime = Date.now();
  };

  // 设置加载状态
  const setLoading = (loading: boolean) => {
    meetingState.isLoading = loading;
  };

  return {
    // 状态
    meetingState,
    
    // 方法
    triggerMeetingRefresh,
    updateMeetingData,
    setLoading
  };
}; 