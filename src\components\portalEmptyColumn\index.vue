<template>
  <div class="portal-empty-column w-full h-full flex flex-col justify-center items-center">
    <AImage
      :src="imageSrc"
      :preview="false"
      :width="imageWidth"
      class="empty-image"
    />
    <div v-if="text" class="empty-text mt-3 text-center text-gray-500">
      {{ text }}
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
  name: 'PortalEmptyColumn',
});
</script>

<script setup lang="ts">
import { computed } from 'vue';
import DefaultEmptyColumn from '@/assets/imgs/empty_column.png';
import { Image as AImage } from 'ant-design-vue';

export interface Props {
  // 自定义图片路径
  image?: string;
  // 空状态文本
  text?: string;
  // 图片宽度
  width?: number;
  // 图片高度
  height?: any;
}

const props = withDefaults(defineProps<Props>(), {
  image: '',
  text: '',
  width: 100,
  height: 100,
});

// 计算图片源
const imageSrc = computed(() => {
  return props.image || DefaultEmptyColumn;
});

// 计算图片尺寸
const imageWidth = computed(() => props.width);
const imageHeight = computed(() => props.height);
</script>

<style scoped lang="less">
.portal-empty-column {
  .empty-image {
    opacity: 0.6;
  }

  .empty-text {
    font-size: 12px;
    line-height: 1.4;
    max-width: 200px;
    color: #8E94A2;
    font-weight: @font-weight-500;
  }
}
</style>

