// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { get, post } from '../config.js';
import type { TypeHistoryAssistParams, TypeHistoryDetailParams } from '@/types/api.ts';

// 获取左侧历史记录
export const getHistoryAsisList = (data: TypeHistoryAssistParams) => {
  return post(`ai-manager/assistant/info/session/record/page`, data);
};

// 获取历史详情
export const getHistoryDetail = (data: TypeHistoryDetailParams) => {
  return post(`ai-manager/assistant/info/session/runStep/page`, data);
};

// 编辑历史名称
export const editSessionHisName = (sessionId: string, data: string) => {
  return post(`ai-manager/assistant/info/session/record/${sessionId}/name`, data, {
    'Content-Type': 'application/json',
  });
};

// 删除历史会话
export const delSessionHis = (sessionId: string) => {
  return post(`ai-manager/assistant/info/session/record/${sessionId}/delete`, {});
};
