<!--
 * @Author: 代琪 <EMAIL>
 * @Date: 2025-07-02 15:08:19
 * @LastEditors: 代琪 <EMAIL>
 * @LastEditTime: 2025-07-25 16:47:54
 * @FilePath: \ai-assistant-web\src\components\todoCardColumn\actionPopoverManager.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- 自定义确认弹窗 -->
  <Teleport to="body">
    <div
      v-if="isVisible"
      ref="popoverRef"
      class="custom-popover"
      :style="popoverStyle"
    >
        <!-- 弹窗内容 -->
        <div class="popover-body">
          <!-- 常用语列表页面 -->
          <PhrasesPanel
            v-show="showPhrasesPanel"
            :height="commentPanelHeight"
            @back="togglePhrasesPanel"
            @select="selectPhrase"
            ref="phrasesPanelRef"
          />

          <!-- 意见输入页面 -->
          <div v-show="!showPhrasesPanel" class="comment-panel">
            <!-- 态度选择 -->
            <div v-if="currentButton?.attitudeList?.length" class="attitude-section">
              <div class="attitude-options">
                <div
                  v-for="attitude in currentButton.attitudeList"
                  :key="attitude.attitudeKey"
                  :class="[
                    'attitude-option',
                    { 'selected': selectedAttitude?.attitudeKey === attitude.attitudeKey }
                  ]"
                  :title="attitude.showValue"
                  @click="selectedAttitude = attitude"
                >
                  <span :class="getAttitudeIconClass(attitude.attitudeId)" class="attitude-icon"></span>
                  <span class="attitude-text truncate">{{ attitude.showValue }}</span>
                </div>
              </div>
            </div>

            <!-- 意见输入区域 -->
            <div class="opinion-section">
              <div class="opinion-input-wrapper">
                <TextArea
                  ref="opinionInputRef"
                  v-model:value="inputOpinion"
                  :placeholder="getOpinionPlaceholder()"
                  :rows="4"
                  :maxlength="getMaxOpinionLength()"
                  @input="saveDraft"
                  class="opinion-textarea"
                />
                <!-- 常用语按钮 - 悬浮在右下角 -->
                <div
                  @click.stop="togglePhrasesPanel"
                  class="phrases-floating-btn"
                >
                  常用语
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 弹窗底部按钮 - 只在非常用语面板时显示 -->
        <div v-if="!showPhrasesPanel" class="popover-footer">
          <Button @click="closePopover" class="cancel-btn">取消</Button>
          <Button
            type="primary"
            @click="handleConfirmOk"
            :loading="isSubmitting"
            class="confirm-btn"
          >
            确定
          </Button>
        </div>
      </div>
  </Teleport>

</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import {
  Button,
  Modal,
  Radio,
  RadioGroup,
  Input,
  message
} from 'ant-design-vue';
import type {
  ButtonType,
  AttitudeOption,
  ConfirmActionData,
  PhraseItem,
  NextStepAction
} from './types';
import PhrasesPanel from './PhrasesPanel.vue';

const { TextArea } = Input;

// 响应式状态
const isVisible = ref(false);
const isSubmitting = ref(false);
const currentButton = ref<ButtonType | null>(null);
const selectedAttitude = ref<AttitudeOption | null>(null);
const inputOpinion = ref('');
const selectedNextStep = ref<NextStepAction>('continue');
const showPhrasesPanel = ref(false);
const phrasesPanelRef = ref();
const opinionInputRef = ref();
const popoverRef = ref<HTMLElement>();
const triggerElementRef = ref<HTMLElement | null>(null);
const onConfirmCallback = ref<((button: ButtonType, confirmData: ConfirmActionData) => void) | null>(null);
const onVisibilityChangeCallback = ref<((visible: boolean) => void) | null>(null);
const isJustOpened = ref(false); // 标记是否刚刚打开弹窗
const currentOptions = ref<{
  draftContent?: string;
  hasPlaceholder?: boolean;
  placeholder?: string;
  maxLength?: number;
  dataType?: string;
} | null>(null); // 当前传入的配置选项

// 弹窗位置样式
const popoverStyle = ref<Record<string, string>>({});

// 确认弹窗标题
const confirmModalTitle = computed(() => {
  if (!currentButton.value) return '';
  return `确认${currentButton.value.name}操作`;
});

// 是否需要显示意见输入框
const shouldShowOpinionInput = computed(() => {
  if (!currentButton.value) return false;

  const { handleType, opinionPolicy } = currentButton.value;
  const currentAttitude = selectedAttitude.value;

  // 如果没有 opinionPolicy 字段，说明不需要显示意见输入
  if (!opinionPolicy || typeof opinionPolicy !== 'object') {
    return false;
  }

  // 处理意见必填
  const hasOpinionHandle = opinionPolicy.opinionPolicy === "1";

  // 撤销/终止意见必填
  const hasCancelOrTerminateOpinion = opinionPolicy.cancelOpinionPolicy === "1" &&
    ['Cancel', 'Terminate', 'Return'].includes(handleType);

  // 不同意意见必填
  const hasDisagreeOpinion = opinionPolicy.disAgreeOpinionPolicy === "1" &&
    currentAttitude?.attitudeKey === "disagree";

  // 特殊组合情况
  const hasDisagreeAndCancelOrTerminate = currentAttitude?.attitudeKey === "disagree" &&
    currentButton.value.paramMap?.customAction?.isOptional === "0" &&
    ['Terminate', 'Cancel'].includes(currentButton.value.paramMap?.customAction?.defaultAction || '') &&
    opinionPolicy.cancelOpinionPolicy === "1";

  return hasOpinionHandle || hasCancelOrTerminateOpinion || hasDisagreeOpinion || hasDisagreeAndCancelOrTerminate;
});


// 动态计算意见面板高度
const commentPanelHeight = computed(() => {
  let height = 120; // 意见输入区域基础高度

  // 如果有态度选择，加上态度区域高度和间距
  if (currentButton.value?.attitudeList?.length) {
    height += 32 + 12; // 态度区域32px + 间距12px
  }

  // 加上底部按钮区域高度，使常用语面板与整个意见面板+底部按钮的高度一致
  height += 50; // 底部按钮区域高度（6px上padding + 32px按钮 + 12px下padding）

  return height;
});

// 获取态度图标类名
const getAttitudeIconClass = (attitudeId: string): string => {
  const iconMap: Record<string, string> = {
    '1': 'iconfont ai-icon-read',      // 已阅
    '2': 'iconfont ai-icon-agree', // 同意
    '3': 'iconfont ai-icon-disagree'   // 不同意
  };
  return iconMap[attitudeId] || '';
};

// 获取意见输入框占位符
const getOpinionPlaceholder = () => {
  if (!currentButton.value) return '请输入意见...';

  // 优先使用传入的占位符
  if (currentOptions.value?.placeholder) {
    return currentOptions.value.placeholder;
  }

  // 特殊判断：会议撤销操作
  if (currentOptions.value?.dataType === '6' && currentButton.value.handleType.toLowerCase() === 'cancel') {
    return '请输入撤销附言（撤销操作不可恢复，请确认后再输入，不超过100字）';
  }

  return `请输入${currentButton.value.name}意见...`;
};

// 获取意见最大长度
const getMaxOpinionLength = (): number => {
  // 优先使用传入的最大长度
  if (currentOptions.value?.maxLength) {
    return currentOptions.value.maxLength;
  }

  // 默认返回500
  return 500;
};

// 计算弹窗位置
const calculatePopoverPosition = (triggerElement: HTMLElement) => {
  const rect = triggerElement.getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  // 弹窗预估尺寸
  const popoverWidth = 386;
  const popoverHeight = 210;

  let left = rect.left;
  let top = rect.bottom + 8;

  // 水平位置调整
  if (left + popoverWidth > viewportWidth - 20) {
    left = viewportWidth - popoverWidth - 20;
  }
  if (left < 20) {
    left = 20;
  }

  // 垂直位置调整
  if (top + popoverHeight > viewportHeight - 20) {
    top = rect.top - popoverHeight - 8;
  }
  if (top < 20) {
    top = 20;
  }

  const finalPosition = {
    left: `${left}px`,
    top: `${top}px`,
    position: 'fixed',
    zIndex: '9999'
  };

  return finalPosition;
};

// 处理文档点击事件（点击外部关闭）
const handleDocumentClick = (event: MouseEvent) => {
  if (!isVisible.value || !popoverRef.value) return;

  // 如果刚刚打开弹窗，忽略这次点击
  if (isJustOpened.value) {
    isJustOpened.value = false;
    return;
  }

  const target = event.target as HTMLElement;

  // 如果点击在弹窗内部，不关闭
  if (popoverRef.value.contains(target)) return;

  // 如果点击在触发按钮上，不关闭
  if (triggerElementRef.value && triggerElementRef.value.contains(target)) return;

  // 如果点击在任何操作按钮或下拉菜单上，不关闭
  const actionButtonsContainer = target.closest('.action-buttons-container');
  if (actionButtonsContainer) return;

  // 如果点击在Ant Design的下拉菜单上，不关闭
  const dropdownMenu = target.closest('.ant-dropdown-menu');
  if (dropdownMenu) return;

  // 如果点击在任何按钮上，不关闭
  const anyButton = target.closest('button');
  if (anyButton && anyButton.closest('.action-buttons')) return;

  // 其他情况关闭弹窗
  closePopover();
};

// 打开弹窗
const openPopover = async (
  button: ButtonType,
  triggerElement: HTMLElement,
  onConfirm: (button: ButtonType, confirmData: ConfirmActionData) => void,
  onVisibilityChange?: (visible: boolean) => void,
  options?: {
    draftContent?: string;
    hasPlaceholder?: boolean;
    placeholder?: string;
    maxLength?: number;
    dataType?: string;
  }
) => {
  // 关闭之前可能存在的弹窗
  closePopover();

  // 设置当前操作按钮和回调
  currentButton.value = button;
  triggerElementRef.value = triggerElement;
  onConfirmCallback.value = onConfirm;
  onVisibilityChangeCallback.value = onVisibilityChange || null;

  // 设置传入的配置选项
  currentOptions.value = options || null;

  // 重置状态
  // 根据 paramMap.attitudeKey 设置默认选中的态度
  if (button.attitudeList?.length) {
    const defaultAttitude = button.attitudeList.find(
      attitude => attitude.attitudeKey === button.paramMap?.attitudeKey
    );
    selectedAttitude.value = defaultAttitude || button.attitudeList[0] || null;
  } else {
    selectedAttitude.value = null;
  }

  inputOpinion.value = options?.draftContent || '';
  isSubmitting.value = false;
  showPhrasesPanel.value = false;

  // 计算位置
  popoverStyle.value = calculatePopoverPosition(triggerElement);

  // 显示弹窗
  isVisible.value = true;
  isJustOpened.value = true; // 标记刚刚打开弹窗

  // 通知父组件弹窗已打开，强制显示按钮
  if (onVisibilityChangeCallback.value) {
    onVisibilityChangeCallback.value(true);
  }

  // 添加一个小延迟后重置标记，防止同一个点击事件触发关闭
  setTimeout(() => {
    isJustOpened.value = false;
  }, 100);

  // 下一个tick后确保弹窗样式正确
  await nextTick();
  if (popoverRef.value) {
    // 强制触发动画，确保弹窗可见
    popoverRef.value.style.opacity = '1';
    popoverRef.value.style.transform = 'translateY(0) scale(1)';
  }

  // 聚焦到意见输入框
  await nextTick();
  if (shouldShowOpinionInput.value && opinionInputRef.value) {
    opinionInputRef.value.focus();
  }

  // 加载草稿和常用语（常用语只在首次需要时加载）
  await loadDraftOpinion();
  // 常用语在第一次切换到常用语面板时才加载
};

// 关闭弹窗
const closePopover = () => {
  // 如果有hasSelectedNextAction标记且弹窗被取消（非确认），清理标记
  if (currentButton.value && (currentButton.value as any).hasSelectedNextAction) {
    console.log('弹窗关闭，清理hasSelectedNextAction标记');
    (currentButton.value as any).hasSelectedNextAction = false;
  }

  isVisible.value = false;
  isJustOpened.value = false; // 重置标记
  currentButton.value = null;
  selectedAttitude.value = null;
  inputOpinion.value = '';
  isSubmitting.value = false;
  showPhrasesPanel.value = false;
  triggerElementRef.value = null;
  currentOptions.value = null;

  // 通知父组件弹窗已关闭
  if (onVisibilityChangeCallback.value) {
    onVisibilityChangeCallback.value(false);
  }

  // 清理回调
  onConfirmCallback.value = null;
  onVisibilityChangeCallback.value = null;
};

// 确认操作
const handleConfirmOk = async () => {
  if (!currentButton.value) {
    return;
  }

  // 只有当显示态度选择器时才校验态度
  if (currentButton.value.attitudeList?.length && !selectedAttitude.value) {
    message.error('请选择态度');
    return;
  }

  // 验证必填意见
  if (!inputOpinion.value.trim()) {
    message.error('意见不能为空');
    return;
  }

  isSubmitting.value = true;

  try {
    const confirmData: ConfirmActionData = {
      attitude: selectedAttitude.value || null,
      content: inputOpinion.value.trim(),
      actionType: 'confirm'
    };

    // 执行确认回调
    if (onConfirmCallback.value) {
      onConfirmCallback.value(currentButton.value, confirmData);
    }

    // 关闭弹窗
    closePopover();
  } catch (error) {
    console.error('确认操作失败:', error);
    message.error('操作失败，请重试');
  } finally {
    isSubmitting.value = false;
  }
};


// 切换常用语面板
const togglePhrasesPanel = () => {
  showPhrasesPanel.value = !showPhrasesPanel.value;
};

// 选择常用语
const selectPhrase = (content: string) => {
  inputOpinion.value = content;
  showPhrasesPanel.value = false;
  if (opinionInputRef.value) {
    opinionInputRef.value.focus();
  }
};

// 保存草稿
const saveDraft = async () => {
  if (!currentButton.value || !inputOpinion.value.trim()) return;

  // TODO: 实现草稿保存逻辑
};

// 加载草稿意见
const loadDraftOpinion = async () => {
  if (!currentButton.value) return;

  // TODO: 实现草稿加载逻辑
};



// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isVisible.value) {
    closePopover();
  }
};

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
  document.addEventListener('click', handleDocumentClick);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
  document.removeEventListener('click', handleDocumentClick);
});

// 暴露方法
defineExpose({
  openPopover,
  closePopover,
  isVisible
});
</script>

<style scoped lang="less">
// 自定义弹窗样式
.custom-popover {
  position: fixed;
  z-index: 9999;
  background: white;
  border-radius: 12px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e8e8e8;
  min-width: 320px;
  max-width: 400px;
  overflow: hidden;
  opacity: 1 !important;
  transform: translateY(0) scale(1) !important;
  animation: popoverFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .popover-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #f8fafb 0%, #ffffff 100%);

    .popover-title {
      font-weight: @font-weight-500;
      color: #1a1a1a;
      font-size: 15px;
      margin: 0;
    }

    .close-btn {
      padding: 6px;
      border: none;
      background: transparent;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #666;
      font-size: 14px;

      &:hover {
        background-color: #f5f5f5;
        color: #333;
      }
    }
  }

  .popover-body {
    width: 386px;
    padding: 20px 20px 6px 20px;
    border-radius: 12px;
    background: #ffffff;
    min-height: 120px;

    .section-label {
      font-weight: @font-weight-500;
      color: #333;
      font-size: 14px;
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .attitude-section {
      margin-bottom: 12px;
      height: 32px; // 固定态度选择区域高度

      .attitude-options {
        display: flex;
        gap: 12px;

        .attitude-option {
          flex: 1;
          min-width: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          line-height: 32px;
          color: #5b6580;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          background: #eeeff2;
          padding: 0 4px;

          &:hover {
            background: #e3e6ee;
          }

          &.selected {
            background: #e5f1ff;
            color: #07f;

            .attitude-text {
              color: #07f;
            }

            .attitude-icon {
              color: #07f;
            }
          }

          .attitude-icon {
            margin-right: 8px;
            font-size: 16px;
            transition: all 0.2s ease;
          }

          .attitude-text {
            font-size: 14px;
            font-weight: @font-weight-400;
            color: #333;
            transition: all 0.2s ease;
          }
        }
      }
    }

    .opinion-section {
      height: 120px; // 设置固定高度，与常用语面板总高度匹配

      .opinion-input-wrapper {
        position: relative;
        height: 100%;

        .opinion-textarea {
          width: 100%;
          height: 100%;
          min-height: unset; // 移除最小高度，使用固定高度
          resize: none;
          font-size: 14px;
          transition: all 0.2s ease;
          background: #ffffff;
          line-height: 1.5;

          &:focus {
            border-color: #4379FF;
            background: #ffffff;
            box-shadow: 0 0 0 2px rgba(67, 121, 255, 0.2);
          }

          &::placeholder {
            color: #999;
          }
        }

        .phrases-floating-btn {
          position: absolute;
          bottom: 0;
          right: 4px;
          padding: 6px 10px;
          font-size: 12px;
          border-radius: 4px;
          color: #666;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            color: #4379FF;
          }
        }
      }
    }


  }

  .popover-footer {
    display: flex;
    gap: 12px;
    padding: 6px 20px 12px;
  }
}

@keyframes popoverFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 态度图标样式
.syIcon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  font-weight: @font-weight-500;
  transition: all 0.2s ease;

}

// 下一步操作弹窗样式
.next-step-content {
  .next-step-radio {
    display: block;
    margin: 8px 0;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
      background-color: #f0f9ff;
    }
  }
}


</style>

<style lang="less">
// 全局样式 - Popover动画效果
.ant-popover.action-button-popover {
  .ant-popover-content {
    animation: popoverFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@keyframes popoverFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}


</style>

