@tailwind base;
@tailwind components;
@tailwind utilities;

/* // 滚动条整体宽度 */

::-webkit-scrollbar {
    width: 2px;
}


/* // 滚动条滑槽样式 */

:not(.copilot-ui-table-container .ant-table-body)::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.3);
    border-radius: 2px;
}


/* // 滚动条样式 */

:not(.copilot-ui-table-container .ant-table-body)::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background: #ddd;
    -webkit-box-shadow: inset 0 0 2x rgba(0, 0, 0, 0.5);
}

:not(.copilot-ui-table-container .ant-table-body)::-webkit-scrollbar-thumb:hover {
    background: #ccc;
}

:not(.copilot-ui-table-container .ant-table-body)::-webkit-scrollbar-thumb:active {
    background: rgba(0, 0, 0, 0.4);
}


/* // 浏览器失焦的样式 */

:not(.copilot-ui-table-container .ant-table-body)::-webkit-scrollbar-thumb:window-inactive {
    background: rgba(0, 0, 0, 0.4);
}

.wrap {
    min-width: 1366PX;
    /* background-color: #e5e7eb; */
}


/* .home_wrap .ant-tree-treenode{
    width: 50%;
} */

.home_wrap .ant-tree-node-selected {
    background-color: #009688 !important;
    color: white;
    /* width: 100%; */
}

.home_wrap .ant-tree-list-holder-inner {
    padding-top: 8px;
}

.home_wrap .ant-tree-treenode {
    margin-bottom: 8px;
}

.home_wrap .ant-tabs-tab {
    font-size: 1.4rem;
    font-weight: bold;
}


/* .search_ipt .ant-input-lg{
    padding: 14px 11px !important;
} */

.search_ipt .ant-btn {
    display: flex;
    justify-content: center;
    align-items: center
}

