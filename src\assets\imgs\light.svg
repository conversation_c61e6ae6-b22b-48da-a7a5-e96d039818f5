<svg width="960" height="720" viewBox="0 0 960 720" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_233_636)">
<ellipse cx="480" cy="360" rx="280" ry="160" fill="#7559F8" fill-opacity="0.2"/>
</g>
<defs>
<filter id="filter0_f_233_636" x="0" y="0" width="960" height="720" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_233_636"/>
</filter>
</defs>
</svg>
