<template>
  <ComiExternalBiCard
    :data="cardData"
    ref="biCardRef"
    :card-options="biCardOptions"
     @update:card-options="cardOptionsChange"
  ></ComiExternalBiCard>
  <Modal
      v-if="isFullScreenPreview"
      :title="title"
      v-model:visible="isFullScreenPreview"
      :width="1200"
      :isFullScreen="true"
      :isPreview="true"
      centered
      :footer="null"
    >
      <BiPreview :transParams="JSON.parse(JSON.stringify(props.cardData))" :height="650" />
    </Modal>
</template>
<script setup lang="ts">
import { ref, inject } from 'vue';
import type { Ref } from 'vue';
import { Modal } from 'ant-design-vue';
import { commonBusinessClass } from '@/utils/commonBusinessClass';
import BiPreview from '@/views/biPreview/index.vue';


const isFullScreenPreview = ref(false);

const props = defineProps({
  cardData: {
    type: Object,
    default: () => {},
  },
});

const biCardRef = ref<any>(null);

// 注入祖先提供的 ref
const aiAnswerRef = inject<Ref<{ getMarkdownContent: () => void }>>('aiAnswerRef', ref({ getMarkdownContent: () => {} }))

// 定义要暴露的方法
const getMarkdownContent = () => {
  return biCardRef.value.getMarkDown() || '';
}

// 将自身实例赋给共享 ref
aiAnswerRef.value = {
  getMarkdownContent
}

const typeSourceEnum = {
  PcSide: 'pcSide',
  PcSideExpand: 'pcSideExpand',
  PcDialog: 'pcDialog',
}

const biCardOptions = ref({
  status: false,
  lookSql: false,
  canLookSql: true,
  typeSource: typeSourceEnum.PcSide
})

const title = ref('');
const cardOptionsChange = (key: any, value: any) => {
  if(key === 'status' && value) {
    openDialogForBi();
  }
  if(key === 'title'){
    title.value = value;
  }
}

const openDialogForBi = () => {
  const businessClass = commonBusinessClass();
  if (businessClass && typeof businessClass.openDialog === 'function') {
    const previewDialog = businessClass.openDialog({
      url: `/seeyon/ai-platform/ai-static/ai-copilot/biPreview/index.html`,
      // url: `/seeyon/ai-platform/copilot#/bi-preview`,
      id: 'BiPreview',
      footer: null,
      title: title.value,
      width: 1200,
      height: 540,
      transParams: JSON.parse(JSON.stringify(props.cardData)),
      onCancel() {
        previewDialog.close();
      },
    });
  } else {
    isFullScreenPreview.value = true;
  }
};

</script>
<style scoped lang="scss">

</style>
