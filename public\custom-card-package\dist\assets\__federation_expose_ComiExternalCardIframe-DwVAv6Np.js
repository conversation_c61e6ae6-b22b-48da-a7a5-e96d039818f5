import { importShared, __tla as __tla_0 } from "./__federation_fn_import-B47IVf2F.js";
import { _export_sfc } from "./_plugin-vue_export-helper-8ijppmbV.js";
let CardIframe;
let __tla = Promise.all([
  (() => {
    try {
      return __tla_0;
    } catch {
    }
  })()
]).then(async () => {
  const { defineComponent: _defineComponent } = await importShared("vue");
  const { createElementVNode: _createElementVNode, normalizeStyle: _normalizeStyle, openBlock: _openBlock, createElementBlock: _createElementBlock } = await importShared("vue");
  const _hoisted_1 = [
    "src"
  ];
  const _sfc_main = _defineComponent({
    __name: "index",
    props: {
      config: {
        type: Object,
        required: true,
        default: () => ({
          url: "",
          height: 100
        })
      }
    },
    setup(__props) {
      const props = __props;
      return (_ctx, _cache) => {
        return _openBlock(), _createElementBlock("div", {
          class: "header-iframe",
          style: _normalizeStyle({
            height: props.config.height + "px"
          })
        }, [
          _createElementVNode("iframe", {
            src: props.config.url,
            scrolling: "no",
            height: "100%",
            onError: _cache[0] || (_cache[0] = ($event) => props.config.url = "")
          }, null, 40, _hoisted_1)
        ], 4);
      };
    }
  });
  CardIframe = _export_sfc(_sfc_main, [
    [
      "__scopeId",
      "data-v-45f518c2"
    ]
  ]);
});
export {
  __tla,
  CardIframe as default
};
