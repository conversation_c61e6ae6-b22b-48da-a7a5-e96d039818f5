import { importShared, __tla as __tla_0 } from "./__federation_fn_import-B47IVf2F.js";
import { _sfc_main as _sfc_main$2, _sfc_main$1 as _sfc_main$3, TableEnum, ChartTypeEnum, _sfc_main$2 as _sfc_main$4, _sfc_main$3 as _sfc_main$5, jsonToMarkdownTable, changeChartData, extractAiCards, __tla as __tla_1 } from "./index-DtGXkB0h.js";
import { _export_sfc } from "./_plugin-vue_export-helper-8ijppmbV.js";
import { cardNav, __tla as __tla_2 } from "./card-nav-DF7f_cgh.js";
let _sfc_main;
let __tla = Promise.all([
  (() => {
    try {
      return __tla_0;
    } catch {
    }
  })(),
  (() => {
    try {
      return __tla_1;
    } catch {
    }
  })(),
  (() => {
    try {
      return __tla_2;
    } catch {
    }
  })()
]).then(async () => {
  const cardHeader = _export_sfc(_sfc_main$2, [
    [
      "__scopeId",
      "data-v-9b08fce2"
    ]
  ]);
  const cardSummary = _export_sfc(_sfc_main$3, [
    [
      "__scopeId",
      "data-v-05db1eb0"
    ]
  ]);
  const { defineComponent: _defineComponent$1 } = await importShared("vue");
  const { unref: _unref, createVNode: _createVNode, openBlock: _openBlock$1, createBlock: _createBlock$1, createCommentVNode: _createCommentVNode, createElementVNode: _createElementVNode, normalizeStyle: _normalizeStyle, createElementBlock: _createElementBlock } = await importShared("vue");
  const _hoisted_1 = {
    class: "card-content-charts"
  };
  const { reactive, ref: ref$1, watch, nextTick } = await importShared("vue");
  const _sfc_main$1 = _defineComponent$1({
    __name: "card",
    props: {
      cardData: {},
      padding: {
        default: 0
      },
      height: {
        default: 276
      }
    },
    emits: [
      "changeCardOptions"
    ],
    setup(__props, { expose: __expose, emit: __emit }) {
      var _a, _b;
      const props = __props;
      const { padding, height } = props;
      const { column_names, data, type, legend_datas, changeColumnNames } = props.cardData.chartData;
      const { tableData, tableColumnNames } = props.cardData.tableData;
      const { title, total, backMaxRows, summaryTotal, summaryContent, isOverMaxRows } = props.cardData;
      const chartData = reactive({
        type,
        data,
        column_names,
        legend_datas,
        changeColumnNames
      });
      const tableDataInfo = reactive({
        tableData,
        tableColumnNames,
        backMaxRows,
        summaryTotal,
        isOverMaxRows
      });
      const tableRef = ref$1(null);
      const changeChartSelectMap = {
        bar_chart: ChartTypeEnum.Bar,
        pie_chart: ChartTypeEnum.Pie,
        line_chart: ChartTypeEnum.Line,
        table: TableEnum.Table
      };
      const typeSelect = changeChartSelectMap[(_b = (_a = props == null ? void 0 : props.cardData) == null ? void 0 : _a.chartData) == null ? void 0 : _b.type];
      const isShowEcharts = ref$1(summaryTotal > 1 && typeSelect !== TableEnum.Table && data.length > 0);
      const chartsValue = Object.values(ChartTypeEnum);
      const selectChartType = ref$1(summaryTotal > 1 && data.length > 0 ? typeSelect ?? TableEnum.Table : TableEnum.Table);
      const changeChartType = (changeChartType2) => {
        selectChartType.value = changeChartType2;
      };
      const copyMarkDown = () => {
        var _a2;
        const tableData2 = (_a2 = tableRef.value) == null ? void 0 : _a2.getTableData();
        const TableMarkDown = jsonToMarkdownTable(tableData2, tableColumnNames);
        if (selectChartType.value === TableEnum.Table) {
          return TableMarkDown + "\n\n" + summaryContent;
        } else {
          return summaryContent;
        }
      };
      const emit = __emit;
      const changeChartDataEvent = (data2) => {
        const cardChartData = [];
        data2.forEach((item) => {
          cardChartData.push(Object.values(item).slice(1));
        });
        const [echartDataShowList, showColumnNames, legend_datas2] = changeChartData(changeColumnNames, cardChartData);
        chartData.data = echartDataShowList;
        chartData.legend_datas = showColumnNames;
        chartData.column_names = legend_datas2;
      };
      const changeStatus = () => {
        emit("changeCardOptions", "status", true);
      };
      const lookSql = () => {
        emit("changeCardOptions", "lookSql", true);
      };
      watch(() => props.cardData.title, (value) => {
        nextTick(() => {
          emit("changeCardOptions", "title", value);
        });
      }, {
        immediate: true,
        deep: true
      });
      __expose({
        copyMarkDown
      });
      return (_ctx, _cache) => {
        return _openBlock$1(), _createElementBlock("div", {
          class: "cardContent",
          style: _normalizeStyle({
            padding: _unref(padding) + "px",
            paddingBottom: 0
          })
        }, [
          _createVNode(_unref(cardHeader), {
            title: _unref(title),
            onChangeStatus: changeStatus
          }, null, 8, [
            "title"
          ]),
          isShowEcharts.value ? (_openBlock$1(), _createBlock$1(_unref(cardNav), {
            key: 0,
            "select-chart-type": selectChartType.value,
            "is-show-echarts": isShowEcharts.value,
            onChangeChartType: changeChartType,
            onLookSql: lookSql
          }, null, 8, [
            "select-chart-type",
            "is-show-echarts"
          ])) : _createCommentVNode("", true),
          _createElementVNode("div", _hoisted_1, [
            selectChartType.value === _unref(TableEnum).Table ? (_openBlock$1(), _createBlock$1(_unref(_sfc_main$4), {
              key: 0,
              ref_key: "tableRef",
              ref: tableRef,
              "table-data-info": tableDataInfo,
              onChangeChartData: changeChartDataEvent
            }, null, 8, [
              "table-data-info"
            ])) : _createCommentVNode("", true),
            _unref(chartsValue).includes(selectChartType.value) && isShowEcharts.value ? (_openBlock$1(), _createBlock$1(_unref(_sfc_main$5), {
              key: chartData.type,
              "chart-data": chartData,
              "select-chart-type": selectChartType.value,
              "filed-name": {
                bar: "bar_chart",
                line: "line_chart",
                pie: "pie_chart"
              },
              height: _unref(height)
            }, null, 8, [
              "chart-data",
              "select-chart-type",
              "height"
            ])) : _createCommentVNode("", true)
          ]),
          _createVNode(_unref(cardSummary), {
            total: _unref(total),
            "back-max-rows": _unref(backMaxRows),
            "summary-total": _unref(summaryTotal),
            "summary-content": _unref(summaryContent),
            "is-over-max-rows": _unref(isOverMaxRows),
            onLookSql: lookSql
          }, null, 8, [
            "total",
            "back-max-rows",
            "summary-total",
            "summary-content",
            "is-over-max-rows"
          ])
        ], 4);
      };
    }
  });
  const card = _export_sfc(_sfc_main$1, [
    [
      "__scopeId",
      "data-v-a216c3aa"
    ]
  ]);
  const { defineComponent: _defineComponent } = await importShared("vue");
  const { openBlock: _openBlock, createBlock: _createBlock } = await importShared("vue");
  const { ref, provide } = await importShared("vue");
  _sfc_main = _defineComponent({
    __name: "index",
    props: {
      data: {},
      cardOptions: {}
    },
    emits: [
      "update:cardOptions"
    ],
    setup(__props, { expose: __expose, emit: __emit }) {
      const childRef = ref(null);
      const props = __props;
      provide("cardOptions", props.cardOptions);
      const cardDataResult = extractAiCards(props.data, props.cardOptions.typeSource);
      const cardData = ref(cardDataResult);
      const emit = __emit;
      const changeCardOptions = (key, value) => {
        emit("update:cardOptions", key, value);
      };
      const getMarkDown = () => {
        var _a;
        return (_a = childRef == null ? void 0 : childRef.value) == null ? void 0 : _a.copyMarkDown();
      };
      __expose({
        getMarkDown
      });
      return (_ctx, _cache) => {
        return _openBlock(), _createBlock(card, {
          ref_key: "childRef",
          ref: childRef,
          "card-data": cardData.value,
          onChangeCardOptions: changeCardOptions
        }, null, 8, [
          "card-data"
        ]);
      };
    }
  });
});
export {
  __tla,
  _sfc_main
};
