<template>
    <!-- 上传按钮 -->
    <div class="upload_btn">
        <Tooltip
            overlayClassName="upload_btn_popover"
            title="支持上传附件（当前仅支持上传1个，大小10MB）支持xlsx、csv、txt、pdf、docx、markdown等"
            arrow-point-at-center
            placement="topLeft"
            :overlayStyle="{display: notShowPopover ? 'none' : ''}"
            >
            <Upload
                :type="uploadType === 'btn'?'select':'drag'"
                :multiple="false"
                :maxCount="1"
                :showUploadList="false"
                :beforeUpload="beforeUpload"
                :accept="supportFileTypes"
                @reject="rejectFn"
            >
                <i class="iconfont ai-icon-fujian"></i>
            </Upload>
        </Tooltip>
    </div>
</template>

<script setup lang="ts">
  import { ref, watch, computed } from 'vue';
  import { Upload, Image, Progress, message, Popover, UploadDragger, Tooltip } from 'ant-design-vue';
  import { cutIt, getfilesize, getFileType, getFileIcon } from '@/utils/tool';
  // 1-2-3
  import axios, { type CancelToken } from 'axios';
  import { toUploadMutyFile } from '@/api/common/index';
  import { useGlobal } from '@/stores/global';

  const uGlobal = useGlobal();
  // props
  const props = defineProps<{
    chatSessionId: string,
    uploadType: string,
    supportFileTypes?: string[],
    notShowPopover?: boolean
  }>();
  const supportFileTypes = computed(() => (props.supportFileTypes || ['.pdf', '.docx', '.xlsx', '.csv', '.txt', '.md', '.markdown','.jpg','.jpeg','.png','.gif']).join(','));

  const _$emit = defineEmits(['getRusultFiles']);
  // ---------
  const pctCmpted = ref(0); // 上传进度
  const fileList = ref<any[]>([]); // 临时文件列表
  const confileList = ref<any[]>([]); // 真实上传文件列表
  const fileUploadPiece = ref(0); // 文件上传分片




  // 控制器
  let tempSoucre: any;

  // --------------------------------------------------辅助助手结束- 文件上传开始-------------------------------------------
  // 文件上传和取消 文件上传 配置
  const configFnc: {
    onUploadProgress: (progressEvent: any) => void;
    cancelToken: CancelToken | null;
  } = {
    onUploadProgress: function (progressEvent: any) {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      pctCmpted.value = percentCompleted;
    },
    cancelToken: null,
  };
  // 拖拽不符合文件格式的文件
  function rejectFn(list: File[]) {
    const fileSuffix = '.' + getFileType(list[0].name);
    if (!supportFileTypes.value.includes(fileSuffix) || fileSuffix === '.xls') {//支持xlsx的时候，xls也会被校验通过
      message.error('上传失败，仅支持上传' + supportFileTypes.value + '格式文件');
      return false;
    }
  }
  // 获取文件名称
  function getFileNameWithoutExtension(fileName:string) {
    if (!fileName) return '';
    if(fileName.indexOf('.')<0) return fileName;
    return fileName.split('.').slice(0, -1).join('.');
  }
  // 上传之前
  const beforeUpload = (file: File, list: File[]) => {
    const fileSuffix = '.' + getFileType(file.name);
    if (!supportFileTypes.value.includes(fileSuffix) || fileSuffix === '.xls') {
      message.error('上传失败，仅支持上传' + supportFileTypes.value + '格式文件');
      return false;
    }
    const fileName = getFileNameWithoutExtension(file.name);
    if (new TextEncoder().encode(fileName).length > 200) {
      message.error('文件名称超长，请修改后重新上传');
      return false;
    }

    fileUploadPiece.value++;
    // 触发上传
    if (fileUploadPiece.value == list.length) {
      beginOwnUpload(list);
      fileUploadPiece.value = 0;
    }
    return false;
  };
  // 开始上传
  const beginOwnUpload = (list: File[]) => {

    // 限制大小
    let isExistUnVadFile = false,
      tempErrFile;
    for (let k = 0; k < list.length; k++) {
      if (list[k].size > 1024 * 1024 * 10) {
        isExistUnVadFile = true;
        tempErrFile = list[k];
        break;
      }
    }
    if (isExistUnVadFile) {
      message.warning(`【${tempErrFile?.name}】文件大小超过10M，请重新上传！`);
      isExistUnVadFile = false;
      tempErrFile = undefined;
      return false;
    }

    let lastFile = list[list.length - 1];
    uGlobal.changeState('isParsing', true);
    const fileItem = {
      isUploading: true,
      name: lastFile.name,
      size: lastFile.size,
      type: getFileIcon(getFileType(lastFile.name) as string).type,
      url: getFileIcon(getFileType(lastFile.name) as string).icon,
      originalFile: lastFile,
    };

    if (fileList.value.length > 0) {
      fileList.value.splice(0, 1, fileItem);
    } else {
      fileList.value.push(fileItem);
    }


    const source = axios.CancelToken.source();
    // 做参数
    const oData = new FormData();
    oData.append(
      'aiAgentFileInfoDto',
      JSON.stringify({
        chatSessionId: props.chatSessionId,
        sessionId: '',
        fileType: lastFile.type,
      }),
    );
    list.forEach((itm: any) => {
      oData.append('file', itm);
    });
    //更新进度
    configFnc.cancelToken = source.token;
    tempSoucre = source;
    // 文件上传请求
    toUploadMutyFile(oData, configFnc)
      .then((res: any) => {
        if (res && res.code == '0' && res.data && res.data.length > 0) {
          fileList.value.forEach((itm: any) => {
            itm.isUploading = false;
          });
          uGlobal.changeState('isParsing', false);
          if (confileList.value.length > 0) {
            confileList.value.splice(0, 1, ...res.data);
          } else {
            confileList.value.push(...res.data);
          }
          pctCmpted.value = 0;
        } else {
          message.error(res.message);
          fileList.value = [];
          pctCmpted.value = 0;
          fileList.value = [];
        }
      })
      .catch((error) => {
        console.log('error', error);
        // source =  axios.CancelToken.source() // 先注释 取消上传 问题较多
        pctCmpted.value = 0;
        fileList.value.forEach((itm: any) => {
          itm.isUploading = false;
        });
        uGlobal.changeState('isParsing', false);
        uGlobal.showParsingTips('文件解析失败，请重新上传');
        fileList.value = [];
      });
  };

  // 删除文件 取消上传
  const delFile = (itm: any, inx: number) => {
    fileList.value.splice(inx, 1);
    confileList.value.splice(inx, 1);
    uGlobal.changeState('isParsing', false);

    // 手动触发更新事件
    _$emit('getRusultFiles', {
      confileList: confileList.value,
      fileList: fileList.value,
    });

    // 取消文件上传
    if (pctCmpted.value > 0 && pctCmpted.value < 100) {
      //  tempSoucre.cancel('Operation canceled by the user.');
      return;
    }
  };
  // --------------------------------------------------文件上传结束-------------------------------------------

  // 监听搜索框 选中助手  上传列表 导致的高度变化  // 可用onUpdate代替
  watch(
    [() => confileList, () => fileList],
    () => {
      _$emit('getRusultFiles', {
        confileList: confileList.value,
        fileList: fileList.value,
      });
    },
    {
      deep: true,
      immediate: false,
    },
  );
  defineExpose({
    delFile
  });
  </script>
  <style scoped lang="less">
    .upload_btn {
      margin-top: 2px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-left: 8px;

      :deep(.ant-upload-wrapper) {
        line-height: 23px;
      }

      .iconfont {
        display: inline-block;
        font-size: 20px;
        color: #00000099;
        margin-top: 2px;
      }
      .font_up {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: @font-weight-400;
        line-height: 22px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        margin-left: 4px;
      }
    }
    .upload_btn:hover {
      // background: #f4f2ff;

      .iconfont,
      .font_up {
        color: @primary-color;
      }
    }
  </style>

  <style>
  .upload_btn_popover {
    padding-bottom: 3px !important;
  }
  .hide-popover {
    display: none;
  }
  </style>
