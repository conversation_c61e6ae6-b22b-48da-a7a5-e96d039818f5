<template>
  <div class="switch-chart">
    <div
      class="switch-item"
      v-for="item in renderSwitchList"
      :key="item.type"
      @click="checkedChart(item)"
      :class="{ 'checked-switch': item.checked }"
    >
      <i class="iconfont icon" :class="item.icon"></i>
      {{ item.name }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
type SwitchItem = {
  name: string;
  type: string;
  checked: boolean;
};
const props = defineProps({
  chartType: {
    type: String,
    default: 'bar_chart',
  },
});
const _$emit = defineEmits(['changeChartType']);
const switchList = [
  {
    name: '饼图',
    type: 'pie_chart',
    checked: false,
    icon: 'ai-icon-bingtu',
  },

  {
    name: '折线图',
    type: 'line_chart',
    checked: false,
    icon: 'ai-icon-zhexiantu',
  },
  {
    name: '柱状图',
    type: 'bar_chart',
    checked: true,
    icon: 'ai-icon-zhuxingtu',
  },
];
let lastItem: SwitchItem = {
  name: '',
  type: '',
  checked: false,
};
const renderSwitchList = computed(() => {
  return switchList.map((item) => {
    if (item.type === props.chartType) {
      lastItem = item;
      item.checked = true;
    } else {
      item.checked = false;
    }
    return item;
  });
});

function checkedChart(item: SwitchItem) {
  if (lastItem.type === item.type) {
    return;
  }
  _$emit('changeChartType', item.type);
}
</script>

<style lang="less" scoped>
.switch-chart {
  padding: 0 12px;
  display: flex;
  justify-content: flex-start;
  .switch-item {
    color: #000000e5;
    font-family: PingFang SC;
    font-weight: @font-weight-400;
    font-size: 14px;
    line-height: 22px;
    margin-right: 16px;
    cursor: pointer;

    .icon {
      font-size: 16px;
      margin-right: 4px;
    }
  }
  .checked-switch {
    color: #4379ff;

    .icon {
      color: #4379ff;
    }
  }
}
</style>
