/*
 * @Author: 代琪 <EMAIL>
 * @Date: 2025-07-14 14:18:54
 * @LastEditors: 代琪 <EMAIL>
 * @LastEditTime: 2025-07-17 10:13:37
 * @FilePath: \ai-assistant-web\src\api\menu\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from 'axios'
import { get, post } from '../config.js';

// 菜单API配置
export interface MenuApiConfig {
  portalId?: string
  spaceId?: string
  menuSource?: string
}

// 菜单API响应类型
export interface MenuApiResponse {
  code: number
  data?: {
    dataMenu: any[]
  }
  message?: string
}

// 历史记录API响应类型
export interface HistoryApiResponse {
  code: number
  data?: any[]
  message?: string
}

// 菜单API类
class MenuApi {
  private baseUrl: string

  constructor() {
    // 根据环境配置API基础路径
    if (import.meta.env.DEV) {
      this.baseUrl = '/seeyon'
    } else {
      this.baseUrl = '/seeyon'
    }
  }

  //获取我的常用报表统计信息
  async getMainPortalId() {
    return get(`${this.baseUrl}/rest/comi-portal/getPortalInfo?option.n_a_s=1`, {}, true);
  };


  // 获取菜单数据
  async getMenuData(config: MenuApiConfig): Promise<MenuApiResponse> {
    try {
      const url = `${this.baseUrl}/rest/portalDesigner/menuSearch`
      config.spaceId = ''
      const response = await axios.post(url, config, {
        params: {
          'option.n_a_s': 1
        },
        headers: {
          'Content-Type': 'application/json'
        }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch menu data:', error)
      throw error
    }
  }

  // 保存搜索历史
  async saveSearchHistory(portalId: string, menuId: string): Promise<any> {
    try {
      const url = `${this.baseUrl}/rest/menu/searchHistory/save/${portalId}/${menuId}`
      const response = await axios.post(url)
      return response.data
    } catch (error) {
      console.error('Failed to save search history:', error)
      throw error
    }
  }

  // 获取搜索历史
  async getSearchHistory(portalId: string, page: number = 1, size: number = 6): Promise<HistoryApiResponse> {
    try {
      const url = `${this.baseUrl}/rest/menu/searchHistory/history/${page}/${size}/${portalId}`
      const response = await axios.get(url)
      return response.data
    } catch (error) {
      console.error('Failed to get search history:', error)
      throw error
    }
  }

  // 添加收藏菜单
  async addFavoriteMenu(menuId: string, portalId: string = '', spaceId: string = ''): Promise<any> {
    const params = new URLSearchParams();
    params.append('managerMethod', 'addTopMenu');
    params.append('arguments', JSON.stringify([portalId, spaceId, [menuId]]));
    // params.append('portalId', portalId);
    // params.append('spaceId', spaceId);
    return fetch('/seeyon/ajax.do?method=ajaxAction&managerName=portalManager&nn=addTopMenu', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: params
    }).then(res => res.json());
  }

  // 移除收藏菜单
  async removeFavoriteMenu(menuId: string, portalId: string = '', spaceId: string = ''): Promise<any> {
    const params = new URLSearchParams();
    params.append('managerMethod', 'removeTopMenu');
    params.append('arguments', JSON.stringify([portalId, spaceId, [menuId]]));
    // params.append('portalId', portalId);
    // params.append('spaceId', spaceId);
    return fetch('/seeyon/ajax.do?method=ajaxAction&managerName=portalManager&nn=removeTopMenu', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: params
    }).then(res => res.json());
  }

  // 获取收藏菜单列表
  async getFavoriteMenus(portalId: string = '', spaceId: string = ''): Promise<any> {
    const params = new URLSearchParams();
    params.append('managerMethod', 'getTopMenu');
    params.append('arguments', JSON.stringify([portalId, spaceId]));
    // params.append('portalId', portalId);
    // params.append('spaceId', spaceId);
    return fetch('/seeyon/ajax.do?method=ajaxAction&managerName=portalManager&nn=getTopMenu', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: params
    }).then(res => res.json());
  }
}

// 创建单例实例
const menuApi = new MenuApi()

export default menuApi

// 导出具体方法供外部使用
export const getMenuData = (config: MenuApiConfig) => menuApi.getMenuData(config)
export const saveSearchHistory = (portalId: string, menuId: string) => menuApi.saveSearchHistory(portalId, menuId)
export const getSearchHistory = (portalId: string, page?: number, size?: number) => menuApi.getSearchHistory(portalId, page, size)

// 收藏相关API导出
export const addFavoriteMenu = (menuId: string, portalId?: string, spaceId?: string) => menuApi.addFavoriteMenu(menuId, portalId, spaceId)
export const removeFavoriteMenu = (menuId: string, portalId?: string, spaceId?: string) => menuApi.removeFavoriteMenu(menuId, portalId, spaceId)
export const getFavoriteMenus = (portalId?: string, spaceId?: string) => menuApi.getFavoriteMenus(portalId, spaceId)

export const getMainPortalId = () => menuApi.getMainPortalId()
