<template>
  <div class="flex agent" :class="{'agent-sidebar':isMinWidth}">
    <AiTabs
        v-if="agentList.length > 0"
        class="w-full create-tabs"
        :tabs="agentList"
        :activeTab="''"
        :canWhell="false"
        :notNeedSelect="true"
        :needDisabled="true"
        @tabClick="handleClick"
      ></AiTabs>
    <!-- <span
    v-for="item in agentList"
      :key="item.id"
      @click="handleAgentClick(item.id)"
      :class="[
        'agent-item  rounded-[12px] px-[26px] py-[8px] text-[13px] cursor-pointer',
        currentAgent === item.id ? 'active-agent' : ''
      ]"
    >{{ item.name }}</span> -->
  </div>
</template>

<script setup lang="ts">
import { type PropType, toRefs, ref } from 'vue'
import AiTabs from '@/components/aiTabs/index.vue';
import { useGlobal } from '@/stores/global';
import { useChatList } from '@/stores/chatList';
const { globalState } = useGlobal();
const uChatList = useChatList();
type agentListType = {
  id: string
  name: string
}
const props = defineProps({
  agentList: {
    type: Array as PropType<agentListType[]>,
    default: () => []
  }
})
const emit = defineEmits(['handleAgentClick'])
const { agentList } = toRefs(props)
const currentAgent = ref('')

const handleClick = (id: string, item = null) => {
  if(!item) return;
  currentAgent.value = id;
  emit('handleAgentClick',id, item)
}
const isMinWidth = computed(()=>{
  return uChatList.dynamicData.dobuleScreenData.show || !globalState.isFullScreen;
});

</script>

<style scoped lang="less">
.agent {
  flex: 1;
  min-width: 100px;
  // padding-top: 10px;
  .agent-item {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(59px);
    -webkit-backdrop-filter:  blur(59px);
    border: 1px solid rgba(255, 255, 255, .4);
    &:hover {
      background-color: rgba(255, 255, 255, 1);
    }
  }
  .active {
    background: #fff;
  }
}

</style>
<style lang="less">
.agent {
  .ai-tabs-wrapper {
    margin: 0;
    .ai-tab-content {
      gap: 10px;
    }
    .ai-tab-content .tab-item {
      width: 100px;
      text-align: center;
      font-weight: @font-weight-400;
      padding: 4px 8px;
      background-color: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(59px);
      -webkit-backdrop-filter:  blur(59px);
      border: 1px solid rgba(255, 255, 255, .4);
      color: #000;
      border-radius: 12px;
      font-size: 13px;
      display: flex;
      align-items: center;
      justify-content: center;
      .text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &:hover {
        background-color: rgba(255, 255, 255, 1);
        color: #000;
        .text {
          color: #4379FF;
        }
      }
    }
  }
}
.agent-sidebar {
  .ai-tabs-wrapper .i-wrapper {
    width: 22px;
    height: 22px;
    line-height: 22px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(59px);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    line-height: 21px;
    &.icon-disabled{
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.33) 19.98%, rgba(255, 255, 255, 0.44) 79.94%);
      .iconfont {
        color: #D8DADF;
      }
    }
    .iconfont {
      font-size: 14px;
    }
  }
  .ai-tabs-wrapper .ai-tab-content .tab-item {
    width: 72px;
    padding: 4px 8px;
    i {
      display: none;
    }
  }
}
</style>
