<template>
  <div class="flex flex-col w-full h-full">
    <PortalTopToolbar></PortalTopToolbar>
    <div class="flex-1 flex">
      <PortalView v-if="view" class="flex-1" :view="view"></PortalView>
      <PortalConfigPanel class="w-[200px]"></PortalConfigPanel>
    </div>
  </div>
</template>

<script setup lang="ts">

import PortalTopToolbar from '@/components/ai-design/PortalTopToolbar.vue';
import PortalView from '@/components/ai-design/PortalView.vue';
import PortalConfigPanel from '@/components/ai-design/PortalConfigPanel.vue';
import { usePortalStore } from '@/stores/portal';
import { getConfig } from '@/api/portal/edit';
import {preprocessView} from '@/components/ai-design/utils/conditionUtil'

const portalStore = usePortalStore();

getConfig().then((res:any) => {
  portalStore.$patch({
    config: res,
  });
});
const config = portalStore.config;

const view:any = computed(() => {
  const views = preprocessView(config);
  if(views.length> 0){
    return views[0];
  }
  return null;
});

</script>

<style scoped lang="less"></style>
