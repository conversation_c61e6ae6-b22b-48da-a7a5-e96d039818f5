<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-59.3023" y="-59.3023" width="140.605" height="140.605"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(29.65px);clip-path:url(#bgblur_0_368_5511_clip_path);height:100%;width:100%"></div></foreignObject><g data-figma-bg-blur-radius="59.3023">
<circle cx="11" cy="11" r="11" transform="matrix(-1 0 0 1 22 0)" fill="url(#paint0_linear_368_5511)" fill-opacity="0.8"/>
<circle cx="11" cy="11" r="10.5" transform="matrix(-1 0 0 1 22 0)" stroke="white" stroke-opacity="0.4"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.7071 17.7071C14.0976 17.3166 14.0976 16.6834 13.7071 16.2929L8.91421 11.5L13.7071 6.70711C14.0976 6.31658 14.0976 5.68342 13.7071 5.29289C13.3166 4.90237 12.6834 4.90237 12.2929 5.29289L6.79289 10.7929C6.40237 11.1834 6.40237 11.8166 6.79289 12.2071L12.2929 17.7071C12.6834 18.0976 13.3166 18.0976 13.7071 17.7071Z" fill="#8D97A6" fill-opacity="0.6"/>
<defs>
<clipPath id="bgblur_0_368_5511_clip_path" transform="translate(59.3023 59.3023)"><circle cx="11" cy="11" r="11" transform="matrix(-1 0 0 1 22 0)"/>
</clipPath><linearGradient id="paint0_linear_368_5511" x1="11" y1="0" x2="11" y2="22" gradientUnits="userSpaceOnUse">
<stop offset="0.199826" stop-color="white" stop-opacity="0.66"/>
<stop offset="0.799428" stop-color="white" stop-opacity="0.88"/>
</linearGradient>
</defs>
</svg>
