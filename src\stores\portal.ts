import { ref,computed } from 'vue'
import { defineStore } from 'pinia'
import { getConfig } from '@/api/portal/edit'
import {preprocessView, findNodeByType} from '@/components/ai-design/utils/conditionUtil'
// ts-ignore
import { useGlobal } from '@/stores/global';

// 定义globalState的类型
interface GlobalState {
  isFullScreen: boolean
  isParsing: boolean
  isNewTopic: boolean
  showDobuleScreen: boolean
  [key: string]: any // 添加索引签名
}



// 全局状态
export const usePortalStore = defineStore('portal', () => {

  //state配置存储
  const config = ref({views:[]});
  const initConfig = () => {
    getConfig().then((res:any) => {
      config.value = res;
    });
  }

  const global = useGlobal();
  // 当前视图
  const view = computed(() => {
    const cpConfig = JSON.parse(JSON.stringify(config.value));
    const views = preprocessView(cpConfig);
    if(views.length> 0 && global.globalState){
      console.log("portal-view", 1);
      return views[0];
    }
    console.log("portal-view", 2);
    return null;
  });
  // 当前视图是否有栏目组件
  const hasComponent = computed(() => {
    const res = findNodeByType(view.value, 'component');
    return res;
  });

  return { config, view, hasComponent, initConfig }

})
