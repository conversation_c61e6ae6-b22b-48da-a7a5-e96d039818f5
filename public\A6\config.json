{"id": "111111", "meta": {"editor": "0.0.1", "ct": "1234564641"}, "env": {}, "views": [{"id": "xxxx", "code": "", "condition": {}, "name": "页面", "options": {"class": "x-portal", "style": {"width": "100%", "height": "100%", "backgroundColor": "rgba(220,224,227,1)"}, "props": {}}, "children": [{"type": "Layout", "tag": "LayoutAside", "style": {"width": "200px"}, "name": "布局", "condition": false, "options": {}, "children": []}, {"type": "Layout", "class": "flex-1", "tag": "LayoutSection", "name": "布局", "options": {}, "children": [{"type": "Layout", "tag": "LayoutMain", "name": "布局", "class": "flex justify-center", "condition": {"eval": "this.global.globalState.currAgentCode == 'comi'"}, "options": {}, "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "布局", "class": "h-full flex", "options": {}, "children": [{"type": "Layout", "tag": "LayoutContainer", "class": "flex-1 flex justify-center flex-col", "name": "容器", "children": [{"type": "Layout", "tag": "Row", "class": "gap-4", "children": [{"type": "Layout", "tag": "Col", "class": "col-span-12", "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "spec": 23, "children": [{"type": "component", "tag": "CoMiHeader", "props": {}}]}]}]}, {"type": "Layout", "tag": "Row", "class": "gap-3", "style": {"marginTop": "-48px", "position": "relative", "zIndex": 2}, "children": [{"type": "Layout", "tag": "Col", "class": "col-span-8", "condition": {"eval": "!localStorage.getItem('A6PriorityRole')"}, "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "spec": 23, "children": [{"type": "component", "class": "2xl:h-[512px] xl:h-[454px] lg:h-[454px] md:h-[454px] sm:h-[454px]", "tag": "TodoCardColumn", "copilot": "1", "props": {}}]}]}, {"type": "Layout", "tag": "Col", "class": "col-span-6", "condition": {"eval": "localStorage.getItem('A6PriorityRole')"}, "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "spec": 23, "children": [{"type": "component", "class": "2xl:h-[512px] xl:h-[512px] lg:h-[512px] md:h-[512px] sm:h-[512px]", "tag": "TodoCardColumn", "copilot": "1", "props": {}}]}]}, {"type": "Layout", "tag": "Col", "class": "col-span-4", "condition": {"eval": "!localStorage.getItem('A6PriorityRole')"}, "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "class": "flex flex-col gap-3", "spec": 23, "children": [{"type": "component", "class": "h-[236px]", "tag": "MeetingCardColumn", "copilot": "1", "roles": [], "props": {}}, {"type": "component", "class": "2xl:h-[266px] xl:h-[206px] lg:h-[206px] md:h-[206px] sm:h-[206px]", "tag": "PortalMessage", "copilot": "1", "props": {}}]}]}, {"type": "Layout", "tag": "Col", "class": "col-span-6", "condition": {"eval": "localStorage.getItem('A6PriorityRole')"}, "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "class": "flex flex-col gap-3", "spec": 23, "children": [{"type": "component", "componentName": "BapExternalBiCard", "tag": "BapExternalBiCard", "class": "2xl:h-[512px] xl:h-[512px] lg:h-[512px] md:h-[512px] sm:h-[512px]", "roles": [], "props": {"config": {"A6BapDataDashboard": {"mainTitle": "战略指标", "chart1title": "动态现金流", "chart1code": "-2013701111308399171", "chart1input": "本年的动态现金流趋势，按月进行展示", "chart1secondTitleCode": "-2013701111308399171", "chart1secondTitleInput": "动态现金流", "chart1height": 170, "chart2title": "本月成交金额", "chart2code": "-2013701111308399171", "chart2input": "成交金额趋势，按月份展示近六个月的，月份为横轴", "chart2secondTitleCode": "-2013701111308399171", "chart2secondTitleInput": "本月成交金额", "chart2height": 170, "chart3title": "本月签约客户数", "chart3code": "-2013701111308399171", "chart3input": "签约客户数趋势，按月份进行展示", "chart3secondTitleCode": "-2013701111308399171", "chart3secondTitleInput": "本月签约客户数", "chart3height": 170}, "A6BapDepMkAdmin": {"mainTitle": "战略指标", "chart1title": "本期销售目标完成率", "chart1code": "-1880797838497723802", "chart1input": "销售目标完成率趋势", "chart1secondTitleCode": "-1880797838497723802", "chart1secondTitleInput": "本期销售目标完成率", "chart1height": 170, "chart2title": "本年成交金额", "chart2code": "-1880797838497723802", "chart2input": "本年成交金额趋势，按月份展示", "chart2secondTitleCode": "-1880797838497723802", "chart2secondTitleInput": "本年成交金额", "chart2height": 170, "chart3title": "本月签约客户数", "chart3code": "-1880797838497723802", "chart3input": "签约客户行业占比情况", "chart3secondTitleCode": "-1880797838497723802", "chart3secondTitleInput": "本月的签约客户数是几个", "chart3height": 170}, "A6BapFIAdmin": {"mainTitle": "动态现金流", "chart1title": "收入趋势", "chart1code": "-6332729496011256095", "chart1input": "动态现金流趋势", "chart1secondTitleCode": "-6332729496011256095", "chart1secondTitleInput": "动态现金流", "chart1height": 170, "chart2title": "当期预算执行率", "chart2code": "-6332729496011256095", "chart2input": "当期预算执行率", "chart2secondTitleCode": "-6332729496011256095", "chart2secondTitleInput": "当月预算执行率", "chart2height": 170, "chart3title": "报销总金额", "chart3code": "-6332729496011256095", "chart3input": "费用类型分布占比", "chart3secondTitleCode": "-6332729496011256095", "chart3secondTitleInput": "报销总金额", "chart3height": 170}, "A6BapIntelligentHrAdmin": {"mainTitle": "战略指标", "chart1title": "员工异动", "chart1code": "6673912454848466060", "chart1input": "员工异动趋势", "chart1secondTitleCode": "6673912454848466060", "chart1secondTitleInput": "员工异动数", "chart1height": 170, "chart2title": "招聘进展情况", "chart2code": "6673912454848466060", "chart2input": "按月统计投递简历的数量、招聘中的职位需求人数、移入已入职阶段的应聘者数量", "chart2secondTitleCode": "6673912454848466060", "chart2secondTitleInput": "在职员工人数", "chart2height": 170, "chart3title": "各部门员工数量", "chart3code": "6673912454848466060", "chart3input": "倒序展示各个部门的员工人数", "chart3secondTitleCode": "6673912454848466060", "chart3secondTitleInput": "员工总人数", "chart3height": 170}, "A6BapMkAdmin": {"mainTitle": "战略指标", "chart1title": "本期销售目标完成率", "chart1code": "3324908029901491704", "chart1input": "销售目标完成率趋势", "chart1secondTitleCode": "3324908029901491704", "chart1secondTitleInput": "本期销售目标完成率（目标金额列合计/完成金额合计）", "chart1height": 170, "chart2title": "本年成交金额", "chart2code": "3324908029901491704", "chart2input": "本年成交金额趋势", "chart2secondTitleCode": "3324908029901491704", "chart2secondTitleInput": "本年成交金额总和", "chart2height": 170, "chart3title": "本月签约客户数", "chart3code": "3324908029901491704", "chart3input": "签约客户趋势", "chart3secondTitleCode": "3324908029901491704", "chart3secondTitleInput": "本月签约客户数", "chart3height": 170}, "A6BapPuAdmin": {"mainTitle": "战略指标", "chart1title": "本年采购支出", "chart1code": "1085002789241636465", "chart1input": "采购支出趋势，按月进行展示", "chart1secondTitleCode": "1085002789241636465", "chart1secondTitleInput": "本年采购支出总数", "chart1height": 170, "chart2title": "剩余采购申请数量", "chart2code": "1085002789241636465", "chart2input": "采购计划状态分布占比", "chart2secondTitleCode": "1085002789241636465", "chart2secondTitleInput": "剩余采购申请数量", "chart2height": 170, "chart3title": "采购执行中数量", "chart3code": "1085002789241636465", "chart3input": "供应商类型的供货金额，已交货和待交货的供货金额用不同颜色进行表示", "chart3secondTitleCode": "1085002789241636465", "chart3secondTitleInput": "采购执行中数量", "chart3height": 170}}, "card-options": {"status": false, "lookSql": false, "canLookSql": false, "typeSource": "pcSide"}, "data": {"data": {"pluginName": "", "pluginKey": "", "cardType": 6, "disabled": false, "moreButtonType": 0, "pageInfo": {"pageNumber": 1, "pageSize": 10, "pages": 1, "total": 1, "needTotal": false}, "result": [{"originApiInfo": {}, "renderInfo": {"type": "bar_chart", "column_names": [], "data": [], "title": "", "options": {}, "maxRows": 200, "isOverMaxRows": false}}], "markdown": ""}}}}]}]}]}]}, {"type": "Layout", "tag": "LayoutContainer", "name": "容器", "class": "w-full bg-sky-500", "style": {"height": "200px"}, "condition": false, "children": [{"type": "component", "tag": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}]}]}]}, {"type": "Layout", "condition": false, "tag": "layout-footer", "name": "布局", "options": {}, "children": []}]}]}]}