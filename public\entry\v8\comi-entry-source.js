(function () {
 
  try {
    var topWindow = window.top || window;
    if (topWindow.COMI_ASSISTANT) {
      return;
    }
    // 不显示图标，只显示drawer, 图标由外面控制
    var hideComiBtn = topWindow.hideComiBtn;
    var topDocument = topWindow.document;

    var polyFillRequestAnimationFrame = topWindow.requestAnimationFrame;
    var polyFillCancelAnimationFrame = topWindow.cancelAnimationFrame;
    (function () {
      if (polyFillRequestAnimationFrame) {
        return;
      }
      var lastTime = 0;
      var vendors = ["ms", "moz", "webkit", "o"];
      for (
        var x = 0;
        x < vendors.length && !polyFillRequestAnimationFrame;
        ++x
      ) {
        polyFillRequestAnimationFrame =
          topWindow[vendors[x] + "RequestAnimationFrame"];
        polyFillCancelAnimationFrame =
          topWindow[vendors[x] + "CancelAnimationFrame"];
      }
      if (!polyFillRequestAnimationFrame) {
        polyFillRequestAnimationFrame = function (callback, element) {
          var currTime = Date.now();
          var timeToCall = Math.max(0, 16 - (currTime - lastTime));
          var id = topWindow.setTimeout(function () {
            callback(currTime + timeToCall);
          }, timeToCall);
          lastTime = currTime + timeToCall;
          return id;
        };
        polyFillCancelAnimationFrame = function (timerId) {
          return clearTimeout(timerId);
        };
      }
    })();
    var iconPositionUpdateId = null;

    var isMouseDown = false;
    var movedWhenMouseDown = false;
    var initialX, initialY;
    var initX, initY;
    var lastx = 0;
    var lasty = 0;
    var timer = null;
    var timeNum = 0;
    var intervalTimer = null;
    var animationed = false


    var newDiv = topDocument.createElement("div");
    newDiv.className = "comi-entry-btn";
    newDiv.style.cursor = "pointer";

    var button = topDocument.createElement("button");
    button.id = "comi-assistant-btn";
    button.style.width = "60px";
    button.style.height = "60px";
    button.style.borderRadius = "50%";
    button.style.border = "none";
    button.style.backgroundImage =
      "url(" +
      (topWindow._ctxPath || "/seeyon") +
      "/ai-platform/ai-static/ai-copilot/public/img/comi.gif)";
    // button.src =
    //   (topWindow._ctxPath || "/seeyon") +
    //   "/ai-platform/ai-static/ai-copilot/public/img/comi.gif";
    button.style.padding = "0";
    button.style.overflow = "hidden";
    button.style.cursor = "pointer";
    button.style.backgroundSize = "cover"
    button.style.backgroundRepeat = "no-repeat"
    button.style.backgroundColor = "transparent"

    // 在comi内部不显示入口comi图标
    if (window.top.location.href.indexOf("inComi") > -1) {
      button.style.display = "none";
    }

    var drawer = topDocument.createElement("div");
    drawer.id = "comi-assistant-drawer";

    drawer.style.position = "fixed";
    drawer.style.top = "0";
    drawer.style.right = "-450px";
    drawer.style.width = "450px";
    drawer.style.height = "100%";
    //drawer.style.overflow = 'hidden';
    drawer.style.borderRadius = "0";
    drawer.style.background = "#EDF2FC";
    drawer.style.boxShadow = "-4px 0 8px 0 rgba(145, 142, 159, 0.12)";
    // drawer.style.transition = "width 0.1s linear 0s"; //使用线性动画

    var aiEntryBtn;
    var openDrawer = function () {
      if (movedWhenMouseDown) return;
      if (drawer.style.right === "0") {
        return;
      } else {
        drawer.style.right = "0";
        drawer.style.display = "block";
        if (!isVreport && !isLoadIframe) {
          newDiv.appendChild(drawer);
          isLoadIframe = true;
        }
      }
      // var __arrowDrawer = document.querySelector(".arrowDrawer");
      // __arrowDrawer.classList.contains("active") &&
      //   __arrowDrawer.classList.remove("active");
      // button.style.display = "none";

      // 隐藏AI入口按钮
      aiEntryBtn = document.getElementsByClassName("AI-entry-btn")[0];
      aiEntryBtn && (aiEntryBtn.style.display = "none");
    };
    var iframe = topDocument.createElement("iframe");
    iframe.id = "content-frame";
    iframe.style.width = "100%";
    iframe.style.height = "100%";
    iframe.style.border = "none";
    iframe.src = _ctxPath + "/ai-platform/copilot?v=" + new Date().getTime() + '&inComi=true';
    drawer.appendChild(iframe);

    var loadingImg = topDocument.createElement("img");
    loadingImg.src =
      (topWindow._ctxPath || "/seeyon") +
      "/ai-platform/ai-static/ai-copilot/public/img/comi-entry-loading.svg";
    loadingImg.style.position = "absolute";
    loadingImg.style.top = "50%";
    loadingImg.style.left = "50%";
    loadingImg.style.transform = "translate(-50%, -50%)";
    loadingImg.style.zIndex = "-1";

    drawer.appendChild(loadingImg);

    newDiv.addEventListener('mousedown', function (event) {
      isMouseDown = true;
      movedWhenMouseDown = false;
      iconPositionUpdateId = null;
      initialX = event.clientX - newDiv.offsetLeft;
      initialY = event.clientY - newDiv.offsetTop;
      lastx = event.clientX;
      lasty = event.clientY;
      initX = event.clientX;
      initY = event.clientY;
    });

    var ICON_STICK_RIGHT = "5px";
    topWindow.addEventListener("mousemove", function (event) {
      if (!isMouseDown) {
        return;
      }
      movedWhenMouseDown = true;
      if (lastx === event.clientX && lasty === event.clientY) {
        return false;
      } else {
        lastx = event.clientX;
        lasty = event.clientY;

        var updatePosition = function () {
          var newX = lastx - initialX;
          var newY = lasty - initialY;
          var rect = newDiv.getBoundingClientRect();
          if (newY <= 0) {
            newDiv.style.top = "0px";
          } else if (newY + rect.height > window.top.innerHeight) {
            newDiv.style.top = window.top.innerHeight - rect.height + "px";
          } else {
            newDiv.style.top = newY + "px";
          }
          if (newX <= 0) {
            newDiv.style.left = "0px";
            newDiv.style.right = "initial";
          } else if (newX + rect.width - 12 > window.top.innerWidth) {
            newDiv.style.left = "initial";
            newDiv.style.right = ICON_STICK_RIGHT;
          } else {
            newDiv.style.left = newX + "px";
            newDiv.style.right = "initial";
          }
        };
        updatePosition();
      }
    });

    topWindow.addEventListener("mouseup", function (event) {
      var openDrawerTag = false;
      if (isMouseDown) {
        console.log(
          "click:",
          Math.abs(initX - lastx) + Math.abs(initY - lasty)
        );
        // 位移不够算click
        if (Math.abs(initX - lastx) + Math.abs(initY - lasty) < 5) {
          openDrawerTag = true;
        }
      }
      isMouseDown = false;
      movedWhenMouseDown = false;
      if (iconPositionUpdateId) {
        polyFillCancelAnimationFrame(iconPositionUpdateId);
        iconPositionUpdateId = null;
      }
      topDocument.body.style.pointerEvents = "initial";
      newDiv.className = newDiv.className.replace(/\bAIhover\b/, "");
      newDiv.style.left = "initial";
      newDiv.style.right = ICON_STICK_RIGHT;
      if (openDrawerTag) {
        if(window.comiEventBus){
          window.comiEventBus.$emit('openDrawer');
        }
        !event.target.classList.contains("comi-entry-arrow-drawer") && openDrawer();
      }
    });

    newDiv.style.position = "fixed";
    newDiv.style.top = "50%";
    newDiv.style.right = '5px';
    newDiv.style.zIndex = "999";

    var isLoadIframe = false;
    var isVreport = topWindow.location.href.indexOf("vReport.do") > -1;

    if (hideComiBtn === true) {
      button.style.display = "none";
    }
    var sdkOption = {
      defaultAssistId: '', // 默认打开助手id
      hideInputBoxAssistIds: [], // 隐藏输入框的助手id 
      defaultSendMsg: false, // 默认发送消息
    };
    topWindow.COMI_ASSISTANT = {
      preOption: sdkOption,
      sdk: null,
      close: function () {
        if (hideComiBtn !== true) {
          button.style.display = "block";
        }
        // changeExpandIcon(0);
        drawer.style.right = "-450px";
        drawer.style.width = "450px";
        drawer.style.display = "none";
        aiEntryBtn && (aiEntryBtn.style.display = "block");
      },
      // 全屏展开
      expand: function (right,width) {
        drawer.style.right = right || "0";
        drawer.style.width = width || "100%";
        drawer.style.display = "block";
        // changeExpandIcon(window.innerWidth);
      },
      // 全屏收缩
      collapse: function () {
        drawer.style.right = "0";
        drawer.style.width = "450px";
        // changeExpandIcon(450);
      },
      handleTagClick:function (config) {
      },
      openWin: function (config) {
      },
      openDialog: function (config) {
      },
      openPersonalInfo: function (config) {
      },

      // 获取侧边栏状态，false关闭，true打开
      getOpenStatus: function () {
        if (drawer.style.display == "none" || drawer.style.display == "") {
          return false;
        } else {
          return true
        }
      },
      getOaUrl: function (config) {
      },
    };
    // markdown标签点击事件
    // if (COMI_UTIL) {
    //   if (COMI_UTIL.handleTagClick) {
    //     topWindow.COMI_ASSISTANT.handleTagClick = COMI_UTIL.handleTagClick;
    //   }
    //   if (COMI_UTIL.openWin) {
    //     topWindow.COMI_ASSISTANT.openWin = COMI_UTIL.openWin;

    //   }
    // }

    var styleElement = topDocument.createElement("style");
    styleElement.innerHTML =
      "@media print {.comi-entry-btn {display:none}} .AIhover {width: 48px; height: 48px; overflow: hidden; border-radius: 28px !important;}.comi-entry-btn{right: 0px;border-bottom-right-radius: 0px;border-top-right-radius: 0px;border-top-left-radius: 48px;border-bottom-left-radius: 48px;padding: 0;border: none;}.comi-entry-btn:hover{right: 5px !important;}";
    topDocument.head.appendChild(styleElement);

    var gifAnimationTime = 5210; // gif动画时间
    // 替换为静态图片
    function replaceToStaticPng(time) {
      var waitNum = time - 10
      timer = setTimeout(function () {
        button.style.backgroundImage = "url(" +
          (topWindow._ctxPath || "/seeyon") +
          "/ai-platform/ai-static/ai-copilot/public/img/comi-static.png)";
        animationed = true
      }, waitNum)
    }

    newDiv.appendChild(button);
    replaceToStaticPng(gifAnimationTime)

    // 避免第一次加载鼠标移入移出
    intervalTimer = setInterval(() => {
      timeNum = timeNum + 10;
    }, 10)

    newDiv.addEventListener("mouseenter", function () {
      // 动画完成之后在处理
      if (animationed) {
        if (timer) {
          clearTimeout(timer);
        }
        if (intervalTimer) {
          clearInterval(intervalTimer);
        }
        animationed = false;
        button.style.backgroundImage = "url(" + (topWindow._ctxPath || "/seeyon") +
          "/ai-platform/ai-static/ai-copilot/public/img/comi.gif)";
        intervalTimer = setInterval(() => {
          timeNum = timeNum + 10;
        }, 10)
      }


    })
    newDiv.addEventListener("mouseleave", function () {
      if (timer) {
        clearTimeout(timer);
      }
      if (intervalTimer) {
        clearInterval(intervalTimer);
      }
      const time = Math.abs(timeNum - gifAnimationTime);
      if (timeNum <= gifAnimationTime) {
        replaceToStaticPng(time)
      } else {
        replaceToStaticPng(time % gifAnimationTime)
      }
      timeNum = 0;
    })


    window.addEventListener('resize', function () {
      if (drawer.style.display == "block" && drawer.clientWidth != 450) {
        var width = window.innerWidth;
        // width = width <= 450 ? 450 : width;
        // changeExpandIcon(width);
        drawer.style.width = width + 'px';
      }
    }); // 每200ms最多执行一次

    var headlessSaerch = topWindow.location.search;
    var searchArray = headlessSaerch.split("?");
    if (searchArray.length > 1) {
      var searchObj = {};
      var searchStr = searchArray[1];
      var searchParams = searchStr.split("&");
      for (var j = 0; j < searchParams.length; j++) {
        var searchItem = searchParams[j].split("=");
        if (searchItem) {
          searchObj[searchItem[0]] = searchItem[1];
        }
      }
      if (searchObj["headless"] !== "true") {
        topDocument.body.appendChild(newDiv);
      }
    } else {
      topDocument.body.appendChild(newDiv);
    }

    // 延迟加载iframe
    function delayApppendIframe() {
      setTimeout(function () {
        isLoadIframe = true;
        newDiv.appendChild(drawer);
      }, 1000);
    }
    // 报表中心且未加载过
    if (isVreport && !isLoadIframe) {
      delayApppendIframe();
    }

    // 通用回调处理函数
    function executeCallback(data, actionName) {
      if (data && typeof data.callback === 'function') {
        try {
          data.callback();
        } catch (e) {
          console.error('执行 ' + actionName + ' 回调时出错:', e);
        }
      }
    }

    // 添加 comiEventBus 监听器来处理 openDrawer 消息
    if (topWindow.comiEventBus && typeof topWindow.comiEventBus.$on === 'function') {
      topWindow.comiEventBus.$on('openDrawer', function(data) {
        console.log('收到 comiEventBus openDrawer 消息:', data);
        openDrawer();
        executeCallback(data, 'openDrawer');
      });
      
      topWindow.comiEventBus.$on('hideComi', function(data) {
        console.log('收到 comiEventBus hideComi 消息:', data);
        newDiv.style.display = 'none';
        executeCallback(data, 'hideComi');
      });
      
      topWindow.comiEventBus.$on('showComi', function(data) {
        console.log('收到 comiEventBus showComi 消息:', data);
        newDiv.style.display = 'block';
        executeCallback(data, 'showComi');
      });
      
      console.log('✅ comiEventBus UI相关监听器已注册');
    }
  } catch (error) {
    console.log("==> ai-entry-error", error);
  }
})();