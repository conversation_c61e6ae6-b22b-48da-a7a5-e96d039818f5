
<!-- 执行过程组件 -->
<template>
  <div class="ai-identify-component" :class="identifyClass">
    <div class="mb-4 ai_item w-fit" :class="{ 'loading_status': loadingStatus }">
      <div class="ai_identify_box" :class="{ 'bg-white': !loadingStatus }">
        <div class="flex items-center ml-1 loader_img" v-if="showLoading">
          <Image :src="Loading" :width="width" :preview="false"  />
        </div>
        <div><slot></slot></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Image } from 'ant-design-vue';
  import Loading from '@/assets/imgs/loading.png'
  const props = defineProps({
    loadingStatus: {
      type: Boolean,
      default: false
    },
    showLoading: {
      type: Boolean,
      default: true
    },
    width: {
      type: Number,
      default: 16
    },
    identifyClass: {
      type: String,
      default: ''
    }
  });
</script>

<style lang="less" scoped>
.ai-identify-component .ai_item .ai_identify_box {
      height: 38px;
      padding: 8px;
      border-radius: 2px 12px 12px 12px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
    }
.ai-identify-component .loading_status{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

.ai-identify-component .loader_img{
    animation: ai-identify-loadcir 800ms linear 0ms infinite normal none;
    margin-right: 5px;
    margin-left: 5px;
    position: relative;
}

.ai-identify-component.column .ai_item .ai_identify_box {
  flex-direction: column;
}

.ai-identify-component.column .loader_img {
  margin-bottom: 10px;
}

@keyframes ai-identify-loadcir {
    from{
    transform: rotateZ(0deg);
    }
    to{
    transform: rotateZ(360deg);
    }
}
</style>
