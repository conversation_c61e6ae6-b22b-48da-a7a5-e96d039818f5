
// 服务列表入参
export interface ServiceParams {
  params: {
    name?: string;
    type: string;
  };
  pageInfo: {
    pageNumber: number;
    pageSize: number;
  };
}

// 树数据类型
export interface TreeItem {
  name: string,
  aiOriginalId: string,
  [key: string]: any,
}


export interface PresentQuestionsRef {
  qr: HTMLElement;
}

export interface AssistInfo {
  id: string,
  code: string,
  name: string,
  introduce: string
  iconUrl: string
  [key: string]: any
}

export interface FileType {
  name: string,
  size: string,
  url: any,

}


// 对话
export interface ChatItem {
  [key: string]: any
}
// 开场白-问题
export interface PresentQuestionsItem {
  id: number;
  name: string;
  assistantId: string
}

// 工具
export interface ToolItem {
  id: number;
  name: string;
  desc?: string;
  type?: string;
}

export interface TabItem {
  key: string;
  label: string;
  content?: any;
  className?: string;
  from?: string;
}

// 对话入参
export interface citationsEle {
  fileNumber: '',
  [key: string]: any
}
export interface ChatUserParams {
  input: string,
  assistantId: string,
  assistantCode: string,
  citations: citationsEle[] | []
}

export interface citationJSONItem {
  id: string,
  number: null,
  name: string,
  fileSize: string,
  fileType: string,
  fileUrl: string,
  fileRelativeUrl: string,
  fileTag: string,
  source: string,
  sourceId: string,
  createTime: number,
  metadata: any;
  [key: string]: any;
}

export interface ChatMessage {
  id: string;
  index: number;
  content: string;
  messageType: number;
  messageTime: string;
  cardData?: any[];
  citationList?: any[];
  assistant?: ExtendedAssistantInfo;
  citationsJson?: string;
  assistantCode?: string;
  assistantId?: string;
  chatSessionId?: string;
  sessionType?: string;
  input?: string;
  hitKnowledgeRunSteps?: any[];
  needMarkBlueRunSteps?: any[];
}

export interface ExtendedAssistantInfo extends AssistantInfo {
  introduce: string;
  prologue: string;
  guideQuestions?: string[];
  id: string;
  code: string;
}

export interface AssistantInfo {
  name?: string,
  iconUrl?: string,
}
