<template>
  <!-- 外层大容器，毛玻璃背景 - 当第一个分类数据为空时隐藏整个组件 -->
  <div
    v-if="shouldShowComponent"
    class="table-card-wrapper flex flex-col w-full py-[16px] rounded-[12px] relative"
  >
    <!-- 全局弹窗管理器 - 放在最顶层 -->
    <ActionPopoverManager ref="globalPopoverManagerRef" />

    <!-- 待办列表区域 -->
    <div
      class="todo-content"
      v-show="!showIntelligentReview"
    >
      <div id="guide-step3" class="w-full px-[16px]">
      <TableCardHeader
        :config="config"
        :total-pending-count="totalPendingCount"
        :show-categories="showCategories"
        @toggle-category="toggleCategoryVisible"
        @dropdown-click="handleDropdownClick"
        :threshold="threshold"
        @more="handleMore"
        @dropdown-visible-change="handleComiDropdownVisibleChange"
      />

      <!-- 分类标签 -->
      <Transition name="category-slide">
        <CategoryTabs
          v-if="showCategories"
          :categories="validClassifications"
          :activeCategory="selectedCategory"
          :canWheel="true"
          :threshold="threshold"
          @category-click="handleCategoryTabClick"
        />
      </Transition>
      </div>
      <!-- 内容区域 -->
      <div class="table-card-content flex-1 flex flex-col">
        <!-- 初始加载状态 -->
        <div
          v-if="initialLoading"
          class="loading-container"
        >
          <!-- <div class="loading-spinner">正在加载待办数据...</div> -->
          <AiIdentify
            class="loading"
            v-if="initialLoading"
            :loadingStatus="true"
          ></AiIdentify>
        </div>

        <!-- 数据加载错误状态 -->
        <div
          v-else-if="dataError"
          class="error-container h-full"
        >
          <!-- <div class="error-message">{{ dataError }}</div> -->
          <PortalEmptyColumn
            :image="ErrorPendingImg"
            text="加载失败~"
            :width="120"
          />
          <!-- <button class="retry-btn" @click="retryLoadData">重试</button> -->
        </div>

        <!-- 分页加载状态 -->
        <div
          v-else-if="isLoading"
          class="loading-container"
        >
          <!-- <div class="loading-spinner">加载中...</div> -->
          <AiIdentify
            class="loading"
            v-if="isLoading"
            :loadingStatus="true"
          ></AiIdentify>
        </div>

        <!-- 数据列表容器 -->
        <div
          v-else-if="currentDataList.length"
          class="data-list-container flex-1"
        >
          <div
            class="data-list px-[16px]"
            :style="{ paddingBottom: shouldShowPagination ? '20px' : '0' }"
          >
            <template
              v-for="(item, index) in currentDataList"
              :key="`${item.affairId || (item as any).id || index}-${selectedCategory}`"
            >
              <TodoCardItem
                :class="index ? 'mt-[12px]' : ''"
                :renderInfo="item"
                :extractionData="getExtractionDataForItem(item)"
                @click="handleClick(item, index)"
                @button-click="handleItemButtonClick"
                @refresh="handleRefresh"
              />
            </template>

            <!-- 信息提取加载状态提示 -->
            <div
              class="info-extraction-loading"
              v-if="isInfoExtracting"
            >
              <div class="loading-container">
                <AiIdentify
                  class="loading"
                  v-if="isInfoExtracting"
                  :loadingStatus="true"
                  identifyClass="column"
                >
                  正在深度解析并提取信息，请稍候～
                </AiIdentify>
                <!-- <a-spin />
                <div class="loading-spinner ml-[10px]">正在提取信息...</div> -->
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div
          v-else
          class="flex-1 flex items-center justify-center"
        >
          <PortalEmptyColumn
            :image="EmptyPendingImg"
            text="暂无数据哦~"
            :width="120"
            :height="120"
          />
        </div>
      </div>
    </div>
    <!-- 悬浮分页组件 -->
    <div
      v-if="shouldShowPagination"
      class="floating-pagination"
    >
      <div class="pagination-backdrop"></div>
      <div class="pagination-buttons">
        <button
          class="pagination-btn prev-btn"
          :disabled="!canGoPrev || isLoading"
          @click="goPrevPage"
        >
          <i class="iconfont ai-icon-zuo"></i>
        </button>
        <span class="pagination-current">{{ pagination.current }}/{{ totalPages }}</span>
        <button
          class="pagination-btn next-btn"
          :disabled="!canGoNext || isLoading"
          @click="goNextPage"
        >
          <i class="iconfont ai-icon-you"></i>
        </button>
      </div>
    </div>

    <!-- 智能预审区域 -->
    <div
      class="intelligent-review-content"
      v-show="showIntelligentReview"
    >
      <IntelligentReviewOverlay
        :visible="showIntelligentReview"
        :total-count="0"
        @close="handleCloseIntelligentReview"
        @process="handleProcessIntelligentReview"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { toRefs, ref, watch, onMounted, onUnmounted, onActivated, onDeactivated, provide, computed, inject } from 'vue';
import TodoCardItem from './todoCardItem.vue';
import ActionPopoverManager from './actionPopoverManager.vue';
import PortalEmptyColumn from '../portalEmptyColumn/index.vue';
import CategoryTabs from './categoryTabs.vue';
import TableCardHeader from './TableCardHeader.vue';
import AiIdentify from '@/components/aiIdentify/index.vue';
import type { TodoRenderInfo, TodoItem, ClassificationItem } from '@/types/portalTypes';
import TodoSvg from '@/assets/imgs/todo.svg';
import EmptyPendingImg from '@/assets/imgs/empty_pending.png';
import ErrorPendingImg from '@/assets/imgs/error.png';

import { openDobuleScreen } from '@/utils/storesUtils';
import {
  getUrgentPendingList,
  getPendingClassification,
  getPendingDataByCategory,
  getPendingCount,
  extractPendingInfo
} from '@/api/pending/index';
import { getBatchSummary } from '@/api/portal';
import AppType from '@/utils/typeEnum';
import {
  Image as AImage,
  Menu as AMenu,
  MenuItem as AMenuItem,
  Spin as ASpin,
  message
} from 'ant-design-vue';
import IntelligentReviewOverlay from './intelligentReviewOverlay.vue';
// 导入会议刷新 composable
import { useMeetingRefresh } from '../../hooks/portal/useMeetingRefresh';
import { cancelAllRequests } from '@/api/config';
import { useTodo } from '@/stores/todoData';
import { useGlobal } from '@/stores/global';
import { dealOaUrl } from '@/utils/common';
const uGlobal = useGlobal();

// 分页配置接口
interface PaginationConfig {
  current: number;
  pageSize: number;
  total: number;
}

// 组件配置接口
interface TodoCardConfig {
  title: string;
  maxItems: number;
  desc?: string;
  icon?: string;
}

// Props 定义 - 简化后的接口
const props = defineProps({
  btns: {
    type: Array as PropType<
      Array<{
        btnIcon: string;
        btnUrl: string;
      }>
    >,
    default: () => [],
  },
  config: {
    type: Object as PropType<TodoCardConfig>,
    default: () => ({
      title: '待办中心',
      maxItems: 5,
      desc: '已按照智能算法规则为您筛选优先待办',
      icon: 'ai-icon-shijian'
    }),
  },
  // 保留向后兼容性的可选props
  data: {
    type: Object,
    default: () => null,
  },
  classifications: {
    type: Array as PropType<Array<ClassificationItem>>,
    default: () => null,
  },
});

const emit = defineEmits<{
  'dropdown-menu-click': [menuKey: string];
  'item-button-click': [button: any, item: any, confirmData?: any];
  'refresh-classifications': [];
  'data-loaded': [data: TodoItem[]];
  'data-error': [error: string];
  'loading-change': [loading: boolean];
  'visibility-change': [visible: boolean];
}>();

const uTodo = useTodo();

// 响应式状态
const pagination = ref<PaginationConfig>({
  current: 1,
  pageSize: 10,
  total: 0
});

// 数据状态
const classificationsData = ref<ClassificationItem[]>([]);
const threshold = ref<number | undefined>(undefined);
const currentDataList = ref<TodoItem[]>([]);
const initialLoading = ref(true);
const isLoading = ref(false);
const dataError = ref<string | null>(null);
const totalPendingCount = ref(0);

// 组件状态
const selectedCategory = ref('');
const currentPortletParams = ref('');
const isComponentMounted = ref(true);
const showCategories = ref(false); // 初始值设为false，会在数据加载后根据isPortal状态动态设置
const userToggledCategories = ref(false); // 记录用户是否手动切换过分类显示状态
const showIntelligentReview = ref(false);
const showInfoExtraction = ref(false);
const infoExtractionData = ref<Record<string, any>>({});
const isInfoExtracting = ref(false);
const hasActiveNextStep = ref(false); // 是否有NextStep操作正在进行

// 全局弹窗管理器实例
const globalPopoverManagerRef = ref<InstanceType<typeof ActionPopoverManager>>();

// 提供全局弹窗管理器给所有子组件
provide('popoverManager', computed(() => globalPopoverManagerRef.value));

// 提供NextStep状态管理方法给子组件
provide('nextStepStateManager', {
  setNextStepActive: (isActive: boolean) => {
    hasActiveNextStep.value = isActive;
    console.log('NextStep状态更新:', isActive);
  },
  getNextStepActive: () => hasActiveNextStep.value
});

// 使用会议刷新 composable
const { triggerMeetingRefresh } = useMeetingRefresh();

// 注入isPortal状态
const isPortal = inject('isPortal') as boolean;
const sdkInstance = inject('sdkInstance') as any;

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(pagination.value.total / pagination.value.pageSize);
});

// 判断当前是否为第一个分类
const isFirstCategory = computed(() => {
  const firstCategory = validClassifications.value[0];
  return firstCategory && selectedCategory.value === firstCategory.appId;
});

const shouldShowPagination = computed(() => {
  // 第一个分类不显示分页
  if (isFirstCategory.value) {
    return false;
  }
  return totalPages.value > 1 && !showIntelligentReview.value && !isInfoExtracting.value;
});

const canGoPrev = computed(() => {
  return pagination.value.current > 1;
});

const canGoNext = computed(() => {
  return pagination.value.current < totalPages.value;
});

const validClassifications = computed(() => {
  const classifications = props.classifications || classificationsData.value;
  const validItems = classifications?.filter(item =>
    item && item.appId && item.classificationName
  ) || [];

  // 对第一个分类的数量进行特殊处理，使用threshold或默认值5
  return validItems.map((item, index) => {
    if (index === 0) {
      // 第一个分类的数量使用threshold或默认值5
      const originalTotal = parseInt(item.total) || 0;
      const maxDisplay = 5;
      const displayTotal = Math.min(originalTotal, maxDisplay);
      return {
        ...item,
        total: displayTotal.toString()
      };
    }
    return item;
  });
});

// 根据affairId获取对应的提取信息
const getExtractionDataForItem = (item: object) => {
  const { appId, handleParam } = item;
  switch (appId) {
    case '1': // 协同
    case '4': // 公文
    default:
      return infoExtractionData.value[handleParam?.summaryId] || null;
    case '6': // 会议
      return infoExtractionData.value[handleParam?.meetingId] || null;
  }
};

// 控制组件是否显示 - 始终显示组件，即使没有数据
const shouldShowComponent = computed(() => {
  // 始终显示组件，无论是否有数据
  // 这样用户可以看到待办栏目，即使暂时没有数据
  return true;
});

// 数据获取方法
const fetchInitialTodoData = async (): Promise<TodoItem[]> => {
  try {
    const response: any = await getUrgentPendingList();
    if (response?.code === '0' && response?.data) {
      return response.data?.pageData?.map((item: any) => ({
        ...item,
        id: item.id || item.affairId || `initial-id-${Math.random()}`,
        title: item.title || '无标题',
        typeName: item.businessType || item.typeName || '',
        userName: item.senderName || item.userName || '',
        userPhoto: item.senderId ? `/seeyon/rest/orgMember/avatar/${item.senderId}?t=${Date.now()}` : item.userPhoto || '',
        createDate: item.startTime || item.createDate || '',
        endDate: item.deadlineDate || item.endDate || '',
        affairId: item.affairId || '',
        templateId: item.templateId || '',
        businessTypeName: String(AppType.AppTypeEnumNumber[parseInt(item.app) as unknown as keyof typeof AppType.AppTypeEnumNumber]),
        link: item.link || `/seeyon/collaboration/collaboration.do?method=summary&openFrom=listPending&affairId=${item.affairId}&showTab=true`,
        type: item.type || '',
        companyName: item.companyName || '',
        importantLevel: item.importantLevel || '',
        rowButtons: item.rowButtons || []
      })) || [];
    }
    return [];
  } catch (error: any) {
    // 如果是取消错误，直接抛出，不处理
    if (error.name === 'CanceledError' || error.message?.includes('请求已被取消')) {
      throw error;
    }
    console.error('获取初始待办数据失败:', error);
    throw new Error('获取待办数据失败，请稍后重试');
  }
};

// 获取待办总数量统计
const fetchTotalPendingCount = async () => {
  try {
    // 调用待办总数量接口，只请求一次
    const response: any = await getPendingCount('pendingSection', '');
    if (response?.code === '0' && response?.data) {
      totalPendingCount.value = parseInt(response.data) || 0;
      console.log('待办总数量:', totalPendingCount.value);
    } else {
      totalPendingCount.value = 0;
    }
  } catch (error: any) {
    // 如果是取消错误，不处理
    if (error.name === 'CanceledError' || error.message?.includes('请求已被取消')) {
      console.log('获取待办总数量被取消，跳过错误处理');
      return;
    }

    console.error('获取待办总数量失败:', error);
    totalPendingCount.value = 0;
  }
};

const fetchCategoryData = async (category: ClassificationItem, page: number = 1, isPageNavigation: boolean = false): Promise<TodoItem[]> => {
  try {
    // 判断是否为第一个分类
    const firstCategory = validClassifications.value[0];
    const isFirstCat = firstCategory && category.appId === firstCategory.appId;

    // 第一个分类限制为5条数据，且只获取第一页
    const actualPageSize = isFirstCat ? 5 : pagination.value.pageSize;
    const actualPage = isFirstCat ? 1 : page;

    sessionStorage.setItem('portletParams', category.portletParams)
    const response: any = await getPendingDataByCategory(actualPage, actualPageSize, category.portletParams);

    if (response && response.code === '0' && response.data) {
      const rawData = response.data?.data || [];
      const processedData = rawData.map((item: any, index: number) => ({
        ...item,
        id: item.id || item.affairId || `category-id-${index}-${Date.now()}`,
        title: item.title || '无标题',
        typeName: item.businessType || item.typeName || '',
        userName: item.senderName || item.userName || '',
        userPhoto: item.senderId ? `/seeyon/rest/orgMember/avatar/${item.senderId}?t=${Date.now()}` : item.userPhoto || '',
        createDate: item.startTime || item.createDate || '',
        endDate: item.deadlineDate || item.endDate || '',
        affairId: item.affairId || '',
        templateId: item.templateId || '',
        businessTypeName: item.businessType || item.businessTypeName || '',
        link: item.link || `/seeyon/collaboration/collaboration.do?method=summary&openFrom=listPending&affairId=${item.affairId}&showTab=true`,
        type: item.type || '',
        companyName: item.companyName || '',
        importantLevel: item.importantLevel || '',
        rowButtons: item.rowButtons || []
      }));
      // 更新分页信息，如果是分页导航操作，保持前端设置的页码
      // 第一个分类不更新分页信息
      if (!isFirstCat) {
        updatePagination(response.data, isPageNavigation);
      }

      return processedData;
    }
    return [];
  } catch (error: any) {
    // 如果是取消错误，直接抛出，不处理
    if (error.name === 'CanceledError' || error.message?.includes('请求已被取消')) {
      throw error;
    }
    console.error('获取分类数据失败:', error);
    throw new Error('获取分类数据失败，请稍后重试');
  }
};

// 初始化数据
const initializeData = async () => {
  console.log('TodoCardColumn initializeData called');
  console.log('Starting data initialization...');
  initialLoading.value = true;
  dataError.value = null;
  emit('loading-change', true);
  let isCanceled = false;

  try {
    console.log('Fetching classifications...');
    // 获取分类数据
    let classifications;
    if (props.classifications) {
      classifications = props.classifications;
    } else {
      const response: any = await getPendingClassification();
      if (response?.code === '0' && response?.data) {
        classifications = response.data?.navData || [];
        // 从接口响应中提取threshold
        if (response.data?.threshold) {
          threshold.value = response.data.threshold;
        }
      } else {
        classifications = [];
      }
    }

    console.log('Classifications fetched:', classifications.length, 'categories');

    if (!isComponentMounted.value) return;

    classificationsData.value = classifications;

    // 分类获取成功，根据isPortal状态设置默认展开状态
    if (classifications.length > 1) {
      // 初始化时，根据isPortal状态设置默认展开状态
      showCategories.value = !!isPortal;
    } else {
      // 只有一个分类时不显示分类行
      showCategories.value = false;
    }

    // 分类数据一定存在，自动选择第一个并加载数据
    const firstCategory = classifications[0];
    console.log('Using first category:', firstCategory.classificationName);
    selectedCategory.value = firstCategory.appId;
    currentPortletParams.value = firstCategory.portletParams;

    // 加载第一个分类的数据
    await loadCategoryData(firstCategory);

    // 获取待办总数量
    await fetchTotalPendingCount();

    console.log('Final data list:', currentDataList.value.length, 'items');
    emit('data-loaded', currentDataList.value);
  } catch (error: any) {
    // 如果是取消错误，不显示错误信息
    if (error.name === 'CanceledError' || error.message?.includes('请求已被取消')) {
      console.log('初始化数据被取消，跳过错误处理');
      isCanceled = true;
      return;
    }

    console.error('初始化数据失败:', error);
    dataError.value = error instanceof Error ? error.message : '数据加载失败';

    // 分类获取失败，隐藏分类行
    showCategories.value = false;
    classificationsData.value = [];

    emit('data-error', dataError.value);
  } finally {
    // 只有在请求未被取消的情况下才设置loading状态
    if (!isCanceled) {
      initialLoading.value = false;
      emit('loading-change', false);
    }
  }
};

// 加载分类数据
const loadCategoryData = async (category: ClassificationItem, page: number = 1, isPageNavigation: boolean = false) => {
  if (!isComponentMounted.value) return;

  isLoading.value = true;
  dataError.value = null;
  let isCanceled = false;

  try {
    const data = await fetchCategoryData(category, page, isPageNavigation);
    if (isComponentMounted.value) {
      currentDataList.value = data;
      // 只在初始化时设置选中状态，用户点击时已经立即设置了
      if (initialLoading.value) {
        selectedCategory.value = category.appId;
        currentPortletParams.value = category.portletParams;
      }
    }
  } catch (error: any) {
    // 如果是取消错误，不显示错误信息，直接返回
    if (error.name === 'CanceledError' || error.message?.includes('请求已被取消')) {
      console.log('加载分类数据被取消，跳过错误处理');
      isCanceled = true;
      return;
    }

    console.error('加载分类数据失败:', error);
    if (isComponentMounted.value) {
      dataError.value = error instanceof Error ? error.message : '加载分类数据失败';
    }
  } finally {
    // 只有在请求未被取消的情况下才设置loading为false
    if (isComponentMounted.value && !isCanceled) {
      isLoading.value = false;
    }
  }
};

// 重试加载数据
const retryLoadData = async () => {
  await initializeData();
};

// 刷新分类数据（更新分类数量）
const refreshClassifications = async () => {
  try {
    console.log('开始刷新分类数量...');
    const response: any = await getPendingClassification();

    if (response?.code === '0' && response?.data) {
      const classifications = response.data?.navData || [];

      if (isComponentMounted.value) {
        classificationsData.value = classifications;

        // 从接口响应中提取threshold
        if (response.data?.threshold) {
          threshold.value = response.data.threshold;
        }

        // 更新分类显示状态，根据isPortal状态设置
        if (classifications.length > 1) {
          // 如果用户在非门户场景下手动展开过分类，则保持展开状态
          if (!isPortal && userToggledCategories.value && showCategories.value) {
            // 保持当前展开状态，不改变
          } else {
            // 当isPortal为true时，分类默认展开；否则默认收起
            showCategories.value = !!isPortal;
          }
        } else {
          // 只有一个分类时不显示分类行
          showCategories.value = false;
        }

        console.log('分类数量刷新完成，共', classifications.length, '个分类');

        // 触发分类刷新事件，通知父组件
        emit('refresh-classifications');
      }
    }
  } catch (error: any) {
    console.error('刷新分类数据失败:', error);

    // 刷新失败时隐藏分类行
    if (error.name !== "CanceledError" && isComponentMounted.value) {
      showCategories.value = false;
      classificationsData.value = [];
    }
  }
};

// 刷新数据
const refreshData = async () => {
  if (selectedCategory.value && currentPortletParams.value) {
    const category = validClassifications.value.find(c => c.appId === selectedCategory.value);
    if (category) {
      resetPagination();
      await loadCategoryData(category);
    }
  } else {
    await initializeData();
  }
};

// 分页相关方法
const resetPagination = () => {
  pagination.value.current = 1;
  pagination.value.total = 0;
};

const updatePagination = (responseData: any, keepCurrentPage: boolean = false) => {
  if (responseData) {
    pagination.value.total = parseInt(responseData.total) || 0;
    // 如果需要保持当前页码（分页操作时），则不更新current
    if (!keepCurrentPage) {
      pagination.value.current = parseInt(responseData.pageNo) || 1;
    }
    pagination.value.pageSize = parseInt(responseData.pageSize) || 10;
  }
};

const loadCurrentCategoryData = async (page: number = pagination.value.current) => {
  if (!currentPortletParams.value || isLoading.value) return;

  const category = validClassifications.value.find(c => c.portletParams === currentPortletParams.value);
  if (category) {
    // 传递true表示这是分页导航操作，保持前端设置的页码
    await loadCategoryData(category, page, true);
  }
};

const goPrevPage = async () => {
  if (!canGoPrev.value || isLoading.value) return;

  pagination.value.current--;
  await loadCurrentCategoryData(pagination.value.current);
};

const goNextPage = async () => {
  if (!canGoNext.value || isLoading.value) return;

  pagination.value.current++;
  await loadCurrentCategoryData(pagination.value.current);
};

// 处理待办参数
const dealTodoParams = () => {
  const category = validClassifications.value.find(c => c.appId === selectedCategory.value);
  if (category) {
    // 判断是否为第一个分类
    const firstCategory = validClassifications.value[0];
    const isFirstCat = firstCategory && category.appId === firstCategory.appId;

    // 第一个分类限制为5条数据，且只获取第一页
    const actualPageSize = isFirstCat ? 5 : pagination.value.pageSize;
    console.log('category', category)
    if(category) {
      uTodo.changeState('searchParams', {
        pageNumber: 1,
        pageSize: actualPageSize,
        portletParams: category.portletParams
      })
    }
  }
}

// 事件处理方法
const handleClick = (item: TodoItem, inx: number) => {
  uTodo.changeState('dataInx', inx)
  dealTodoParams();
  uTodo.changeState('dataList', currentDataList.value)
  // TODO:此处临时设置了处理待办列表和待办详情的特殊逻辑，后续还需要进一步优化
  uGlobal.changeState('preLinkIsTodoList', false);
  const ctxPath = window._ctxPath || '/seeyon';
  let url = `${ctxPath}${item.link}`;
  // 如果是公文, 综合办公审批，在新窗口打开
  if (item.link.indexOf('showTab=true') < 0) {
    url = dealOaUrl(url);
    const topWindow: any = window.top || window;
    if (topWindow?.getCtpTop && topWindow?.getCtpTop()?.openCtpWindow) {
      topWindow.getCtpTop().openCtpWindow({url: url, comiOrigin: 'v5'});
    } else {
      topWindow.open(url, '_blank');
    }
  } else {
    openDobuleScreen(url, 'iframe', {origin: 'portal'});
  }
};

const handleMore = () => {
  // TODO:此处临时设置了处理待办列表和待办详情的特殊逻辑，后续还需要进一步优化
  const ctxPath = window._ctxPath || '/seeyon';
  uGlobal.changeState('preLinkIsTodoList', true);
  openDobuleScreen(`${ctxPath}/collaboration/collaboration.do?method=listPending&isFromComi=true`, 'iframe', {origin: 'portal'});
};

const handleDropdownClick = (key: string) => {
  if (key === 'intelligent-review') {
    showIntelligentReview.value = true;
  } else if (key === 'info-extract') {
    handleInfoExtraction();
  }
  emit('dropdown-menu-click', key);
};

const handleItemButtonClick = async (button: any, item: any, confirmData?: any) => {
  // 通知父组件处理按钮点击
  emit('item-button-click', button, item, confirmData);

  // 移除了重复的自动刷新逻辑，因为 todoCardItem.vue 中已经有刷新处理
  // 避免重复刷新的问题
};

const handleCategoryClick = async (category: ClassificationItem) => {
  // 如果点击的是当前已选中的标签，则不进行任何操作
  if (selectedCategory.value === category.appId && !initialLoading.value) {
    console.log('点击的是当前已选中的标签，跳过操作');
    return;
  }

  // 立即更新选中状态，提供即时的用户反馈
  selectedCategory.value = category.appId;
  currentPortletParams.value = category.portletParams;

  // 取消所有包含特定URL的请求
  cancelAllRequests('pending-section/portlet');
  cancelAllRequests('pending-section/classificationAll');
  cancelAllRequests('pending-section/count');

  resetPagination();

  // 清空关键信息数据，因为切换分类后原有的关键信息不再适用
  infoExtractionData.value = {};
  console.log('分类切换：已清空关键信息数据');

  try {
    // 在切换分类时，同时刷新分类数量、当前数据和待办总数
    const refreshTasks = [];

    // 1. 加载选中分类的数据
    refreshTasks.push(loadCategoryData(category));

    // 2. 刷新分类数据（更新各分类的数量统计）
    refreshTasks.push(refreshClassifications());

    // 3. 刷新待办总数量
    refreshTasks.push(fetchTotalPendingCount());

    // 并行执行所有刷新任务
    await Promise.all(refreshTasks);

    console.log('分类切换完成：分类数量、当前数据和总数已更新');
  } catch (error: any) {
    // 如果是取消错误，不处理
    if (error.name === 'CanceledError' || error.message?.includes('请求已被取消')) {
      console.log('分类切换被取消，跳过错误处理');
      return;
    }

    console.error('分类切换失败:', error);
    // 如果并行刷新失败，至少确保分类数据加载成功
    try {
      await loadCategoryData(category);
    } catch (retryError: any) {
      // 重试时如果也是取消错误，同样跳过
      if (retryError.name === 'CanceledError' || retryError.message?.includes('请求已被取消')) {
        console.log('分类切换重试被取消，跳过错误处理');
        return;
      }
      console.error('重试加载分类数据失败:', retryError);
    }
  }
};

const handleCategoryTabClick = (categoryId: string, categoryData?: ClassificationItem) => {
  if (categoryData) {
    handleCategoryClick(categoryData);
  }
};

const toggleCategoryVisible = () => {
  showCategories.value = !showCategories.value;
  userToggledCategories.value = true; // 记录用户手动操作
};

const handleComiDropdownVisibleChange = (visible: boolean) => {
  // 处理下拉菜单可见性变化，现在状态由TableCardHeader组件内部管理
};

const handleCloseIntelligentReview = (hasProcessed: boolean) => {
  showIntelligentReview.value = false;

  if (hasProcessed) {
    // 一键处理完成后，执行完整的刷新：分类数量、当前数据、总数量
    handleRefresh();
  }
};

const handleProcessIntelligentReview = (data: any) => {
  console.log('处理智能预审数据:', data);
  // 一键处理完成后不关闭弹窗，让用户手动关闭
};

// 处理信息提取
const handleInfoExtraction = async () => {
  if (currentDataList.value.length === 0) {
    message.warn('没有待办数据可以提取信息');
    return;
  }

  let isCanceled = false;

  try {
    isInfoExtracting.value = true;

    // 提取当前待办列表的ID数组
    const pendingIds = currentDataList.value.map(item => {
      const { appId, handleParam } = item;
      switch (appId) {
        case '1': // 协同
        case '4': // 公文
        default:
          return handleParam?.summaryId;
        case '6': // 会议
          return handleParam?.meetingId;
      }
    });

    console.log('开始信息提取，待办ID数组:', pendingIds);

    // 调用信息提取接口
    const response: any = await getBatchSummary({ entityIdList: pendingIds });
    if (response?.code == 0 && response?.data) {
      // 解析content字段
      const extractedDataMap: Record<string, any> = {};

      if (response.data) {
        try {
          // content是JSON字符串，需要解析
          const contentData = response.data;

          if (Array.isArray(contentData)) {
            // 将数据按affairId存储到map中
            contentData.forEach((item: any) => {
              const { entityId, keyInformation } = item;
              const keywords = keyInformation ? JSON.parse(keyInformation) : {};

              if (entityId) {
                // 将keywords对象转换为数组格式
                const keywordsList = Object.values(keywords || {}).join(' | ');

                extractedDataMap[entityId] = {
                  entityId,
                  summary: keyInformation,
                  keywords: keywords || {}
                };
              }
            });
          }
        } catch (parseError) {
          console.error('解析content字段失败:', parseError);
        }
      }

      infoExtractionData.value = extractedDataMap;
      console.log('信息提取成功:', extractedDataMap);
    } else {
      console.error('信息提取失败:', response?.message || '未知错误');
      infoExtractionData.value = {};
    }
  } catch (error: any) {
    // 如果是取消错误，不处理
    if (error.name === 'CanceledError' || error.message?.includes('请求已被取消')) {
      console.log('信息提取被取消，跳过错误处理');
      isCanceled = true;
      return;
    }

    console.error('信息提取请求失败:', error);
    infoExtractionData.value = [];
  } finally {
    // 只有在请求未被取消的情况下才设置loading状态
    if (!isCanceled) {
      isInfoExtracting.value = false;
    }
  }
};

// 检查是否有操作正在进行
const hasActiveOperation = computed(() => {
  // 1. 检查弹窗是否打开
  const isPopoverOpen = globalPopoverManagerRef.value?.isVisible || false;

  // 2. 检查信息提取是否正在进行
  const isInfoExtracting_active = isInfoExtracting.value;

  // 3. 检查智能预审是否正在进行
  const isIntelligentReviewing = showIntelligentReview.value;

  // 4. 检查是否有加载状态
  const isCurrentlyLoading = isLoading.value;

  // 5. 检查是否有NextStep操作正在进行
  const hasNextStepActive = hasActiveNextStep.value;

  return isPopoverOpen || isInfoExtracting_active || isIntelligentReviewing || isCurrentlyLoading || hasNextStepActive;
});

// 处理刷新
const handleRefresh = async (params?: any) => {
  try {
    // 智能页码处理
    const shouldResetPage = currentDataList.value.length === 1 && pagination.value.current > 1;

    // 同时刷新分类数量和当前数据
    const refreshTasks = [];

    // 1. 刷新分类数据（包含各分类的数量统计）
    refreshTasks.push(refreshClassifications());

    // 2. 刷新当前分类的数据
    if (shouldResetPage) {
      // 当前页只有1条数据且不在第1页，重置到第1页
      pagination.value.current = 1;
      refreshTasks.push(loadCurrentCategoryData(1));
    } else {
      // 刷新当前页
      refreshTasks.push(loadCurrentCategoryData(pagination.value.current));
    }

    // 3. 刷新待办总数量
    refreshTasks.push(fetchTotalPendingCount());

    // 4. 触发会议数据刷新
    if (params && params.type !== 'refresh') {
      triggerMeetingRefresh();
    }

    // 并行执行所有刷新任务
    await Promise.all(refreshTasks);

    console.log('刷新完成：分类数量、当前数据和会议数据已更新');
  } catch (error: any) {
    // 如果是取消错误，不处理
    if (error.name === 'CanceledError' || error.message?.includes('请求已被取消')) {
      console.log('刷新被取消，跳过错误处理');
      return;
    }

    console.error('刷新失败:', error);
    // TODO: 添加错误提示
  }
};

// 对外暴露的方法
defineExpose({
  refresh: refreshData,
  reload: initializeData,
  getCurrentData: () => currentDataList.value,
  getClassifications: () => validClassifications.value
});

const getTodoData = () => {
  // 检查是否有操作正在进行，如果有则不执行刷新
  if (!hasActiveOperation.value) {
    handleRefresh({ type: 'refresh' });

    return;
  }
}

// 监听storage事件刷新待办数据
const refreshDataByStorage = (event: StorageEvent) => { 
  // 如果是协同新建或者待办处理或者会议，则刷新待办数据
  const refreshKeys = ['pendingDoneEvent', 'sendEvent', 'sendMeetingEvent', 'comiPendingEvent'];
  if (event.key && refreshKeys.includes(event.key)) {
    getTodoData();
  }
}

// 生命周期 - 支持 keep-alive 缓存
onMounted(async () => {
  sdkInstance.onRefresh('refreshData', getTodoData);
  sdkInstance.onRefresh('refreshTodoData', getTodoData);
  window.addEventListener('storage', refreshDataByStorage);
  await initializeData();
});

onActivated(() => {
});

onDeactivated(() => {
});

onUnmounted(() => {
  isComponentMounted.value = false;
  sdkInstance.offRefresh('refreshData', getTodoData);
  sdkInstance.offRefresh('refreshTodoData', getTodoData);
  window.removeEventListener('storage', refreshDataByStorage);
});

// 监听props变化（保持向后兼容性）
watch(() => props.data, (newData) => {
  if (newData && (newData as any).dataList) {
    const dataList = (newData as any).dataList || [];
    const safeDataList = dataList.map((item: any, index: number) => ({
      ...item,
      id: item.id || item.affairId || `props-id-${index}`,
      affairId: item.affairId || '',
      title: item.title || '无标题',
      rowButtons: item.rowButtons || []
    }));
    currentDataList.value = safeDataList;
  }
}, { deep: true, immediate: true });

watch(() => props.classifications, (newClassifications) => {
  if (newClassifications) {
    classificationsData.value = newClassifications;
  }
}, { deep: true, immediate: true });

// 监听组件显示状态变化
watch(() => shouldShowComponent.value, (isVisible) => {
  emit('visibility-change', isVisible);
}, { immediate: true });
</script>

<style scoped lang="less">
.table-card-wrapper {
  background-color: rgba(255, 255, 255, 0.65);
  backdrop-filter: blur(59px);
  -webkit-backdrop-filter: blur(59px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  font-family: PingFang SC;

  // 待办列表区域
  .todo-content {
    position: absolute;
    top: 16px;
    left: 0px;
    right: 0px;
    bottom: 10px;
    display: flex;
    flex-direction: column;
  }

  // 智能预审区域
  .intelligent-review-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
  }



  // 内容区域样式
  .table-card-content {
    position: relative;
    min-height: 0; // 重要：允许flex子项收缩

    .data-list-container {
      position: relative;
      height: 100%; // 设置明确高度
      overflow: hidden; // 外层容器隐藏溢出

      .data-list {
        height: 100%; // 占满父容器
        // padding-right: 4px; // 避免滚动条遮挡内容
        box-sizing: border-box; // 确保padding计算正确
        overflow-y: auto; // 纵向滚动
        overflow-x: hidden; // 横向隐藏
      }
    }

    // 加载状态样式
    .loading-container {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;

      .loading-spinner {
        color: rgba(0, 0, 0, 0.6);
        font-size: 14px;
      }
    }

    // 错误状态样式
    .error-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 200px;
      gap: 12px;

      .error-message {
        color: rgba(255, 77, 79, 1);
        font-size: 14px;
        text-align: center;
      }

      .retry-btn {
        padding: 6px 16px;
        border: 1px solid rgba(64, 169, 255, 1);
        border-radius: 4px;
        background: rgba(64, 169, 255, 0.1);
        color: rgba(64, 169, 255, 1);
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(64, 169, 255, 0.2);
        }
      }
    }

    // 信息提取加载状态样式
    .info-extraction-loading {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(6px);
      -webkit-backdrop-filter: blur(6px);

      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 60px;

        .loading-spinner {
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;
        }
      }
    }
  }

  // 悬浮分页组件样式
  .floating-pagination {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    height: 36px;
    z-index: 10;
    border-radius: 0 0 12px 12px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.5) 25.48%, rgba(255, 255, 255, 0) 100%);
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.5) 25.48%, rgba(255, 255, 255, 0) 100%);


    .pagination-backdrop {
      position: absolute;
      inset: 0;
    }

    .pagination-buttons {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 100%;
      padding: 0 10px 10px;
      gap: 8px;

      .pagination-current {
        font-size: 13px;
        font-weight: @font-weight-500;
        font-family: PingFang SC;
        color: #3F434D;
      }

      .pagination-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        transition: background-color 0.2s ease;
        border: 1px solid rgba(255, 255, 255, 0.4);

        .iconfont {
          font-size: 14px;
          color: rgba(142, 148, 162, 1);
        }

        &:disabled {
          cursor: not-allowed;

          .iconfont {
            color: rgba(216, 218, 223, 1);
          }
        }

        &:hover:not(:disabled) {
          background: #fff;

          .iconfont {
            color: rgba(111, 118, 134, 1);
          }
        }

        &:active:not(:disabled) {
          background: #fff;

          .iconfont {
            color: rgba(111, 118, 134, 1);
          }
        }

      }
    }
  }
}

// 分类标签滑动动画
.category-slide-enter-active,
.category-slide-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.category-slide-enter-from {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.category-slide-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.category-slide-enter-to,
.category-slide-leave-from {
  opacity: 1;
  max-height: 80px;
  transform: translateY(0);
}
</style>

<style>
.next-step-modal .ant-modal-confirm-content {
  margin-inline-start: 6px !important;
}
</style>
