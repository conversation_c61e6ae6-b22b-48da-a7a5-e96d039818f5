<template>
  <div class="cardWithoutLabel">
    <PluginDesction :title="cardData?.data?.pluginName" :total="cardData?.data?.pageInfo?.total" />
    <div class="item-box pb-16px">
      <ListItem v-for="(item, index) in cardData?.data?.result" :key="index" :item="item" :styles="cardData?.data?.cardStyles" />
    </div>
    <CheckButton v-if="cardData?.data?.pageInfo?.total > 5" :type="cardData?.data?.moreButtonType" @handleClick="handleClickMore" />
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, PropType } from "vue"
import type { ChatItem } from '@/types/index'
import { CardResponse } from '@/components/commonCard/types'
import PluginDesction from "../pluginDesction.vue"
import ListItem from './list-item.vue'
import CheckButton from '../check-button.vue'

const props = defineProps({
   chatData: {
     type: Object as PropType<ChatItem>,
     default: () => {}
   },
   cardData: {
    type: Object as PropType<CardResponse>,
    default: () => {}
   }
})

const handleClickMore = () => {
  console.log('=====> cardData', props.cardData)
}
</script>
<style lang="less" scoped>

</style>
