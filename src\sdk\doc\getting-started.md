# 快速开始

> COMI Assistant SDK v2.0 快速入门指南，从零开始构建智能助手集成

本指南将帮助您快速上手 COMI Assistant SDK v2.0，从环境配置到实现完整功能。

## 🚀 快速上手

### 1. 在Vue项目中使用

#### 安装和初始化

```typescript
// main.ts
import { createApp } from 'vue'
import App from './App.vue'
import { initBusinessSdk } from '@/sdk/business/index.ts'

const app = createApp(App)

// 初始化SDK
const sdkInstance = initBusinessSdk({
  defaultAssistId: '5605278465501839487',  // 默认助手ID
  autoInit: true,                          // 自动初始化
  hideInputBoxAssistIds: ['assist1']       // 隐藏输入框的助手ID
})

// 提供给全局使用
app.provide('sdkInstance', sdkInstance)

app.mount('#app')
```

#### 在组件中使用

```vue
<template>
  <div class="comi-demo">
    <h2>COMI助手集成演示</h2>
    
    <div class="actions">
      <button @click="sendMessage">发送消息</button>
      <button @click="redirectAssist">切换助手</button>
      <button @click="generateOaUrl">生成OA链接</button>
    </div>
    
    <div class="results">
      <p v-if="oaUrl">生成的URL: <a :href="oaUrl" target="_blank">{{ oaUrl }}</a></p>
    </div>
  </div>
</template>

<script setup>
import { ref, inject } from 'vue'

// 注入SDK实例
const sdk = inject('sdkInstance')
const oaUrl = ref('')

// 发送消息
const sendMessage = () => {
  sdk.sendMsg('请帮我分析本周工作总结')
}

// 重定向助手
const redirectAssist = () => {
  sdk.redirectAssistId('5605278465501839487', true, () => {
    console.log('重定向完成')
    sdk.sendMsg('您好，我是数据分析助手', { isHide: true })
  })
}

// 生成OA系统链接
const generateOaUrl = () => {
  oaUrl.value = sdk.getOaUrl({
    appType: '1',        // 协同应用
    linkId: 'DOC_12345', // 文档ID
    clientType: '1'      // Web客户端
  })
}
</script>

<style scoped>
.comi-demo {
  padding: 20px;
}

.actions button {
  margin: 10px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.actions button:hover {
  background: #0056b3;
}

.results {
  margin-top: 20px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}
</style>
```

### 2. 在第三方系统中使用

#### HTML集成方式

```html
<!DOCTYPE html>
<html>
<head>
    <title>ERP系统 - COMI智能助手</title>
</head>
<body>
    <div id="app">
        <h1>ERP业务系统</h1>
        <button onclick="askComiForHelp()">🤖 询问COMI助手</button>
        <button onclick="analyzeData()">📊 数据分析</button>
    </div>
    
    <!-- 引入COMI SDK -->
    <script src="https://your-domain.com/static/js/comi-entry.js"></script>
    
    <script>
        let comiSdk = null;
        
        // 等待SDK就绪 - v2.0智能回调执行
        window.COMI_ASSISTANT.preSdk.openDrawer(() => {
            comiSdk = window.COMI_ASSISTANT.sdk;
            console.log('COMI助手已就绪，所有功能可用');
            
            // 发送欢迎消息
            comiSdk.sendMsg('欢迎使用ERP智能助手！');
        });
        
        // 业务功能
        function askComiForHelp() {
            if (!comiSdk) {
                alert('COMI助手正在加载中...');
                return;
            }
            
            comiSdk.sendMsg('我需要帮助处理业务问题');
        }
        
        function analyzeData() {
            if (!comiSdk) return;
            
            // 重定向到数据分析助手
            comiSdk.redirectAssistId('数据分析助手ID', true, () => {
                comiSdk.sendMsg('请分析今日销售数据：订单100单，总额￥50万', { 
                    isHide: true 
                });
            });
        }
    </script>
</body>
</html>
```

### 3. 核心概念理解

#### v2.0的智能回调执行

```javascript
// v2.0不再需要setTimeout，而是基于事件驱动
window.COMI_ASSISTANT.preSdk.openDrawer(() => {
    // 这个回调在SDK完全就绪后执行，包括：
    // ✅ 核心SDK初始化完成
    // ✅ 业务SDK初始化完成  
    // ✅ 所有方法绑定完成 (getOaUrl, handleTagClick等)
    // ✅ comiEventBus集成完成
    
    const sdk = window.COMI_ASSISTANT.sdk;
    
    // 现在可以安全使用所有功能
    sdk.sendMsg('SDK完全就绪');
    sdk.getOaUrl({ appType: '1', linkId: '123', clientType: '1' });
});
```

#### 防重复执行机制

```javascript
// v2.0自动防止回调重复执行
// 以下代码即使多次调用，回调也只会执行一次

// 第一次调用
window.COMI_ASSISTANT.preSdk.openDrawer(() => {
    console.log('只会执行一次 - 调用1');
});

// 第二次调用（会被智能去重）
window.COMI_ASSISTANT.preSdk.openDrawer(() => {
    console.log('只会执行一次 - 调用2');
});
```

## 🔧 配置选项

### BusinessConfig 配置（v2.0）

```typescript
interface BusinessConfig {
  defaultAssistId?: string;           // 默认助手ID
  hideInputBoxAssistIds?: string[];   // 需要隐藏输入框的助手ID列表
  defaultSendMsg?: boolean;           // 是否默认发送消息
  autoInit?: boolean;                 // 是否自动初始化
}

const sdk = initBusinessSdk({
  defaultAssistId: '5605278465501839487',
  hideInputBoxAssistIds: ['assist1', 'assist2'],
  defaultSendMsg: true,
  autoInit: true
});
```

### CoreConfig 配置（v2.0新增）

```typescript
interface CoreConfig {
  timeout?: number;                   // 超时时间（毫秒）
  systemId?: string;                  // 系统标识
  debug?: boolean;                    // 调试模式
}

const sdk = initBusinessSdk({
  // 业务配置
  defaultAssistId: '5605278465501839487'
}, {
  // 核心配置
  timeout: 15000,
  systemId: 'my-erp-system',
  debug: process.env.NODE_ENV !== 'production'
});
```

## 🌟 主要功能演示

### 1. 消息通信

```typescript
// 基础消息发送
sdk.sendMsg('Hello COMI!');

// 带选项的消息发送
sdk.sendMsg('重要通知', {
  isHide: true,           // 隐藏输入框
  priority: 'high',       // 高优先级
  timestamp: Date.now()
});

// 预设消息内容
sdk.setSendMsg('预设的消息内容', {
  autoSend: false,        // 不自动发送
  placeholder: true       // 作为占位符
});

// 推送自定义卡片
sdk.pushCustomCard({
  title: '业务报告',
  content: '今日销售额：¥100,000',
  type: 'info',
  actions: [
    { text: '查看详情', action: 'viewReport' },
    { text: '生成PDF', action: 'generatePDF' }
  ]
});
```

### 2. 助手控制

```typescript
// 基础重定向
sdk.redirectAssistId('5605278465501839487');

// 带回调的重定向
sdk.redirectAssistId('5605278465501839487', true, () => {
  console.log('重定向完成');
  sdk.sendMsg('您好，我是专业分析助手');
});

// 链式操作：重定向 → 发送消息
sdk.redirectAssistId('5605278465501839487', true, () => {
  sdk.sendMsg('请分析以下数据：销售额￥50万，订单200单', { 
    isHide: true 
  });
});
```

### 3. OA系统集成（v2.0新功能）

```typescript
// 生成协同应用链接
const collabUrl = sdk.getOaUrl({
  appType: '1',              // 协同应用
  linkId: 'DOC_123456',      // 文档ID
  clientType: '1',           // Web客户端
  params: {
    readonly: false,         // 可编辑
    version: 'latest'        // 最新版本
  }
});

// 生成公文链接
const officialUrl = sdk.getOaUrl({
  appType: '4',              // 公文
  linkId: 'OFF_789012',      // 公文ID
  clientType: '1'            // Web客户端
});

// 生成新闻链接
const newsUrl = sdk.getOaUrl({
  appType: '8',              // 新闻
  linkId: 'NEWS_345678',     // 新闻ID
  clientType: '2'            // 移动端
});

console.log('协同链接:', collabUrl);
console.log('公文链接:', officialUrl);
console.log('新闻链接:', newsUrl);
```

### 4. 界面控制

```typescript
// 控制输入框显示/隐藏
sdk.hideInputBox(true);     // 隐藏
sdk.hideInputBox(false);    // 显示

// 界面模式控制
sdk.expand();               // 全屏模式
sdk.collapse();             // 普通模式
sdk.close();                // 关闭界面

// 下次不再显示
await sdk.hideNextTime();
console.log('已设置下次不再显示');
```

### 5. 状态查询

```typescript
// 获取嵌套关系
const iscomi = sdk.getISCOMI();
if (iscomi === true) {
  console.log('COMI嵌入第三方系统');
} else if (iscomi === false) {
  console.log('第三方系统嵌入COMI');
}

// 获取系统状态
const status = sdk.getSystemStatus();
console.log('系统状态:', status);

// 获取调试信息
const debugInfo = sdk.getDebugInfo();
console.log('调试信息:', debugInfo);

// 检查初始化状态
if (sdk.isSDKInitialized()) {
  console.log('SDK已完全初始化');
}
```

## 🎯 实际应用场景

### 场景1：销售管理系统集成

```vue
<template>
  <div class="sales-dashboard">
    <h2>销售管理系统</h2>
    
    <div class="sales-summary">
      <div class="metric">
        <span>今日销售额</span>
        <strong>¥{{ todaySales }}</strong>
      </div>
      <div class="metric">
        <span>订单数量</span>
        <strong>{{ orderCount }}单</strong>
      </div>
    </div>
    
    <div class="comi-actions">
      <button @click="analyzeSalesData" class="ai-btn">
        🤖 AI销售分析
      </button>
      <button @click="generateReport" class="ai-btn">
        📊 生成销售报告
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, inject } from 'vue'

const sdk = inject('sdkInstance')
const todaySales = ref(156800)
const orderCount = ref(89)

// AI销售数据分析
const analyzeSalesData = () => {
  sdk.redirectAssistId('销售分析助手ID', true, () => {
    const analysisPrompt = `请分析今日销售数据：
销售额：¥${todaySales.value}
订单数：${orderCount.value}单
平均客单价：¥${Math.round(todaySales.value / orderCount.value)}

请分析：
1. 销售趋势
2. 客单价水平
3. 改进建议`

    sdk.sendMsg(analysisPrompt, { isHide: true })
  })
}

// 生成销售报告
const generateReport = () => {
  sdk.redirectAssistId('报告生成助手ID', true, () => {
    sdk.sendMsg('请生成今日销售报告，包含数据分析和图表', { 
      isHide: true 
    })
  })
}
</script>
```

### 场景2：客服系统集成

```typescript
class CustomerServiceBot {
  private sdk: any;
  
  constructor(sdkInstance: any) {
    this.sdk = sdkInstance;
  }
  
  // 处理客户咨询
  async handleCustomerInquiry(customerData: any, inquiry: string) {
    // 重定向到客服助手
    this.sdk.redirectAssistId('客服助手ID', true, () => {
      const context = `客户信息：
姓名：${customerData.name}
等级：${customerData.level}
历史订单：${customerData.orderHistory}

客户咨询：${inquiry}

请提供专业的客服解答。`;

      this.sdk.sendMsg(context, { isHide: true });
    });
  }
  
  // 推送客服工单卡片
  pushServiceTicket(ticketData: any) {
    this.sdk.pushCustomCard({
      title: '新工单提醒',
      content: `工单编号：${ticketData.id}
客户：${ticketData.customer}
问题类型：${ticketData.type}
紧急程度：${ticketData.priority}`,
      type: 'warning',
      actions: [
        { text: '立即处理', action: 'processTicket', data: ticketData },
        { text: '查看详情', action: 'viewDetails', data: ticketData }
      ]
    });
  }
}

// 使用示例
const customerService = new CustomerServiceBot(sdk);

// 处理客户咨询
customerService.handleCustomerInquiry({
  name: '张先生',
  level: 'VIP',
  orderHistory: '近6个月10单'
}, '我的订单什么时候能到货？');
```

### 场景3：文档管理集成

```typescript
class DocumentManager {
  private sdk: any;
  
  constructor(sdkInstance: any) {
    this.sdk = sdkInstance;
  }
  
  // 打开文档并发送AI分析请求
  openDocumentForAnalysis(docId: string, docTitle: string) {
    // 生成文档链接
    const docUrl = this.sdk.getOaUrl({
      appType: '3',           // 文档系统
      linkId: docId,
      clientType: '1',
      params: {
        mode: 'view',
        highlight: true
      }
    });
    
    // 重定向到文档分析助手
    this.sdk.redirectAssistId('文档分析助手ID', true, () => {
      const prompt = `请分析文档《${docTitle}》：
文档链接：${docUrl}

请提供：
1. 文档要点总结
2. 关键信息提取
3. 相关建议`;

      this.sdk.sendMsg(prompt, { isHide: true });
    });
  }
  
  // 批量文档处理
  batchProcessDocuments(docList: any[]) {
    this.sdk.redirectAssistId('批量处理助手ID', true, () => {
      const docInfo = docList.map(doc => 
        `- ${doc.title} (${doc.type}, ${doc.size})`
      ).join('\n');
      
      this.sdk.sendMsg(`请帮我批量处理以下文档：\n${docInfo}\n\n处理要求：提取关键信息并生成汇总报告`, { 
        isHide: true 
      });
    });
  }
}
```

## ⚡ 性能优化建议

### 1. SDK初始化优化

```typescript
// 推荐：延迟初始化，按需加载
class SDKManager {
  private sdk: any = null;
  private initPromise: Promise<any> | null = null;
  
  async getSDK() {
    if (this.sdk) return this.sdk;
    
    if (!this.initPromise) {
      this.initPromise = this.initializeSDK();
    }
    
    return this.initPromise;
  }
  
  private async initializeSDK() {
    const { initBusinessSdk } = await import('@/sdk/business/index.ts');
    
    this.sdk = initBusinessSdk({
      defaultAssistId: '5605278465501839487',
      autoInit: true
    });
    
    await this.sdk.waitForInitialization();
    return this.sdk;
  }
}

// 使用
const sdkManager = new SDKManager();

// 需要时才初始化
const sdk = await sdkManager.getSDK();
sdk.sendMsg('Hello COMI!');
```

### 2. 事件监听优化

```typescript
// 推荐：使用防抖和节流
import { debounce, throttle } from 'lodash-es';

// 防抖处理消息发送
const debouncedSendMsg = debounce((message: string) => {
  sdk.sendMsg(message);
}, 300);

// 节流处理状态更新
const throttledStatusUpdate = throttle(() => {
  const status = sdk.getSystemStatus();
  console.log('状态更新:', status);
}, 1000);
```

## 🐛 故障排除

### 常见问题及解决方案

#### 1. SDK未加载

```javascript
// 检查COMI_ASSISTANT是否存在
if (typeof window.COMI_ASSISTANT === 'undefined') {
  console.error('COMI SDK未正确加载，请检查：');
  console.error('1. comi-entry.js是否正确引入');
  console.error('2. 脚本路径是否正确');
  console.error('3. 网络连接是否正常');
  return;
}
```

#### 2. 回调不执行

```javascript
// v2.0智能检测，如果还是不执行，检查：
window.COMI_ASSISTANT.preSdk.openDrawer(() => {
  console.log('回调执行了');
  
  // 检查SDK是否真正就绪
  if (!window.COMI_ASSISTANT.sdk) {
    console.error('SDK未就绪');
    return;
  }
  
  const sdk = window.COMI_ASSISTANT.sdk;
  
  // 检查关键方法是否存在
  if (typeof sdk.sendMsg !== 'function') {
    console.error('sendMsg方法未绑定');
    return;
  }
  
  // 使用SDK
  sdk.sendMsg('测试消息');
});
```

#### 3. getOaUrl方法未定义

```javascript
// v2.0会自动绑定getOaUrl，如果未定义：
if (typeof sdk.getOaUrl !== 'function') {
  console.error('getOaUrl方法未绑定，可能原因：');
  console.error('1. window.COMI_ASSISTANT.getOaUrl不存在');
  console.error('2. SDK初始化未完成');
  console.error('3. 自动绑定失败');
  
  // 手动检查外部方法
  if (typeof window.COMI_ASSISTANT.getOaUrl === 'function') {
    console.log('外部getOaUrl存在，但未自动绑定');
  }
}
```

### 调试工具

```typescript
// 获取完整调试信息
const debugInfo = sdk.getDebugInfo();
console.log('SDK调试信息:', debugInfo);

// 检查系统状态
const status = sdk.getSystemStatus();
console.log('系统状态:', status);

// 检查初始化状态
console.log('SDK初始化状态:', {
  isInitialized: sdk.isSDKInitialized(),
  hasGetOaUrl: typeof sdk.getOaUrl === 'function',
  hasHandleTagClick: typeof sdk.handleTagClick === 'function',
  iscomi: sdk.getISCOMI()
});
```

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **查看控制台错误信息** - 大多数问题都会有详细的错误日志
2. **使用调试工具** - 通过 `getDebugInfo()` 获取详细信息
3. **检查初始化顺序** - 确保在SDK就绪后调用功能方法
4. **参考示例代码** - 本文档提供了大量实际使用示例

---

**智能产品部-应用部-前端团队**
