<template>
  <div id="guide-step5" class="portal-message">
    <div class="header">
      <div class="text">重要提醒</div>
      <div class="read-button"  @click="setMessageRead">
        <div class="setting-read">一键已读</div>
      </div>
    </div>
    <div class="message-container" ref="messageContainer" @wheel="handleScroll">
      <div v-if="isShowMarkdown" class="content" >
        <!-- <ComiMarkdown
          :content="meetingMarkdownContent"
          class="markdown-content"
        /> -->
        <AiAnswer :transAiInfo="transAiInfo" :lastIndex="11" :isColumn="true" />
      </div>
      <AiIdentify class="loading" v-if="showLoading" :loadingStatus="true"></AiIdentify>
      <PortalEmptyColumn v-if="showEmpty || showError" :image="stateObj.imageUrl" :text="stateObj.text" :width="80" class="w-full flex-1"/>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ref, computed, nextTick, onMounted, inject, onUnmounted} from 'vue';
import {getStreamAgentResult} from '@/api/common/index';
import requests from '@/api/v5/index';
import { generateSecure16DigitNumber } from '@/utils/uuid';
import '@seeyon/seeyon-comi-plugins-library/dist/seeyon-comi-plugins-library.css';
import { message } from 'ant-design-vue';
import AiIdentify from '@/components/aiIdentify/index.vue';
import emptyImage from '@/assets/imgs/no-data.png';
import errorImage from '@/assets/imgs/error.png';
import AiAnswer from '@/components/aiAnswer/index.vue';
import cardInstance from '@/stores/card';
import { getAssistIdByCode } from '@/api/home';
import PortalEmptyColumn from '@/components/portalEmptyColumn/index.vue';

const meetingMarkdownContent = ref({});
const transAiInfo = ref({});
const unreadMessage = ref(0);
const lastMessageId = ref('');
const showContent = ref(false);
const showEmpty = ref(false);
const showError = ref(false);
const messageContainer = ref<HTMLElement | null>(null);
let allowScroll = true;
const sdkInstance = inject('sdkInstance') as any;

const stateObj = computed(()=>{
  if(showEmpty.value) {
    return {
      imageUrl: emptyImage,
      text: '暂无数据哦～'
    };
  }else if(showError.value) {
    return {
      imageUrl: errorImage,
      text: '加载失败～'
    };
  }
  return {
    imageUrl: '',
    text: ''
  };
});
let messageList:any = [];
const isShowMarkdown = computed(()=>{
  return showContent.value && !showEmpty.value && !showError.value;
});
const showLoading = computed(()=>{
  return !showContent.value && !showEmpty.value && !showError.value;
})

// localStorage 缓存相关函数
const MESSAGE_CACHE_KEY = 'portal_message_cache';

function checkMessageCache(messageId: string) {
  if (!messageId) return null;

  try {
    const cachedStr = localStorage.getItem(MESSAGE_CACHE_KEY);
    if (!cachedStr || cachedStr.indexOf('无法获取消息，请联系系统管理员！') !== -1) return null;

    const cached = JSON.parse(cachedStr);
    if (cached.messageId === messageId) {
      return cached.data;
    }
  } catch (error) {
    console.error('读取消息缓存失败:', error);
    localStorage.removeItem(MESSAGE_CACHE_KEY);
  }

  return null;
}

function saveMessageCache(messageId: string, data: any) {
  if (!messageId || !data) return;

  try {
    // 删除之前的缓存，保存新的缓存
    const cacheData = {
      messageId: messageId,
      data: data,
      timestamp: Date.now()
    };
    localStorage.setItem(MESSAGE_CACHE_KEY, JSON.stringify(cacheData));
  } catch (error) {
    console.error('保存消息缓存失败:', error);
  }
}

async function messageInfo (enforceRefresh = false) {
  await getUnreadMessage();

  // 检查localStorage缓存
  const cachedData = checkMessageCache(lastMessageId.value);
  if (cachedData && !enforceRefresh) {
    // 使用缓存数据
    transAiInfo.value = cachedData;
    showContent.value = true;
    return;
  }
  getMessageContent();
}

const getMessageContent = ()=>{
  const chatSessionId = generateSecure16DigitNumber();

  // 对消息的流式接口做一些调整
  const params = {
    "agentCode": "message_for_portal",
    "chatSessionId": chatSessionId,
    "input": JSON.stringify(messageList),
  }
    transAiInfo.value = {
      "data": {
        "cardData": [],
        "status": 1,
        "isHide": false,
        "processData": [
            {
              "expand": true,
              "type": "think",
              "loading": true,
              "title": "意图识别中",
              "content": ""
            }
        ]
    }
  }
  showContent.value = true;
  allowScroll = true;
  setTimeout(()=>{
    cardInstance.getSteamMessage("查询我的消息", params, (res)=>{
      transAiInfo.value = res;
      // showContent.value = true;
      if(res.data && res.data.status === 3){
        // 存储到localStorage
        saveMessageCache(lastMessageId.value, res);
      }
      if(allowScroll) {
        scrollToBottom();
      }
    },(err)=>{
      showError.value = true;
      localStorage.removeItem(MESSAGE_CACHE_KEY);
    });
  }, 500)
}

function getUnreadMessage() {
  return requests.getUnreadMessageCountAndLastId().then((res:any) => {
    if(res && res.code === '0' && res.data){
      unreadMessage.value = Number(res.data.total);
      const resultData = res.data?.data || [];
      const currentLastMessageId = resultData[0]?.id || '';
      messageList = resultData.map((item:any) => {
        return {
          content: item.messageContent,
          category: item.messageCategoryName,
          sender: item.senderName,
          creationDate: item.creationDate
        }
      });
      lastMessageId.value = currentLastMessageId;
      if(unreadMessage.value === 0) {
        showEmpty.value = true;
      }else {
        showContent.value = false;
        showEmpty.value = false;
        showError.value = false;
      }
    }else {
      showError.value = true;
    }
  }, (err)=>{
    showError.value = true;
  })
}
async function setMessageRead() {
  if(Number(unreadMessage.value) === 0) {
    message.warning('暂时没有重要提醒，无法一键已读');
    return;
  }
  requests.messaegAllRead().then(async (res:any) => {
    if(Number(res.code) === 0) {
      message.success(`已经一键处理${unreadMessage.value}条重要提醒`);
      // 清除缓存，因为消息状态已经改变
      localStorage.removeItem(MESSAGE_CACHE_KEY);
      await messageInfo();
    }else {
      message.error(res.message);
    }
  })
}

function scrollToBottom() {
  nextTick(() => {
    const scrollContainer = messageContainer.value;
    scrollContainer?.scrollTo({
      top: scrollContainer.scrollHeight + 40,
      behavior: 'smooth',
    });
  });
}

function handleScroll() {
  const container = messageContainer.value;
  if(container) {
    const isAtBottom = container.scrollTop + container.clientHeight >=
    container.scrollHeight - 5; //允许5px的误差
    if(isAtBottom) {
      allowScroll = true;
    }else {
      allowScroll = false;
    }
  }
}

const handleRefreshData = (data: any) => {
  messageInfo(true);
};

onMounted(()=>{
  messageInfo();
  sdkInstance.onRefresh('refreshData',handleRefreshData);
})

onUnmounted(()=>{
  sdkInstance.offRefresh('refreshData',handleRefreshData);
})
</script>

<style scoped lang="less">
.portal-message {
  display: flex;
  padding: 16px;
  justify-content: flex-end;
  border-radius: 12px;
  border: 1.5px solid rgba(255, 255, 255, 0.80);
  background: rgba(255, 255, 255, 0.65);
  backdrop-filter: blur(29px);
  flex-direction: column;

  :deep(.ai_answer_box){
    background-color: transparent !important;
    padding: 0;
  }
  :deep(.ai_answer_item){
    background-color: transparent !important;
  }
  .header {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    .text {
      color: #000;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: @font-weight-500;
      line-height: 24px;
    }
    .setting-read {
      padding: 3px 8px;
      cursor: pointer;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: @font-weight-500;
      line-height: 22px;
      background: linear-gradient(117deg, #4AEAFF 5.37%, #97F0FA 9.48%, #4FE1F7 16.53%, #566BFF 55.05%, #4379FF 80.71%);
      background-clip: text;
      // -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
      color: transparent;
    }
    .read-button {
      background: rgba(237, 242, 252, 1);
      border-radius: 8px;
    }
    .read-button:hover {
      background: #FFFFFF;
    }

  }
  .sarmmary {
    display: flex;
    margin-bottom: 8px;
    .message-start {
      width: 16px;
      margin-right: 4px;
    }
  }
  .message-container {
    color:rgba(0, 0, 0, 0.90);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: @font-weight-400;
    line-height: 22px;
    flex: 1;
    min-height: 50px;
    overflow: auto;
    position: relative;
    .content {
      // overflow: hidden;
      // text-overflow: ellipsis;
      // display: -webkit-box;
      // -webkit-box-orient: vertical;
      // -webkit-line-clamp: 3;

    }
    .loading {
      width: 100%;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
    .ai_item {
      min-width: 231px;
    }
  }
  .message-empty {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .no-data {
      width: 80px;
    }
    .no-data-text {
      margin-top: 12px;
      color: #8E94A2;
      font-size: 12px;
      font-weight: @font-weight-500;
      text-align: center;
    }
  }
}
</style>

<style lang="less">
.message-container {
  .data-stream h1,
  .data-stream h2,
  .data-stream h3,
  .data-stream h4,
  .data-stream h5,
  .data-stream h6{
    overflow: initial;
    -webkit-line-clamp: initial;
    text-overflow: initial;
  }
}
</style>
