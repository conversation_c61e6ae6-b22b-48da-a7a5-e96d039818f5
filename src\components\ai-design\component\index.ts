import type { App } from 'vue';
import { defineAsyncComponent } from 'vue';
import * as components from './components';

const componentsMap = {
  "WorkInstructionCardColumn": defineAsyncComponent(() => import('@/components/workInstructionCardColumn/index.vue')),
  "TodoCardColumn": defineAsyncComponent(() => import('@/components/todoCardColumn/index.vue')),
  "MeetingCardColumn": defineAsyncComponent(() => import('@/components/meetingCardColumn/index.vue')),
  "ComiExternalCardIframe": defineAsyncComponent(() => import('remote_app/ComiExternalCardIframe')),
  "BapExternalBiCard": defineAsyncComponent(() => import('remote_app/BapExternalBiCard')),
}

export const install = function (app: App) {
  // 标准栏目组件
  Object.keys(components).forEach(key => {
    const component = (components as Record<string, any>)[key];
    if (component.install) {
      app.use(component);
    }
  });
  // 旧栏目组件
  Object.keys(componentsMap).forEach(key => {
    const component = (componentsMap as Record<string, any>)[key];
    app.component(key, component);
  });
  // app.use(cssinjs.StyleProvider);
  // app.config.globalProperties.$message = components.message;
  // app.config.globalProperties.$notification = components.notification;
  // app.config.globalProperties.$info = components.Modal.info;
  // app.config.globalProperties.$success = components.Modal.success;
  // app.config.globalProperties.$error = components.Modal.error;
  // app.config.globalProperties.$warning = components.Modal.warning;
  // app.config.globalProperties.$confirm = components.Modal.confirm;
  // app.config.globalProperties.$destroyAll = components.Modal.destroyAll;
  return app;
};

// export { version, cssinjs };

export default {
  version: '1.0.0',
  install,
};
