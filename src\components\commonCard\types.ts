export interface CardResponse {
  code: string
  message: string
  data: CardData
}

export interface CardData {
  pluginName: string
  pluginKey: string
  cardType: number
  cardStyles: any
  disabled: boolean
  moreButtonType: number // 为0时，超过5条出现"查看更多 > " 点击后跳转到具体业务页面；为1时，超过5条出现“查看更多 v” 点击后追加5条数据； 为3时始终不展示
  pageInfo: {
    pageNumber: number
    pageSize: number
    pages: number
    total: number
    needTotal: boolean
  }
  result: CardItem[] | CardItem
}

export interface CardItem {
  originApiInfo: {
    [key: string]: any
  }
  renderInfo: {
    [key: string]: any
  }
  buttons?: {
    [key: string]: any
  }
  styles: {
    [key: string]: any
  }
}
