/* .driver-active .driver-overlay,
.driver-active * {
  pointer-events: none;
} */
/* 禁止点击内部元素点击 */
.driver-overlay .driver-overlay path{
  pointer-events: none !important;
}

/* .driver-active .driver-active-element,
.driver-active .driver-active-element *,
.driver-popover,
.driver-popover * {
  pointer-events: auto;
} */
@keyframes animate-fade-in {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.driver-fade .driver-overlay {
  animation: animate-fade-in 0.2s ease-in-out;
}
/* .driver-fade .driver-popover {
  animation: animate-fade-in 0.2s ease-in-out;
} */
.driver-popover {
  all: unset;
  box-sizing: border-box;
  color: #2d2d2d;
  margin: 0;
  padding: 12px;
  border-radius: 5px;
  min-width: 250px;
  max-width: 400px;
  box-shadow: 0 1px 10px #0006;
  z-index: 99999;
  position: fixed;
  top: 0;
  right: 0;
  background-color: #fff;
}
.driver-popover * {
  font-family: <PERSON><PERSON>,"PingFang SC","Microsoft YaHei",Helvetica,sans-serif,SimSun;
}
.driver-popover-title {
  font: 19px / normal sans-serif;
  font-weight: 700;
  display: block;
  position: relative;
  line-height: 1.5;
  zoom: 1;
  margin: 0;
}
.driver-popover-close-btn {
  all: unset;
  position: absolute;
  top: 0;
  right: 0;
  width: 32px;
  height: 28px;
  cursor: pointer;
  font-size: 22px;
  color: #858585;
  z-index: 1;
  text-align: center;
  transition: color;
  transition-duration: 0.2s;
  padding-right: 3px;
  padding-top: 4px;
}
.driver-popover-close-btn:hover {
  color: #2d2d2d;
}
.driver-popover-title[style*="block"] + .driver-popover-description {
  margin-top: 5px;
}
.driver-popover-description {
  margin-bottom: 0;
  font: 14px / normal sans-serif;
  line-height: 1.5;
  font-weight: 400;
  zoom: 1;
  word-break: break-all;
}
.driver-popover-footer {
  margin-top: 13px;
  text-align: right;
  zoom: 1;
  display: flex;
  align-items: end;
  justify-content: space-between;
}
.driver-popover-progress-text {
  font-size: 13px;
  font-weight: 400;
  line-height: 1;
  color: #a2a2a2;
  zoom: 1;
}
.driver-popover-footer button {
  all: unset;
  display: inline-block;
  box-sizing: border-box;
  padding: 3px 7px;
  text-decoration: none;
  text-shadow: 1px 1px 0 #fff;
  background-color: #fff;
  color: #2d2d2d;
  font: 12px / normal sans-serif;
  cursor: pointer;
  outline: 0;
  zoom: 1;
  line-height: 1.3;
  border: 1px solid #ccc;
  border-radius: 3px;
}
.driver-popover-footer .driver-popover-btn-disabled {
  opacity: 0.5;
  pointer-events: none;
}
:not(body):has(> .driver-active-element) {
  overflow: hidden !important;
}
.driver-popover-footer button:hover {
  background-color: #f7f7f7;
}
.driver-popover-navigation-btns {
  display: flex;
  flex-grow: 1;
  justify-content: flex-end;
}
.driver-popover-navigation-btns button + button {
  margin-left: 14px;
}
.driver-popover-arrow {
  content: "";
  position: absolute;
  border: 5px solid #fff;
}
.driver-popover-arrow-side-over {
  display: none;
}
.driver-popover-arrow-side-left {
  left: 100%;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-top-color: transparent;
}
.driver-popover-arrow-side-right {
  right: 100%;
  border-left-color: transparent;
  border-bottom-color: transparent;
  border-top-color: transparent;
}
.driver-popover-arrow-side-top {
  top: 100%;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
}
.driver-popover-arrow-side-bottom {
  bottom: 100%;
  border-left-color: transparent;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: #e4efff !important;
}
.driver-popover-arrow-side-center {
  display: none;
}
.driver-popover-arrow-side-left.driver-popover-arrow-align-start,
.driver-popover-arrow-side-right.driver-popover-arrow-align-start {
  top: 15px;
}
.driver-popover-arrow-side-top.driver-popover-arrow-align-start,
.driver-popover-arrow-side-bottom.driver-popover-arrow-align-start {
  left: 15px;
}
.driver-popover-arrow-align-end.driver-popover-arrow-side-left,
.driver-popover-arrow-align-end.driver-popover-arrow-side-right {
  bottom: 15px;
}
.driver-popover-arrow-side-top.driver-popover-arrow-align-end,
.driver-popover-arrow-side-bottom.driver-popover-arrow-align-end {
  right: 15px;
}
.driver-popover-arrow-side-left.driver-popover-arrow-align-center,
.driver-popover-arrow-side-right.driver-popover-arrow-align-center {
  top: 50%;
  margin-top: -5px;
}
.driver-popover-arrow-side-top.driver-popover-arrow-align-center,
.driver-popover-arrow-side-bottom.driver-popover-arrow-align-center {
  left: 50%;
  margin-left: -5px;
}
.driver-popover-arrow-none {
  display: none;
}
/* 主题 */
.driver-popover.driverjs-theme {
  background-image: linear-gradient(180deg, #e4efff 0%, #ffffff 100%);
  border-radius: 8px;
  color: #333333;
  max-width: 80%;
}

.driver-popover.driverjs-theme .driver-popover-title {
  font-size: 20px;
}
/* .driver-popover.driverjs-theme .driver-popover-progress-text {
  display: none !important;
} */
.driver-popover.driverjs-theme .driver-popover-title {
  font-weight: bold;
  font-size: 14px;
  color: #121212;
}
.driver-popover.driverjs-theme .driver-popover-description {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 22px;
}

.driver-popover.driverjs-theme .driver-popover-description ul {
  list-style: circle;
}

.driver-popover.driverjs-theme .driver-popover-description ul li{
  list-style: inside;
}
.driver-popover.driverjs-theme .driver-popover-description ul li ::marker{
  
}

.driver-popover.driverjs-theme .driver-popover-footer button {
  text-align: center;
  background-color: rgba(67, 121, 255, 1);
  color: #ffffff;
  text-shadow: none;
  font-size: 12px;
  padding: 4px 12px;
  line-height: 28px;
  height: 28px;
  border-radius: 4px;
  border: 0;
  padding: 0 16px;
}
.driver-popover.driverjs-theme .driver-popover-footer .driver-popover-navigation-btns .driver-popover-prev-btn {
  border: 1px solid rgba(67, 121, 255, 1);
  background: #FFFFFF;
  color: rgba(67, 121, 255, 1);
  line-height: 18px;
  height: 28px;
  padding: 4px 12px;
}
.driver-popover.driverjs-theme .driver-popover-btn-disabled{
  display: none !important;
}

.driver-overlay path {
  opacity: 0.3!important;
}
.driver-popover-close-btn {
  display: block!important;
}
.driver-popover .guide-checkbox-default{
  margin-top: 6px;
  line-height: 20px;
}
.driver-popover .guide-checkbox-default label {
  display: inline-block;
  line-height: 20px;
}
.driver-popover .guide-checkbox-default label input {
  vertical-align: middle;
  margin-right: 3px;
}