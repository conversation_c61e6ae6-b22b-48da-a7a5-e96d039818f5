<template>
  <div class="button_radius" @click.stop="goOperate">
    <slot name="head_icon"></slot>
    <slot name="default"></slot>
    <slot name="tail_icon"></slot>
  </div>
</template>

<script setup lang="ts">
  const  emit =  defineEmits(['go-operate'])

  const goOperate = () => {
    emit('go-operate')
  }
</script>

<style scoped lang="less">
.button_radius {
  height: 32px;
  line-height: 32px;
  padding-top: 6px;
  padding-right: 8px;
  padding-bottom: 6px;
  padding-left: 8px;
  gap: 4px;
  border-width: 1px;
  border-radius: 15px;
  background: #ffffffcc;
  border: 1px solid #ffffff;
  backdrop-filter: blur(16.309690475463867px);
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
}
.button_radius:hover {
  color: #4379ff;
}
</style>
