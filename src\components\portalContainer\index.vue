<template>
  <!-- 渲染行 (Row) -->
  <div
    v-if="layoutConfig?.type === 'row'"
    :class="[
      'w-full',
      'row-container',
      'relative',
      'flex',
      'flex-row',
      `gap-[${layoutConfig.gutter}px]`,
    ]"
    :style="calculateStyle(layoutConfig)"
    :key="layoutConfig.id || uniqueKey('row')"
  >
    <!-- 递归地渲染行内的列 -->
    <PortalContainer
      v-for="col in layoutConfig.children"
      :key="col.id || uniqueKey('col-child')"
      :layoutConfig="col"
    />
  </div>

  <!-- 渲染列 (Col) -->
  <div
    v-else-if="layoutConfig?.type === 'col'"
    :style="calculateStyle(layoutConfig)"
    :class="[
      'w-full',
      'col-container',
      'relative',
      'flex',
      'flex-col',
      `min-w-0`,
    ]"
    :key="layoutConfig.id || uniqueKey('col')"
  >
    <!-- 递归地渲染列内的组件或嵌套行 -->
    <PortalContainer
      v-for="(child, index) in layoutConfig.children"
      :key="child.id || uniqueKey('comp-child')"
      :layoutConfig="child"
      :class="{ 'col-child-margin': index > 0 }"
    />
  </div>

  <!-- 渲染组件 (Component) -->
  <div v-else-if="layoutConfig?.type === 'component'" class="component-wrapper w-full">
    <component
      :is="resolveComponent(layoutConfig.componentName)"
      v-bind="layoutConfig.props"
      :key="layoutConfig.id || uniqueKey('comp')"
    />
  </div>

  <!-- 可选：处理无效或缺失的 layoutConfig 的情况 -->
  <div v-else>
    <!-- 无效的布局配置 -->
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'PortalContainer',
})
</script>
<script setup lang="ts">
// 引入 Vue 和 Ant Design Vue 的相关 API 和组件
import { defineProps, defineAsyncComponent, computed } from 'vue';
import type { LayoutConfigProp, ColNode, RowNode } from '@/types/portalTypes'

// --- 组件定义 ---

// 定义组件接收的 Props
const props = defineProps<{
  layoutConfig?: LayoutConfigProp; // 布局配置节点 (初始可能未定义)
}>()

// 用于生成唯一 key 的辅助函数 (基础实现，生产环境建议用更健壮方法)
let keyCounter = 0;
const uniqueKey = (prefix: string) => `${prefix}-${keyCounter++}`;

// --- 组件解析策略：异步加载 ---
const componentMap = {
  WorkInstructionCardColumn: defineAsyncComponent(() => import('@/components/workInstructionCardColumn/index.vue')),
  TodoCardColumn: defineAsyncComponent(() => import('@/components/todoCardColumn/index.vue')), // 确保路径和组件存在
  MeetingCardColumn: defineAsyncComponent(() => import('@/components/meetingCardColumn/index.vue')),
  ComiExternalCardIframe: defineAsyncComponent(() => import('remote_app/ComiExternalCardIframe')),
  BapExternalBiCard: defineAsyncComponent(() => import('remote_app/BapExternalBiCard')),
};

function resolveComponent(componentName: string) {
   // 尝试从映射中查找异步组件，如果找不到，则直接返回名称 (依赖全局注册或其他解析方式作为后备)
   return componentMap[componentName as keyof typeof componentMap] || componentName;
}
// --- End 组件解析策略 ---

const calculateStyle = (layoutConfig:any) => {
  const { style, gutter, span  } = layoutConfig
  return {
    ...style,
    gap: gutter ? `${gutter}px` : undefined,
    width: style ? `${style.width}` : '100%'
  }
}
</script>

<style scoped lang="less">
/* 为在列内垂直堆叠的子元素添加上外边距 */
:deep(.col-child-margin)
:deep(.component-wrapper.col-child-margin)
 {
  margin-top: 14px; /* 保持 14px 间距 */
}

.row-container {
  width: 100%;
}

</style>
