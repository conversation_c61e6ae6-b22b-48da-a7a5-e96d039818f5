// 文字颜色
@violet: #7559f8;
@primary-color: #4379FF;
@success-color: #61B109;
@error-color: #FF4F4B;
@gray-color: rgba(0, 0, 0, 0.4);

// 背景色
@violet-bg: #F4F2FF;
@primary-bg: #E5F1FF;
@success-bg: #F1F7F3;
@error-bg: #FFEDED;
@gray-bg: #F7F7F7;

// border颜色
@violet-border-color: #8781FE;
@primary-border-color: #6394FF;
@success-border-color: #77B38D;
@error-border-color: #FF6764;
@gray-border-color: #D5D6DB;

@azure:linear-gradient(90deg, #5973FE 0%, #4379FF 100%);

@sky:#4379FF;

.violet-color {
    color: @violet;
}
.primary-color {
    color: @primary-color;
}
.success-color {
    color: @success-color;
}
.error-color {
    color: @error-color;
}
.gray-color {
    color: @gray-color;
}

.sky-bg {
    background-color: @sky;
}
.violet-bg {
    background-color: @violet-bg;
}
.primary-bg {
    background-color: @primary-bg;
}
.success-bg {
    background-color: @success-bg;
}
.error-bg {
    background-color: @error-bg;
}
.gray-bg {
    background-color: @gray-bg;
}

.violet-border-color {
    border-color: @violet-border-color;
}
.primary-border-color {
    border-color: @primary-border-color;
}
.success-border-color {
    border-color: @success-border-color;
}
.error-border-color {
    border-color: @error-border-color;
}
.gray-border-color {
    border-color: @gray-border-color;
}
@font-weight-500: 500;
@font-weight-400: 400;
@font-weight-600: 600;