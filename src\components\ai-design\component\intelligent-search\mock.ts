export const mockData = [
    {
        title: '最近问过',
        questions: [
            {
                text: '几点下班',
                id: Math.random().toString(),
            },
            {
                text: '端午节放假几天',
                id: Math.random().toString(),
            },
            {
                text: '生育津贴怎么领取',
                id: Math.random().toString(),
            },
            {
                text: '费用报销标准',
                id: Math.random().toString(),
            },
            {
                text: '报销开票信息',
                id: Math.random().toString(),
            },
            {
                text: '加班几点可以打车报销',
                id: Math.random().toString(),
            },

        ],
    },
    {
        title: '常用指引',
        questions: [
            {
                text: '如何查看工资条',
                id: Math.random().toString(),
            },
            {
                text: '工资条忘记密码怎么办',
                id: Math.random().toString(),
            },
            {
                text: '员工手册',
                id: Math.random().toString(),
            },
            {
                text: '员工内推奖金发放规则',
                id: Math.random().toString(),
            },
            {
                text: '新员工入职培训手册',
                id: Math.random().toString(),
            },
            {
                text: '办公用品如何申领',
                id: Math.random().toString(),
            },
            {
                text: '门禁申请流程',
                id: Math.random().toString(),
            },
        ],
    },
    {
        title: '制度政策',
        questions: [
            {
                text: '员工考勤制度',
                id: Math.random().toString(),
            },
            {
                text: '差旅报销制度',
                id: Math.random().toString(),
            },
            {
                text: '加班调休政策',
                id: Math.random().toString(),
            },
        ],
    },
    {
        title: '文档资料',
        questions: [
            {
                text: 'CoMi产品白皮书',
                id: Math.random().toString(),
            },
            {
                text: 'CoMi产品帮助文档',
                id: Math.random().toString(),
            },
        ],
    },
];