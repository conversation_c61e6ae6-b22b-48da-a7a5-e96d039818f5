import { ref, computed, watch, type Ref } from 'vue'
import { debounce } from 'lodash-es'
import type { MenuItem } from '../types/menu'
import { getSearchMenuUtils } from '@/utils/searchMenuUtils'

export function useMenuSearch(menuList: Ref<MenuItem[]>, navigationList: Ref<MenuItem[]>) {
  const searchValue = ref('')
  const searchResults = ref<MenuItem[]>([])
  const searchLoading = ref(false)
  const searchExpandAll = ref(false) // 搜索结果的展开状态

  // 搜索函数
  const search = async (value: string) => {
    if (!value.trim()) {
      searchResults.value = []
      // return
    }

    try {
      searchLoading.value = true

      // 获取内部SearchMenuUtils实例
      const searchMenuUtils = getSearchMenuUtils()

      // 如果有searchMenuUtils并且已经准备好，使用它进行搜索
      if (searchMenuUtils && searchMenuUtils.isReady) {
        console.log('Using internal SearchMenuUtils for search:', value)

        const results = searchMenuUtils.search(value, (data) => {
          console.log('SearchMenuUtils search callback results:', data?.length || 0)
          // 转换数据格式
          const transformedData = (data || []).map((item: any) => ({
            id: item.id || item.idKey,
            idKey: item.idKey || `search_${item.id}`,
            nameKey: item.nameKey || item.name,
            urlKey: item.urlKey || item.url,
            resourceCode: item.resourceCode,
            children: item.children || []
          }))

          // 同时搜索导航数据
          const navigationResults = searchInMenuList(navigationList.value, value).map(item => ({
            ...item,
            isNavigationItem: true // 标记为导航项
          }))


          // 合并菜单搜索结果和导航搜索结果
          searchResults.value = [...transformedData, ...navigationResults]
          searchLoading.value = false
        })

        // 如果search方法同步返回结果
        if (Array.isArray(results)) {
          console.log('SearchMenuUtils search sync results:', results.length)
          const transformedResults = results.map((item: any) => ({
            id: item.id || item.idKey,
            idKey: item.idKey || `search_${item.id}`,
            nameKey: item.nameKey || item.name,
            urlKey: item.urlKey || item.url,
            resourceCode: item.resourceCode,
            children: item.children || []
          }))

          // 同时搜索导航数据
          const navigationResults = searchInMenuList(navigationList.value, value).map(item => ({
            ...item,
            isNavigationItem: true // 标记为导航项
          }))


          // 合并菜单搜索结果和导航搜索结果
          searchResults.value = [...transformedResults, ...navigationResults]
          searchLoading.value = false
        }
      } else {
        console.log('Using local search:', value)
        // 否则使用本地搜索，同时搜索菜单和导航数据
        const menuResults = searchInMenuList(menuList.value, value)
        const navigationResults = searchInMenuList(navigationList.value, value).map(item => ({
          ...item,
          isNavigationItem: true // 标记为导航项
        }))
        searchResults.value = [...menuResults, ...navigationResults]
        searchLoading.value = false
      }
    } catch (err) {
      console.error('搜索失败:', err)
      searchResults.value = []
      searchLoading.value = false
    }
  }

  // 本地搜索函数
  const searchInMenuList = (menus: MenuItem[], keyword: string): MenuItem[] => {
    const results: MenuItem[] = []

    const searchRecursive = (items: MenuItem[]) => {
      items.forEach(item => {
        // 检查当前项是否匹配
        if (item.nameKey.toLowerCase().includes(keyword.toLowerCase()) ||
            (item.resourceCode && item.resourceCode.toLowerCase().includes(keyword.toLowerCase()))) {
          results.push(item)
        }

        // 递归搜索子项
        if (item.children && item.children.length > 0) {
          searchRecursive(item.children)
        }
      })
    }

    searchRecursive(menus)
    return results
  }

  // 防抖搜索
  const debouncedSearch = debounce(search, 300)

  // 监听搜索值变化
  watch(searchValue, (newValue) => {
    debouncedSearch(newValue)
  })

  // 显示的菜单列表
  const displayMenuList = computed(() => {
    if (searchValue.value) {
      // 搜索时显示搜索结果，并将搜索结果转换为菜单块格式
      console.log('--------searchResults.value', convertSearchResultsToMenuBlocks(searchResults.value))
      return convertSearchResultsToMenuBlocks(searchResults.value)
    }
    console.log('--------menuList.value', menuList.value)
    return menuList.value
  })

  // 将搜索结果转换为菜单块格式
  const convertSearchResultsToMenuBlocks = (results: MenuItem[]): MenuItem[] => {
    if (!results || results.length === 0) {
      return []
    }

    // 分离菜单搜索结果和导航搜索结果
    const menuResults = results.filter(item => !item.isNavigationItem)
    const navigationResults = results.filter(item => item.isNavigationItem)

    const blocks: MenuItem[] = []

    // 菜单搜索结果保持原来的展示方式，直接返回原始结构
    // 这里需要从原始菜单数据中找到包含搜索结果的菜单块
    const menuBlocks = getMenuBlocksWithSearchResults(menuResults)
    blocks.push(...menuBlocks)

    // 如果有导航搜索结果，创建导航数据块并放在第一位
    if (navigationResults.length > 0) {
      blocks.unshift({
        id: 'search_navigation_block',
        idKey: 'search_navigation_block',
        nameKey: '导航',
        urlKey: '',
        resourceCode: '',
        children: navigationResults,
        isBlock: true,
        isNavigationBlock: true
      })
    }

    return blocks
  }

  // 从原始菜单数据中找到包含搜索结果的菜单块
  const getMenuBlocksWithSearchResults = (searchResults: MenuItem[]): MenuItem[] => {
    const resultBlocks: MenuItem[] = []

    // 遍历原始菜单数据，找到包含搜索结果的菜单块
    const findBlocksWithResults = (menuBlocks: MenuItem[]) => {
      menuBlocks.forEach(block => {
        // 检查当前块是否包含搜索结果
        const hasResults = hasSearchResultsInBlock(block, searchResults)
        if (hasResults) {
          // 创建包含搜索结果的菜单块副本
          const resultBlock = createBlockWithSearchResults(block, searchResults)
          resultBlocks.push(resultBlock)
        }
      })
    }

    // 从menuList中查找
    findBlocksWithResults(menuList.value)

    return resultBlocks
  }

  // 检查菜单块是否包含搜索结果
  const hasSearchResultsInBlock = (block: MenuItem, searchResults: MenuItem[]): boolean => {
    const searchResultIds = new Set(searchResults.map(item => item.idKey))

    const checkBlock = (menuBlock: MenuItem): boolean => {
      if (searchResultIds.has(menuBlock.idKey)) {
        return true
      }
      if (menuBlock.children) {
        return menuBlock.children.some(checkBlock)
      }
      return false
    }

    return checkBlock(block)
  }

  // 创建包含搜索结果的菜单块
  const createBlockWithSearchResults = (originalBlock: MenuItem, searchResults: MenuItem[]): MenuItem => {
    const searchResultIds = new Set(searchResults.map(item => item.idKey))
    const searchResultMap = new Map(searchResults.map(item => [item.idKey, item]))

    // 递归过滤菜单块，只保留包含搜索结果的项
    const filterBlock = (block: MenuItem): MenuItem | null => {
      if (searchResultIds.has(block.idKey)) {
        // 从搜索结果中获取完整的项目信息，包括isNavigationItem标记
        const searchResult = searchResultMap.get(block.idKey)
        return searchResult || block
      }

      if (block.children) {
        const filteredChildren = block.children
          .map(filterBlock)
          .filter((item): item is MenuItem => item !== null)

        if (filteredChildren.length > 0) {
          return {
            ...block,
            children: filteredChildren
          }
        }
      }

      return null
    }

    const filteredBlock = filterBlock(originalBlock)
    return filteredBlock || originalBlock
  }

  // 处理搜索
  const handleSearch = (value: string) => {
    search(value)
  }

  // 清空搜索
  const handleClearSearch = () => {
    searchValue.value = ''
    searchResults.value = []
    searchExpandAll.value = false // 清空搜索时重置展开状态
  }

  // 切换搜索结果的展开状态
  const toggleSearchExpandAll = () => {
    searchExpandAll.value = !searchExpandAll.value
  }

  // 判断是否正在搜索
  const isSearching = computed(() => {
    return searchValue.value.trim().length > 0
  })

  return {
    searchValue,
    searchResults,
    searchLoading,
    displayMenuList,
    isSearching,
    searchExpandAll,
    toggleSearchExpandAll,
    handleSearch,
    handleClearSearch,
    search
  }
}
