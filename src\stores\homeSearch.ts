import { ref } from 'vue'
import { defineStore } from 'pinia'


// @后面的参数
export const useCheckAssistantParams = defineStore('ckAstPms',()=>{
  const ckAstStr = ref('')
  const setCkAstStr = (val:string)=>{
    ckAstStr.value=val
  }
  return{
    ckAstStr,
    setCkAstStr
  }
})




// 主页查询参数公共入参
export const useHomeSearch = defineStore('homeSearch', () => {
  const hmShPms = ref({
    assistantId:"",
    assistantCode:"",
    input:'',
    citations:[],
  })
  const  changeParams = (val:any)=> {
    hmShPms.value=val
  }
  return { hmShPms,changeParams}
})




// 临时保存当前正在运行的助手信息:名称、头像
export const useTempRunningAssistInfo = defineStore('tmpAstIfo', () => {
  const astInfo = ref({
    id:'',
    name:'',
    iconUrl:'',
    code:'',
    prologue:'',
    introduce:'',
    prologuePreQuestions:[],
    isComi:false,
    isAsit:false,
    customName: ''
  })
  const changeAssistInfo = (val:any)=> {
    astInfo.value=val
  }
  return { astInfo,changeAssistInfo}
})


// 卡片-按钮-会话
export const useTempBtnContext = defineStore('tmpBtnActionInfo', () => {
  const btnActInfo = ref({
      text:'',
  })
  const  setBtnActInfo = (val:any)=> {
    btnActInfo.value=val
  }
  return { btnActInfo,setBtnActInfo}
})

// 对话流
export const useStream = defineStore('chatStream', () => {
  const chatNum = ref(0)
  const changeChatNum = ()=> {
    chatNum.value += 1
  }
  return { chatNum, changeChatNum}
})
