import { createApp, h, type App, type Component } from 'vue';
import Toast from '@/components/ui/toast/index.vue';

type ToastPosition = 'top' | 'center' | 'bottom';

interface ToastOptions {
  content: string;
  positionTop?: ToastPosition;
  timer?: number;
}

interface ToastInstance {
  instance: App<Element> | null;
  container: HTMLElement | null;
}

let currentToast: ToastInstance = {
  instance: null,
  container: null
};

export const aiToast = (options: ToastOptions): void => {
  // 销毁现有 Toast
  const destroyExisting = (): void => {
    if (currentToast.instance && currentToast.container) {
      currentToast.instance.unmount();
      document.body.removeChild(currentToast.container);
      currentToast = { instance: null, container: null };
    }
  };

  destroyExisting();

  // 创建新容器
  const container: HTMLElement = document.createElement('div');
  document.body.appendChild(container);

  // 创建 Vue 应用实例
  const toastApp: App<Element> = createApp({
    setup() {
      const closeToast = (): void => {
        destroyExisting();
      };

      // 设置自动关闭
      if (options.timer) {
        setTimeout(closeToast, options.timer);
      }

      return { closeToast };
    },
    render() {
      return h(Toast as Component, {
        ...options,
        onClose: this.closeToast
      });
    }
  });

  // 保存新实例引用
  currentToast = {
    instance: toastApp,
    container: container
  };

  // 挂载实例
  toastApp.mount(container);
};

export default {
  aiToast,
  install(app: App): void {
    app.config.globalProperties.$aiToast = aiToast;
  }
};
