# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build
/dist
/dist.zip
/src/sdk/doc/advanced-usage.md
/src/sdk/doc/refresh-mechanism-quickstart.md
/src/sdk/doc/refresh-mechanism-usage.md
/src/sdk/doc/isPortal-usage.md

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.eslintcache

npm-debug.log*
yarn-debug.log*
yarn-error.log*

stats.html

package-lock.json
pnpm-lock.yaml
yarn.lock
ai-assistant-web-workspace.code-workspace