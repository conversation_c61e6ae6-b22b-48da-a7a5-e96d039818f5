<template>
  <div class="portal-layout-row grid grid-cols-12">
    <component :key="index" v-for="(item, index) in nodes" v-bind="item.props" :class="item.class"
      :nodes="item.children" :style="item.style" :is="item.tag" />
  </div>
</template>
<script setup lang="ts">
import Col from '@/components/ai-design/layout/Col.vue';

const { nodes } = defineProps({
  nodes: {
    type: Array,
    default: () => [],
  },
});
const componentsMap = {
  "Col": Col
}
</script>

<style scoped lang="less">
.portal-layout-row {
  width: 100%;
}
</style>
