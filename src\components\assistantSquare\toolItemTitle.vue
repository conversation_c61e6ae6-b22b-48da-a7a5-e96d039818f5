<template>
  <div>
    <div class="tool_item_title ellipsis" :title="item.name" v-if="item.isHighlight" v-html="item.hightName"></div>
    <div class="tool_item_title ellipsis" :title="item.name" v-else>{{ item.name }}</div>
  </div>
</template>
<script setup>
defineProps({
  item: {
    type: Object,
    default: () => ({
      name: '',
      isHighlight: false,
      hightName: '',
    }),
  },
});
</script>
<style lang="less" scoped>
.tool_item_title {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: @font-weight-500;
  line-height: 22px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #000000;
}
</style>
<style lang="less">
.tool_item_title {
  .highlight {
    color: @primary-color;
    font-weight: @font-weight-500;
  }
}
</style>
