<template>
  <div :class="['component-item', isShowArrow ? 'cursor-pointer' : '']" @click="handleItemClick">
    <slot></slot>
    <h3 v-if="!hideLabel">{{ label }}<span v-if="required">*</span></h3>
    <div>
      <p v-if="isShowText">
        <span v-if="showValue" @mouseover="handleValueOver($event, showValue)" class="value">{{ component === 'dateTimePick' ? dayjs(props.value).format(DATE_FORMAT) : showValue }}</span>
        <span v-else @mouseover="handleValueOver($event, showValue)" class="placeholder">{{ placeholder }}</span>
        <DatePicker
          v-if="component === 'dateTimePick'"
          class="date-picker"
          v-model:value="showValue"
          :format="DATE_FORMAT"
          :show-time="{format:'HH:mm'}"
          :allowClear="false"
          @change="handleDateChange"
        />
      </p>
      <textarea
        v-else-if="component === 'textarea'"
        ref="textarea"
        :style="textareaStyle"
        v-model="showValue"
        :placeholder="placeholder"
        :disabled="disabled"
        @input="$emit('change', name, $event.target.value)"
      />
      <input
        v-else-if="component === 'input'"
        v-model="showValue"
        :class="[hideLabel ? 'text-field' : '']"
        :disabled="disabled"
        @input="$emit('change', name, $event.target.value)"
        :placeholder="placeholder"
      />
      <span v-if="isShowArrow">
        <!-- <i class="iconfont ai-icon-fuzhi1"></i> -->
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineEmits, nextTick } from 'vue';
import { DatePicker } from 'ant-design-vue';
import dayjs from 'dayjs';
import calcTextareaHeight from './calcTextareaHeight';

dayjs.locale('zh-cn');

interface Props {
  name: string;
  component: string;
  label: string
  hideLabel?: boolean;
  value?: any;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  maxLine?: number;
  showBorderTop?: boolean;
  showBorderBottom?: boolean;
  fieldParam?: any;
}

const props = defineProps<Props>();

const emit = defineEmits(['change', 'click']);

const DATE_FORMAT = 'YYYY-MM-DD HH:mm';
const textareaStyle = ref({});
const showValue = ref('');
const initShowValue = ref<string | null>(null);

const isShowArrow = computed(() => (['textarea', 'input'].includes(props.component) ? false : true));
const isShowText = computed(() => ['text', 'dateTimePick', 'singlePeople', 'multiplePeople', 'meetingRoom'].includes(props.component));

const showDomTitle = function (e: MouseEvent, value: string | null, key = 'offsetWidth') {
    const target = e.target as HTMLElement;
    if (target && !target.title && target[key] > (target.parentNode as HTMLElement)?.offsetWidth) {
      target.title = value;
    }
};

const handleItemClick = () => {
  if (props.disabled) {
    return;
  }
  if (props.component === 'singlePeople' || props.component === 'multiplePeople') {
      emit('change', props.name, [{ id: '123', name: '李志伟' }]);
  } else if (props.component === 'meetingRoom') {
      emit('change', props.name, [{ id: '321', name: 'O座302' }]);
  }
  emit('click', props.name);
};

const handleValueOver = (event: MouseEvent, value: string | null) => {
  showDomTitle(event, value);
};

const resizeTextarea = () => {
  if (props.component === 'textarea') {
    textareaStyle.value = calcTextareaHeight(textareaRef.value, 1, 3.5);
  }
};

const handleDateChange = (value: string, formatValue: string) => {
  emit('change', props.name, formatValue);
}

const textareaRef = ref<HTMLTextAreaElement | null>(null);

const getShowValue = () => {
  if (props.component === 'dateTimePick') {
    if (!props.value) return ''
    const formatValue = dayjs(props.value, DATE_FORMAT)
    console.log('====>? formatValue', formatValue)
    return formatValue
  }
  return typeof (props.value) === 'object' ? props?.value?.map((ele: { name: string; }) => ele.name)?.join() : props.value
}

watch(() => props.value, () => {
  console.log('====》 name', props.name)
  if (initShowValue.value === null) {
    showValue.value = getShowValue()
  }
  nextTick(() => resizeTextarea());
}, {
  deep: true,
  immediate: true
});

// onMounted(() => {
//   if (showValue.value === null) {
//     initShowValue.value = null;
//     showValue.value = getShowValue()
//   }
//   resizeTextarea();
// });
</script>

<style lang="less" scoped>
.cursor-pointer {
  cursor: pointer;
}
.component-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 0;

  > h3 {
    margin: 0;
    flex-shrink: 0;
    width: 106px;
    color: #121212;
    font-weight: @font-weight-400;
    line-height: 22px;
    font-size: 14px;

    > span {
      color: #ff4d4f;
    }
  }

  > div {
    flex-grow: 1;
    display: flex;
    align-items: center;

    > p {
      position: relative;
      flex-grow: 1;
      width: 1px;
      font-size: 14px;
      font-weight: @font-weight-400;
      text-align: right;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;

      .value {
        color: #000000;
      }

      .placeholder {
        color: #cccccc;
      }
    }

    > span {
      margin-left: 4px;
      color: #ccc;
    }
  }

  textarea {
    resize: none;
    border: none;
  }

  textarea,
  input {
    flex-grow: 1;
    font-size: 14px;
    font-weight: @font-weight-400;
    text-align: right;
    color: #000000;

    background-color: rgba(0, 0, 0, 0);
    border: 0;
    outline: 0;
  }

  input::-webkit-input-placeholder,
  textarea::-webkit-input-placeholder {
    text-align: right;
    color: #cccccc;
  }

  input::-ms-input-placeholder,
  textarea::-ms-input-placeholder {
    text-align: right;
    color: #cccccc;
  }

  .date-picker {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    opacity: 0;
  }

  .text-field {
    text-align: left;
    width: 100%;
    border: none !important;
    font-size: 16px !important;
    line-height: 24px !important;
    font-weight: @font-weight-500;
    color: #121212;
    background: inherit;
  }
}
</style>
