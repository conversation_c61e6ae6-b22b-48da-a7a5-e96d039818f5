<template>
  <div class="assistant_menu_box">
    <div class="menu_box_title">常用助手</div>
    <div class="menu_box_list">
      <div
        :class="{ assist_item: true, assist_active_item: currentInx == inx }"
        v-for="(itm, inx) in assistantsList"
        :key="inx"
        @click="() => goSecThisAssist(itm, inx)"
      >
        <div class="assist_item_icon w-9 h-9">
          <Image :width="36" :height="36" :src="itm.iconUrl || AiAvatar" :preview="false" />
        </div>
        <div class="assist_item_info">
          <div class="assist_item_title">{{ itm.name }}</div>
          <div class="assist_item_desc" :title="itm.introduce">{{ itm.introduce }}</div>
        </div>
      </div>
    </div>
    <div v-if="assistantsList.length == 0" class="flex items-center justify-center w-full h-full">
      <Empty description="暂无常用助手" :image="simpleImage"></Empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { AssistInfo } from '@/types/index';
// a-b-c
import AiAvatar from '@/assets/imgs/ai-avatar.png';
import { Image, Empty } from 'ant-design-vue';
// 1-2-3
import { getUsualAssistList } from '@/api/usualAssistant/index';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const currentInx = ref(0); // 激活序号
const currentItem = ref<AssistInfo>(); // 激活序号
const assistantsList = ref<AssistInfo[]>([]); // 常用助手列表

const emit = defineEmits(['to-gosec-assistant']);

// 获取常用助手
const gettingUalAsiLst = async (val: number) => {
  try {
    const res: any = await getUsualAssistList(val);
    if (res && res.code == 0 && res.data && res.data.length > 0) {
      assistantsList.value = [...res.data];
      currentInx.value = 0;
      currentItem.value = res.data[0];
      emit('to-gosec-assistant', res.data.slice(0, 1));
    } else {
      // message.error(res.msg);
      assistantsList.value = [];
    }
  } catch (error) {
    console.log('错误', error);
    assistantsList.value = [];
  }
};

// 选择助手
const goSecThisAssist = (itm: any, inx: number) => {
  if (currentInx.value == inx) {
    return;
  }
  currentInx.value = inx;
  currentItem.value = itm;
  emit('to-gosec-assistant', [
    {
      ...itm,
    },
  ]);
};
defineExpose({ menuList: assistantsList });
// 周期
onMounted(() => {
  gettingUalAsiLst(10);
});
</script>

<style scoped lang="less">
.assistant_menu_box {
  width: 300px;
  height: 100%;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 1);
  background: rgba(255, 255, 255, 0.6);
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.06);
  position: relative;
  .menu_box_title {
    //styleName: 20px/Semibold;
    font-family: PingFang SC;
    font-size: 20px;
    font-weight: @font-weight-500;
    line-height: 28px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: rgba(0, 0, 0, 1);
    margin-bottom: 12px;
  }
  .menu_box_list {
    display: flex;
    flex-direction: column;
    .assist_item {
      display: flex;
      padding: 8px;
      border-radius: 4px;

      .assist_item_icon {
        border-radius: 50%;
      }
      .assist_item_info {
        margin-left: 8px;
        .assist_item_title {
          //styleName: 14px/Semibold;
          font-family: PingFang SC;
          font-size: 14px;
          font-weight: @font-weight-500;
          line-height: 22px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #000000;
        }
        .assist_item_desc {
          //styleName: 12px/Regular;
          margin-top: 2px;
          font-family: PingFang SC;
          font-size: 12px;
          font-weight: @font-weight-400;
          line-height: 20px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: rgba(0, 0, 0, 0.4);
          letter-spacing: 1px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .assist_item:hover {
      background: #f4f3f6;
      cursor: pointer;
    }
    .assist_active_item {
      background: #f4f3f6;
    }
  }
}
</style>
