<template>
  <div class="menu-block">
    <div class="menu-block-content">
      <div
        class="menu-block-title"
        :title="menuBlock.nameKey"
      >
        <span class="menu-expand-content"></span>
        <span class="menu-block-title-text">{{ menuBlock.nameKey }}</span>
      </div>

      <div class="menu-block-items">
        <ul class="menu-list">
          <li
            v-for="item in menuBlock.children"
            :key="item.idKey"
            class="menu-list-item"
          >
            <!-- 递归渲染菜单项 -->
            <RecursiveMenuItem
              :item="item"
              :searchValue="searchValue"
              :expandAll="expandAll"
              :isItemExpanded="isItemExpanded"
              :setItemExpanded="setItemExpanded"
              :favoriteIds="favoriteIds"
              :level="0"
              @select="handleSelect"
              @favorite="handleFavorite"
              @expand="handleExpand"
              :expandedItems="expandedItems"
            />
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { MenuItem } from '../types/menu'
import RecursiveMenuItem from './RecursiveMenuItem.vue'

interface Props {
  menuBlock: MenuItem
  searchValue: string
  expandAll: boolean
  favoriteIds: Set<string>
}

interface Emits {
  (e: 'select', item: MenuItem): void
  (e: 'navigationSelect', item: MenuItem): void
  (e: 'favorite', item: MenuItem): void
  (e: 'expand', item: MenuItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 在 MenuBlock 中管理展开状态
const expandedItems = ref<Set<string>>(new Set())

// 检查菜单项是否展开
const isItemExpanded = (itemId: string): boolean => {
  return expandedItems.value.has(itemId)
}

// 设置菜单项展开状态
const setItemExpanded = (itemId: string, expanded: boolean) => {
  if (expanded) {
    expandedItems.value.add(itemId)
  } else {
    expandedItems.value.delete(itemId)
  }
}

// 递归设置所有子项的展开状态
const setAllChildrenExpanded = (item: MenuItem, expanded: boolean) => {
  if (item.children && item.children.length > 0) {
    item.children.forEach((child: MenuItem) => {
      setItemExpanded(child.idKey, expanded)
      setAllChildrenExpanded(child, expanded)
    })
  }
}

// 监听 expandAll 变化
watch(() => props.expandAll, (newExpandAll) => {
  console.log('MenuBlock expandAll changed:', newExpandAll)
  if (newExpandAll) {
    // 全部展开时，设置所有子项为展开状态
    if (props.menuBlock.children) {
      props.menuBlock.children.forEach(child => {
        setItemExpanded(child.idKey, true)
        setAllChildrenExpanded(child, true)
      })
    }
  } else {
    // 全部收起时，清空所有展开状态
    expandedItems.value.clear()
  }
}, { immediate: true })

// 监听搜索状态变化
watch(() => props.searchValue, (newSearchValue) => {
  if (newSearchValue && props.menuBlock.children) {
    // 搜索时展开所有子项
    props.menuBlock.children.forEach(child => {
      setItemExpanded(child.idKey, true)
      setAllChildrenExpanded(child, true)
    })
  }
}, { immediate: true })

// 事件处理
const handleSelect = (item: MenuItem) => {
  // 检查是否为导航项 - 更精确的判断
  // 只有在明确标记为导航项或者有navType字段且不是菜单项时才作为导航处理
  if (item.isNavigationItem || (item.navType && !item.resourceCode)) {
    emit('navigationSelect', item)
  } else {
    emit('select', item)
  }
}

const handleFavorite = (item: MenuItem) => {
  emit('favorite', item)
}

const handleExpand = (item: MenuItem) => {
  const isExpanded = isItemExpanded(item.idKey)
  setItemExpanded(item.idKey, !isExpanded)
  emit('expand', item)
}
</script>

<style lang="less" scoped>
.menu-block {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.2s;

  .menu-block-content {
    padding: 16px 10px;

    .menu-block-title {
      margin-bottom: 8px;
      line-height: 18px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 0 0 3px 18px;

      .menu-block-title-text {
        font-weight: 600;
        font-size: 14px;
        color: var(--theme-brand6, #4379FF);
      }
    }

    .menu-block-items {
      .menu-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }
    }
  }
}
</style>
