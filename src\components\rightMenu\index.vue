<template>
  <div :class="{ 'ai-menu': true, 'isFullScreenMenu': isFullScreen }">
    <div class="top" v-if="isFullScreen">
      <img src="@/assets/imgs/comi-logo.png" alt="" class="logo" />
      <img src="@/assets/imgs/fullscreen_logo.png" class="comi_logo" />
    </div>
    <CopilotHeader class="top" v-else />
    <div class="menu-content menu-content-1">
      <div class="menu-box">
        <div
          v-for="item in menuList"
          :key="item.id"
          :class="[
            'menu-item menu-item-1',
            currentTabKey === item.id ? 'menu-item-active' : 'menu-item-normal',
          ]"
          @click="handleChange(item)"
        >
          <i v-show="currentTabKey !== item.id" :class="`iconfont ${item.icon}`"></i>
          <img v-show="currentTabKey === item.id" :src="item.activeIcon" class="w-[24px]" />
          <Tooltip placement="left" :title="item.name" arrow-point-at-center>
            <p class="menu-label">{{ item.name }}</p>
          </Tooltip>
        </div>
      </div>
    </div>
    <div class="split-line"></div>
    <div class="menu-content menu-content-2">
      <span class="common-title-box">
        <span class="common-icon" v-if="isFullScreen"> <i class="iconfont ai-icon-xing" /></span>
        <span class="common-title">常用助手</span>
      </span>
      <div class="menu-box common-menu-box">
        <div
          v-for="item in concatAssistantList"
          :key="item.id"
          :class="['menu-item menu-item-2', currentTabKey === item.id ? 'menu-item-active' : '']"
          @click="handleChange(item)"
        >
          <!-- <Image :width="31.5" :height="31.5" :src="item.iconUrl || AiAvatar" alt="" :preview="false" style="border-radius: 50%;" /> -->
          <Avatar
            :class="item.iconUrl ? '' : 'sky-bg'"
            :src="item.iconUrl"
            :size="isFullScreen ? 20 : 32"
          >
            {{ item.iconUrl ? '' : item.name.slice(0, 2) }}
          </Avatar>
          <Tooltip placement="left" :title="item.name" arrow-point-at-center>
            <p class="menu-label">{{ item.name }}</p>
          </Tooltip>
        </div>
      </div>
    </div>
    <ComiSetting />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
  name: 'RightMenu',
});
</script>
<script setup lang="ts">
import { Avatar, Tooltip } from 'ant-design-vue';
import { ref, computed, watch, onUnmounted, inject, reactive, h } from 'vue';
import { getUsualAssistList } from '@/api/usualAssistant/index';
import { getAssistantList } from '@/api/common';
import { useStream, useTempRunningAssistInfo } from '@/stores/homeSearch';
import ComiChatActive from '@/assets/imgs/comi-chat-active.svg';
import AgentSqareActive from '@/assets/imgs/agent-sqare-active.svg';
import { useGlobal, useMenu, useGeneralAsit } from '@/stores/global';
import CopilotHeader from '@/components/copilotHeader/index.vue';
import historyActive from '@/assets/imgs/history_active.svg';
import ComiSetting from '@/components/comiSetting/index.vue';

const uTmStream = useStream(); // store实例
const uTempRanAst = useTempRunningAssistInfo();
const uGlobal = useGlobal();
const uMenu = useMenu();
const uGenealAsit = useGeneralAsit();

const currentTabKey = computed(() => {
  return uMenu.currentMenuInfo?.id || 'comi';
});
// menu信息
const menuList = ref([
  {
    id: 'comi',
    name: '超级助理',
    icon: 'ai-icon-a-tabduihuaye',
    activeIcon: ComiChatActive,
    notAssistant: true,
  },
  {
    id: 'square',
    name: '智能体',
    icon: 'ai-icon-a-tabzhinengti',
    activeIcon: AgentSqareActive,
    notAssistant: true,
  },
  {
    id: 'history',
    name: '历史会话',
    icon: 'ai-icon-a-tablishihuihualiebiao',
    activeIcon: historyActive,
    notAssistant: true,
  },
]);

const assistantsList = ref<any[]>([]);
const allAssistantList = ref<any[]>([]);

const isFullScreen = computed(() => {
  return uGlobal.globalState.isFullScreen;
});

const concatAssistantList = computed(() => {
  const allList = allAssistantList.value.filter((item) => {
    return !assistantsList.value.some((it) => it.id === item.id);
  });
  return [...assistantsList.value, ...allList].slice(0, 7);
});

const sdkInstance = inject('sdkInstance') as any;
const defaultAssistId = sdkInstance?.preConfig?.defaultAssistId || '';

if (defaultAssistId) {
  uMenu.changeMenu({
    id: defaultAssistId,
  });
}

// 菜单切换
const handleChange = async (item: any) => {
  if (currentTabKey.value === item.id) {
    return;
  }
  const isAsit = item?.notAssistant ? false : true;
  uMenu.changeMenu(item, { isAsit });
};

// 获取常用助手
const gettingUalAsiLst = async (val: number) => {
  try {
    const res: any = await getUsualAssistList(val, defaultAssistId);
    if (res && res.code == 0 && res.data && res.data.length > 0) {
      assistantsList.value = [...res.data];
    } else {
      // message.error(res.msg);
      assistantsList.value = [];
    }
  } catch (error) {
    console.log('错误', error);
    assistantsList.value = [];
  }
};

// 获取助手列表
const getAllAssistantList = async () => {
  try {
    const res: any = await getAssistantList({
      pageInfo: {
        pageNumber: 1,
        pageSize: 500,
        needTotal: true,
      },
      params: {
        keyword: '',
        assistantType: 0,
      },
    });
    if (res && res.code === '0') {
      const { content = [] } = res.data;
      allAssistantList.value = [...content];
    } else {
      allAssistantList.value = [];
    }
    // console.log('api助手列表______', res);
  } catch (error) {
    console.log('错误', error);
  }
};
// 处理会话完成后，更新最新常用助手
uTmStream.$subscribe(async (mute, state) => {
  if (state.chatNum) {
    await gettingUalAsiLst(7);
  }
});

gettingUalAsiLst(7);
getAllAssistantList();

defineExpose({ gettingUalAsiLst });
</script>
<style scoped lang="less">
.ai-menu {
  position: relative;
  width: 68px;
  height: 100%;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.4);
  background: rgba(255, 255, 255, 0.5);
  display: flex;
  flex-direction: column;

  .top {
    display: flex;
    align-items: center;
    height: 52px;

    :deep(.menu_item) {
      margin: 0;
    }
  }

  .iconfont {
    font-size: 20px;
    color: #8e94a2;
    &.close_icon {
      color: rgba(0, 0, 0, 0.6);
    }
  }
  .split-line {
    height: 1px;
    background-color: #d8dadf;
    width: calc(100% - 16px);
    margin: 8px;
  }

  .menu-content-2 {
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .common-title-box {
    // padding: 8px 0;
    // border-top: 1px solid rgba(216, 218, 223, 1);
    font-size: 11px;
    width: calc(100% - 16px);
    display: block;
    margin: auto;
    .common-title {
      display: inline-block;
      line-height: 18px;
      letter-spacing: 0%;
      text-align: center;
      width: 100%;
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-size: 12px;
    }
  }
  .menu-box {
    width: 100%;
    &.common-menu-box {
      flex: 1;
      min-height: 1px;
      overflow-y: auto;
    }
  }
  .menu-item {
    width: 60px;
    margin: 0 3px 12px;
    padding: 0 3px;
    cursor: pointer;
    border-radius: 8px;
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    &.menu-item-1 {
      height: 56px;
      margin-bottom: 4px;
      .iconfont {
        font-size: 24px;
        line-height: 1;
      }
    }
    &.menu-item-2 {
      margin: 0 3px 8px;
      padding: 6px 3px 4px;
      .menu-label {
        font-size: 11px;
        margin-top: 4px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 显示两行 */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
      }
    }
    .menu-label {
      max-width: 90%;
      color: rgba(0, 0, 0, 0.4);
      text-align: center;
      font-size: 12px;
      font-style: normal;
      line-height: 15px;
      margin-top: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      letter-spacing: 0%;
    }
    &:not(.menu-item-active):hover {
      background: rgba(209, 224, 255, 0.8);
    }
    &-active {
      background: rgba(209, 224, 255, 0.8);
      border-radius: 8px;
      .menu-label,
      .iconfont {
        color: @sky !important;
      }
    }
  }
}
.help {
  width: calc(100% - 16px);
  height: 52px;
  margin: 0 8px;
  border-top: 1px solid rgba(216, 218, 223, 1);
  line-height: 52px;
  text-align: center;
  .iconfont {
    cursor: pointer;
    width: 16px;
  }
  &:hover .iconfont {
    color: rgba(67, 121, 255, 1);
  }
}
</style>

<style scoped lang="less">
// 全屏菜单样式覆盖
.isFullScreenMenu {
  width: 240px;
  .top {
    height: 56px;
    display: flex;
    align-items: center;
    padding: 12px;
    box-sizing: border-box;

    .logo {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
    .comi_logo {
      height: 14px;
    }
    .title {
      color: #000000;
      line-height: 24px;
    }
  }
  .split-line {
    width: calc(100% - 48px);
    margin: 12px 24px;
  }
  .menu-content {
    padding: 0 16px;
    box-sizing: border-box;
    .common-title-box {
      // height: 22px;
      display: flex;
      align-items: center;
      padding: 3px 5px;
      margin: 0px;
      margin-bottom: 8px;
      .common-icon {
        height: 22px;
        display: flex;
        align-items: center;
      }

      .ai-icon-xing {
        color: #4379ff;
        font-size: 20px;
        margin-right: 3px;
      }
      .common-title {
        text-align: left;
        font-family: PingFang SC;
        font-weight: @font-weight-500;
        font-size: 14px;
        line-height: 22px;
        letter-spacing: 0%;
        color: #4a4e5a;
      }
    }

    .menu-box {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .menu-item {
      width: 100%;
      box-sizing: border-box;
      height: 38px;

      &.menu-item-normal {
        &:hover .menu-label,
        &:hover .iconfont {
          color: #4379ff;
        }
        &:hover {
          background: transparent;
        }
      }

      &.menu-item-1 {
        height: 46px;
        margin: 0;
        padding: 12px 8px;
        display: flex;
        align-items: center;
        flex-direction: row;

        .iconfont {
          font-size: 20px;
          color: #4a4e5a;
          // margin-top: 1.5px;
        }
        .menu-label {
          flex: 1;
          min-width: 0;
          text-align: left;
          margin-left: 8px;
          font-size: 14px;
          color: #4a4e5a;
          font-family: PingFang SC;
          font-weight: @font-weight-500;
          line-height: 22px;
          margin-top: 0;
        }
        img {
          width: 20px;
          height: 20px;
        }
      }

      &.menu-item-2 {
        display: flex;
        align-items: center;
        flex-direction: row;
        margin: 0;
        padding: 8px;
        box-sizing: border-box;
        margin-bottom: 4px;
        .menu-label {
          margin-top: 0;
          margin-left: 8px;
          -webkit-line-clamp: 1; /* 显示两行 */
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: normal;
          flex: 1;
          text-align: left;
          min-width: 0;
          font-size: 14px;
          color: #00000099;
        }
      }
    }
  }
  .help {
    display: flex;
    align-items: center;
    justify-content: left;
    text-align: left;
    padding: 16px 12px;
    width: calc(100% - 24px);
    margin: 0 auto;

    &:hover .iconfont,
    &:hover .help-text {
      color: rgba(67, 121, 255, 1);
    }

    .iconfont {
      font-size: 16px;
      color: #00000099;
    }

    .help-text {
      margin-left: 4px;
      font-size: 14px;
      color: #00000099;
      cursor: pointer;
    }
  }
}
</style>
