<template>
  <div class="content-box w-full flex flex-col flex-1">
    <!-- <header class="content-header flex flex-col justify-center pl-[36px] py-[20px] rounded-[12px] relative mb-[12px]">
      <p class="user-text text-[16px] font-bold">HI {{ userName }}~</p>
      <p class="welcome-text text-[24px] font-bold mt-[8px] gradient-text-welcome">CoMi帮您提高办公效率</p>
      <img src="@/assets/imgs/portal_logo.gif" class="absolute top-[-10px] right-[36px] w-[100px] h-[100px]" alt="portal-header-bg" />
    </header> -->
    <!-- <div class="content-body flex flex-col flex-1">
      <PortalContainer :layoutConfig="layoutConfig" />
    </div> -->
    <PortalView class="flex-1" v-if="view" :view="view"></PortalView>
  </div>
</template>
<script setup lang="ts">
import { usePortalStore } from '@/stores/portal';
import PortalView from '@/components/ai-design/PortalView.vue';

const portalStore = usePortalStore();

const view = computed(() => {
    return portalStore.view;
});

</script>
<style scoped lang="less">
.content-box {
    overflow: auto;
    .content-header {
      background: linear-gradient(to right, #D1DFFF, #CDE9FF, #EAECFF, #E9EDFD);
    }
  }
  .gradient-text-welcome {
    width: fit-content;
    /* 1. 设置背景为从左到右的渐变 */
    background: linear-gradient(to right, #AB7DFE, #5873F6); /* 您可能需要微调这些颜色值 */

    /* 2. 将背景裁剪为文字形状 (需要 Webkit 前缀兼容) */
    -webkit-background-clip: text;
    background-clip: text;

    /* 3. 使文字本身透明，显示背景渐变 */
    color: transparent;

    /* 4. (可选) 确保元素是 inline-block 或 block 以便背景正确应用 */
    /* display: inline-block; */
  }
  :deep(.search_box_wrap) {
    margin-top: 10px;
  }
</style>

