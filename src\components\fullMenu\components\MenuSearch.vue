<template>
  <div class="menu-search-content">
    <i class="iconfont ai-icon-m3-search"></i>
    <input
      ref="searchInput"
      v-model="searchValue"
      class="menu-search-input"
      type="text"
      maxlength="85"
      placeholder="请输入关键字搜索"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown.enter="handleEnter"
      @keydown.escape="handleEscape"
    />
    <div
      v-if="searchResults.length"
      class="menu-search-dropdown"
    >
      <div
        v-for="(item, index) in searchResults"
        :key="item.idKey"
        :class="['menu-search-item', { active: selectedIndex === index }]"
        @click="handleSelect(item)"
        @mouseenter="selectedIndex = index"
      >
        <div class="menu-search-title">{{ item.nameKey }}</div>
        <div class="menu-search-label" v-if="item.resourceCode">{{ item.resourceCode }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, computed } from 'vue'
import { debounce } from 'lodash-es'
import { getSearchMenuUtils } from '../../../utils/searchMenuUtils'
import type { MenuItem } from '../types/menu'

interface Props {
  searchValue: string
}

interface Emits {
  (e: 'update:searchValue', value: string): void
  (e: 'search', value: string): void
  (e: 'clear'): void
  (e: 'select', item: MenuItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const searchInput = ref<HTMLInputElement>()
const showDropdown = ref(false)
const searchResults = ref<MenuItem[]>([])
const selectedIndex = ref(-1)

const searchValue = computed({
  get: () => props.searchValue,
  set: (value) => emit('update:searchValue', value)
})

// 搜索处理
const handleSearch = debounce(async (value: string) => {
  if (!value.trim()) {
    searchResults.value = []
    // return
  }
  // emit('search', value)

  // try {
  //   // 使用内部SearchMenuUtils
  //   const searchMenuUtils = getSearchMenuUtils()
  //   if (searchMenuUtils && searchMenuUtils.isReady) {
  //     searchMenuUtils.search(value, (data: MenuItem[]) => {
  //       searchResults.value = data || []
  //       selectedIndex.value = -1
  //     })
  //   } else {
  //     // 否则触发父组件搜索
  //   }
  // } catch (error) {
  //   console.error('搜索失败:', error)
  //   searchResults.value = []
  // }
}, 300)

// 监听搜索值变化
watch(() => searchValue.value, (newValue) => {
  handleSearch(newValue)
  if (!newValue) {
    searchResults.value = []
    showDropdown.value = false
    emit('clear')
  }
})

// 处理焦点事件
const handleFocus = () => {
  if (searchValue.value) {
    showDropdown.value = true
  }
}

const handleBlur = () => {
  // 延迟隐藏，以便点击下拉项
  setTimeout(() => {
    showDropdown.value = false
    selectedIndex.value = -1
  }, 200)
}

// 处理键盘事件
const handleEnter = () => {
  if (selectedIndex.value >= 0 && selectedIndex.value < searchResults.value.length) {
    const selectedItem = searchResults.value[selectedIndex.value]
    handleSelect(selectedItem)
  } else if (searchResults.value.length > 0) {
    // 如果没有选中项，选择第一个
    handleSelect(searchResults.value[0])
  }
}

const handleEscape = () => {
  showDropdown.value = false
  selectedIndex.value = -1
  searchInput.value?.blur()
}

// 处理选择
const handleSelect = (item: MenuItem) => {
  emit('select', item)
  showDropdown.value = false
  selectedIndex.value = -1

  // 保存搜索历史
  const searchMenuUtils = getSearchMenuUtils()
  if (searchMenuUtils && searchMenuUtils.isReady) {
    searchMenuUtils.saveSearchHistory({
      idKey: item.idKey
    })
  }
}

// 监听搜索结果变化
watch(() => searchResults.value, (newResults) => {
  if (newResults.length > 0 && searchValue.value) {
    showDropdown.value = true
  }
})

// 暴露方法
defineExpose({
  focus: () => searchInput.value?.focus(),
  blur: () => searchInput.value?.blur(),
  clear: () => {
    searchValue.value = ''
    searchResults.value = []
    showDropdown.value = false
  }
})
</script>

<style lang="less" scoped>
.menu-search-content {
  position: relative;

  .ai-icon-m3-search {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #999;
    z-index: 2;
  }

  .menu-search-input {
    width: 100%;
    height: 32px;
    padding: 0 15px 0 32px;
    border: 1px solid #D8DADF;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    backdrop-filter: blur(30px);

    &:focus {
      border-color: var(--theme-brand6, #4379FF);
    }

    &::placeholder {
      color: #bfbfbf;
    }
  }

  .menu-search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d9d9d9;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;

    .menu-search-item {
      padding: 8px 12px;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover,
      &.active {
        background: #f5f5f5;
      }

      .menu-search-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 2px;
      }

      .menu-search-label {
        font-size: 12px;
        color: #999;
      }
    }
  }

  .menu-search-empty {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d9d9d9;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;

    .empty-text {
      padding: 20px;
      text-align: center;
      color: #999;
      font-size: 14px;
    }
  }
}
</style>
