let dynamicLoadingCss, get, init;
let __tla = (async () => {
  const currentImports = {};
  const exportSet = /* @__PURE__ */ new Set([
    "Module",
    "__esModule",
    "default",
    "_export_sfc"
  ]);
  let moduleMap = {
    "./ComiExternalBiCard": () => {
      dynamicLoadingCss([
        "index-BK43m4G8.css",
        "index-CT9M4od_.css"
      ], false, "./ComiExternalBiCard");
      return __federation_import("./__federation_expose_ComiExternalBiCard-B3zfJowI.js").then((module) => Object.keys(module).every((item) => exportSet.has(item)) ? () => module.default : () => module);
    },
    "./BapExternalBiCard": () => {
      dynamicLoadingCss([
        "__federation_expose_BapExternalBiCard-zMLiTAuJ.css",
        "index-CT9M4od_.css"
      ], false, "./BapExternalBiCard");
      return __federation_import("./__federation_expose_BapExternalBiCard-CDo7XB0e.js").then((module) => Object.keys(module).every((item) => exportSet.has(item)) ? () => module.default : () => module);
    },
    "./ComiExternalBiCardDialog": () => {
      dynamicLoadingCss([
        "index-BZCQoEvJ.css",
        "index-CT9M4od_.css"
      ], false, "./ComiExternalBiCardDialog");
      return __federation_import("./__federation_expose_ComiExternalBiCardDialog-ig_SkLqj.js").then((module) => Object.keys(module).every((item) => exportSet.has(item)) ? () => module.default : () => module);
    },
    "./ComiExternalCardIframe": () => {
      dynamicLoadingCss([
        "__federation_expose_ComiExternalCardIframe-BSPU-wol.css"
      ], false, "./ComiExternalCardIframe");
      return __federation_import("./__federation_expose_ComiExternalCardIframe-DwVAv6Np.js").then((module) => Object.keys(module).every((item) => exportSet.has(item)) ? () => module.default : () => module);
    }
  };
  const seen = {};
  dynamicLoadingCss = (cssFilePaths, dontAppendStylesToHead, exposeItemName) => {
    const metaUrl = import.meta.url;
    if (typeof metaUrl === "undefined") {
      console.warn('The remote style takes effect only when the build.target option in the vite.config.ts file is higher than that of "es2020".');
      return;
    }
    const curUrl = metaUrl.substring(0, metaUrl.lastIndexOf("remoteEntry.js"));
    const base = "/";
    "assets";
    cssFilePaths.forEach((cssPath) => {
      let href = "";
      const baseUrl = base || curUrl;
      if (baseUrl) {
        const trimmer = {
          trailing: (path) => path.endsWith("/") ? path.slice(0, -1) : path,
          leading: (path) => path.startsWith("/") ? path.slice(1) : path
        };
        const isAbsoluteUrl = (url) => url.startsWith("http") || url.startsWith("//");
        const cleanBaseUrl = trimmer.trailing(baseUrl);
        const cleanCssPath = trimmer.leading(cssPath);
        const cleanCurUrl = trimmer.trailing(curUrl);
        if (isAbsoluteUrl(baseUrl)) {
          href = [
            cleanBaseUrl,
            cleanCssPath
          ].filter(Boolean).join("/");
        } else {
          if (cleanCurUrl.includes(cleanBaseUrl)) {
            href = [
              cleanCurUrl,
              cleanCssPath
            ].filter(Boolean).join("/");
          } else {
            href = [
              cleanCurUrl + cleanBaseUrl,
              cleanCssPath
            ].filter(Boolean).join("/");
          }
        }
      } else {
        href = cssPath;
      }
      if (dontAppendStylesToHead) {
        const key = "css__comi-custom-card__" + exposeItemName;
        window[key] = window[key] || [];
        window[key].push(href);
        return;
      }
      if (href in seen) return;
      seen[href] = true;
      const element = document.createElement("link");
      element.rel = "stylesheet";
      element.href = href;
      document.head.appendChild(element);
    });
  };
  async function __federation_import(name) {
    currentImports[name] ?? (currentImports[name] = import(name).then(async (m) => {
      await m.__tla;
      return m;
    }));
    return currentImports[name];
  }
  get = (module) => {
    if (!moduleMap[module]) throw new Error("Can not find remote module " + module);
    return moduleMap[module]();
  };
  init = (shareScope) => {
    globalThis.__federation_shared__ = globalThis.__federation_shared__ || {};
    Object.entries(shareScope).forEach(([key, value]) => {
      for (const [versionKey, versionValue] of Object.entries(value)) {
        const scope = versionValue.scope || "default";
        globalThis.__federation_shared__[scope] = globalThis.__federation_shared__[scope] || {};
        const shared = globalThis.__federation_shared__[scope];
        (shared[key] = shared[key] || {})[versionKey] = versionValue;
      }
    });
  };
})();
export {
  __tla,
  dynamicLoadingCss,
  get,
  init
};
