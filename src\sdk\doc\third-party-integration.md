# 第三方系统集成指南

> 第三方系统嵌入COMI助手的完整集成方案，包含环境配置、功能调用和最佳实践

## 📋 概述

本指南详细介绍如何在第三方系统中集成COMI助手，实现完整的智能对话和业务功能调用。通过简单的脚本引入和标准API调用，即可在您的系统中获得强大的AI助手能力。

## 🚀 快速开始

### 1. 引入COMI SDK

在您的HTML页面中引入COMI入口脚本：

```html
<!DOCTYPE html>
<html>
<head>
    <title>我的系统 - 集成COMI助手</title>
</head>
<body>
    <!-- 您的页面内容 -->
    <div id="app">
        <!-- 系统内容 -->
    </div>
    
    <!-- 引入COMI SDK -->
    <script src="https://your-comi-domain.com/static/js/comi-entry.js"></script>
    
    <!-- 您的业务脚本 -->
    <script>
        // COMI助手集成代码
        window.COMI_ASSISTANT.preSdk.openDrawer(() => {
            console.log('COMI助手已就绪');
            
            // 在这里添加您的业务逻辑
            initializeComiFeatures();
        });
        
        function initializeComiFeatures() {
            const sdk = window.COMI_ASSISTANT.sdk;
            
            // 发送欢迎消息
            sdk.sendMsg('欢迎使用COMI助手！');
        }
    </script>
</body>
</html>
```

### 2. 基础功能调用

```javascript
// 等待SDK就绪
window.COMI_ASSISTANT.preSdk.openDrawer(() => {
    const sdk = window.COMI_ASSISTANT.sdk;
    
    // 发送消息
    sdk.sendMsg('请帮我分析本月的销售数据');
    
    // 重定向到专业助手
    sdk.redirectAssistId('数据分析助手ID', true, () => {
        sdk.sendMsg('开始数据分析', { isHide: true });
    });
    
    // 获取OA系统链接
    const oaUrl = sdk.getOaUrl({
        appType: '1',          // 协同应用
        linkId: 'ORDER_123',   // 业务单据ID
        clientType: '1'        // 客户端类型
    });
    
    console.log('生成的OA链接:', oaUrl);
});
```

## 🔧 完整集成示例

### 业务系统集成案例

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>ERP系统 - COMI智能助手集成</title>
    <style>
        .business-container {
            display: flex;
            height: 100vh;
        }
        .main-content {
            flex: 1;
            padding: 20px;
        }
        .comi-trigger {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        .order-list {
            margin-top: 20px;
        }
        .order-item {
            padding: 10px;
            border: 1px solid #ddd;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="business-container">
        <div class="main-content">
            <h1>ERP订单管理系统</h1>
            
            <!-- 智能助手功能按钮 -->
            <div class="comi-actions">
                <button class="comi-trigger" onclick="askComiAnalysis()">
                    📊 智能数据分析
                </button>
                <button class="comi-trigger" onclick="askComiSummary()">
                    📋 生成工作总结
                </button>
                <button class="comi-trigger" onclick="openComiChat()">
                    💬 打开智能对话
                </button>
            </div>
            
            <!-- 订单列表 -->
            <div class="order-list">
                <h3>今日订单</h3>
                <div class="order-item">
                    <span>订单 #12345 - 客户A - ¥15,800</span>
                    <button onclick="analyzeOrder('12345')">分析订单</button>
                </div>
                <div class="order-item">
                    <span>订单 #12346 - 客户B - ¥8,200</span>
                    <button onclick="analyzeOrder('12346')">分析订单</button>
                </div>
                <div class="order-item">
                    <span>订单 #12347 - 客户C - ¥22,400</span>
                    <button onclick="analyzeOrder('12347')">分析订单</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 引入COMI SDK -->
    <script src="https://your-comi-domain.com/static/js/comi-entry.js"></script>
    
    <script>
        // 全局变量，保存COMI SDK实例
        let comiSdk = null;
        
        // 初始化COMI助手
        window.COMI_ASSISTANT.preSdk.openDrawer(() => {
            comiSdk = window.COMI_ASSISTANT.sdk;
            console.log('COMI助手初始化完成');
            
            // 发送欢迎消息
            comiSdk.sendMsg('欢迎使用ERP智能助手！我可以帮您分析订单数据、生成报告等。');
        });
        
        // 智能数据分析
        function askComiAnalysis() {
            if (!comiSdk) {
                alert('COMI助手正在加载中，请稍后再试...');
                return;
            }
            
            // 重定向到数据分析助手
            comiSdk.redirectAssistId('数据分析助手ID', true, () => {
                comiSdk.sendMsg('请分析今日的订单数据：\n' +
                    '订单总数：3单\n' +
                    '订单总额：¥46,400\n' +
                    '平均订单金额：¥15,467\n' +
                    '请给出分析结果和建议', { isHide: true });
            });
        }
        
        // 生成工作总结
        function askComiSummary() {
            if (!comiSdk) {
                alert('COMI助手正在加载中，请稍后再试...');
                return;
            }
            
            comiSdk.redirectAssistId('总结助手ID', true, () => {
                comiSdk.sendMsg('请基于以下信息生成今日工作总结：\n' +
                    '1. 处理订单3单，总金额¥46,400\n' +
                    '2. 客户回访2次\n' +
                    '3. 库存盘点完成\n' +
                    '请生成专业的工作总结报告', { isHide: true });
            });
        }
        
        // 打开智能对话
        function openComiChat() {
            if (!comiSdk) {
                alert('COMI助手正在加载中，请稍后再试...');
                return;
            }
            
            // 切换到默认助手，开始自由对话
            comiSdk.redirectAssistId('默认助手ID', true, () => {
                comiSdk.sendMsg('您好！有什么可以帮助您的吗？');
            });
        }
        
        // 分析单个订单
        function analyzeOrder(orderId) {
            if (!comiSdk) {
                alert('COMI助手正在加载中，请稍后再试...');
                return;
            }
            
            // 获取订单详情（模拟数据）
            const orderDetails = getOrderDetails(orderId);
            
            comiSdk.redirectAssistId('订单分析助手ID', true, () => {
                comiSdk.sendMsg(`请分析订单 #${orderId}：\n` +
                    `客户：${orderDetails.customer}\n` +
                    `金额：¥${orderDetails.amount}\n` +
                    `商品：${orderDetails.products}\n` +
                    `下单时间：${orderDetails.orderTime}\n` +
                    `请分析订单特点和风险点`, { isHide: true });
            });
        }
        
        // 模拟获取订单详情
        function getOrderDetails(orderId) {
            const orders = {
                '12345': {
                    customer: '客户A',
                    amount: '15,800',
                    products: '电子产品套装',
                    orderTime: '2024-12-17 10:30'
                },
                '12346': {
                    customer: '客户B',
                    amount: '8,200',
                    products: '办公用品',
                    orderTime: '2024-12-17 14:15'
                },
                '12347': {
                    customer: '客户C',
                    amount: '22,400',
                    products: '设备采购',
                    orderTime: '2024-12-17 16:45'
                }
            };
            return orders[orderId] || {};
        }
        
        // 生成OA系统链接示例
        function generateOaLink(orderId) {
            if (!comiSdk) return '';
            
            return comiSdk.getOaUrl({
                appType: '1',           // 协同应用
                linkId: orderId,        // 订单ID
                clientType: '1',        // 客户端类型
                openType: 'dialog'      // 弹窗打开
            });
        }
    </script>
</body>
</html>
```

## 📊 高级功能集成

### 1. 自定义卡片推送

```javascript
// 推送业务数据卡片
function pushBusinessCard() {
    const sdk = window.COMI_ASSISTANT.sdk;
    
    const cardData = {
        title: '今日业绩报告',
        content: `
            📈 销售总额：¥46,400
            📦 订单数量：3单
            👥 新客户：2位
            ⭐ 客户满意度：4.8/5.0
        `,
        actions: [
            {
                text: '查看详情',
                action: 'viewDetails',
                data: { reportId: 'R20241217' }
            },
            {
                text: '生成报表',
                action: 'generateReport',
                data: { type: 'daily' }
            }
        ]
    };
    
    sdk.pushCustomCard(cardData);
}
```

### 2. 动态助手切换

```javascript
// 根据业务场景智能切换助手
function switchAssistantByContext(context) {
    const sdk = window.COMI_ASSISTANT.sdk;
    
    const assistantMap = {
        'data_analysis': '5605278465501839487',  // 数据分析助手
        'customer_service': '5605278465501839488', // 客服助手
        'report_generation': '5605278465501839489', // 报表助手
        'order_management': '5605278465501839490'   // 订单管理助手
    };
    
    const assistantId = assistantMap[context];
    if (assistantId) {
        sdk.redirectAssistId(assistantId, true, () => {
            // 发送上下文信息
            sdk.sendMsg(`已切换到${context}模式，请问有什么可以帮助您的？`);
        });
    }
}
```

### 3. 业务流程自动化

```javascript
// 自动化业务流程
class BusinessAutomation {
    constructor(sdk) {
        this.sdk = sdk;
        this.workflows = new Map();
    }
    
    // 注册业务流程
    registerWorkflow(name, steps) {
        this.workflows.set(name, steps);
    }
    
    // 执行业务流程
    executeWorkflow(name, data) {
        const steps = this.workflows.get(name);
        if (!steps) return;
        
        this.executeSteps(steps, data, 0);
    }
    
    // 执行流程步骤
    executeSteps(steps, data, index) {
        if (index >= steps.length) return;
        
        const step = steps[index];
        const nextStep = () => {
            this.executeSteps(steps, data, index + 1);
        };
        
        switch (step.type) {
            case 'switch_assistant':
                this.sdk.redirectAssistId(step.assistantId, true, nextStep);
                break;
                
            case 'send_message':
                this.sdk.sendMsg(step.message.replace(/\{(\w+)\}/g, (_, key) => data[key]), {
                    isHide: step.hide
                });
                setTimeout(nextStep, step.delay || 1000);
                break;
                
            case 'generate_link':
                const url = this.sdk.getOaUrl({
                    appType: step.appType,
                    linkId: data[step.linkField],
                    clientType: step.clientType
                });
                data.generatedUrl = url;
                nextStep();
                break;
        }
    }
}

// 使用示例
const automation = new BusinessAutomation(window.COMI_ASSISTANT.sdk);

// 注册订单处理流程
automation.registerWorkflow('order_process', [
    {
        type: 'switch_assistant',
        assistantId: '订单处理助手ID'
    },
    {
        type: 'send_message',
        message: '正在处理订单 #{orderId}，客户：{customer}',
        hide: true,
        delay: 500
    },
    {
        type: 'generate_link',
        appType: '1',
        linkField: 'orderId',
        clientType: '1'
    },
    {
        type: 'send_message',
        message: '订单处理完成，查看详情：{generatedUrl}',
        hide: false
    }
]);

// 执行流程
automation.executeWorkflow('order_process', {
    orderId: '12345',
    customer: '张三'
});
```

## 🔗 OA系统集成

### getOaUrl 参数说明

```javascript
const url = sdk.getOaUrl({
    appType: '1',           // 应用类型
    linkId: 'DOC_12345',    // 业务单据ID
    clientType: '1',        // 客户端类型
    openType: 'dialog',     // 打开方式
    params: {               // 额外参数
        readonly: true,
        highlight: 'section1'
    }
});
```

#### 应用类型（appType）

| 值 | 应用类型 | 说明 |
|---|---------|-----|
| '1' | 协同 | 协同办公应用 |
| '3' | 文档 | 文档管理系统 |
| '4' | 公文 | 公文流转系统 |
| '8' | 新闻 | 新闻资讯系统 |
| '16' | 邮件 | 邮件系统 |
| '32' | 论坛 | 论坛讨论系统 |

#### 客户端类型（clientType）

| 值 | 客户端 | 说明 |
|---|--------|-----|
| '1' | Web | 浏览器客户端 |
| '2' | Mobile | 移动端 |
| '3' | Desktop | 桌面客户端 |

### 实际应用示例

```javascript
// 文档管理集成
function openDocument(docId) {
    const url = sdk.getOaUrl({
        appType: '3',
        linkId: docId,
        clientType: '1',
        openType: 'newTab',
        params: {
            mode: 'edit',
            version: 'latest'
        }
    });
    
    window.open(url, '_blank');
}

// 公文流转集成
function processOfficalDoc(docId) {
    sdk.redirectAssistId('公文助手ID', true, () => {
        const url = sdk.getOaUrl({
            appType: '4',
            linkId: docId,
            clientType: '1'
        });
        
        sdk.sendMsg(`请处理公文：${url}`, { isHide: true });
    });
}
```

## 🎯 最佳实践

### 1. 错误处理

```javascript
// 封装SDK调用，增加错误处理
class ComiSDKWrapper {
    constructor() {
        this.sdk = null;
        this.isReady = false;
        this.pendingCalls = [];
    }
    
    // 初始化
    init() {
        return new Promise((resolve, reject) => {
            if (typeof window.COMI_ASSISTANT === 'undefined') {
                reject(new Error('COMI SDK未加载'));
                return;
            }
            
            window.COMI_ASSISTANT.preSdk.openDrawer(() => {
                this.sdk = window.COMI_ASSISTANT.sdk;
                this.isReady = true;
                
                // 执行等待中的调用
                this.pendingCalls.forEach(call => {
                    try {
                        call.fn();
                        call.resolve();
                    } catch (error) {
                        call.reject(error);
                    }
                });
                this.pendingCalls = [];
                
                resolve(this.sdk);
            });
        });
    }
    
    // 安全调用SDK方法
    call(fn) {
        return new Promise((resolve, reject) => {
            if (this.isReady) {
                try {
                    fn();
                    resolve();
                } catch (error) {
                    reject(error);
                }
            } else {
                this.pendingCalls.push({ fn, resolve, reject });
            }
        });
    }
    
    // 发送消息（带错误处理）
    async sendMessage(message, options = {}) {
        return this.call(() => {
            this.sdk.sendMsg(message, options);
        });
    }
    
    // 重定向助手（带错误处理）
    async redirectAssistant(assistantId, showChat = true, callback) {
        return this.call(() => {
            this.sdk.redirectAssistId(assistantId, showChat, callback);
        });
    }
}

// 使用示例
const comiWrapper = new ComiSDKWrapper();

// 初始化
comiWrapper.init()
    .then(() => {
        console.log('COMI SDK初始化成功');
    })
    .catch(error => {
        console.error('COMI SDK初始化失败:', error);
    });

// 安全调用
comiWrapper.sendMessage('测试消息')
    .then(() => {
        console.log('消息发送成功');
    })
    .catch(error => {
        console.error('消息发送失败:', error);
    });
```

### 2. 性能优化

```javascript
// 延迟加载COMI SDK
function loadComiSDK() {
    return new Promise((resolve, reject) => {
        if (window.COMI_ASSISTANT) {
            resolve();
            return;
        }
        
        const script = document.createElement('script');
        script.src = 'https://your-comi-domain.com/static/js/comi-entry.js';
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('Failed to load COMI SDK'));
        document.head.appendChild(script);
    });
}

// 按需加载
async function initComiOnDemand() {
    try {
        await loadComiSDK();
        
        window.COMI_ASSISTANT.preSdk.openDrawer(() => {
            console.log('COMI助手按需加载完成');
        });
    } catch (error) {
        console.error('按需加载COMI失败:', error);
    }
}
```

### 3. 用户体验优化

```javascript
// 加载状态提示
function showComiLoading() {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'comi-loading';
    loadingDiv.innerHTML = `
        <div style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 10000;
        ">
            <div style="text-align: center;">
                <div style="margin-bottom: 10px;">🤖</div>
                <div>COMI助手加载中...</div>
            </div>
        </div>
    `;
    document.body.appendChild(loadingDiv);
}

function hideComiLoading() {
    const loadingDiv = document.getElementById('comi-loading');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

// 使用示例
showComiLoading();

window.COMI_ASSISTANT.preSdk.openDrawer(() => {
    hideComiLoading();
    console.log('COMI助手就绪');
});
```

## 🐛 故障排除

### 常见问题

1. **SDK未加载**
   ```javascript
   if (typeof window.COMI_ASSISTANT === 'undefined') {
       console.error('COMI SDK未正确加载，请检查脚本引入');
   }
   ```

2. **回调不执行**
   ```javascript
   // 检查是否在正确的时机调用
   if (window.COMI_ASSISTANT && window.COMI_ASSISTANT.preSdk) {
       window.COMI_ASSISTANT.preSdk.openDrawer(() => {
           // 确保在这里调用SDK方法
       });
   }
   ```

3. **方法未定义**
   ```javascript
   // 检查SDK是否完全就绪
   window.COMI_ASSISTANT.preSdk.openDrawer(() => {
       const sdk = window.COMI_ASSISTANT.sdk;
       if (typeof sdk.sendMsg === 'function') {
           sdk.sendMsg('测试消息');
       }
   });
   ```

### 调试工具

```javascript
// 调试信息获取
function getComiDebugInfo() {
    if (!window.COMI_ASSISTANT) {
        return { error: 'COMI_ASSISTANT not found' };
    }
    
    return {
        hasPreSdk: !!window.COMI_ASSISTANT.preSdk,
        hasSdk: !!window.COMI_ASSISTANT.sdk,
        sdkMethods: window.COMI_ASSISTANT.sdk ? 
            Object.keys(window.COMI_ASSISTANT.sdk) : [],
        comiEventBus: !!window.comiEventBus,
        isReady: window.COMI_ASSISTANT.sdk && 
                typeof window.COMI_ASSISTANT.sdk.sendMsg === 'function'
    };
}

// 使用调试信息
console.log('COMI调试信息:', getComiDebugInfo());
```

## 📞 技术支持

如果您在集成过程中遇到问题：

1. 检查控制台是否有错误信息
2. 确认SDK脚本是否正确加载
3. 使用调试工具获取详细信息
4. 参考本文档的故障排除部分

**智能产品部-应用部-前端团队**
