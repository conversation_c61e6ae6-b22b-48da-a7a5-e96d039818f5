{"id": "111111", "meta": {"editor": "0.0.1", "ct": "1234564641"}, "env": {}, "views": [{"id": "xxxx", "code": "", "condition": {}, "name": "页面", "options": {"class": "x-portal", "style": {"width": "100%", "height": "100%", "backgroundColor": "rgba(220,224,227,1)"}, "props": {}}, "children": [{"type": "Layout", "tag": "LayoutAside", "style": {"width": "200px"}, "name": "布局", "condition": false, "options": {}, "children": []}, {"type": "Layout", "class": "flex-1", "tag": "LayoutSection", "name": "布局", "options": {}, "children": [{"type": "Layout", "tag": "LayoutMain", "name": "布局", "class": "flex justify-center", "condition": {"eval": "this.global.globalState.currAgentCode == 'comi'"}, "options": {}, "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "布局", "class": "h-full flex", "options": {}, "children": [{"type": "Layout", "tag": "LayoutContainer", "class": "flex-1 flex justify-center flex-col", "name": "容器", "children": [{"type": "Layout", "tag": "Row", "class": "gap-4", "children": [{"type": "Layout", "tag": "Col", "class": "col-span-12", "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "spec": 23, "children": [{"type": "component", "tag": "CoMiHeader", "props": {}}]}]}]}, {"type": "Layout", "tag": "Row", "class": "gap-3", "style": {"marginTop": "-48px", "position": "relative", "zIndex": 2}, "children": [{"type": "Layout", "tag": "Col", "class": "col-span-8", "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "spec": 23, "children": [{"type": "component", "class": "2xl:h-[512px] xl:h-[454px] lg:h-[454px] md:h-[454px] sm:h-[454px]", "tag": "TodoCardColumn", "copilot": "1", "props": {}}]}]}, {"type": "Layout", "tag": "Col", "class": "col-span-4", "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "class": "flex flex-col gap-3", "spec": 23, "children": [{"type": "component", "class": "h-[236px]", "tag": "MeetingCardColumn", "copilot": "1", "roles": [], "props": {}}, {"type": "component", "class": "2xl:h-[266px] xl:h-[206px] lg:h-[206px] md:h-[206px] sm:h-[206px]", "tag": "PortalMessage", "copilot": "1", "props": {}}]}]}]}]}, {"type": "Layout", "tag": "LayoutContainer", "name": "容器", "class": "w-full bg-sky-500", "style": {"height": "200px"}, "condition": false, "children": [{"type": "component", "tag": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}]}]}]}, {"type": "Layout", "condition": false, "tag": "layout-footer", "name": "布局", "options": {}, "children": []}]}]}]}