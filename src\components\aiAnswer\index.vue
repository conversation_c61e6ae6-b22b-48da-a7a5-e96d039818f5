<template>
  <div class="mb-4 ai_item">
    <div>
      <div :class="`bg-white ai_answer_box anb_${inx} ${aicardWidth} `" ref="real_content_ele">
        <!-- 主内容区 -->
        <!-- 思考过程 -->
        <ThinkProcess
          :processData="transAiInfo.data.processData"
          v-if="transAiInfo.data.processData?.length"
        />
        <AiIdentify
          :loadingStatus="true"
          v-if="transAiInfo.data.cardData.length === 0 && transAiInfo.data.status === 2"
        ></AiIdentify>
        <div
          v-for="(item, index) in transAiInfo.data.cardData"
          :key="`item-${index}`"
          class="bg-white ai_answer_item"
          :style="{
            width: item.type === 'text' ? 'fit-content' : '100%',
          }"
        >
          <!-- TODO: 临时增加一个判断，如果是辅助审批则不显示知识源 -->
          <span
            v-if="item.isDobuleScreen && showKnowledgeSource"
            @click="handleCitationClick(item)"
            class="knowledge_source"
          >
            <!-- {{ item }} -->

            <span>参考知识源{{ getKnowledgeDataLength(item.context) }}个</span>
            <i class="iconfont ai-icon-you" />
          </span>
          <ComiMarkdown
            v-if="showMarkdown(item)"
            :content="item"
            @tagClick="handleTagClick"
            ref="comiMarkdownRef"
            class="markdown-content"
          />
          <!-- 卡片 -->
          <CommonCard
            v-else-if="item.isCard"
            :content="item.context"
            :chatData="transAiInfo"
            ref="commonCardRef"
            :class="{
              common_card: true,
            }"
            >{{ item.content }}</CommonCard
          >
          <!-- iframe -->
          <div v-else-if="item.isIframe" v-html="item.context" class="iframe-box"></div>
          <!-- <KnowledgeSource
            v-if="item.isKnowledgeData"
            :knowledgeData="item.context || []"
          /> -->
        </div>
        <!-- 已停止回答 -->
        <div
          class="is_stop"
          v-if="transAiInfo.data.status === 5"
          :class="transAiInfo.data.content ? '!text-[12px]' : ''"
        >
          已停止回答
        </div>
        <!-- 错误提示 -->
        <div class="is_stop_error" v-if="transAiInfo.data.status === 7">
          comi不小心发生错误啦，请稍后重试
        </div>
        <!-- 文件列表 -->
        <!-- <ChatFileList :fileList="transAiInfo?.citationList || []" /> -->
        <!-- 操作区 -->
        <!-- 无内容的时候，不显示操作区 -->
        <div
          class="flex justify-between w-full operate_btns"
          v-if="
            !isColumn &&
            [3, 5, 7].includes(transAiInfo.data.status) &&
            transAiInfo.data?.cardData?.length !== 0
          "
        >
          <div class="copy_reload_btns">
            <CopyBtn :transAiInfo="transAiInfo" v-if="transAiInfo.data.status !== 7" />
            <RebuildBtn :transAiInfo="transAiInfo" />
          </div>
          <div class="give_up-down_btns">
            <SuggestBtn :transAiInfo="transAiInfo" v-if="transAiInfo.data.status !== 7" />
          </div>
        </div>
      </div>
      <!-- <div @click="handleClick" class="ai_item_click"></div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits, provide, watch } from 'vue';
import type { ChatItem, citationJSONItem } from '@/types/index';
import CommonCard from '@/components/commonCard/index.vue';
import { ComiMarkdown } from '@seeyon/seeyon-comi-plugins-library';
import { commonBusinessClass } from '@/utils/commonBusinessClass';
import type { CardContentType } from '@/types/api';
import { ThinkProcess } from '@seeyon/seeyon-comi-plugins-library';
import { dealFileData } from '@/utils/oaDataMap';
import AiIdentify from '@/components/aiIdentify/index.vue';
// 引入特有组件
import CopyBtn from './components/copy.vue';
import RebuildBtn from './components/rebuild.vue';
import SuggestBtn from './components/suggest.vue';
import { useChatList } from '@/stores/chatList';
import { useMenu } from '@/stores/global';
import { getDobuleScreenIframeWindow, openDobuleScreen } from '@/utils/storesUtils';
import cardInstance from '@/stores/card';
import { injectWindowFun } from '@/utils/windowFunOverride';
import { useGlobal } from '@/stores/global';

// 常量定义
const FUZHU_MENU_CODE = 'assist-3756233093049728520';
const isPortal = inject('isPortal') as boolean;

// 定义更严格的 Props 类型
interface Props {
  inx: number;
  tagData: any;
  transAiInfo: ChatItem;
  transDisAble?: boolean;
  isColumn?: boolean;
  lastIndex: number;
}

const props = defineProps<Props>();
const topWindow = window.top || window;
// 定义 Emits 类型
interface Emits {
  handleTagClick: [data: any];
  handleCitationClick: [item: citationJSONItem];
}

const emit = defineEmits<Emits>();
let timer: any = null;
let iframeWindow: Window | null = null;

// Store 实例
const uMenu = useMenu();
const uChatList = useChatList();

// 创建共享的 ref 此处是为了复制卡片的内容，为了第三方的组件做的兼容处理
const aiAnswerRef = ref<HTMLElement | null>(null);
const comiMarkdownRef = ref<any>(null);

// 提供给所有后代组件
provide('aiAnswerRef', aiAnswerRef);
provide('comiMarkdownRef', comiMarkdownRef);

// 判断是否显示 Markdown 内容
const showMarkdown = (item: CardContentType): boolean => {
  return Boolean(
    item.context && !item.isCard && !item.isIframe && !item.isKnowledgeData && !item.isDobuleScreen,
  );
};

// 辅助审批助手不展示知识源
const showKnowledgeSource = computed(() => {
  if (
    uMenu.currentMenuInfo?.code === FUZHU_MENU_CODE ||
    (uMenu.currentMenuInfo?.id === 'comi' &&
      uChatList.dynamicData.selectedAgentInfo?.code === FUZHU_MENU_CODE)
  ) {
    return false;
  }
  return true;
});

// 真实内容元素
const real_content_ele = ref<HTMLElement | null>(null);
// 动态 components 的 ref
const commonCardRef = ref<any>(null);

// 卡片宽度计算
const aicardWidth = computed((): string => {
  const { cardData, processData } = props.transAiInfo.data || {};

  // 如果有处理数据，直接返回计算宽度
  if (processData?.length) {
    return 'item-caculate-width';
  }

  // 检查卡片数据是否需要计算宽度
  if (cardData?.length) {
    const needCalculateWidth = cardData.some(
      (item: any) =>
        item?.type === 'aicard' ||
        item?.type === 'iframe' ||
        item?.isCard ||
        (showMarkdown(item) && item?.context && /```html[\s\S]*?```/.test(item.context)),
    );

    return needCalculateWidth ? 'item-caculate-width' : '';
  }

  return '';
});

// 双屏数据计算
const dobuleScreenData = computed(() => {
  return props.transAiInfo?.data?.cardData?.find((item: CardContentType) => item.isDobuleScreen);
});

// 获取知识源数据长度
const getKnowledgeDataLength = (data: any): number => {
  if (!data) return 0;

  try {
    const filterData = dealFileData(JSON.parse(JSON.stringify(data)));
    return Object.values(filterData).reduce((total: number, items: any) => {
      return total + (Array.isArray(items) ? items.length : 0);
    }, 0);
  } catch (error) {
    console.error('Error processing knowledge data:', error);
    return 0;
  }
};

// 点击知识源
const handleCitationClick = (item: citationJSONItem): void => {
  if (!item?.context) {
    console.warn('Citation item context is missing');
    return;
  }
  openDobuleScreen(item.context, 'knowledge');
};

// 定义标签动作类型
type TagAction =
  | 'openUrl'
  | 'confirmSendMsg'
  | 'member'
  | 'workSummary'
  | 'createProcess'
  | 'createProcessWithDetail'
  | 'openCard';

// 检查标签是否已被点击
const isTagAlreadyClicked = (data: any): boolean => {
  const { lastIndex, tagData } = props;
  return Boolean(
    tagData?.[lastIndex]?.some((item: any) => item.matched_word === data?.matched_word),
  );
};

// 处理 URL 打开
const handleOpenUrl = (data: any): void => {
  // TODO:此处临时设置了处理待办列表和待办详情的特殊逻辑，后续还需要进一步优化
  const jumpUrl = data?.content?.url;
  if (data?.content?.url) {
    if(jumpUrl === `${window._ctxPath}/collaboration/collaboration.do?method=listPending&isFromComi=true`){
      useGlobal().changeState('preLinkIsTodoList', true);
    }
    openDobuleScreen(data.content.url, 'iframe');
  }
};

// 处理发送消息
const handleSendMessage = (data: any): void => {
  if (data?.content?.value) {
    cardInstance.sendMessage(data.content.value, [], false);
  }
};

// 处理业务类标签
const handleBusinessTag = (data: any): void => {
  const businessClass = commonBusinessClass();
  if (!businessClass || typeof businessClass.handleTagClick !== 'function') {
    console.warn('Business class or handleTagClick method not available');
    return;
  }

  // 点击业务类标签的时候，
  injectWindowFun('openCtpWindow');

  businessClass.handleTagClick(data, (oaData: any) => {
    const responseData = {
      index: props.lastIndex,
      data: oaData,
      matched_word: data?.matched_word,
    };
    if (data.type === 'member') {
      emit('handleTagClick', responseData);
    }
  });
};

// markdown标签点击事件
const handleTagClick = async (eventData: any): Promise<void> => {
  useGlobal().changeState('preLinkIsTodoList', false);
  const { data } = eventData;
  if (!data) {
    console.warn('Tag click data is missing');
    return;
  }
  // 检查标签是否已被点击
  if (isTagAlreadyClicked(data)) {
    return;
  }

  const action = data.action as TagAction;
  if (data.type === 'dispatchSdkAction') {
    switch (action) {
      case 'openUrl':
        handleOpenUrl(data);
        break;

      case 'confirmSendMsg':
        handleSendMessage(data);
        break;

      // case 'member':
      case 'workSummary':
      case 'createProcess':
      case 'createProcessWithDetail':
        const params = {
          ...data.content,
          type: action,
        };
        if (
          action === 'workSummary' ||
          action === 'createProcess' ||
          action === 'createProcessWithDetail'
        ) {
          params.type = 'newCollaboration';
        }
        console.log('member', params);

        handleBusinessTag(params);

        break;

      case 'openCard':
        // TODO: 实现 openCard 逻辑
        console.log('openCard action not implemented yet');
        break;

      default:
        console.warn(`Unknown tag action: ${action}`);
    }
  } else if (data.type === 'dispatchCusAction') {
    if (action === 'member') {
      const params = {
        ...data.content,
        type: action,
      };
      handleBusinessTag(params);
    }
  } else {
    handleBusinessTag(data);
  }
};

// 监听双屏数据变化
watch(
  () => dobuleScreenData.value,
  (newVal, oldVal) => {
    // 如果是辅助审批菜单，或者非知识源列表，不自动打开
    if (!showKnowledgeSource.value || uChatList.dynamicData.dobuleScreenData.type === 'iframe') {
      return;
    }

    // 检查是否应该自动打开双屏
    const shouldOpenDoubleScreen =
      props.lastIndex === props.inx && // 是最后一条数据
      newVal && // 有新数据
      newVal.timeStamp !== oldVal?.timeStamp && // 时间戳不同，防止重复打开
      (!uChatList.dynamicData.historyModel || uChatList.dynamicData.isDialogued); // 不是历史会话或已对话过

    if (shouldOpenDoubleScreen) {
      useGlobal().changeState('preLinkIsTodoList', false);
      handleCitationClick(newVal);
    }
  },
  { immediate: false },
);

onMounted(() => {});
</script>

<style scoped lang="less">
.ai_item {
  width: 100%;
  min-width: 240px;
  .user_info {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    :deep(.ant-image) {
      border-radius: 50%;
      overflow: hidden;
    }
    .ai_name {
      margin-left: 4px;
      font-size: 12px;
      font-weight: @font-weight-400;
      color: rgba(0, 0, 0, 0.6);
      //styleName: 14px/Regular;
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }
  }
  .ai_answer_box {
    border-radius: 2px 12px 12px 12px;
    padding: 8px 16px;
    overflow: hidden;
    width: fit-content;
    max-width: 100%;
    // .running_shirt_box{
    //   width: calc(100% + 28px);
    //   margin-left: -14px;
    // }

    .iframe-box {
      min-height: 400px;
      height: 600px;
      iframe {
        height: 600px !important;
        min-height: 400px !important;
      }
    }
    .ai_answer_wrap {
      background-color: #ffffff;
      font-size: 14px;
      display: inline-block;
      width: auto;
    }
    .operate_btns {
      padding-top: 8px;
      padding-left: 0px;
      span {
        cursor: pointer;
      }
      .reload_btn {
        /* margin-left: 8px; */
      }
      .copy_btn {
        margin-right: 8px;
      }

      .btn_name {
        color: rgba(0, 0, 0, 0.6);
        //styleName: 14px/Regular;
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: @font-weight-400;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        margin-left: 4px;
      }
      .down_btn {
        // margin-left: 8px;
        color: rgba(0, 0, 0, 0.6);
      }
      .up_btn {
        margin-left: 8px;
        color: rgba(0, 0, 0, 0.6);
      }
      .ud_ac_btn {
        color: @sky;
      }
      .copy_btn:hover,
      .reload_btn:hover,
      .down_btn:hover,
      .up_btn:hover {
        color: @sky;
        span {
          color: @sky;
        }
      }
      .iconfont:hover {
        color: @sky;
      }
    }
  }
  .item-caculate-width {
    width: 100%;
  }
  .anb_0 {
    width: 100%;
  }
}
.ai_answer_item {
  width: 100% !important;
  overflow-x: auto;
  scrollbar-width: none !important ;

  .knowledge_source {
    font-family: PingFang SC;
    font-weight: @font-weight-400;
    font-size: 14px;
    line-height: 22px;
    color: @sky;
    margin-bottom: 8px;
    cursor: pointer;
    display: inline-flex;

    .iconfont {
      font-size: 14px;
      margin-top: 1px;
    }
  }
}
.dis_card {
  pointer-events: none;
  opacity: 0.45;
  cursor: not-allowed;
}
.common_card {
  margin-bottom: 10px;
}
.ans_font {
  color: rgba(0, 0, 0, 0.4);
  font-size: 16px;
}
.tag_box {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 12px;
  .tag_itm {
    height: 32px;
    padding: 5px 8px;
    border-radius: 4px;
    background: #fff;
    color: rgba(0, 0, 0, 0.6);
    border: 1px solid #e3e3e3;
    font-family: PingFang SC;
    font-weight: @font-weight-400;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
  }
  .act_tag_itm {
    border: 1px solid #aac6ff;
    background: #edf2fc;
    color: #4379ff;
  }
}
</style>

<style lang="less">
.is_stop {
  font-weight: @font-weight-400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  color: rgba(0, 0, 0, 0.4);
}
.is_stop_error {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}
</style>
