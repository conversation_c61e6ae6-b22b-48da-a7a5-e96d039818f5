import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { MenuItem } from '@/components/fullMenu/types/menu'
import { useMenuData } from '@/components/fullMenu/hooks/useMenuData'
import { useMenuFavorites } from '@/components/fullMenu/hooks/useMenuFavorites'
import { useMenuNavigation } from '@/components/fullMenu/hooks/useMenuNavigation'
import { getMainPortalId } from '@/api/menu/index'

export const useMenuStore = defineStore('menu', () => {
  // 基础数据
  const portalId = ref('')
  const spaceId = ref('')
  
  // 菜单数据
  const menuList = ref<MenuItem[]>([])
  const historyList = ref<MenuItem[]>([])
  const favoritesList = ref<MenuItem[]>([])
  const navigationList = ref<MenuItem[]>([])
  const favoriteIds = ref<Set<string>>(new Set())
  
  // 加载状态
  const loading = ref(false)
  const menuLoaded = ref(false)
  const favoritesLoaded = ref(false)
  const navigationLoaded = ref(false)
  const error = ref<string | null>(null)
  
  // 搜索工具实例
  const searchMenuUtils = ref<any>(null)

  // 计算属性
  const isAllDataLoaded = computed(() => {
    return menuLoaded.value && favoritesLoaded.value && navigationLoaded.value
  })

  // 初始化Portal配置
  const initPortalConfig = async () => {
    try {
      const result = await getMainPortalId()
      if (result && (result as any).code === '0') {
        portalId.value = (result as any).data?.portalId || ''
        spaceId.value = (result as any).data?.spaceId || ''
        console.log('获取到Portal配置:', { portalId: portalId.value, spaceId: spaceId.value })
        return true
      }
    } catch (error) {
      console.error('获取Portal配置失败:', error)
      return false
    }
    return false
  }

  // 预加载菜单数据
  const preloadMenuData = async () => {
    if (loading.value || menuLoaded.value) return

    try {
      loading.value = true
      error.value = null

      // 初始化Portal配置
      const configLoaded = await initPortalConfig()
      if (!configLoaded) {
        throw new Error('Portal配置加载失败')
      }

      // 并行加载所有数据
      const [menuDataHook, favoritesHook, navigationHook] = await Promise.all([
        useMenuData(portalId, spaceId),
        useMenuFavorites(portalId, spaceId),
        useMenuNavigation(portalId)
      ])

      // 并行获取数据
      await Promise.all([
        menuDataHook.fetchMenuData(),
        menuDataHook.fetchHistoryData(),
        favoritesHook.fetchFavorites(),
        navigationHook.fetchNavigation()
      ])

      // 更新状态
      menuList.value = menuDataHook.menuList.value
      historyList.value = menuDataHook.historyList.value
      favoritesList.value = favoritesHook.favoritesList.value
      favoriteIds.value = favoritesHook.favoriteIds.value
      navigationList.value = navigationHook.navigationList.value
      searchMenuUtils.value = menuDataHook.searchMenuUtils.value

      // 标记加载完成
      menuLoaded.value = true
      favoritesLoaded.value = true
      navigationLoaded.value = true

      console.log('菜单数据预加载完成')
    } catch (err) {
      console.error('菜单数据预加载失败:', err)
      error.value = err instanceof Error ? err.message : '加载失败'
    } finally {
      loading.value = false
    }
  }

  // 获取菜单数据（如果未加载则触发加载）
  const getMenuData = async () => {
    if (!menuLoaded.value) {
      await preloadMenuData()
    }
    return {
      menuList: menuList.value,
      historyList: historyList.value,
      favoritesList: favoritesList.value,
      navigationList: navigationList.value,
      favoriteIds: favoriteIds.value,
      searchMenuUtils: searchMenuUtils.value,
      loading: loading.value,
      error: error.value
    }
  }

  // 刷新数据
  const refreshData = async () => {
    menuLoaded.value = false
    favoritesLoaded.value = false
    navigationLoaded.value = false
    await preloadMenuData()
  }

  // 清除缓存
  const clearCache = () => {
    menuList.value = []
    historyList.value = []
    favoritesList.value = []
    navigationList.value = []
    favoriteIds.value = new Set()
    menuLoaded.value = false
    favoritesLoaded.value = false
    navigationLoaded.value = false
    error.value = null
  }

  return {
    // 状态
    menuList,
    historyList,
    favoritesList,
    navigationList,
    favoriteIds,
    loading,
    menuLoaded,
    favoritesLoaded,
    navigationLoaded,
    isAllDataLoaded,
    error,
    searchMenuUtils,
    
    // 方法
    preloadMenuData,
    getMenuData,
    refreshData,
    clearCache,
    initPortalConfig
  }
}) 