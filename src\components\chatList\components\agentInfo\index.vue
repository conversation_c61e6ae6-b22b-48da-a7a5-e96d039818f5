<template>
  <div class="agent-info">
    <div class="agent-info-avatar">
      <Avatar
        :class="selectedAgentInfo.iconUrl || selectedAgentInfo.isComi ? '' : 'sky-bg'"
        :size="20"
        :src="selectedAgentInfo.isComi ? ComiLogo : selectedAgentInfo.iconUrl"
        style="flex-shrink: 0"
      >
        {{ selectedAgentInfo.iconUrl || selectedAgentInfo.isComi ? '' : selectedAgentInfo.name?.slice(0, 2) }}
      </Avatar>
    </div>
    <div class="agent-info-name">
      {{ selectedAgentInfo?.name }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { useChatList } from '@/stores/chatList';
import { Avatar } from  'ant-design-vue';
import ComiLogo from '@/assets/imgs/comi-logo.png';

const uChatList = useChatList();

const { selectedAgentInfo } = uChatList.dynamicData as any;
</script>

<style scoped lang="less">
.agent-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  .agent-info-avatar {
    width: 20px;
    height: 20px;
    img {
      border-radius: 50%;
      overflow: hidden;
    }
  }
  .agent-info-name {
    margin-left: 4px;
    font-size: 12px;
    font-weight: @font-weight-400;
    color: rgba(0, 0, 0, 0.6);
    //styleName: 14px/Regular;
    font-family: PingFang SC;
    font-weight: @font-weight-400;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
}
</style>
