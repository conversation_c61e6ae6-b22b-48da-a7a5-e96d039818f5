<template>
  <div class="menu-favorites-block">
    <div class="menu-favorites-content">
      <div class="menu-favorites-header">
        <div class="menu-favorites-title">
          <span class="menu-favorites-title-text">我的菜单</span>
          <!-- <span class="menu-favorites-count">({{ favoritesList.length }})</span> -->
        </div>
      </div>

      <div
        class="menu-favorites-items"
        v-if="favoritesList.length > 0"
      >
        <ul class="menu-favorites-list">
          <li
            v-for="item in displayFavorites"
            :key="item.idKey"
            class="menu-favorites-item"
            @click="handleSelect(item)"
            :title="item.nameKey"
          >
            <span class="menu-favorites-item-text">{{ item.nameKey }}</span>
            <button
              class="menu-favorites-remove"
              @click.stop="handleRemove(item)"
              title="取消收藏"
            >
              <i class="iconfont ai-icon-shanchu1"></i>
            </button>
          </li>
        </ul>

      </div>

      <!-- 空状态 -->
      <div
        v-else
        class="menu-favorites-empty"
      >
        <span>内容为空，可在下方收藏末级菜单</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { MenuItem } from '../types/menu'

interface Props {
  favoritesList: MenuItem[]
  searchValue: string
}

interface Emits {
  (e: 'select', item: MenuItem): void
  (e: 'remove', item: MenuItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算显示的收藏列表
const displayFavorites = computed(() => {
  return props.favoritesList
})


// 处理选择
const handleSelect = (item: MenuItem) => {
  emit('select', item)
}

// 处理移除收藏
const handleRemove = (item: MenuItem) => {
  emit('remove', item)
}
</script>

<style lang="less" scoped>
.menu-favorites-block {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 10px;
  overflow: hidden;

  .menu-favorites-content {
    padding: 16px 10px 16px 28px;

    .menu-favorites-header {
      margin-bottom: 8px;
      line-height: 18px;

      .menu-favorites-title {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: var(--theme-brand6, #4379FF);
        padding: 0 0 3px 0;

        .menu-favorites-title-text {
          flex: 1;
          font-weight: 600;
        }

        .menu-favorites-count {
          font-size: 14px;
          color: #8c8c8c;
          font-weight: normal;
        }
      }
    }

    .menu-favorites-items {
      .menu-favorites-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .menu-favorites-item {
          display: flex;
          align-items: center;
          padding: 3px 0 3px 0;
          font-size: 12px;
          color: #333;
          line-height: 20px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &::after {
            content: '';
            position: absolute;
            width: 100%;
            bottom: 0;
            left: 0;
            border-bottom: 1px dashed transparent;
          }

          &:hover {
            color: var(--theme-brand6, #4379FF);

            &::after {
              border-color: #ddd;
            }

            .menu-favorites-remove {
              opacity: 1;
            }
          }

          .menu-favorites-item-text {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .menu-favorites-remove {
            opacity: 0;
            color: var(--theme-brand6, #4379FF);
          }
        }
      }

      .menu-favorites-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        padding: 8px;
        margin-top: 8px;
        font-size: 13px;
        color: #1890ff;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          color: #40a9ff;
        }
      }
    }

    .menu-favorites-empty {
      display: flex;
      flex-direction: column;
      color: #999;
      font-size: 12px;
      line-height: 20px;
      padding-top: 4px;
    }
  }
}
</style>
