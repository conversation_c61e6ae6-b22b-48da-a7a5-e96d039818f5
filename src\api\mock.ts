export const mockData = {
  "code": "0",
  "message": null,
  "data": {
      "pluginName": "",
      "pluginKey": "",
      "cardType": 6,
      "disabled": false,
      "moreButtonType": 0,
      "pageInfo": {
          "pageNumber": 1,
          "pageSize": 10,
          "pages": 1,
          "total": 1,
          "needTotal": false
      },
      "result": [
          {
              "originApiInfo": {},
              "renderInfo": {
                  "type": "bar_chart",
                  "column_names": [
                      "订单ID",
                      "订单日期",
                      "客户ID",
                      "客户姓名",
                      "商品ID",
                      "商品名称",
                      "商品类别",
                      "销售数量",
                      "单价",
                      "订单金额",
                      "支付方式",
                      "配送地址",
                      "销售地区",
                      "销售人员姓名"
                  ],
                  "data": [
                      [
                          "ORD20031253",
                          "2024-11-19 00:00:00",
                          "CUS9989",
                          "冯峰",
                          "PROD3024",
                          "生活支持",
                          "家居用品",
                          "8",
                          633.34,
                          5066.72,
                          "微信支付",
                          "香港特别行政区佛山县秀英崔路N座 558640",
                          "华南地区",
                          "李阳"
                      ],
                      [
                          "ORD20113576",
                          "2025-03-08 00:00:00",
                          "CUS5559",
                          "杨鑫",
                          "PROD6876",
                          "专业完全",
                          "电子产品",
                          "3",
                          350.97,
                          1052.91,
                          "微信支付",
                          "上海市北京县新城饶街r座 393723",
                          "华南地区",
                          "李阳"
                      ],
                      [
                          "ORD20187351",
                          "2024-10-19 00:00:00",
                          "CUS3716",
                          "王丽华",
                          "PROD9568",
                          "最后资料",
                          "电子产品",
                          "7",
                          863.55,
                          6044.85,
                          "支付宝",
                          "北京市秀珍县海陵贵阳街U座 117614",
                          "华南地区",
                          "李阳"
                      ]
                  ],
                  "title": "",  // 拿来使用的大模型总结标题
                  "options": {},
                  "maxRows": 10, // 最大行数
                  "isOverMaxRows": false,  // 数据量是否达到最大行数
              }
          }
      ],
      "markdown":'### 洞察分析\n1. 数据显示华南地区订单主要集中在家居用品和电子产品类别，且订单金额较高。\n2. 支付方式以微信支付和支付宝为主，客户分布在不同省市，配送地址较为分散。\n3. 销售人员李阳负责了所有订单，表现出较强的销售能力。\n\n### 建议\n1. 针对家居用品和电子产品类别，进一步优化库存和促销策略，以满足市场需求。\n2. 提高支付方式的多样性，吸引更多客户选择其他支付方式。\n3. 考虑增加销售人员或分配更多资源给李阳，以提升整体销售覆盖率和效率。'
  }
}