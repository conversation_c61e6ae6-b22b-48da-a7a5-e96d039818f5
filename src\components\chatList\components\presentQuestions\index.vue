<template>
  <div :class="['prolog_wrap', astInfo?.isComi ? 'blue_wrap' : 'liner_wrap']">
    <div class="prolog_head">
      <div class="prolog_head_left">
        <div class="prolog_title">您好，我是{{ astInfo?.name }}</div>
        <div class="truncate prolog_desc">
          {{ astInfo?.introduce || '您的智能助理' }}
        </div>
      </div>
      <div class="prolog_head_right_wrap">
        <img
          class="prolog_head_right"
          :src="astInfo?.isComi ? ComiPrologBg : AssistantPrologBg"
        />
      </div>
    </div>
    <div
      v-if="astInfo?.prologue || (astInfo?.prologuePreQuestions && astInfo?.prologuePreQuestions?.length > 0)"
      :class="['question_wrap']"
    >
      <div class="prolog_content">{{ astInfo.prologue }}</div>
      <div class="question_wrap_inner" v-if="astInfo.prologuePreQuestions && astInfo.prologuePreQuestions.length > 0">
      <!-- 开场引导问题，最多只显示4个 -->
        <div
          :class="{
            question_item: true,
            question_item_last:
              astInfo?.prologuePreQuestions?.length / 2 != 0 && index == astInfo?.prologuePreQuestions?.length - 1 && isPortal &&  !uChatList.dynamicData.dobuleScreenData.show,
          }"
          v-for="(itm, index) in astInfo?.prologuePreQuestions?.slice(0, 4)"
          :key="index"
          @click="handleQuestionClick(itm)"
        >
          <div class="question_title ellipsis" :title="itm?.name">{{ itm?.name }}</div>
          <div class="question_icon">
            <Image :width="14" :src="ArrowIcon" :preview="false" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useTempRunningAssistInfo } from '@/stores/homeSearch';
import AssistantPrologBg from '@/assets/imgs/assistant_prolog_bg.png';
import ComiPrologBg from '@/assets/imgs/welcome-min.gif';
import ArrowIcon from '@/assets/imgs/arrow.png';
import type { PresentQuestionsItem } from '@/types/index';
import cardInstance from '@/stores/card';
import { Image } from 'ant-design-vue';
import { useChatList } from '@/stores/chatList';
const uChatList = useChatList();
// 从 store 中获取助手信息
const uTmpAstIfo = useTempRunningAssistInfo();
const astInfo = computed(() => uTmpAstIfo.astInfo);
const isPortal = inject('isPortal');
// 询问开场【问题】
const handleQuestionClick = (itm: PresentQuestionsItem) => {
  cardInstance.sendMessage(itm.name);
};

</script>

<style scoped lang="less">

.prolog_wrap {
    border-radius: 12px;
    position: relative;
    .prolog_head {
      color: #fff;
      z-index: -1;
      width: 100%;
      height: 86px;
      display: flex;
      border-top-right-radius: 12px;
      border-top-left-radius: 12px;
      justify-content: space-between;
      align-items: center;
      padding: 12px 12px 24px;
      background: linear-gradient(
        270deg,
        rgba(230, 195, 255, 0.6) 0%,
        rgba(128, 238, 254, 0.6) 25.91%,
        rgba(69, 122, 255, 0.85) 97.21%
      );
      box-shadow: 0px 4px 6.9px 0px rgba(0, 0, 0, 0.04);
      .prolog_head_left {
        max-width: calc(100% - 122px);
        .prolog_title {
          font-size: 16px;
          font-weight: @font-weight-500;
          line-height: 24px;
          letter-spacing: 0%;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
        .prolog_desc {
          padding-top: 4px;
          font-weight: @font-weight-400;
          font-size: 14px;
          line-height: 20px;
          letter-spacing: 0%;
          color: rgba(255, 255, 255, 0.85);
        }
      }
    }
    .question_wrap {
      padding: 12px;
      border-radius: 12px;
      margin-top: -12px;
      background: #fff;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .prolog_content {
        padding-bottom: 8px;
        font-family: PingFang SC;
        font-weight: @font-weight-400;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0%;
        color: rgba(0, 0, 0, 0.6);
      }
      .question_wrap_inner {
        display: flex;
        gap: 8px;
        justify-content: space-between;
        flex-wrap: wrap;
        width: 100%;
      }

      .question_item {
        display: flex;
        cursor: pointer;
        // margin-bottom: 8px;
        // width: 100%;
        padding: 12px;
        border-radius: 4px;
        background: #f3f6ff;
        min-width: 284px;
        flex: 1 1 calc(50% - 8px);

        .question_title {
          flex: 1;
          height: 100%;
          line-height: 40px;
          font-family: PingFang SC;
          font-weight: @font-weight-400;
          font-size: 14px;
          line-height: 16px;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          font-size: 14px;
        }
        .question_icon {
          padding-left: 12px;
          text-align: center;
          i {
            width: 14.27px;
            height: 14.27px;
            color: linear-gradient(118.82deg, #2a69fe 14.12%, #c994ff 87.99%);
          }
        }
      }
      .question_item_last {
        flex: none;
        width: calc(50% - 4px);
      }
    }
    .question_item:hover {
      .question_title {
        color: #297ffb;
      }
    }
    .prolog_head_right_wrap {
      width: 90px;
      height: 90px;
      position: absolute;
      top: -13px;
      right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      .prolog_head_right {
        width: 90px;
        height: 90px;
        margin: 0;
      }
    }

    &.blue_wrap {
      .prolog_head_right_wrap {
        width: 84px;
        height: 84px;
        top: -9px;
        .prolog_head_right {
          width: 84px;
          height: 84px;
          margin: 0;
        }
      }
    }
    &.blue_wrap {
      margin-top: 5px;
    }

    &.liner_wrap {
      margin-top: 5px;
      .prolog_head {
        background: linear-gradient(226.31deg, #c9f4ef 0%, #cbdef8 49.66%, #d8d1f7 100%);
      }
      .prolog_title {
        color: rgba(0, 0, 0, 1);
      }
      .prolog_desc {
        color: rgba(0, 0, 0, 0.9) !important;
      }
    }
  }
  @media screen and (max-width: 690px) {
  .chat_list .prolog_wrap .question_wrap .question_item_last {
    width: 100%;
  }
}
</style>
