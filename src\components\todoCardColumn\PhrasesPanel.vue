<template>
  <div class="phrases-panel" :style="{ height: height + 'px' }">
    <div class="phrases-header">
      <Button type="text" size="small" @click.stop="$emit('back')" class="back-btn">
        <span class="back-icon">‹</span>
        <span>常用语</span>
      </Button>
    </div>
    <div class="phrases-list">
      <div
        v-for="(phrase, index) in phrasesList"
        :key="index"
        class="phrase-item"
        @click.stop="handleSelectPhrase(phrase.content)"
      >
        <span class="phrase-number">{{ index + 1 }}:</span>
        <span class="phrase-content">{{ phrase.content }}</span>
      </div>
      <!-- 加载状态 -->
      <div v-if="loading" class="phrases-loading">
        正在加载常用语...
      </div>
      <!-- 空状态 -->
      <div v-else-if="phrasesList.length === 0" class="phrases-empty">
        暂无常用语
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Button } from 'ant-design-vue';
import type { PhraseItem } from './types';

interface Props {
  height?: number;
}

const props = withDefaults(defineProps<Props>(), {
  height: 200
});

const emit = defineEmits<{
  'back': [];
  'select': [content: string];
}>();

// 响应式状态
const phrasesList = ref<PhraseItem[]>([]);
const loading = ref(false);
const phrasesLoaded = ref(false);

// 常用语API缓存
let phrasesCache: any[] | null = null;
let phrasesCacheTime: number = 0;
const PHRASES_CACHE_DURATION = 10 * 60 * 1000; // 10分钟缓存

/**
 * 获取常用语列表（带缓存）
 */
const getAllPhrases = async () => {
  const now = Date.now();

  // 检查缓存是否有效
  if (phrasesCache && (now - phrasesCacheTime) < PHRASES_CACHE_DURATION) {
    console.log('使用常用语缓存数据');
    return phrasesCache;
  }

  const params = new URLSearchParams();
  params.append('managerMethod', 'getAllPhrases');
  params.append('arguments', JSON.stringify([]));

  try {
    console.log('请求常用语接口数据');
    const response = await fetch(`${(window as any)._ctxPath || '/seeyon'}/ajax.do?method=ajaxAction&managerName=phraseManager`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cache-Control': 'no-cache'
      },
      body: params
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // 更新缓存
    phrasesCache = data || [];
    phrasesCacheTime = now;

    return phrasesCache;
  } catch (error) {
    console.error('获取常用语失败:', error);
    // 如果请求失败但有缓存数据，返回缓存数据
    if (phrasesCache) {
      console.log('接口失败，使用缓存的常用语数据');
      return phrasesCache;
    }
    return [];
  }
};

/**
 * 加载常用语（带组件级缓存）
 */
const loadPhrases = async () => {
  // 如果已经加载过且有数据，直接返回
  if (phrasesLoaded.value && phrasesList.value.length > 0) {
    console.log('使用组件缓存的常用语数据');
    return;
  }

  loading.value = true;

  try {
    const data = await getAllPhrases();
    if (Array.isArray(data)) {
      phrasesList.value = data.map((item: any) => ({
        content: item.content || item.text || item.name || String(item)
      }));
      phrasesLoaded.value = true;
    } else {
      console.warn('常用语接口返回数据格式异常:', data);
      phrasesList.value = [];
    }
  } catch (error) {
    console.error('加载常用语失败:', error);
    // 加载失败时使用默认常用语作为备选
    phrasesList.value = [];
    phrasesLoaded.value = true; // 即使使用默认数据也标记为已加载
  } finally {
    loading.value = false;
  }
};

/**
 * 处理选择常用语
 */
const handleSelectPhrase = (content: string) => {
  emit('select', content);
};

/**
 * 刷新常用语列表
 */
const refresh = async () => {
  phrasesLoaded.value = false;
  phrasesList.value = [];
  await loadPhrases();
};

// 组件挂载时自动加载常用语
onMounted(() => {
  loadPhrases();
});

// 暴露方法给父组件
defineExpose({
  loadPhrases,
  refresh
});
</script>

<style scoped lang="less">
.phrases-panel {
  display: flex;
  flex-direction: column;
  background: white;

  .phrases-header {
    flex-shrink: 0; // 头部不收缩
    padding-bottom: 12px;

    .back-btn {
      padding: 4px 8px;
      border: none;
      background: transparent;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #666;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 4px;

      .back-icon {
        font-size: 18px;
        font-weight: @font-weight-500;
      }

      &:hover {
        background-color: #f5f5f5;
        color: #4379FF;
      }
    }
  }

  .phrases-list {
    flex: 1; // 占据剩余空间
    overflow-y: auto; // 超出时滚动
    padding-right: 4px; // 给滚动条留出空间

    .phrase-item {
      display: flex;
      align-items: flex-start;
      cursor: pointer;
      transition: all 0.2s ease;
      border-radius: 6px;
      margin-bottom: 4px;
      line-height: 24px;
      padding-left: 8px;

      &:hover {
        background: #f0f9ff;
      }

      .phrase-number {
        color: #999;
        font-size: 14px;
        font-weight: @font-weight-500;
        width: 20px;
      }

      .phrase-content {
        flex: 1;
        font-size: 14px;
        color: #333;
        transition: color 0.2s ease;
      }

    }

    .phrases-loading,
    .phrases-empty {
      text-align: center;
      padding: 20px;
      color: #999;
      font-size: 14px;
    }

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}
</style> 