<template>
  <div class="recursive-menu-item">
    <div
      class="menu-item-title"
      :class="{ 'is-leaf': !item.children?.length }"
      :style="{ paddingLeft: `${level * 10}px` }"
      @click="handleItemClick(item)"
    >
      <span class="menu-expand-content">
        <i
          v-if="item.children?.length"
          :class="getExpandIconClass(item)"
          class="menu-folder"
        ></i>
      </span>
      <span class="menu-expand-font" :title="item.nameKey">{{ item.nameKey }}</span>
      <span
        v-if="!item.children?.length"
        class="menu-favorite-btn"
        :class="{ active: isFavorited(item) }"
        @click.stop="handleFavoriteClick(item)"
      >
        <i
          :class="getFavoriteIconClass(item)"
          :style="{ color: getFavoriteIconColor(item) }"
        ></i>
      </span>
    </div>

    <!-- 递归渲染子菜单 -->
    <ul
      v-if="item.children?.length"
      class="menu-sub-list"
      :style="{ display: getSubListDisplay(item) }"
    >
      <li
        v-for="childItem in item.children"
        :key="childItem.idKey"
        class="menu-sub-item"
      >
        <RecursiveMenuItem
          :item="childItem"
          :searchValue="searchValue"
          :expandAll="expandAll"
          :favoriteIds="favoriteIds"
          :level="level + 1"
          @select="(item) => emit('select', item)"
          @favorite="(item) => emit('favorite', item)"
          @expand="(item) => emit('expand', item)"
              :expandedItems="expandedItems"
        />
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import type { MenuItem } from '../types/menu'

interface Props {
  item: MenuItem
  searchValue: string
  expandAll: boolean
  isItemExpanded: (itemId: string) => boolean
  setItemExpanded: (itemId: string, expanded: boolean) => void
  favoriteIds: Set<string>
  level?: number,
  expandedItems: Set<string>
}

interface Emits {
  (e: 'select', item: MenuItem): void
  (e: 'favorite', item: MenuItem): void
  (e: 'expand', item: MenuItem): void
}

const props = withDefaults(defineProps<Props>(), {
  level: 0
})
const emit = defineEmits<Emits>()

const handleItemClick = (item: MenuItem) => {
  if (item.children?.length) {
    // 有子菜单，切换展开状态
    // const isExpanded = props.isItemExpanded(item.idKey)
    // props.setItemExpanded(item.idKey, !isExpanded)
    emit('expand', item)
  } else {
    // 没有子菜单，选中该项
    emit('select', item)
  }
}

const handleFavoriteClick = (item: MenuItem) => {
  emit('favorite', item)
}

const getExpandIconClass = (item: MenuItem) => {
  if (!item.children?.length) return ''
  const isExpanded = props.expandedItems.has(item.idKey)
  return isExpanded ? 'iconfont ai-icon-shu-zhankai' : 'iconfont ai-icon-shu-shouqi'
}

const getSubListDisplay = (item: MenuItem) => {
  if (!item.children?.length) return 'none'
  const isExpanded = props.expandedItems.has(item.idKey)
  return isExpanded ? 'block' : 'none'
}

// 检查菜单项是否已收藏
const isFavorited = (item: MenuItem): boolean => {
  // 检查多种可能的ID格式
  return props.favoriteIds.has(item.id) ||
         props.favoriteIds.has(item.idKey) ||
         props.favoriteIds.has(item.idKey.replace('menu_', '')) ||
         props.favoriteIds.has(item.id.toString())
}

// 获取收藏图标样式
const getFavoriteIconClass = (item: MenuItem): string => {
  return isFavorited(item) ? 'iconfont ai-icon-yishoucang' : 'iconfont ai-icon-shoucang'
}

// 获取收藏图标颜色
const getFavoriteIconColor = (item: MenuItem): string => {
  return isFavorited(item) ? 'var(--theme-brand6, #4379FF)' : '#666666'
}
</script>

<style lang="less" scoped>
.recursive-menu-item {
  .menu-item-title {
    display: flex;
    align-items: center;
    padding-top: 3px;
    padding-right: 30px;
    padding-bottom: 3px;
    font-size: 12px;
    color: #333;
    line-height: 20px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.is-leaf {
      border-bottom: 1px transparent dashed;

      &::after {
        content: '';
        position: absolute;
        width: 100%;
        bottom: 0;
        left: 21px;
        border-bottom: 1px dashed transparent;
      }
    }

    &:hover {
      color: var(--theme-brand6, #4379FF);

      .menu-favorite-btn {
        display: inline-block;
      }

      &::after {
        border-color: #ddd;
      }
    }

    .menu-expand-font {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 22px;
      vertical-align: middle;
    }

    .menu-favorite-btn {
      display: none;
      position: absolute;
      right: -10px;
      width: 40px;
      height: 100%;
      text-align: center;
      line-height: 24px;
      cursor: pointer;

      &.active {
        display: inline-block;
      }

      i {
        font-size: 14px;
        transition: color 0.2s;
      }
    }
  }

  .menu-sub-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .menu-expand-content {
    display: inline-block;
    width: 14px;
    height: 100%;
    margin-right: 5px;
    text-align: center;
    vertical-align: top;

    .menu-folder {
      font-size: 10px;
      color: #666;
      width: 14px;
      height: 14px;
      line-height: 22px;
      vertical-align: middle;
    }
  }
}

// 搜索高亮样式
:deep(mark) {
  background-color: #fff2e8;
  color: #d48806;
  padding: 0 2px;
  border-radius: 2px;
}
</style>
