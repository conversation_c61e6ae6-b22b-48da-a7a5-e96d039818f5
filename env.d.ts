/// <reference types="vite/client" />
/// <reference types="node" />

//  声明*vue文件
declare module '*.vue' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<unknown, unknown, any>
  export default component
}

// declarations/vue-eternal-loading.d.ts
declare module '@ts-pro/vue-eternal-loading' {
  import { Plugin } from 'vue';
  const VueEternalLoading: Plugin;
  export { VueEternalLoading };
}