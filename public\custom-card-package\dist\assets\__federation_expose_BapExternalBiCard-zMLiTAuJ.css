@charset "UTF-8";
.card-summary__subtitle[data-v-7b2bcb8a] {
  height: 28px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  vertical-align: middle;
}
.card-summary__subtitle-title[data-v-7b2bcb8a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12px 12px 12px -3px;
}
.card-summary__subtitle-title .comiClose[data-v-7b2bcb8a] {
  padding: 3px;
}
.card-summary__subtitle-title .comiClose[data-v-7b2bcb8a]:hover {
  background-color: #F6F6F8;
  border-radius: 4px;
}
.card-summary__subtitle-text[data-v-7b2bcb8a] {
  margin-left: 4px;
  background: linear-gradient(90deg, #AB7DFE 0%, #5873F6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.card-header[data-v-7b2bcb8a] {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card-header .card-title[data-v-7b2bcb8a] {
  font-size: 14px;
  font-weight: 600;
}
.card-header .iconScale[data-v-7b2bcb8a] {
  position: absolute;
  top: 0;
  right: 0;
  height: 20px;
  width: 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-header .iconScale i[data-v-7b2bcb8a] {
  cursor: pointer;
  font-size: var(--card-font-size);
  color: var(--card-color-tertiary);
}
.scale-tooltip[data-v-7b2bcb8a] {
  font-size: var(--card-font-size-small);
  color: #8e94a2;
}
.card-summary__subtitle-content[data-v-7b2bcb8a] {
  width: 400px;
  max-height: 328px;
  overflow-y: auto;
  scrollbar-width: thin !important;
  scrollbar-color: transparent transparent !important;
  /* 设置所有子元素为 inline-block */
}
.card-summary__subtitle-content[data-v-7b2bcb8a] * {
  display: inline-block !important;
}
.card-summary__subtitle-content[data-v-7b2bcb8a]::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}
.card-summary__subtitle-content[data-v-7b2bcb8a]::-webkit-scrollbar-track {
  background: transparent !important;
  border-radius: 3px !important;
  -webkit-box-shadow: none;
}
.card-summary__subtitle-content[data-v-7b2bcb8a]::-webkit-scrollbar-thumb {
  background: transparent !important;
  border-radius: 3px !important;
}
.card-summary__subtitle-content[data-v-7b2bcb8a]:hover {
  scrollbar-color: #ccc transparent !important;
}
.card-summary__subtitle-content[data-v-7b2bcb8a]:hover::-webkit-scrollbar-thumb {
  background: #ccc !important;
}
.card-summary__subtitle-content[data-v-7b2bcb8a]::-webkit-scrollbar-thumb:hover {
  background: #bbb !important;
}
[data-v-7b2bcb8a] .popover-custom-container .ant-popover .ant-popover-inner {
  background: #fff var(--popover-bg);
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
  border-radius: 12px;
}
[data-v-7b2bcb8a] .popover-custom-container .ant-popover .ant-popover-inner {
  background: #fff var(--popover-bg);
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
  border-radius: 12px;
}
[data-v-7b2bcb8a] .popover-custom-container .ant-popover .ant-popover-inner .ant-popover-inner-content {
  max-width: 450px;
}
.popover-custom-container[data-v-7b2bcb8a] {
  height: 28px;
  position: absolute;
  top: -7px;
  width: 77px;
  display: flex;
  right: -8px;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
}
.popover-custom-container[data-v-7b2bcb8a]:hover {
  background-color: #F6F6F8;
}.card-content-charts[data-v-6f91ce1c] {
  margin-top: var(--card-space);
}
.cardContent[data-v-6f91ce1c] {
  box-sizing: border-box;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
}
.second-title[data-v-6f91ce1c] {
  height: 16px;
  font-size: 14px;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}
.bap-card-failed[data-v-6f91ce1c] {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.bap-card-failed-image[data-v-6f91ce1c] {
  width: 60px;
  height: 60px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}
.bap-card-failed-text[data-v-6f91ce1c] {
  font-size: 12px;
  color: #8E94A2;
  margin-top: 12px;
  margin-bottom: 12px;
}
.bap-card[data-v-41d37f14] {
  width: 100%;
  backdrop-filter: blur(59px);
  -webkit-backdrop-filter: blur(59px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, .4);
  padding: 20px 16px;
  background-color: rgba(255, 255, 255, 0.5);
}
.bap-card-title[data-v-41d37f14] {
  padding-bottom: 12px;
}
.bap-card-title-text[data-v-41d37f14] {
  font-size: 16px;
  font-weight: 600 !important;
}
.bap-card-title-time[data-v-41d37f14] {
  font-size: 12px;
  color: #0006;
  margin-left: 8px;
}
.bap-card-title-time-icon>.icon[data-v-41d37f14] {
  width: 12px;
  height: 12px;
  display: inline-block;
  margin-left: 6px;
}
