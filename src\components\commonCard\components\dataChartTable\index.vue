<template>
  <div class="data-chart-table">
    <div v-for="(item, index) in cardData?.data.result" :key="index">
      <DataTable
        v-if="item?.renderInfo?.type === 'table'"
        :isPreview="isPreview"
        :cardItem="item"
      />
      <DataChart class="data-chart" v-if="item?.renderInfo?.type !== 'table' && item.renderInfo.data.length > 0" :isPreview="isPreview" :cardItem="item" />
    </div>
  </div>
</template>
<script setup lang="ts">
import type { PropType } from 'vue';
import DataChart from '@/components/commonCard/components/dataChartTable/dataChart/index.vue';
import DataTable from '@/components/commonCard/components/dataChartTable/dataTable/index.vue';
import type { ChatItem } from '@/types/index';
import type { CardResponse } from '../../types';

defineProps({
  isPreview: {
    type: Boolean,
    default: false,
  },
  chatData: {
    type: Object as PropType<ChatItem>,
    default: () => {},
  },
  cardData: {
    type: Object as PropType<CardResponse>,
    default: () => {},
  },
});
</script>
<style lang="less" scoped>
.data-chart-table {
  min-width: 324px;
  width: 100%;

  .data-chart {
    width: 100%;
    min-height: 350px;
  }
}
</style>
