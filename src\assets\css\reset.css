	html,
	body,
	div,
	span,
	applet,
	object,
	iframe,
	h1,
	h2,
	h3,
	h4,
	h5,
	h6,
	p,
	blockquote,
	pre,
	a,
	abbr,
	acronym,
	address,
	big,
	cite,
	code,
	del,
	dfn,
	em,
	font,
	img,
	ins,
	kbd,
	q,
	s,
	samp,
	small,
	strike,
	strong,
	sub,
	sup,
	tt,
	var,
	b,
	u,
	i,
	center,
	dl,
	dt,
	dd,
	ol,
	ul,
	li,
	fieldset,
	form,
	label,
	legend,
	table,
	caption,
	tbody,
	tfoot,
	thead,
	tr,
	th,
	td,
	p {
	    border: 0;
	    margin: 0;
	    padding: 0;
	    vertical-align: baseline;
	    font-style: normal;
	    font-weight: normal;
	    -webkit-text-size-adjust: none;
	    box-sizing: border-box;
	}
	
	body {
	    /* line-height: 1; */
	    overflow-x: auto;
	}
	
	html,
	body {
	    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
	    font-size: 16px;
	    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	    height: 100%;
	    /* -webkit-touch-callout: none;
	    pointer-events: none;
	    -webkit-user-select: none;
	    -webkit-touch-callout: none; */
		scrollbar-width: none !important ; 
	}

	/* Safari 浏览器兼容性修复 */
	button,
	div[role="button"],
	.clickable,
	[onclick] {
	    outline: none !important; /* 移除 focus 轮廓 */
	    -webkit-appearance: none; /* 移除 Safari 默认样式 */
	    -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
	    -webkit-user-select: none; /* 防止文本选择 */
	    user-select: none;
	}
	
	p {
	    margin-bottom: 0px !important;
	}
	
	table {
	    border-collapse: collapse;
	    border-spacing: 0
	}
	
	object,
	 :focus {
	    outline: 0
	}
	
	li {
	    list-style: none
	}
	
	article,
	aside,
	details,
	figcaption,
	figure,
	footer,
	header,
	hgroup,
	menu,
	nav,
	section {
	    display: block
	}
	
	a img {
	    border: 0
	}
	
	input,
	button,
	textarea,
	select,
	optgroup,
	option {
	    font: inherit
	}
	
	a {
	    text-decoration: none;
	}
	
	.fl {
	    float: left;
	}
	
	.fr {
	    float: right;
	}
	
	.cf:after {
	    content: "";
	    display: block;
	    height: 0;
	    clear: both
	}
	
	.cf {
	    zoom: 1
	}