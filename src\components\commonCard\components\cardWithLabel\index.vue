<template>
  <div class="cardWithLabel">
    <PluginDesction :title="cardData?.data?.pluginName" :total="cardData?.data?.pageInfo?.total" />
    <div class="item-box">
      <ListItem v-for="(item, index) in cardData?.data?.result" :key="index" :item="item" :styles="cardData?.data?.cardStyles" />
    </div>
    <CheckButton v-if="cardData?.data?.moreButtonType != 3 && cardData?.data?.pageInfo?.total > 5 && !loadFinished" :type="cardData?.data?.moreButtonType" @handleClick="handleClickMore" />
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, PropType } from 'vue';
import { ChatItem } from '@/types/index';
import { CardResponse, CardItem } from '@/components/commonCard/types'
import PluginDesction from "../pluginDesction.vue"
import ListItem from './list-item.vue'
import CheckButton from '../check-button.vue'

const props = defineProps({
  chatData: {
    type: Object as PropType<ChatItem>,
    default: () => {},
  },
  cardData: {
    type: Object as PropType<CardResponse>,
    default: () => {},
  },
});

// 点击更多加载的条数(moreButtonType为1也就是点击更多原位展开模式下)
const PAGE_SIZE = 2
const page = ref(0)
const loadFinished = ref(false)
const resultData = ref<CardItem[]>([])

const loadData = () => {
  const items = props.cardData?.data?.result.slice(page.value * PAGE_SIZE, (page.value + 1) * PAGE_SIZE)
  page.value += 1
  resultData.value.push(...items)
  loadFinished.value = resultData.value.length >= props.cardData?.data?.result?.length
}

const handleClickMore = () => {
  console.log('=====> cardData', props.cardData)
  // 跳转到业务页面
  if (props.cardData?.data?.moreButtonType == 0) {

    // 原位展开，新增STEP条
  } else if (props.cardData?.data?.moreButtonType == 1) {
    loadData()
  }
}
loadData()
</script>
<style lang="less" scoped>
.cardWithLabel {
  .item-box {
    padding-bottom: 16px;
  }
}
</style>
