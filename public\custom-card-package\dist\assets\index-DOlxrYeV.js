import { importShared, __tla as __tla_0 } from "./__federation_fn_import-B47IVf2F.js";
import { _sfc_main as _sfc_main$1, __tla as __tla_1 } from "./index.vue_vue_type_script_setup_true_lang-oQ7yMYKS.js";
import { _sfc_main as _sfc_main$2, __tla as __tla_2 } from "./index.vue_vue_type_script_setup_true_lang-Cr-R5YZX.js";
import CardIframe, { __tla as __tla_3 } from "./__federation_expose_ComiExternalCardIframe-DwVAv6Np.js";
import { TypeSourceEnum, __tla as __tla_4 } from "./index-DtGXkB0h.js";
import { _export_sfc } from "./_plugin-vue_export-helper-8ijppmbV.js";
Promise.all([
  (() => {
    try {
      return __tla_0;
    } catch {
    }
  })(),
  (() => {
    try {
      return __tla_1;
    } catch {
    }
  })(),
  (() => {
    try {
      return __tla_2;
    } catch {
    }
  })(),
  (() => {
    try {
      return __tla_3;
    } catch {
    }
  })(),
  (() => {
    try {
      return __tla_4;
    } catch {
    }
  })()
]).then(async () => {
  const mockData = {
    "code": "0",
    "message": null,
    "data": {
      "pluginName": "",
      "pluginKey": "",
      "cardType": 6,
      "disabled": false,
      "moreButtonType": 0,
      "pageInfo": {
        "pageNumber": 1,
        "pageSize": 10,
        "pages": 1,
        "total": 1,
        "needTotal": false
      },
      "result": [
        {
          "originApiInfo": {},
          "renderInfo": {
            "type": "table",
            "column_names": [
              "ID",
              "TITLE",
              "MEETING_TYPE",
              "TEMPLATE_ID",
              "EMCEE_ID",
              "CONFEREES",
              "RECORDER_ID",
              "PROJECT_ID",
              "BEGIN_DATE",
              "END_DATE",
              "REMIND_FLAG",
              "BEFORE_TIME",
              "ADDRESS",
              "ROOM",
              "DATA_FORMAT",
              "CONTENT",
              "CREATE_DATE",
              "CREATE_USER",
              "PIGEONHOLE_USER_ID",
              "PIGEONHOLE_DATE",
              "PIGEONHOLE_PATH",
              "UPDATE_DATE",
              "UPDATE_USER",
              "STATE",
              "EXT1",
              "EXT2",
              "EXT3",
              "EXT4",
              "EXT5",
              "HAS_ATTACHMENTS",
              "ACCOUNT_ID",
              "MEETING_TYPE_ID",
              "ROOM_STATE",
              "LOOK_LEADERS",
              "RECORD_STATE",
              "RECORD_ID",
              "MT_APP_ID",
              "MEET_PLACE",
              "MT_TITLE",
              "LEADER",
              "ATTENDER",
              "TEL",
              "NOTICE",
              "MT_PLAN",
              "IS_DELETE",
              "IS_EDIT",
              "MEETING_PASSWORD",
              "MEETING_CHARACTER",
              "PERIODICITY_ID",
              "SINGLE_MODIFY_DATE",
              "IMPART",
              "IS_SENDTEXTMESSAGES",
              "MEETING_CATEGORY",
              "ALL_COUNT",
              "JOIN_COUNT",
              "UNJOIN_COUNT",
              "PENDING_COUNT",
              "QRCODE_SIGN",
              "QR_CODE_CHECK_IN",
              "SOURCE_TYPE",
              "SOURCE_ID",
              "IS_PUBLIC",
              "QR_CODE_INVITE",
              "ROOM_IDS",
              "MEETING_PROVIDER_CODE"
            ],
            "data": [
              [
                "6164735873436436810",
                "\u664B\u5347\u4F1A\u8BAE",
                "1",
                null,
                "-7615305713128617311",
                "Member|3432682429209526950",
                "-7615305713128617311",
                "-1",
                "2025-06-27 10:00:00",
                "2027-06-17 11:00:00",
                1,
                10,
                null,
                null,
                "10",
                "<p>\u664B\u5347\u4F1A\u8BAE</p>",
                "2025-06-27 09:53:45",
                "-7615305713128617311",
                null,
                null,
                null,
                "2025-06-27 09:53:45",
                "-7615305713128617311",
                20,
                null,
                "",
                null,
                null,
                null,
                0,
                "-1502048900241602583",
                "-4204474494988170048",
                1,
                null,
                1,
                null,
                null,
                null,
                "",
                null,
                "",
                "",
                null,
                null,
                null,
                null,
                "",
                null,
                null,
                null,
                null,
                0,
                0,
                2,
                1,
                0,
                0,
                0,
                "5809355659574723420",
                0,
                "0",
                0,
                "-8307008093161698287",
                null,
                null
              ]
            ],
            "title": "\u65F6\u95F4\u6700\u957F\u7684\u4F1A\u8BAE",
            "options": {},
            "maxRows": null,
            "isOverMaxRows": false
          }
        }
      ],
      "markdown": ""
    }
  };
  const { defineComponent: _defineComponent } = await importShared("vue");
  const { createElementVNode: _createElementVNode, unref: _unref, createVNode: _createVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = await importShared("vue");
  const _hoisted_1 = {
    class: "copilot-container"
  };
  const { reactive, ref } = await importShared("vue");
  const _sfc_main = _defineComponent({
    __name: "App",
    setup(__props) {
      const CardOptions = {
        status: false,
        lookSql: false,
        canLookSql: true,
        typeSource: TypeSourceEnum.PcSide
      };
      const cardOptionsPcSide = reactive({
        ...CardOptions,
        typeSource: TypeSourceEnum.PcSide
      });
      const cardOptionsPcSideExpand = reactive({
        ...CardOptions,
        typeSource: TypeSourceEnum.PcSideExpand
      });
      const cardOptionsApp = reactive({
        ...CardOptions,
        typeSource: TypeSourceEnum.App
      });
      const cardOptionsPcDialog = reactive({
        ...CardOptions,
        typeSource: TypeSourceEnum.PcDialog
      });
      const cardOptionsAppDialog = reactive({
        ...CardOptions,
        typeSource: TypeSourceEnum.AppDialog
      });
      const cardOptionsChange = (key, value) => {
        if (key === "status") {
          console.log("status", key, value);
        }
        if (key === "lookSql") {
          console.log("lookSql", key, value);
        }
      };
      const child = ref(null);
      const setClick = () => {
        var _a;
        const markText = (_a = child.value) == null ? void 0 : _a.getMarkDown();
        console.log(markText);
      };
      return (_ctx, _cache) => {
        return _openBlock(), _createElementBlock("div", _hoisted_1, [
          _cache[0] || (_cache[0] = _createElementVNode("h1", {
            class: "title"
          }, "pc\u5F39\u7A97", -1)),
          _createElementVNode("h1", {
            class: "title",
            onClick: setClick
          }, "pc\u4FA7\u8FB9\u680F"),
          _createVNode(_sfc_main$1, {
            ref_key: "child",
            ref: child,
            data: _unref(mockData),
            "card-options": cardOptionsPcSide,
            "onUpdate:cardOptions": cardOptionsChange
          }, null, 8, [
            "data",
            "card-options"
          ]),
          _cache[1] || (_cache[1] = _createElementVNode("h1", {
            class: "title"
          }, "pc\u5168\u90E8\u5C55\u5F00", -1)),
          _createVNode(_sfc_main$1, {
            data: _unref(mockData),
            "card-options": cardOptionsPcSideExpand,
            "onUpdate:cardOptions": cardOptionsChange
          }, null, 8, [
            "data",
            "card-options"
          ]),
          _cache[2] || (_cache[2] = _createElementVNode("h1", {
            class: "title"
          }, "app", -1)),
          _createVNode(_sfc_main$1, {
            data: _unref(mockData),
            "card-options": cardOptionsApp,
            "onUpdate:cardOptions": cardOptionsChange
          }, null, 8, [
            "data",
            "card-options"
          ]),
          _cache[3] || (_cache[3] = _createElementVNode("h1", {
            class: "title"
          }, "pc\u5F39\u7A97", -1)),
          _createVNode(_sfc_main$2, {
            class: "comi-container-pc",
            data: _unref(mockData),
            "card-options": cardOptionsPcDialog,
            "onUpdate:cardOptions": cardOptionsChange
          }, null, 8, [
            "data",
            "card-options"
          ]),
          _cache[4] || (_cache[4] = _createElementVNode("h1", {
            class: "title"
          }, "app\u6A2A\u5C4F\u5F39\u7A97", -1)),
          _createVNode(_sfc_main$2, {
            data: _unref(mockData),
            "card-options": cardOptionsAppDialog,
            "onUpdate:cardOptions": cardOptionsChange
          }, null, 8, [
            "data",
            "card-options"
          ]),
          _cache[5] || (_cache[5] = _createElementVNode("h1", {
            class: "title"
          }, "\u5D4C\u5165iframe", -1)),
          _createVNode(CardIframe, {
            config: {
              url: "http://www.hao123.com",
              height: 100
            },
            "onUpdate:cardOptions": cardOptionsChange
          })
        ]);
      };
    }
  });
  const App = _export_sfc(_sfc_main, [
    [
      "__scopeId",
      "data-v-66fbc60a"
    ]
  ]);
  const { createApp } = await importShared("vue");
  const app = createApp(App);
  app.mount("#app");
});
