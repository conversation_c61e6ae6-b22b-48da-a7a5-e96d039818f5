<template>
  <i :class="iconClass"></i>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
export default defineComponent({
  name: 'AiIcon'
})
</script>
<script setup lang="ts">
const props = defineProps({
  iconName: {
    type: String,
    default: ''
  }
})

const iconClass = computed(() => {
  return ['iconfont', `${props.iconName}`]
})

</script>

<style scoped>
/* Add any specific styles for the wrapper if needed, though often unnecessary */
</style>