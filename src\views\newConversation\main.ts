import { createApp, defineAsyncComponent } from 'vue'
import { createPinia } from 'pinia'
import App from './index.vue'
import { initBusinessSdk } from '@/sdk/business'
import AiDesignComponent from '@/components/ai-design/component/index'
import AiDesignLayout from '@/components/ai-design/layout/index'
import { proxyWindow } from '@/utils/commonBusinessClass'
import { getQueryString } from '@/utils/common'
// 引入icon
import '#/icon/iconfont.css'
import '#/icon/iconfont.js'
// 引入公共的css文件
import '@/assets/css/index.less'
// 引入文件icon
import '@/assets/icon/doc-iconfont.css';
//引入antd的css文件
import 'ant-design-vue/dist/reset.css'
// 引入基础插件库样式
import '@seeyon/seeyon-comi-plugins-library/dist/seeyon-comi-plugins-library.css';

// 初始化sdk
const app = createApp(App)
// 初始化pinia
app.use(createPinia())
app.use(AiDesignComponent)
app.use(AiDesignLayout)
// 初始化sdk
const sdkInstance = initBusinessSdk();

// 直接提供SDK实例
app.provide('sdkInstance', sdkInstance);

const topWindow:any = window.top || window;
const comiContainerName = window.localStorage.getItem('comiContainerName') || 'v5';
const viewModeName = 'comiTargetVersion';
// 默认值为 1.0 版本，如果存在comiTarget参数，则优先使用comiTarget参数，否则使用topWindow.comiTarget，最后使用localStorage和sessionStorage中的comiTarget
// 1.0 版本需要去除智能门户模块、智能门户的入口按钮、快捷磁贴、服务站、创作坊、
// viewMode 的值为 '1.0','1.1','A6','A8','A9'
const viewMode = getQueryString(viewModeName) || window.sessionStorage.getItem(viewModeName) || topWindow[viewModeName] || window.localStorage.getItem(viewModeName) || '1.0';

app.provide('viewMode', viewMode);
// 设置自定义指令区分视图类型
app.directive('viewMode', {
  mounted(el, binding) {
    if(!binding.value.includes(viewMode)){
      el.remove();
    }
  },
});

app.directive('onlyV5',{
  mounted(el, binding) {
    if(comiContainerName !== 'v5'){
      el.remove();
    }
  },
});

// 引入远程组件
//@ts-expect-error 远程模块类型未定义
const ComiExternalBiCard = defineAsyncComponent(() => import("remote_app/ComiExternalBiCard"));
//@ts-expect-error 远程模块类型未定义
const ComiExternalBiCardDialog = defineAsyncComponent(() => import("remote_app/ComiExternalBiCardDialog"));
//@ts-expect-error 远程模块类型未定义
const ComiExternalCardIframe = defineAsyncComponent(() => import("remote_app/ComiExternalCardIframe"));

// 代理顶层窗口属性到当前window，避免内嵌的iframe使用window.parent出现找不到对象问题
proxyWindow();

// 注册远程组件
app.component("ComiExternalBiCard", ComiExternalBiCard);
app.component("ComiExternalBiCardDialog", ComiExternalBiCardDialog);
app.component("ComiExternalCardIframe", ComiExternalCardIframe);

app.mount('#app')

