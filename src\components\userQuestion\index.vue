<template>
  <div class="mb-4 user_question">
    <div class="user_question_content">
      {{ transUserInfo.staticData.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ChatItem } from '@/types/index';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  transUserInfo: ChatItem;
}>();
</script>

<style scoped lang="less">
.user_question {
  width: 100%;
  .user_question_content {
    max-width: 90%;
    float: right;
    padding: 8px 14px;
    border-radius: 12px 12px 2px;
    background: linear-gradient(90deg, #5973fe, #4379ff);
    color: #fff;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: @font-weight-400;
    line-height: 22px;
    text-align: left;
    text-underline-position: from-font;
    -webkit-text-decoration-skip-ink: none;
    text-decoration-skip-ink: none;
    word-break: break-all;
    white-space: pre-wrap;
  } // 保留空白符和换行符
}
.fullscreen_container {
  .user_question {
    max-width: 85%;
  }
}
</style>
