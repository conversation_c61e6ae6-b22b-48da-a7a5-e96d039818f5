<template>
  <div class="state-display">
    <img v-if="stateObj.imageUrl" class="state-image" :src="stateObj.imageUrl"/>
    <p v-if="stateObj.text" class="state-text">{{ stateObj.text }}</p>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps<{
  stateObj: {
    imageUrl: string;
    text: string;
  };
}>();
</script>
<style lang="less" scoped>
.state-display {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .state-image {
      width: 80px;
    }
    .state-text{
      margin-top: 12px;
      color: #8E94A2;
      font-size: 12px;
      font-weight: @font-weight-500;
      text-align: center;
    }
  }
</style>
