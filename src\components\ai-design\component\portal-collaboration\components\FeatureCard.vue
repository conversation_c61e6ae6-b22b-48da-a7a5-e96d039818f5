<!--
 * @Author: 代琪 <EMAIL>
 * @Date: 2025-07-18 13:25:54
 * @LastEditors: 代琪 <EMAIL>
 * @LastEditTime: 2025-07-18 17:58:13
 * @FilePath: \ai-assistant-web\src\components\ai-design\component\portal-collaboration\components\FeatureCard.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="feature-card h-[120px]" @click="handleClick">
      <div class="feature-card__header mb-[8px]">
        <div class="feature-icon w-[20px] h-[20px] mr-[4px]">
          <Image
            :width="20"
            :preview="false"
            :src="featureData.icon"
            class="w-full h-full object-contain"
          />
        </div>
        <div class="flex-1 feature-card__title text-md">{{ featureData.title }}</div>
        <Image class="feature-card__arrow" :width="14" :src="ArrowIcon" :preview="false" />
      </div>
    <div class="feature-card__content text-sm">{{ featureData.description }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Image } from 'ant-design-vue';
import ArrowIcon from '@/assets/imgs/arrow.png';

interface FeatureData {
  id: string
  title: string
  description: string
  icon: string
  url: string
}

interface Props {
  data: FeatureData
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: [data: FeatureData]
}>()

const featureData = computed(() => props.data)

const handleClick = () => {
  emit('click', props.data)
}
</script>

<style scoped lang="less">
.feature-card {
  min-width: 150px;
  background: #fff;
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(59.3);
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;

  &:hover {
    box-shadow: 0px 0px 28px 0px rgba(8, 62, 221, 0.12);
  }

  &__header {
    display: flex;
    align-items: center;
  }

  &__title {
    font-weight: @font-weight-600;
    line-height: 22px;
    color: #1C1C1C;
  }

  &__content {
    line-height: 20px;
    color: #3F434D;
  }

  &__arrow {
    font-size: 12px;
    color: #999;
    transition: transform 0.2s ease;
  }
}
</style>
