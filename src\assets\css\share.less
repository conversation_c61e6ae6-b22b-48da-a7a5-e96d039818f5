@tailwind base;
@tailwind components;
@tailwind utilities;

/* // 滚动条整体宽度 */

::-webkit-scrollbar {
  width: 0px;
}

/* // 滚动条滑槽样式 */

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.3);
  border-radius: 0px;
}

/* // 滚动条样式 */

::-webkit-scrollbar-thumb {
  border-radius: 0px;
  background: #ddd;
  -webkit-box-shadow: inset 0 0 2x rgba(0, 0, 0, 0.5);
}

::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}

::-webkit-scrollbar-thumb:active {
  background: rgba(0, 0, 0, 0.4);
}

/* // 浏览器失焦的样式 */

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.4);
}

.wrap {
  width: 100%;
  height: 100%;
  // background: linear-gradient(136.77deg,
  //         #fffaed 2.91%,
  //         #f6effa 19.06%,
  //         #e6f0fc 78.22%,
  //         #e6f8e8 100%);
  background-color: #edf2fc;
  background-image: url('../imgs/top_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: 0px 0px;
  box-shadow: -4px 0px 8px 0px #918e9f1f;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dis_box {
  pointer-events: none;
}

.ai_answer_wrap .md-editor-preview-wrapper {
  padding: 0px;
  font-family: PingFang SC;
}

.ai_answer_wrap .md-editor-preview-wrapper div.default-theme p {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #000000;
  margin-top: 0px;
}

.history_menu_box {
  .go_next {
    display: flex;
    align-items: center;

    .ant-image {
      height: 100%;
    }
  }
}

.own_pop_cfirm {
  width: 200px;
  .ant-popover-inner {
    padding: 8px 18px !important;
  }
  .ant-btn-primary {
    background-color: @error-color;
  }

  .ant-btn-primary:hover {
    background-color: @error-color;
  }

  .ant-btn-default {
    // border-color: @sky;
  }

  .ant-btn-default:hover {
    border-color: @sky;
    color: black;
  }
}

.assist_item_icon {
  .ant-image {
    border-radius: 50%;
    overflow: hidden;
  }
}

.unvalid_edit_icon {
  cursor: default;
  opacity: 0.5;
}

.assistant_dia_wrap {
  .item_box {
    .ant-image {
      border-radius: 50%;
      overflow: hidden;
    }
  }
}

// 兼容firfox
.search_box_shirt textarea:focus-visible {
  outline: none !important;
}

.main_dlg,
.main_dlg > div:first-child {
  scrollbar-width: none !important;
}

.tool_item_icon {
  .ant-image {
    border-radius: 50%;
    overflow: hidden;
  }
}

.assist_close {
  svg {
    color: rgba(0, 0, 0, 0.6);
  }
}

.loading_point {
  margin: 0px !important;
  border: none !important;
  // display: none !important;
  display: inline;
}

.loading_point_hide {
  display: none !important;
}

/* 滚动条轨道 */
*:not(
  .copilot-ui-table-container .ant-table-body,
  .markdown-table,
  .knowledge-source-wrapper,
  .double-screen-content,
  .data-chart-table .data-body
) {
  scrollbar-width: none !important;
}

/* 滑块 */
*:not(
    .copilot-ui-table-container .ant-table-body,
    .markdown-table,
    .knowledge-source-wrapper,
    .double-screen-content,
    .data-chart-table .data-body
  )::-moz-scrollbar-thumb {
  border-radius: 0px;
  background: #ddd;
}

/* 轨道 */
*:not(
    .copilot-ui-table-container .ant-table-body,
    .markdown-table,
    .knowledge-source-wrapper,
    .double-screen-content,
    .data-chart-table .data-body
  )::-moz-scrollbar-track {
  border-radius: 0px;
}

/* 滚动条整体宽度 */
*:not(
    .copilot-ui-table-container .ant-table-body,
    .markdown-table,
    .knowledge-source-wrapper,
    .double-screen-content,
    .data-chart-table .data-body
  )::-moz-scrollbar {
  width: 0px; /* 垂直滚动条宽度 */
}
