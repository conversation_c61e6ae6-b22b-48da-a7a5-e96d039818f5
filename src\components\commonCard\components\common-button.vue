<template>
  <button
    :class="['default', type, disabled ? 'disabled' : '', active ? 'active' : '']"
  >
    <slot></slot>
  </button>
</template>
<script setup lang="ts">
import {ref,reactive} from "vue"

 const props = defineProps({
   disabled: {
    type: Boolean,
    default: false,
   },
   active: {
    type: Boolean,
    default: false,
   },
   // 按钮类型：error(红色)、success(绿色)、default(灰色)、primary(蓝色)、violet(紫色)
   type: {
    type: Boolean,
    default: false,
   }
  })
</script>
<style lang="less" scoped>
    button {
        flex-grow: 1;
        padding: 0 12px;
        height: 32px;
        border: 1px solid;
        border-radius: 4px;
        font-size: 14px;
        font-weight: @font-weight-400;
        background: #FFFFFF;

        &:not(:last-child) {
            margin-right: 8px;
        }
    }

    .active {
      border-color: #7559F8;
      color: #7559F8;
    }

    .default {
        border-color: #D5D6DB;
        color: rgba(0, 0, 0, 0.6);

        &:not(.disabled):hover {
            border-color: #7559F8;
            color: #7559F8;

        }
    }

    .primary, .success {
        border-color: #7559F8;
        color: #7559F8;

        &:not(.disabled):hover {
            border-color: #8781FE;
            color: #8781FE;

        }
    }

    .danger {
        border-color: #FF4D4F;
        color: #FF4D4F;

        &:not(.disabled):hover {
            border-color: #ff7875;
            color: #ff7875;

        }
    }

    .disabled {
        opacity: .45;
        cursor: not-allowed;
    }

    .info {
        background: #F5F5F5;
        border-color: #D9D9D9;
        color: #CCCCCC;

        &::before {
            content: '';
        }
    }
</style>
