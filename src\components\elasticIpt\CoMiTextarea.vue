<template>
  <div v-if="inputTemplate && inputTemplate.length" ref="rootEl" @keydown="keydown" class="comi-textarea"
    contenteditable="true" tabindex="0" :class="!isPlaceholder ? '' : 'custom-placeholder'" :placeholder="placeholder"
    deletion="1" enterkeyhint="send">
    <template :key="item.id" v-for="(item) in inputTemplate">
      <template v-if="item.type === 'input'">
        <span :name="'name_' + item.id" contenteditable="false" :class="'input-custom-before_name_' + item.id"
          class="input-custom-before input-custom-no-width">&nbsp;</span>
        <span :input-id="item.id" :name="'name_' + item.id" tabindex="0" @focus="focus" contenteditable="true"
          :placeholder="item.placeholder" deletion="0" :class="'input-custom-input_name_' + item.id" class="input-custom-placeholder input-custom-input">
        </span>
        <span :name="'name_' + item.id" contenteditable="false" :class="'input-custom-after_name_' + item.id"
          class="input-custom-after input-custom-no-width">&nbsp;</span>
      </template>
      <template v-else>{{ item.text }}</template>
    </template>
  </div>
  <textarea v-else :value="modelValue" @input="(e) => _$emit('update:modelValue', e.target.value)"
    @focus="(e) => _$emit('focus', e)" @blur="(e) => _$emit('blur', e)" :placeholder="placeholder"
    @keydown="(e) => _$emit('keydown', e)" class="comi-textarea comi-textarea-base">

</textarea>
</template>
<script setup lang="ts">
import { ref, onMounted, defineEmits, nextTick, watch } from 'vue';

const { inputTemplate } = defineProps<{
  placeholder: string; // 暂时没用到
  inputTemplate: any;
  modelValue: string;
}>();
const _$emit = defineEmits(['update:modelValue', 'keydown', 'focus', 'blur']);
// const editableSpan = ref<HTMLElement[]>([]);
const rootEl = ref(null);

const isPlaceholder = ref(false);
  // 检测Safari浏览器
const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;

watch(
  () => inputTemplate,
  (nv: any[]) => {
    if (nv && nv.length) {
      nextTick(() => {
        const el = (rootEl.value as any);
        const focusItem = nv.find(el => el.focus);
        if (nv && nv.length > 0) {
          isPlaceholder.value = false;
          initInput();
        }
        if (el) {
          const range = document.createRange();
          const sel = window.getSelection();
          let isFocus = false;

          // 检测Safari浏览器
          const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

          if (focusItem) {
            const focusEl = el.querySelector(`[input-id="${focusItem.id}"]`);
            if (focusEl) {
              if (isSafari) {
                // Safari特殊处理：直接设置焦点
                focusEl.focus();
                range.selectNodeContents(focusEl);
                range.collapse(true);
                sel?.removeAllRanges();
                sel?.addRange(range);
              } else {
                // 其他浏览器使用原有逻辑
                focusEl.innerHTML = "temp";
                const t = focusEl.childNodes[0];
                range.setStartBefore(t);
                range.collapse(true);
                sel?.removeAllRanges();
                sel?.addRange(range);
                el?.focus();
                focusEl.innerHTML = "";
              }
              isFocus = true;
            }
          }
          if (!isFocus) {
            el?.focus();
          }
        }
      });
    }
  },
);

function focus(e: any) {
  console.log("focus:", e.target)
  const el = (rootEl.value as any);
  const focusEl = e.target;
  if (isSafari) {
    // Safari特殊处理：阻止默认行为并手动处理
    if (focusEl && focusEl.getAttribute('contenteditable') === 'true') {
      // 阻止父级div的contenteditable行为
      el.setAttribute('contenteditable', 'false');

      // 设置焦点到目标span
      focusEl.focus();

      // 监听输入事件，清理Safari自动生成的标签
      const handleSafariInput = () => {
        // 清理自动生成的font标签
        const fonts = focusEl.querySelectorAll('font, span:not(.input-custom-input)');
        fonts.forEach((tag: Element) => {
          const text = tag.textContent || '';
          const textNode = document.createTextNode(text);
          tag.parentNode?.replaceChild(textNode, tag);
        });
      };

      // 恢复父级contenteditable的函数
      const restoreParentContenteditable = () => {
        el.setAttribute('contenteditable', 'true');
        // 清理事件监听器
        focusEl.removeEventListener('input', handleSafariInput);
        focusEl.removeEventListener('blur', restoreParentContenteditable);
        delete focusEl._safariInputHandler;
        delete focusEl._safariBlurHandler;
      };

      // 清理已有的监听器
      if (focusEl._safariInputHandler) {
        focusEl.removeEventListener('input', focusEl._safariInputHandler);
      }
      if (focusEl._safariBlurHandler) {
        focusEl.removeEventListener('blur', focusEl._safariBlurHandler);
      }

      // 添加新的监听器
      focusEl.addEventListener('input', handleSafariInput);
      focusEl.addEventListener('blur', restoreParentContenteditable);
      focusEl._safariInputHandler = handleSafariInput;
      focusEl._safariBlurHandler = restoreParentContenteditable;

      // 设置光标位置
      const sel = window.getSelection();
      const range = document.createRange();
      range.selectNodeContents(focusEl);
      range.collapse(false); // 设置到末尾
      sel?.removeAllRanges();
      sel?.addRange(range);
    }
  } else if (isFirefox) {
    // 其他浏览器使用原有逻辑
    if (el) {
      const range = document.createRange();
      const sel = window.getSelection();
      let isFocus = false;

      if (focusEl && !focusEl.innerHTML) {
        // 空内容时的处理
        focusEl.innerHTML = "temp";
        const t = focusEl.childNodes[0];
        range.setStartBefore(t);
        range.collapse(true);
        sel?.removeAllRanges();
        sel?.addRange(range);
        el?.focus();
        focusEl.innerHTML = "";
        // focusEl?.focus();
        isFocus = true;
      }
      if (!isFocus) {
        el?.focus();
      }
    }
  }
}

function keydown(e: any) {
  console.log('keydown:', e.target.innerHTML)
  // 发送
  if (e.type === 'keydown' && e.keyCode === 13) {
    // Enter键+shiftKey
    if (e.shiftKey) {
      // 强制换行

    } else {
      // 发送
      _$emit('keydown', { type: 'click' });
    }
    e.preventDefault();

  }
}
function initInput() {
  const el = (rootEl.value as any);
  let isDelete = false;
  if (!el) {
    return;
  }

  const observer = new MutationObserver(function (mutationsList) {
    if (!inputTemplate || inputTemplate.length == 0) {
      return;
    }
    if (isDelete) {
      isDelete = false;
      return;
    }
    if (mutationsList && mutationsList.length > 0) {
      console.log('mutationsList:', mutationsList);
      mutationsList.forEach(mutation => {
        const lastItem = mutation;
        if (lastItem) {
          if (lastItem.removedNodes) {
            Array.from(lastItem.removedNodes).forEach(node => {
              const nodeN = (node as HTMLElement);
              if (nodeN.classList?.contains('input-custom-after')) {
                console.log('back-fater');
                const name = nodeN.getAttribute('name');
                const namedElements = (lastItem.target as HTMLElement).querySelectorAll('[name="' + name + '"]');
                namedElements.forEach((element) => {
                  lastItem.target.removeChild(element);
                });
                // option.value = option.value.filter
                isDelete = true;
              } else if (nodeN.classList?.contains('input-custom-input')) {
                console.log('back-input');
                const name = nodeN.getAttribute('name');
                const nextSibling = (lastItem.target as HTMLElement).querySelector(`.input-custom-after_${name}`);
                // 如果下一个兄弟节点存在，则将新元素插入到该节点之前
                nodeN.innerHTML = '';
                if (nextSibling) {
                  lastItem.target.insertBefore(node, nextSibling);
                } else {
                  // 否则将新元素添加到父节点的末尾
                  lastItem.target.appendChild(node);
                }
                inputChange(nodeN);
                
                // Firefox修复：重新插入input元素后设置焦点
                if (isFirefox) {
                  setTimeout(() => {
                    const inputEl = nodeN as HTMLElement;
                    inputEl.focus();
                    // 设置光标到开始位置
                    const selection = window.getSelection();
                    const range = document.createRange();
                    range.setStart(inputEl, 0);
                    range.setEnd(inputEl, 0);
                    selection?.removeAllRanges();
                    selection?.addRange(range);
                  }, 0);
                }
                
                isDelete = true;
              } else if (nodeN.classList?.contains('input-custom-before')) {
                console.log('back-before');
                const name = nodeN.getAttribute('name');
                let nextSibling = (lastItem.target as HTMLElement).querySelector(`.input-custom-input_${name}`);
                if(!nextSibling){
                  nextSibling = (lastItem.target as HTMLElement).querySelector(`.input-custom-after_${name}`);
                }
                console.log('back-before', nextSibling);
                // 如果下一个兄弟节点存在，则将新元素插入到该节点之前
                if (nextSibling) {
                  lastItem.target.insertBefore(node, nextSibling);
                  // 设置光标位置
                  // nodeN.focus();
                  // 光标迁移
                  setCursorPosition(el, -1)
                  // let pos = lastItem.target.selectionStart;
                  // // 光标不能已经在最前面
                  // if (pos > 0) {
                  //   pos -= 1; // 向前移动一个字符
                  //   lastItem.target.setSelectionRange(pos, pos); // 设置光标位置
                  //   lastItem.target.focus(); // 确保输入框获得焦点
                  // }
                } else {
                  // 否则将新元素添加到父节点的末尾
                  // lastItem.target.appendChild(node);
                }
                isDelete = true;
              }
            });
          }

          let target: HTMLElement = lastItem.target as HTMLElement;
          target = (lastItem.type === 'characterData') ? target.parentElement! : target;
          if (target && target.getAttribute('input-id')) {
            inputChange(target);
          }
        }
      });
    }
    // this.$emit('input', (el as HTMLElement).innerText)
    let text = el.innerText;
    text = text.replace(/\n/g, '');
    console.log('input:', !!text);
    if (!!text) {
      isPlaceholder.value = false;
      el.classList.remove('custom-placeholder');
    } else {
      isPlaceholder.value = true;
      el.classList.add('custom-placeholder');
      el.innerText = '';
    }
    _$emit('update:modelValue', text);
    console.log('input:', `'${text}'`);
  });
  observer.observe(el, {
    childList: true,         // 监听子节点的添加或删除
    characterData: true,     // 监听节点文本内容的变化
    subtree: true,           // 监听所有后代节点
    attributes: false        // 不监听属性变化
  })
  if (el) {
    let text = el.innerText;
    text = text.replace(/\n/g, '');
    _$emit('update:modelValue', text);
    // 获取焦点
    el.focus();
  }
}

onMounted(() => {
  initInput();
});
// 设置光标位置
const setCursorPosition = (dom: HTMLElement, pos: number) => {
  if(isFirefox){
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const textLength = range.startOffset;

    if (textLength > 0) {
      range.setStart(range.endContainer, textLength - 1);
      range.setEnd(range.endContainer, textLength - 1);

      selection.removeAllRanges();
      selection.addRange(range);
    }
  }

};

// 停止会话
const inputChange = (dom: HTMLElement) => {
  if (dom.innerHTML.trim() == '') {
    dom.classList.add('input-custom-placeholder');;
  } else {
    dom.classList.remove('input-custom-placeholder');
  }
  // console.log('inputChange', option);
  // option.value[Number(index)].value = innerHTML;
};
// const setFocus = (event: any) => {
//   if(autoFocus){
//     console.log('focus:2', event);
//     // const obj = event.srcElement; //获得焦点对象
//     // const txt =obj.createTextRange(); //用createTextRange来创建Range对象操作文本
//     // txt.moveStart('character',obj.value.length); //修改文档的开始节点，向后移动长度
//     // txt.collapse(true);
//     // txt.select();
//     const el = event.target
//     const range = document.createRange()
//     const selection = window.getSelection()

//     range.selectNodeContents(el)
//     range.collapse(false)
//     selection.removeAllRanges()
//     selection.addRange(range)
//   }
// };
</script>
<style scoped lang="less">
.comi-textarea {
  width: 100%;
  font-size: 14px;
  line-height: 32px;
  // line-height: 22px;
  min-height: 44px; //初始高度44px
  max-height: 400px;

  // 处理火狐浏览器聚焦时，输入框边框显示问题
  &:focus {
    outline: none;
  }

  &.comi-textarea-base {
    background: transparent;
    resize: none;
    line-height: 22px;
  }

  &.custom-placeholder {
    &:after {
      color: rgba(0, 0, 0, 0.6);
      content: attr(placeholder);
    }
  }

  .input-custom-input {
    background-color: #EDF2FC;
    border-radius: 4px;
    color: rgb(67, 121, 255);
    display: inline-block;
    min-width: 20px;
    font-weight: @font-weight-500;
    line-height: 30px;
    max-width: calc(100% - 27px);
    padding: 0 9px;
    cursor: text;
    white-space: pre-wrap;
    word-wrap: break-word;

    &:focus {
      outline: none;
    }
  }

  .input-custom-no-width {
    display: inline-block;
    user-select: none;
    pointer-events: none;
    /* 可选，禁止交互 */
    width: 3px;
  }

  .input-custom-placeholder {
    &:after {
      color: rgba(67, 121, 255, 0.8);
      content: attr(placeholder);
    }
  }

  .input-custom-no-width {
    display: inline-block;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 3px;
  }
}
</style>
