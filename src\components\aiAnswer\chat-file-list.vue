<template>
  <div v-if="fileList.length > 0" class="files-list">
    <div
      :class="['files-item', 'cursor-pointer', `files-item-`]"
      v-for="(item, index) in fileList"
      :key="index"
    >
      <!-- 图片 -->
      <div class="item-shirt" v-if="FILE_TYPE_LIST.includes(item.fileType)">
        <AImage
          v-if="FILE_TYPE_LIST.includes(item.fileType)"
          :src="item.fileRelativeUrl"
          width="auto"
        >
          <template #previewMask>
            <eye-outlined />
          </template>
        </AImage>
      </div>
      <!-- 其他类型文件 -->
      <div class="item-shirt" v-else @click="handClickItem(item)">
        <Image
          :src="getFileIcon(getFileType(item.name) as string)"
          :width="24"
          :preview="false"
        />
        <div class="file-info">
          <div :title="item.name" class="truncate file-name">{{ item.name }}</div>
          <div class="file-size">{{ getfilesize(item.fileSize || item.size) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// 这个文件没用到
import { withDefaults } from 'vue'
import { EyeOutlined } from '@ant-design/icons-vue';
import { Image } from 'ant-design-vue';
import { getfilesize, downloadFile, getFileType, getFileIcon } from '@/utils/tool';
import { FILE_TYPE_LIST } from '@/utils/constant';

const AImage = Image;
// props
const props = withDefaults(defineProps<{
    fileList: any[] | [];
}>(),{
    fileList: ()=>[]
})

const handClickItem = (item: any) => {
  downloadFile(item.fileRelativeUrl, item.name);
};
</script>
<style lang="less" scoped>
.files-list {
  min-height: 48px;
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;

  .files-item {
    // width: 227px;
    width: auto;
    padding: 4px 14px;
    border-radius: 4px;
    background: #ffffff;
    position: relative;
    display: inline-block;

    &.files-item-1 {
      height: 48px;
    }

    &.files-item-2 {
      height: auto;
    }

    .item-shirt {
      display: flex;

      .spin_box {
        position: relative;
        top: 50%;
        display: flex;
        align-items: center;
      }

      .file-info {
        margin-left: 8px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .file-name {
          //styleName: 12px/Regular;
          font-family: PingFang SC;
          font-size: 12px;
          font-weight: @font-weight-400;
          line-height: 20px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #000;
          max-width: 160px;
        }

        .file-size {
          //styleName: 12px/Regular;
          font-family: PingFang SC;
          font-size: 12px;
          font-weight: @font-weight-400;
          line-height: 20px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: rgba(0, 0, 0, 0.4);
        }
      }
    }
  }
}
</style>
