<template>
  <!-- 运行过程 -->
  <div>
    <!-- 意图识别 -->
    <div class="w-full ai_input_wrap" v-show="props.status === 0">
      <AiIdentify>意图识别中···</AiIdentify>
    </div>
    <!-- agent执行 -->
    <div class="w-full ai_input_wrap" v-show="props.status === 1">
      <AiIdentify>Agent执行中···</AiIdentify>
    </div>
  </div>
  <!-- 取消执行 -->
  <div class="w-full ai_input_wrap" v-show="props.status === 3">
    <AiIdentify>取消执行中···</AiIdentify>
  </div>
  <!-- 已停止回答 -->
  <div class="w-full ai_input_wrap" v-show="props.status === 5">
    <AiIdentify>已停止回答</AiIdentify>
  </div>
  <!-- 取消执行 -->
  <div class="w-full ai_input_wrap" v-show="props.status === 6">
    <AiIdentify>网络超时，请检查网络设置</AiIdentify>
  </div>
  <!-- 取消执行 -->
  <div class="w-full ai_input_wrap" v-show="props.status === 7">
    <AiIdentify :showLoading="false">comi不小心发生错误啦，请稍后重试</AiIdentify>
  </div>
  <!-- 调用步骤 -->
  <!-- <div
    v-if="
      finishProgress?.finish == 0 &&
      index == chatList.length - 1 &&
      aiTempSteps.length != 0 &&
      !finishProgress?.isHaveContent
    "
  >
    <AiSteps :transtSteps="aiTempSteps" />
  </div> -->
</template>

<script setup lang="ts">
  import AiIdentify from '@/components/aiIdentify/index.vue';
  const props = defineProps({
    status: {
      type: Number,
      default: 0
    }
  });
</script>

<style scoped lang="less">
</style>