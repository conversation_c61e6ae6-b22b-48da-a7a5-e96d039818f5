<template>
  <div
    v-if="visible"
    class="intelligent-review-overlay"
    :style="{ width: width, height: height }"
  >
    <!-- 头部区域 -->
    <div class="overlay-header">
      <div class="header-left">
        <span class="header-title">智能预审<span v-if="!isLoading">·{{ actualTotalCount }}</span></span>
      </div>
      <div class="header-right">
        <div class="close-btn" @click="handleClose">
          <i class="iconfont ai-icon-cha"></i>
        </div>
      </div>
    </div>
    <!-- 操作区域 -->
    <div class="overlay-footer">
      <div class="footer-content">
        <span class="footer-description" v-if="!isLoading">为您智能预审<span class="count-number">{{ actualTotalCount }}</span>个待办事项，建议阅读后一键处理</span>
        <span class="footer-description" v-else>正在智能预审处理中...</span>
        <button
          class="process-btn"
          @click="handleProcess"
          :disabled="isProcessing || !canProcess || hasCompletedProcessing"
        >
          {{ isProcessing ? '处理中...' : hasCompletedProcessing ? '已处理' : '一键处理' }}
        </button>
      </div>
    </div>
    <!-- 内容区域 -->
    <div class="overlay-content" ref="overlayContent" @wheel="handleScroll">
      <slot name="content">
        <!-- 默认内容，如果没有传入 slot -->
        <div class="default-content">
          <div class="content-section">
            <div class="section-header">
              <img :src="PendingStarImg" class="section-icon" alt="摘要总结" />
              <span class="section-title">摘要总结</span>
            </div>
            <div class="section-content">
              <div v-if="isLoading" class="loading-content">
                <!-- <AiIdentify class="loading" v-if="isLoading" :loadingStatus="true"></AiIdentify> -->
                <p>正在获取摘要信息...</p>
              </div>
              <div v-else class="summary-content">
                <AiAnswer v-if="transAiInfo" :transAiInfo="transAiInfo" :lastIndex="11" :isColumn="true" />
                <!-- <div v-if="actualTotalCount" class="summary-content-item mb-[8px]">
                  <span v-if="summaryStatusCount.ready">已经为您生成【{{summaryStatusCount.ready}}条】</span>
                  <span v-if="summaryStatusCount.loading">，正在生成中【{{summaryStatusCount.loading}}条】</span>
                  <span v-if="summaryStatusCount.empty">，无正文未生成【{{summaryStatusCount.empty}}条】</span></div>
                <div v-else class="summary-content-item mb-[8px]">没有正文内容，未生成有效摘要。</div>
                <div v-for="(item, index) in summaryContent" :key="item" class="summary-content-item mb-[8px]">{{index + 1}}、{{item}}</div> -->
              </div>
            </div>
          </div>
        </div>
      </slot>
    </div>

  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import PendingStarImg from '@/assets/imgs/pending-star.png';
import { getIntelligentReviewSummary } from '@/api/pending/index';
import { autoRunPending } from '@/api/pending/index';
import { ComiMarkdown } from '@seeyon/seeyon-comi-plugins-library';
import AiIdentify from '@/components/aiIdentify/index.vue';
import { getBatchSummary } from '@/api/portal';
import cardInstance from '@/stores/card';
import AiAnswer from '@/components/aiAnswer/index.vue';

export interface Props {
  visible: boolean;
  width?: string;
  height?: string;
  data?: any;
  totalCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  width: '100%',
  height: '100%',
  data: null,
  totalCount: 13,
});

const emit = defineEmits<{
  close: [hasProcessed: boolean];
  process: [data: any];
}>();

// 智能预审数据接口
interface IntelligentReviewData {
  data: string;
  ids: string[];
}

interface SummaryStatusCount {
  loading: number;
  empty: number;
  ready: number;
}

// 摘要内容和批处理数据
const summaryContent = ref<string>('');
const batchIdList = ref<string[]>([]);
const isLoading = ref<boolean>(false);
const isProcessing = ref<boolean>(false);
const hasProcessed = ref<boolean>(false);
const hasCompletedProcessing = ref<boolean>(false); // 新增：标记是否已完成一键处理
const summaryStatusCount = ref<SummaryStatusCount>({
  loading: 0,
  empty: 0,
  ready: 0
}); //统计数量
const transAiInfo = ref(null);
const overlayContent = ref<HTMLElement | null>(null);
let allowScroll = true;
// 计算是否可以进行一键处理
const canProcess = computed(() => {
  return batchIdList.value && batchIdList.value.length > 0;
});

// 计算实际的待办数量
const actualTotalCount = computed(() => {
  return batchIdList.value?.length || props.totalCount || 0;
});

// 构造ComiMarkdown需要的数据格式
const summaryMarkdownContent = computed(() => {
  return {
    context: summaryContent.value || '',
    type: 'markdown',
    isCard: false,
    isIframe: false,
    isKnowledgeData: false,
    isDobuleScreen: false,
    finish: 1
  };
});

// 监听visible变化，当组件显示时获取摘要数据
watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    // 组件显示时先重置所有状态到初始状态
    hasProcessed.value = false;
    hasCompletedProcessing.value = false;
    isProcessing.value = false;
    isLoading.value = false;
    summaryContent.value = '';
    batchIdList.value = [];
    transAiInfo.value = null;
    // 然后重新加载预审数据
    await loadSummaryData();
  }
});

// 加载摘要数据
const loadSummaryData = async () => {
  isLoading.value = true;
  allowScroll = true;
    cardInstance.getSteamSummary((res)=>{
      if(isLoading.value && res.data?.cardData[0]){
        isLoading.value = false;
        const context = res.data?.cardData[0].context;
        const contextJson = JSON.parse(context);
        batchIdList.value = contextJson.ids;
      }else{
        if(res.data.cardData.length > 0 ){
            transAiInfo.value = {
            ...res,
            data: {
              ...res.data,
              // 去掉数组第一个的数据
              cardData: [...res.data.cardData].slice(1)
            }
          }
        }

        if(allowScroll){
          scrollToBottom();
        }
        // transAiInfo.value = res;
      }

    },(err)=>{

    });


  // try {
  //   isLoading.value = true;
  //   const responseIds: any = await getIntelligentReviewSummary();
  //   const pendingIds = responseIds.code == 0 ? responseIds.data?.pageData.map((item: any) => item.summaryId) : [];
  //   const affairIds = responseIds.code == 0 ? responseIds.data?.pageData.map((item: any) => item.affairId) : [];

  //   if (!pendingIds.length) {
  //     summaryContent.value = '没有正文内容，未生成有效摘要。';
  //     batchIdList.value = [];
  //     return
  //   }
  //   const response: any = await getBatchSummary({ entityIdList: pendingIds });

  //   if (response && response.code == 0 && response.data) {
  //     const responseData = response.data;

  //     // 检查content字段是否为字符串格式（JSON字符串）
  //     if (responseData.length) {
  //       try {
  //         summaryStatusCount.value = {
  //           loading: responseData.filter((item: any) => item.summary === null).length,
  //           empty: responseData.filter((item: any) => item.summary === '').length,
  //           ready: responseData.filter((item: any) => item.summary !== null && item.summary !== '').length
  //         };
  //         summaryContent.value = responseData.filter((item: any) => item.summary !== null && item.summary !== '').map((item: any) => item.summary);
  //         batchIdList.value = affairIds;
  //         console.log(summaryContent.value)
  //       } catch (parseError) {
  //         summaryContent.value = '解析摘要数据失败，请稍后重试';
  //         batchIdList.value = [];
  //       }
  //     } else {
  //       summaryContent.value = '没有正文内容，未生成有效摘要。';
  //       batchIdList.value = [];
  //     }
  //   } else {
  //     summaryContent.value = '获取摘要失败，请稍后重试';
  //     batchIdList.value = [];
  //   }
  // } catch (error) {
  //   console.error('获取智能预审摘要失败:', error);
  //   summaryContent.value = '获取摘要失败，请稍后重试';
  //   batchIdList.value = [];
  // } finally {
  //   isLoading.value = false;
  // }
};

// 处理关闭
const handleClose = () => {
  // 如果已完成一键处理，则在关闭时标记为已处理，触发数据刷新
  if (hasCompletedProcessing.value) {
    hasProcessed.value = true;
  }
  emit('close', hasProcessed.value);
};

// 处理一键处理
const handleProcess = async () => {
  if (!canProcess.value || isProcessing.value) {
    return;
  }

  try {
    isProcessing.value = true;

    // 调用一键处理接口
    const params = {
      importLevelList: [1, 2, 3],
      batchIdList: batchIdList.value,
      count: batchIdList.value.length
    };

    const response: any = await autoRunPending(params);

    if (response && response.code === '0') {
      // 处理成功，但不立即设置已处理状态，等用户关闭弹窗时再处理
      hasCompletedProcessing.value = true;
      message.success(`一键处理成功，共处理 ${params.count} 个待办事项`);
    } else {
      // 处理失败
      const errorMsg = response?.message || '一键处理失败，请稍后重试';
      message.error(errorMsg);
    }
  } catch (error) {
    console.error('一键处理失败:', error);
    message.error('网络错误，请检查网络连接后重试');
  } finally {
    isProcessing.value = false;
  }

  emit('process', props.data);
};
//滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    const scrollContainer = overlayContent.value;
    scrollContainer?.scrollTo({
      top: scrollContainer.scrollHeight + 40,
      behavior: 'smooth',
    });
  });
}
const handleScroll = () => {
  const container = overlayContent.value;
  if(container) {
    const isAtBottom = container.scrollTop + container.clientHeight >=
    container.scrollHeight - 5; //允许5px的误差
    if(isAtBottom) {
      allowScroll = true;
    }else {
      allowScroll = false;
    }
  }
}
</script>

<style scoped lang="less">
.intelligent-review-overlay {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px 16px;
  :deep(.ai_answer_box){
    background-color: transparent !important;
    padding: 0;
  }
  :deep(.ai_answer_item){
    background-color: transparent !important;
  }
  .overlay-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 12px 0;

    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;

      .header-icon {
        width: 16px;
        height: 16px;
      }

      .header-title {
        font-size: 16px;
        font-weight: @font-weight-500;
        color: #000;
        font-family: PingFang SC;
      }
    }

    .header-right {
      .close-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.06);
        }

        .iconfont {
          font-size: 16px;
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }
  }

  .overlay-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    backdrop-filter: blur(60px);
    border-radius: 12px;

    .default-content {
      .content-section {
        .section-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;

          .section-icon {
            width: 16px;
            height: 16px;
          }

          .section-title {
            font-size: 14px;
            font-weight: @font-weight-500;
          }
        }

        .section-content {
          .loading-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            p {
              margin: 0;
              font-size: 14px;
              line-height: 22px;
              color: rgba(0, 0, 0, 0.6);
              text-align: center;
              padding: 20px 0;
            }
          }

          .summary-content {
            .summary-content-item {
              margin: 0 0 12px 0;
              font-size: 14px;
              line-height: 22px;
              color: rgba(0, 0, 0, 0.9);

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }

  .overlay-footer {
    padding: 0 0 12px;

    .footer-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .footer-description {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.9);
        font-family: PingFang SC;

        .count-number {
          color: rgba(67, 121, 255, 1);
        }
      }

      .process-btn {
        flex-shrink: 0;
        padding: 0 8px;
        line-height: 28px;
        height: 28px;
        background: transparent;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: @font-weight-500;
        cursor: pointer;
        font-family: PingFang SC;
        transition: all 0.2s ease;
        background: linear-gradient(90deg, #AB7DFE 0%, #5873F6 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: transparent;
          border-radius: 4px;
          z-index: -1;
          transition: background 0.2s ease;
        }

        &:hover:not(:disabled) {
          &::before {
            background: white;
          }
        }

        &:active:not(:disabled) {
          opacity: 0.8;
        }

        &:disabled {
          cursor: not-allowed;
          opacity: 0.5;
          background: rgba(0, 0, 0, 0.25);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}
</style>
