<template>
  <div>
    <div
      class="item_title ellipsis"
      v-if="item.isHighlight"
      v-html="item.hightName"
    ></div>
    <div class="item_title ellipsis" v-else>{{ item.name }}</div>
  </div>
</template>
<script setup>
defineProps({
  item: {
    type: Object,
    default: () => ({
      name: '',
      isHighlight: false,
      hightName: '',
    }),
  },
  idx: {
    type: Number,
  },
});
</script>
<style lang="less" scoped>
.item_title {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: @font-weight-400;
  line-height: 22px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}
</style>
<style lang="less">
.item_title {
  .highlight {
    font-weight: @font-weight-500;
    color: @primary-color;
  }
}
</style>
