import { ref, reactive, type Ref } from 'vue'
import type { MenuItem, SpaceItem, MenuTreeResponse, HistoryResponse } from '../types/menu'
import { createSearchMenuUtils, getSearchMenuUtils } from '../../../utils/searchMenuUtils'
import type SearchMenuUtils from '../../../utils/searchMenuUtils'

export function useMenuData(portalId?: Ref<string>, spaceId?: Ref<string>) {
  const menuList = ref<MenuItem[]>([])
  const historyList = ref<MenuItem[]>([])
  const spaceList = ref<SpaceItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const searchMenuUtils = ref<SearchMenuUtils | null>(null)

  // 初始化SearchMenuUtils
  const initSearchMenuUtils = (): Promise<boolean> => {
    return new Promise((resolve) => {
      try {
        // 获取现有实例或创建新实例
        let utils = getSearchMenuUtils()
        if (!utils) {
          // 使用传入的portalId和spaceId创建实例
          const currentPortalId = portalId?.value || ''
          const currentSpaceId = spaceId?.value || ''
          console.log('创建SearchMenuUtils实例，portalId:', currentPortalId, 'spaceId:', currentSpaceId)
          utils = createSearchMenuUtils(currentSpaceId, currentPortalId)
        }

        searchMenuUtils.value = utils

        // 如果已经ready，直接返回
        if (utils.isReady) {
          resolve(true)
          return
        }

        // 等待SearchMenuUtils初始化完成
        utils.ready((dataTime: number, changed: boolean) => {
          resolve(true)
        })
      } catch (error) {
        resolve(false)
      }
    })
  }

  // 获取菜单数据
  const fetchMenuData = async (spaceId?: string) => {
    try {
      loading.value = true
      error.value = null

      // 先初始化SearchMenuUtils
      const searchMenuUtilsReady = await initSearchMenuUtils()

      if (searchMenuUtilsReady && searchMenuUtils.value) {
        // 从searchMenuUtils获取菜单数据
        const menuMap = searchMenuUtils.value.menuMap || {}
        const menuTree = searchMenuUtils.value.menuTree || []

        // 如果menuTree有数据，直接使用
        if (menuTree.length > 0) {
          const transformedData = transformMenuTree(menuTree)
          menuList.value = transformedData
        } else if (Object.keys(menuMap).length > 0) {
          // 否则从menuMap构建
          const menuArray = Object.values(menuMap) as MenuItem[]
          const menuTreeFromMap = buildMenuTree(menuArray)
          menuList.value = menuTreeFromMap
        } else {
          menuList.value = []
        }
      }

      // 获取历史记录
      await fetchHistoryData()

      loading.value = false

    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取菜单数据失败'
      loading.value = false
    }
  }

  // 转换SearchMenuUtils的menuTree数据格式
  const transformMenuTree = (menuTree: any[]): MenuItem[] => {
    if (!Array.isArray(menuTree) || menuTree.length === 0) {
      return []
    }

    const transformed = menuTree.map((category: any, index: number) => {
      const result = {
        id: category.id || category.idKey,
        idKey: category.idKey || `menu_${category.id}`,
        nameKey: category.nameKey || category.name,
        children: transformMenuItems(category.children || category.items || [])
      }

      return result
    })

    // 修改过滤条件 - 不过滤掉没有子项的分类
    const filtered = transformed.filter(category => {
      const hasValidName = category.nameKey && category.nameKey.trim() !== ''
      return hasValidName // 只要有名称就包含
    })

    return filtered
  }

  // 递归转换菜单项
  const transformMenuItems = (items: any[]): MenuItem[] => {
    if (!Array.isArray(items)) {
      return []
    }

    const result: MenuItem[] = []

    items.forEach((item: any, index: number) => {
      const menuItem = {
        id: item.id || item.idKey,
        idKey: item.idKey || `menu_${item.id}`,
        nameKey: item.nameKey || item.name,
        urlKey: item.urlKey || item.url,
        resourceCode: item.resourceCode,
        target: item.target || '',
        children: item.children && item.children.length > 0
          ? transformMenuItems(item.children)
          : []
      }
      result.push(menuItem)
    })

    return result
  }

  // 获取历史记录数据
  const fetchHistoryData = async () => {
    try {
      if (searchMenuUtils.value && typeof searchMenuUtils.value.getSearchHistory === 'function') {
        // 使用SearchMenuUtils获取历史记录
        searchMenuUtils.value.getSearchHistory((historyData: any[]) => {
          if (Array.isArray(historyData)) {
            historyList.value = historyData.map((item: any) => ({
              id: item.id || item.idKey,
              idKey: item.idKey || `history_${item.id}`,
              nameKey: item.nameKey || item.name,
              urlKey: item.urlKey || item.url,
              resourceCode: item.resourceCode,
              target: item.target || '',
              children: []
            }))
          } else {
            historyList.value = []
          }
        })
      } else {
        historyList.value = []
      }
    } catch (err) {
      historyList.value = []
    }
  }

  // 构建菜单树
  const buildMenuTree = (menuArray: MenuItem[]): MenuItem[] => {
    const menuMap = new Map<string, MenuItem>()
    const rootMenus: MenuItem[] = []

    // 首先创建所有菜单项的映射
    menuArray.forEach(menu => {
      menuMap.set(menu.idKey, { ...menu, children: [] })
    })

    // 然后构建树结构
    menuArray.forEach(menu => {
      const menuItem = menuMap.get(menu.idKey)
      if (menuItem) {
        if (menu.parentId) {
          const parent = menuMap.get(menu.parentId)
          if (parent) {
            parent.children = parent.children || []
            parent.children.push(menuItem)
          }
        } else {
          rootMenus.push(menuItem)
        }
      }
    })

    return rootMenus
  }

  // 刷新菜单数据
  const refreshMenuData = async (spaceId?: string) => {
    await fetchMenuData(spaceId)
  }

  return {
    menuList,
    historyList,
    spaceList,
    loading,
    error,
    searchMenuUtils,
    fetchMenuData,
    fetchHistoryData,
    refreshMenuData
  }
}
