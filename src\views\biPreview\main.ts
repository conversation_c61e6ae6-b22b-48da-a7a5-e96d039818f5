import { createApp, defineAsyncComponent } from 'vue'
import { createPinia } from 'pinia'
import App from './index.vue'
import { initBusinessSdk } from "@/sdk/business"
// 引入icon
import '#/icon/iconfont.css'
import '#/icon/iconfont.js'
// 引入公共的css文件
import '@/assets/css/index.less'

// 引入文件icon
import '@/assets/icon/doc-iconfont.css';

// 新css
//引入antd的css文件
import 'ant-design-vue/dist/reset.css'

// 引入基础插件库样式
import '@seeyon/seeyon-comi-plugins-library/dist/seeyon-comi-plugins-library.css';

// 初始化sdk
const app = createApp(App)
// 初始化sdk
const sdkInstance = initBusinessSdk();
app.provide('sdkInstance', sdkInstance);

// 引入远程组件
const ComiExternalBiCard = defineAsyncComponent(() => import("remote_app/ComiExternalBiCard"));
const ComiExternalBiCardDialog = defineAsyncComponent(() => import("remote_app/ComiExternalBiCardDialog"));
const ComiExternalCardIframe = defineAsyncComponent(() => import("remote_app/ComiExternalCardIframe"));

// 注册远程组件
app.component("ComiExternalBiCard", ComiExternalBiCard);
app.component("ComiExternalBiCardDialog", ComiExternalBiCardDialog);
app.component("ComiExternalCardIframe", ComiExternalCardIframe);

app.use(createPinia())
app.mount('#app')

