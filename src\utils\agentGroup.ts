/**
 * 根据labels对助手数据进行分组
 * @param content 助手数据数组
 * @returns Map<labelId, {key: labelId, value: content[], title: labelName}>
 */
export const createAssistantLabelMap = (content: any[], isIntelligentCreate: boolean = false) => {
  const labelMap = new Map<string, { key: string; value: any[]; title: string }>();
  const noLabelItems: any[] = []; // 存储没有labels的items

  const formatData = (item: any, label: any) => {
    const labelId = label.id;
    if (!labelMap.has(labelId)) {
      // 确保key、value和title在同一层级
      labelMap.set(labelId, {
        key: labelId, // label的id作为key，与value和title同层级
        value: [], // content数组
        title: isIntelligentCreate ? label.name.split('/')[1] : label.name // label名称，与key和value同层级
      });
    }
    labelMap.get(labelId)!.value.push(item);
  }

  content.forEach(item => {
    // 检查item是否有有效的labels
    if (item.labels && Array.isArray(item.labels) && item.labels.length > 0) {
      let hasValidLabel = false;
      item.labels.forEach((label: any) => {
        // 过滤掉name包含"/"的labels
        if (isIntelligentCreate) {
          if (label.name && label.name.includes('/')) {
            formatData(item, label);
            hasValidLabel = true;
          }
        } else {
          if (label.name && !label.name.includes('/') && label.name != '创作坊') {
            formatData(item, label);
            hasValidLabel = true;
          }
        }
      });

      // 如果没有找到有效的label，则添加到无标签组
      if (!hasValidLabel) {
        noLabelItems.push(item);
      }
    } else {
      // 没有labels或labels为空，添加到无标签组
      noLabelItems.push(item);
    }
  });

  // 如果有无标签的items，创建一个特殊的分组
  if (noLabelItems.length > 0 && !isIntelligentCreate) {
    labelMap.set('no-label', {
      key: 'no-label',
      value: noLabelItems,
      title: '其它'
    });
  }

  return labelMap;
};

/**
 * 根据Map数据创建tabs数据
 * @param labelMap 分组后的Map数据
 * @param allCount 全部数据的数量
 * @returns TabItem[]
 */
export const createTabsFromLabelMap = (labelMap: Map<string, { key: string; value: any[]; title: string }>, allCount: number) => {
  const tabList: any[] = [];
  const squareList: any[] = [];
  // 遍历labelMap创建tabs
  labelMap.forEach((labelData, labelId) => {
    tabList.push({
      key: labelData.key, // 使用labelData中的key
      label: labelData.title, // 直接使用存储的title
      count: labelData.value.length
    });
    squareList.push(labelData);
  });

  return {
    tabList,
    squareList
  };
};
