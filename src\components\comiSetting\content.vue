<template>
  <div
    class="comi-setting-content-wrap"
    :class="{
      'comi-setting-content-wrap-small': !isPortal,
    }"
  >
    <template v-if="comiSettingActive === 'content'">
      <div class="title" v-if="isPortal">CoMi设置</div>
      <div class="setting-content">
        <template v-for="item in settingList" :key="item.category">
          <div class="category-item" v-if="item.show">
            <div class="category-title" v-if="showTitle(item)">{{ item.title }}</div>

            <div class="setting-item-wrap">
              <template v-for="(subItem, subIndex) in item.childs" :key="subIndex">
                <div
                  v-if="item.category === 'switch' && comiSetting[subItem.key].show"
                  class="setting-item switch-item"
                >
                  <div class="head-wrap">
                    <span class="setting-item-title">{{ subItem.title }}</span>
                    <Switch
                      :checked="comiSetting[subItem.key].status"
                      @change="handleSwitchChange($event, subItem.key)"
                    ></Switch>
                  </div>

                  <img v-if="subItem?.img" :src="subItem?.img" />
                </div>

                <div
                  v-else-if="item.category === 'other'"
                  class="setting-item other-item"
                  @click="handleClick(subItem)"
                >
                  <span class="setting-item-title">{{ subItem.title }}</span>
                  <i class="iconfont ai-icon-you" />
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
    </template>
    <template v-else>
      <BackBtn />
      <component :is="componentName" />
    </template>
  </div>
</template>
<script setup lang="ts">
import {
  reactive,
  onMounted,
  watch,
  h,
  computed,
  ref,
  nextTick,
  onUnmounted,
  inject,
  defineOptions,
  onActivated,
} from 'vue';
import { useGlobal, useMenu, useUserInfo } from '@/stores/global';
import { message, Switch } from 'ant-design-vue';
import fuzhushenpiPng from '@/assets/imgs/fuzhushenpi.png';
import { useStateRouter } from '@/stores/stateRouter';
import ComiAbout from './about.vue';
import BackBtn from '@/components/common/backBtn/index.vue';
import { getPortalState, setPortalState } from '@/api/portal';
import type { TypeResponse } from '@/types/api';
import { getUsualAssistList } from '@/api/usualAssistant';
import { useChatList } from '@/stores/chatList';
import { getAssistIdByCode } from '@/api/home';

const useGlobalStore = useGlobal();

defineOptions({
  name: 'ComiSettingContent',
});
type ChildItem = {
  title: string;
  type: string;
  key: keyof typeof comiSetting; // Restrict key to valid keys of comiSetting
  value?: string;
  img?: string;
  [key: string]: any;
};

const { userInfo } = useUserInfo();
const uMenu = useMenu();
const stateRouter = useStateRouter();
const comiSettingActive = ref('content');
const isPortal = inject('isPortal') as boolean;
const sdkInstance = inject('sdkInstance') as any;
const viewMode = inject('viewMode') as string;

const comiSetting = reactive({
  fuzhushenpi: {
    status: false,
    show: true,
  },
  defaultPortal: {
    show: false,
    status: false,
  },
});

const comiCollaborationAbstractStateKey = computed(() => {
  return 'comiCollaborationAbstractState_' + userInfo?.id;
});
const windowTop = window?.top || window;
let isSwitched = windowTop?.localStorage.getItem(comiCollaborationAbstractStateKey.value);
const settingList = [
  {
    category: 'switch',
    title: '自动弹出',
    show: true,
    childs: [
      {
        title: '辅助审批侧边栏自动弹出',
        key: 'fuzhushenpi',
        type: 'switch',
        img: fuzhushenpiPng,
      },
    ] as ChildItem[],
  },
  {
    category: 'other',
    title: '关于帮助',
    show: true,
    childs: [
      {
        title: '查看帮助',
        key: 'help',
        type: 'link',
        name: 'help',
      },
      {
        title: '关于CoMi',
        key: 'about',
        type: 'link',
        name: 'about',
      },
    ] as unknown as ChildItem[],
  },
  {
    category: 'switch',
    title: '其他设置',
    show: ['1.1'].includes(viewMode) && useGlobalStore.globalState.comiContainerName === 'v5',
    childs: [
      {
        title: '将CoMi工作台设为“系统门户”',
        key: 'defaultPortal',
        type: 'switch',
      },
    ] as ChildItem[],
  },
];

const componentName = computed(() => {
  switch (comiSettingActive.value) {
    case 'about':
      return ComiAbout;
    default:
      return null;
  }
});

const showTitle = (item: { childs: ChildItem[]; category: string }) => {
  let count = 0;
  const childs = item.childs;
  for (let i = 0; i < childs.length; i++) {
    if (
      item.category === 'switch' &&
      !comiSetting[childs[i].key as keyof typeof comiSetting].show
    ) {
      count += 1;
    }
  }
  if (count === childs.length) {
    return false;
  } else {
    return true;
  }
};

const handleSwitchChange = async (val: any, key: keyof typeof comiSetting) => {
  if (key === 'fuzhushenpi') {
    comiSetting[key].status = val;

    window?.top?.localStorage.setItem(
      comiCollaborationAbstractStateKey.value,
      val ? 'true' : 'false',
    );
    const lines = val
      ? ['自动弹出已开启！', '在辅助审批场景CoMi会自动弹出。']
      : ['自动弹出已关闭！', '若要再次开启，可通过设置功能开启。'];

    const content = h('span', [lines[0], h('br'), lines[1]]);

    message.success({ content, class: 'custom-message' });
  } else if (key === 'defaultPortal') {
    const res = (await setPortalState({
      enable: val,
    })) as unknown as TypeResponse;
    if (res.code == '0') {
      comiSetting[key].status = val;
      const topWindow = window?.top || window;
      nextTick(() => {
        topWindow.location.reload();
      });
    } else {
      message.error('设置失败！');
    }
  }
};

const handleClick = async (item: ChildItem) => {
  if (item.name === 'help') {
    // 等待 SDK 完全初始化后再执行重定向
    try {
      if (sdkInstance.waitForInitialization) {
        await sdkInstance.waitForInitialization();
      }
      const assistId = await getAssistId('assist5780755953276634619');
      if (assistId) {
        redirectToAssit(assistId, false, null, true);
      }
    } catch (error) {
      console.error('SDK 初始化失败:', error);
      const assistId = await getAssistId('assist5780755953276634619');
      if (assistId) {
        redirectToAssit(assistId, false, null, true);
      }
    }
  } else {
    stateRouter.clearHistory();
    stateRouter.push({
      page: 'comiSetting',
      state: 'index',
    });
    stateRouter.push({
      page: item.key,
      state: item.key,
      context: {
        backBtnPos: 1,
        canBack: true,
      },
    });
    comiSettingActive.value = item.key;
  }
};

const getAssistId = async (code: string) => {
  const res: any = await getAssistIdByCode([code]);
  if (res.code === '0' && res.data && res.data.length > 0) {
    return res.data[0].id;
  }
  return null;
};
const currentMenuKey = computed(() => {
  return uMenu.currentMenuInfo?.id || 'comi';
});
const chatListStore = useChatList();
// 重定向助手
const redirectToAssit = (
  assistId?: string,
  defaultSendMsg?: boolean,
  callback?: any,
  showProlog?: boolean,
) => {
  if (assistId || (sdkInstance?.preConfig && sdkInstance.preConfig.defaultAssistId)) {
    const defaultAssistId = assistId || sdkInstance.preConfig.defaultAssistId;
    // 开始重定向，如果已经是自己了，不再执行
    if (currentMenuKey.value == defaultAssistId) {
      // TODO 要告知是否静默 发消息，如果要发，这里设置为true，定位到智能体的时候，不请求开场白，直接发消息后再清除isHide,如果不静默发消息，这里就不需要缓存，会请求开场白
      // if ((sdkInstance?.preConfig && sdkInstance.preConfig.defaultSendMsg) || defaultSendMsg) {
      //   localStorage.setItem('isHide', 'true')
      // }
      if (callback) {
        callback();
      }
      return;
    }
    // 先接口调用判断一下，常用助手是否存在，不存在则不跳转
    getUsualAssistList(7, defaultAssistId).then((res: any) => {
      if (res && res.code == 0 && res.data && res.data.length > 0) {
        const item = res.data.find((item: any) => item.id == defaultAssistId);
        let aist = item;
        if (!aist) {
          aist = {
            id: defaultAssistId,
          };
        }
        uMenu.changeMenu(item, { isAsit: true, isRedirectAist: true });
        chatListStore.chatActions.setDynamicData('showProlog', showProlog || false);
      }
      setTimeout(() => {
        if (callback) {
          callback();
        }
      }, 300);
    });
  } else {
    if (callback) {
      callback();
    }
  }
};

const getPortalSetting = async () => {
  const res = (await getPortalState()) as TypeResponse;
  if (res.code == '0' && res.data) {
    comiSetting.defaultPortal.show = res.data.isAuth;
    comiSetting.defaultPortal.status = res.data.status;
  }
};

const upDateFuzhushenpiStatus = () => {
  isSwitched = windowTop?.localStorage.getItem(comiCollaborationAbstractStateKey.value);
  if (isSwitched === 'false' || isSwitched == null || isSwitched === undefined) {
    comiSetting.fuzhushenpi.status = false;
  } else {
    comiSetting.fuzhushenpi.status = true;
  }
};

onMounted(() => {
  upDateFuzhushenpiStatus();
  getPortalSetting();
});
onUnmounted(() => {
  stateRouter.clearHistory();
});

onActivated(() => {
  upDateFuzhushenpiStatus();
  getPortalSetting();
});

watch(
  () => stateRouter.currentRoute,
  (newRoute) => {
    // 回到设置页时或者是初始化页面的时候，重置激活项
    if (newRoute && (newRoute.page === 'comiSetting' || newRoute.page === 'home')) {
      comiSettingActive.value = 'content';
    }
  },
);
</script>

<style lang="less" scoped>
.comi-setting-content-wrap {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  width: 100%;
  .title {
    font-family: PingFang SC;
    font-weight: @font-weight-500;
    font-size: 24px;
    line-height: 32px;
  }
  .setting-content {
    background: #ffffffbf;
    flex: 1;
    padding: 16px;
    box-sizing: border-box;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;

    .setting-item-wrap {
      border-radius: 12px;
      background: #edf2fc;

      .setting-item-title {
        font-family: PingFang SC;
        font-weight: @font-weight-400;
        font-size: 14px;
        line-height: 22px;
        color: #000000;
      }

      .setting-item {
        .iconfont {
          color: #999;
        }
      }
    }

    .category-item {
      margin-bottom: 20px;
    }

    .category-title {
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-size: 12px;
      line-height: 20px;
      color: #00000066;
      margin-bottom: 8px;
    }

    .switch-item {
      max-height: 218px;
      padding: 17px 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;

      .head-wrap {
        width: 100%;
        display: flex;
        justify-content: space-between;
      }
      img {
        width: 300px;
        height: 150px;
      }
    }
    .other-item {
      padding: 14px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;

      &:hover .setting-item-title {
        color: @sky;
      }
    }
  }
}
</style>
<style lang="less">
.custom-message {
  .ant-message-success {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    text-align: left;

    .anticon {
      margin-top: 3px;
    }
  }
}
</style>
