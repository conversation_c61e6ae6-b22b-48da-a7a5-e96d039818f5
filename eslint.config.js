import pluginVue from 'eslint-plugin-vue'
import vueTsEslintConfig from '@vue/eslint-config-typescript'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'

export default [{
        name: 'app/files-to-lint',
        files: ['**/*.{ts,mts,tsx,vue}'],
    },

    {
        name: 'app/files-to-ignore',
        ignores: ['**/dist/**', '**/dist-ssr/**', '**/coverage/**', 'public/**'],
    },

    ...pluginVue.configs['flat/essential'],
    ...vueTsEslintConfig(),
    skipFormatting,
    {
        rules: {
            '@typescript-eslint/no-explicit-any': 'off', // 可以使用 `any` 类型
            'vue/multi-word-component-names': 'off', // 关闭文件在规则中(rules)关闭命名规则
            '@typescript-eslint/no-unused-vars': 'off', // 忽略未使用的变量
        },
    },
]
