<template>
  <div class="data-table" ref="scrollContainer">
    <div
      class="data-body"
      :style="{
        maxHeight: '100%',
        maxWidth: '100%',
        ...tableBodyStyle,
        overflow: 'auto'
      }"
      @scroll="handleScroll"
      @click="handleClick"
      >
      <!-- overflow: isPreview ? 'auto' : 'hidden', 之前有预览，现在没有预览了，先改为auto -->
      <table class="custom-table">
        <thead>
          <tr>
            <td :key="item" v-for="item in columns" class="column-name" :title="item">
              <span>{{ item }}</span>
            </td>
          </tr>
        </thead>

        <tbody>
          <tr :key="datas[0]" v-for="datas in tableData">
            <td :key="el" v-for="el in datas" :title="el">
              <span>{{ el }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script setup lang="ts">
// import hammer from 'hammerjs';
import { ref, onMounted, watch, reactive, onBeforeUnmount, computed, defineEmits } from 'vue';
const { cardItem, isPreview } = defineProps({
  cardItem: {
    type: Object,
    default: () => {},
  },

  isPreview: {
    type: Boolean,
    default: false,
  },
});
const tableData = ref([]);
const scrollContainer = ref(null);
const scaling = ref(1);
const tableBodyStyle = reactive({
  transform: '',
});

let mc = null;

const columns = computed(() => {
  return cardItem?.renderInfo?.column_names || [];
});


onMounted(() => {
  tableData.value = cardItem?.renderInfo?.data;
  // 缩放
  // if (scrollContainer.value) {
  //   if (isPreview) {
  //     mc = new hammer(scrollContainer.value);
  //     const pinch = new hammer.Pinch();

  //     mc.add(pinch);
  //     mc.on('pinchstart pinchmove pinchend', function (ev) {
  //       if (ev.type === 'pinchstart') {
  //         // 存储初始缩放级别
  //         ev.target.dataset.scale = scaling.value;
  //       } else if (ev.type === 'pinchmove') {
  //         scaling.value = ev.scale;
  //       } else if (ev.type === 'pinchend') {
  //         if (ev.scale < 0.25) {
  //           scaling.value = 0.25;
  //         } else if (ev.scale > 2) {
  //           scaling.value = 2;
  //         } else {
  //           scaling.value = ev.scale;
  //         }
  //       }
  //     });
  //   } else {
  //   }
  // }
});
onBeforeUnmount(() => {
  if (scrollContainer.value) {
    if (mc) {
      mc.destroy();
      mc = null;
    }
  }
});
watch(
  () => scaling.value,
  (newVal, oldVal) => {
    let scalingVal = newVal.toFixed(2);
    if (scalingVal < 1) {
      tableBodyStyle.transform = `scale(${scalingVal})`;
    } else {
      tableBodyStyle.transform = `scale(${scalingVal})`;
    }
  }
);
const handleScroll = event => {
  // const { scrollTop, clientHeight, scrollHeight } = event.target;
  // // 判断是否滚动到底部
  // const isBottom = scrollTop + clientHeight >= scrollHeight;
  // if (isBottom && !this.isLoading) {
  //   // this.isLoading = true;
  //   this.page_num++;
  //   if (this.page_num <= this.maxPage) {
  //     console.log('page_num', this.page_num, store);
  //     const params = {
  //       page_num: this.page_num,
  //       page_size: this.page_size,
  //       parent_chat_id: store?.state?.context?.parent_chat_id || '',
  //       session_id: store?.state?.context?.session_id || '',
  //     };
  //     if (params.parent_chat_id && params.session_id) {
  //       this.API.action.getTableData(params).then(res => {
  //         console.log('getTableData', res);
  //         this.isLoading = false;
  //         this.tableData = this.tableData.concat(res?.data);
  //         console.log('page_num', this.tableData);
  //       });
  //     }
  //   }
  // }
};
</script>
<style lang="less">
.data-table {
  width: 100%;
  overflow: auto;
  position: relative;
  overflow: auto;
  // overflow-x: auto;
  /* 确保内部元素能够正确定位 */
  .custom-table {
    border-collapse: collapse;
    /* 合并边框 */
  }

  .data-body {
    max-height: 240px;
    max-width: 622px;
    overflow: hidden;
  }
  .custom-table,
  .custom-table th,
  .custom-table td {
    border: 1px solid #bdc5d6;
    white-space: normal;
  }
  .custom-table {
    width: 100%;
    .column-name {
      background-color: #f3f6ff;
      color: rgba(0, 0, 0, 0.6);
    }
    th,
    td {
      min-width: 68px;
      /* 设置边线颜色为蓝色 */
      padding: 8px 12px;
      height: 39px;
      box-sizing: border-box;
      text-align: center;
      span {
        word-break: keep-all;
        white-space: nowrap;
      }
    }
    tbody {
      background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%) !important;
    }
  }
}
</style>
