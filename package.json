{"name": "ai-assistant-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"start": "npm run dev", "dev": "npm run dev:v5", "dev:v5": "vite --mode v5.development", "dev:v8": "vite --mode v8.development", "build:v5": "vite build --mode v5.production", "build:v8": "vite build --mode v8.production", "build": "vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix", "format": "prettier --write src/", "prepare": "husky install"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@microsoft/fetch-event-source": "^2.0.1", "@originjs/vite-plugin-federation": "^1.4.1", "@seeyon/seeyon-comi-plugins-library": "^1.0.85", "@ts-pro/vue-eternal-loading": "^1.3.1", "ant-design-vue": "^4.2.5", "axios": "^1.7.7", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "echarts": "^5.5.1", "husky": "^9.1.7", "json5": "^2.2.3", "lint-staged": "^15.2.11", "lodash-es": "^4.17.21", "lossless-json": "^4.0.2", "md-editor-v3": "5.4.5", "mitt": "^3.0.1", "moment": "^2.30.1", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.1.3", "vite-plugin-top-level-await": "^1.5.0", "vue": "^3.5.12", "vue-json-pretty": "^2.0.6", "vue-router": "^4.4.5"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@tsconfig/node22": "^22.0.0", "@types/node": "^22.9.0", "@vitejs/plugin-vue": "^5.1.4", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.21", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "less": "^4.2.0", "less-loader": "^12.2.0", "npm-run-all2": "^7.0.1", "postcss": "^8.4.48", "postcss-px-to-viewport": "^1.1.1", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "^3.3.3", "rollup-plugin-visualizer": "^5.12.0", "tailwindcss": "^3.4.14", "typescript": "~5.6.3", "unplugin-auto-import": "^19.1.2", "vite": "^5.4.10", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^7.7.5", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^2.1.10"}, "lint-staged": {"*.{js,jsx,ts,css,less,md.vue}": "eslint --cache --fix", "*.src/": "prettier --write"}}