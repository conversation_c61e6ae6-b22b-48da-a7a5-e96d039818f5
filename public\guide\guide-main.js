var GuideAPI = function () {
  // 测试模式
  this.isTest = false;
};
GuideAPI.prototype.env = {
  count: 0, // 重试次数
  time: 500, // 重试延迟
  currCount: 0, // 当前重试次数
  maxLoopCount: 5,
};
GuideAPI.prototype.getGuidePath = function () {
  return "/seeyon/ai-platform/ai-static/ai-copilot/public/guide";
};
GuideAPI.prototype.init = function (options) {
  window.GuideItem = null;
  GuideAPI.prototype.env.options = options;
  // GuideAPI.prototype.env.currUsetId = cmp.member.id || '';
  GuideAPI.prototype.initKeys(options);
};
GuideAPI.prototype.initKeys = function (options) {
  var keys = options.keys;
  // 检查keys 是否已经触发
  GuideAPI.prototype.checkLocalKeys.call(this, keys, function(reskeys) {
    console.log("--->", reskeys);
    if(Object.keys(reskeys).length){
      GuideAPI.prototype.loadDriverPlugin(function () {
        if(window.GuideItem){
          var guideItem = new GuideItem(reskeys);
          GuideAPI.prototype.loopRunFunction.call(this,  guideItem);
        }else{
          var url = "";
          var itemName = "/guide-item-"+options.itemKey+".js";
          // var urltag = "?t="+new Date().getTime();
          var urltag = "?t="+new Date().getTime();
          if (window.guideAPI.isTest) {
            urltag = "";
          }
          url =  GuideAPI.prototype.getGuidePath() + itemName + urltag;
          //加载模块引导
          GuideAPI.prototype.loadJs(url, function() {
            var guideItem = new GuideItem(reskeys);
            GuideAPI.prototype.loopRunFunction.call(this,  guideItem);
          });
        }
      });
    }
  });
};
GuideAPI.prototype.loadJs = function (url, callback) {
  var script = document.createElement('script');
  script.type = 'text/javascript';
  script.src = url;
  script.onload = function () {
    callback && callback();
  };
  document.getElementsByTagName('head')[0].appendChild(script);
};
GuideAPI.prototype.loadDriverPlugin = function (callback) {
  if(window.driver){
    callback && callback();
  }else{
    var oCss = document.createElement('link');
    oCss.rel = 'stylesheet';
    oCss.type = 'text/css';
    oCss.href = GuideAPI.prototype.getGuidePath() + '/driver.css';
    // document.body.appendChild(oCss);
    document.getElementsByTagName('head')[0].appendChild(oCss);
    var script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = GuideAPI.prototype.getGuidePath() + '/driver.js';
    script.onload = function () {
      callback && callback();
    };
    document.getElementsByTagName('head')[0].appendChild(script);
  }
};
GuideAPI.prototype.loopRunFunction = function (guideItem, time) {
  if (this.GuideAPI.prototype.env.currCount > GuideAPI.prototype.env.maxLoopCount) {
    return;
  }
  this.GuideAPI.prototype.env.currCount++;
  setTimeout(function () {
    var status = guideItem.start();
    if (!status) {
      GuideAPI.prototype.loopRunFunction.call(this, guideItem);
    }
  }, time || GuideAPI.prototype.env.time);
};
// 存储
GuideAPI.prototype.checkLocalKeys = function (keys, callback) {
  if (keys) {
    var readyCount = 0;
    var reskeys = {};
    var useLessKeys = [];
    for (var keyIndex = 0; keyIndex < keys.length; keyIndex++) {
      var key = keys[keyIndex];
      var userKey = GuideAPI.prototype.env.currUsetId + '_' + key;
      if (window.guideAPI.isTest) {
        localStorage.removeItem(userKey);
        reskeys[key] = userKey;
      }
      var userKeyValue = localStorage.getItem(userKey);
      if (!userKeyValue) {
        useLessKeys.push(key);
        // TODO: 接入后端以后就删除
        reskeys[key] = userKey;
      }
    }
    if (window.guideAPI.isTest) {
      callback(reskeys);
      return;
    }
    if (useLessKeys.length == 0) {
      return;
    }
    // var paramKeys = useLessKeys.join(',');
    // var url = `${_ctxPath}/ajax.do?method=ajaxAction&managerName=pendingManager&rnd=${this.getRandom()}`;
    // var url = cmp.util.getSeeyonPath() + '/rest/guide/page/remind/record/get';
    // "arguments": "[{\"module\":\"202402_NewFeatureGuide\"}]"
    // cmp.ajax({
    //   type: 'POST',
    //   url: url,
    //   data: JSON.stringify({
    //     module: paramKeys
    //   }),
    //   contentType: 'application/json;charset=UTF-8',
    //   success: function (res) {
    //     readyCount = readyCount + 1;
    //     console.log('res', res);
    //     if (res.code == 0) {
    //       var data = res.data;
    //       for (var item in data) {
    //         if (!data[item]) {
    //           var currentKey = GuideAPI.prototype.env.currUsetId + '_' + item;
    //           reskeys[item] = currentKey;
    //         }
    //       }
    //       callback(reskeys);
    //     } else {
    //       window.console && console.error('result: ', JSON.stringify(result));
    //     }
    //   },
    //   error: function (err) {
    //     readyCount = readyCount + 1;
    //     console.log('err', err);
    //     if (readyCount === keys.length) {
    //       callback(reskeys);
    //     }
    //   },
    // });
    // console.log('err', err);
    if (keys.length) {
      callback(reskeys);
    }
  }
};
GuideAPI.prototype.splitKeys = function (frontKeys) {
  var splitKeys = frontKeys.split('_');
  var realKey = '';
  if (splitKeys && splitKeys.length === 2) {
    realKey = splitKeys[1];
  }
  return realKey;
};

// 存储
GuideAPI.prototype.saveLocal = function (key, callback) {
  // if (key) {
  //   var url = cmp.util.getSeeyonPath() + '/rest/guide/page/remind/record/save';
  //   cmp.ajax({
  //     type: 'POST',
  //     url: url,
  //     data: JSON.stringify({
  //       module: key
  //     }),
  //     contentType: 'application/json;charset=UTF-8',
  //     success: function (res) {
  //       console.log('res', res);
  //     },
  //     error: function (err) {
  //       console.log('err', err);
  //     },
  //   });
  // }
};
GuideAPI.prototype.getQueryString = function (name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return '';
};

function getChromeVersion() {
  var arr = navigator.userAgent.split(' ');
  var chromeVersion = '';
  for (var i = 0; i < arr.length; i++) {
    if (/chrome/i.test(arr[i])) chromeVersion = arr[i];
  }
  if (chromeVersion) {
    var version = chromeVersion.split('/');
    if (version[1]) {
      return Number(version[1].split('.')[0]);
    }
  } else {
    return false;
  }
}
window.guideAPI = new GuideAPI();
