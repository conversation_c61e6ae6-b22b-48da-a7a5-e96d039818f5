import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { MenuItem } from '../types/menu'

export function useMenuWaterfall() {
  const screenWidth = ref(window.innerWidth)
  const expandAll = ref(false)
  const expandedItems = ref<Set<string>>(new Set())

  // 计算列数
  const columnCount = computed(() => {
    if (screenWidth.value > 1440) return 4
    return 3
  })

  // 监听窗口大小变化
  const handleResize = () => {
    screenWidth.value = window.innerWidth
  }

  // 切换全部展开/收起
  const toggleExpandAll = () => {
    console.log('toggleExpandAll called, current expandAll:', expandAll.value)
    expandAll.value = !expandAll.value
    console.log('toggleExpandAll after change, expandAll:', expandAll.value)

    if (expandAll.value) {
      // 全部展开时，清空手动展开的记录
      expandedItems.value.clear()
      console.log('expandedItems cleared for expand all')
    } else {
      // 全部收起时，清空所有展开记录
      expandedItems.value.clear()
      console.log('expandedItems cleared for collapse all')
    }
  }

  // 处理单个菜单项的展开/收起
  const handleToggleExpand = (item: MenuItem) => {
    console.log('切换展开状态:', item)
    if (expandedItems.value.has(item.idKey)) {
      expandedItems.value.delete(item.idKey)
    } else {
      expandedItems.value.add(item.idKey)
    }
  }

  // 检查菜单项是否展开
  const isItemExpanded = (itemId: string): boolean => {
    return expandAll.value || expandedItems.value.has(itemId)
  }

  // 设置菜单项展开状态
  const setItemExpanded = (itemId: string, expanded: boolean) => {
    if (expanded) {
      expandedItems.value.add(itemId)
    } else {
      expandedItems.value.delete(itemId)
    }
  }

  // 将菜单列表分配到各列
  const distributeMenusToColumns = (menuList: MenuItem[]) => {
    const columns: MenuItem[][] = Array.from({ length: columnCount.value }, () => [])

    menuList.forEach((menu, index) => {
      const columnIndex = index % columnCount.value
      columns[columnIndex].push(menu)
    })

    return columns
  }

  // 获取菜单项高度估算（用于瀑布流布局优化）
  const getMenuItemHeight = (menu: MenuItem): number => {
    // 基础高度
    let height = 60 // 标题高度

    // 计算子菜单项高度
    if (menu.children && menu.children.length > 0) {
      height += menu.children.length * 24 // 每个子菜单项约24px

      // 如果有嵌套子菜单，再加上高度
      menu.children.forEach(child => {
        if (child.children && child.children.length > 0) {
          height += child.children.length * 24
        }
      })
    }

    return height
  }

  // 优化版本的瀑布流分配（考虑高度平衡）
  const distributeMenusBalanced = (menuList: MenuItem[]) => {
    const columns: MenuItem[][] = Array.from({ length: columnCount.value }, () => [])
    const columnHeights = new Array(columnCount.value).fill(0)

    menuList.forEach(menu => {
      // 找到当前高度最小的列
      let minHeightIndex = 0
      for (let i = 1; i < columnHeights.length; i++) {
        if (columnHeights[i] < columnHeights[minHeightIndex]) {
          minHeightIndex = i
        }
      }

      // 将菜单项添加到高度最小的列
      columns[minHeightIndex].push(menu)
      columnHeights[minHeightIndex] += getMenuItemHeight(menu)
    })

    return columns
  }

  // 处理菜单项点击
  const handleMenuItemClick = (item: MenuItem) => {
    console.log('菜单项点击:', item)
  }

  // 获取菜单项的样式类
  const getMenuItemClass = (item: MenuItem) => {
    const classes = ['menu-item']

    if (item.children && item.children.length > 0) {
      classes.push('has-children')
    }

    return classes.join(' ')
  }

  // 检查菜单项是否应该显示
  const shouldShowMenuItem = (item: MenuItem, searchValue: string) => {
    if (!searchValue) return true

    // 检查菜单项名称是否匹配搜索
    if (item.nameKey.toLowerCase().includes(searchValue.toLowerCase())) {
      return true
    }

    // 检查资源代码是否匹配搜索
    if (item.resourceCode && item.resourceCode.toLowerCase().includes(searchValue.toLowerCase())) {
      return true
    }

    // 检查子菜单是否匹配搜索
    if (item.children && item.children.length > 0) {
      return item.children.some(child => shouldShowMenuItem(child, searchValue))
    }

    return false
  }

  // 过滤菜单项
  const filterMenuItems = (menuList: MenuItem[], searchValue: string): MenuItem[] => {
    if (!searchValue) return menuList

    return menuList.filter(item => shouldShowMenuItem(item, searchValue))
  }

  // 生命周期钩子
  onMounted(() => {
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  return {
    screenWidth,
    columnCount,
    expandAll,
    expandedItems,
    toggleExpandAll,
    handleToggleExpand,
    isItemExpanded,
    setItemExpanded,
    distributeMenusToColumns,
    distributeMenusBalanced,
    getMenuItemHeight,
    handleMenuItemClick,
    getMenuItemClass,
    shouldShowMenuItem,
    filterMenuItems
  }
}
