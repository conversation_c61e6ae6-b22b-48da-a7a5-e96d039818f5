<template>
  <div class="suggest_content">
    <div class="tag_box">
      <div
        :class="['tag_itm', item.isUnActive ? 'act_tag_itm' : '']"
        v-for="(item, index) in adviceTags"
        :key="index"
        @click="goSecThisAdvice(item.id, index)"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps<{
  adviceTags: { id: number; name: string; isUnActive: boolean }[];
  goSecThisAdvice: (d: string | number, inx: number) => void;
  suggestContent: string;
}>();
</script>
<style lang="less" scoped>
.tag_box {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 12px;
  .tag_itm {
    height: 32px;
    padding: 5px 8px;
    border-radius: 4px;
    background: #fff;
    color: rgba(0, 0, 0, 0.6);
    border: 1px solid #e3e3e3;
    font-family: PingFang SC;
    font-weight: @font-weight-400;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
  }
  .act_tag_itm {
    border: 1px solid #aac6ff;
    background: #edf2fc;
    color: #4379ff;
  }
}
</style>
