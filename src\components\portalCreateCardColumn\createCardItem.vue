<template>
  <div
    class="assistant-card flex rounded-lg bg-white"
    @click="handleClick"
  >
    <!-- iconUrl/Icon -->
    <div class="flex-shrink-0 mr-[8px]">
      <img v-if="iconUrl" :src="iconUrl"  class="w-[50px] h-[50px]  rounded-full object-cover border border-gray-200" />
      <!-- Placeholder Icon if no iconUrl -->
      <div v-else class="w-[50px] h-[50px]  rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center text-blue-500 text-xl">
        <!-- You can replace this with your preferred icon library -->
        <i class="i-ant-design-robot-filled" />
      </div>
    </div>

    <!-- Title and Description -->
    <div class="flex-1 min-w-0">
      <div class="text-[14px] font-bold text-gray-800 truncate mb-[8px]" :title="title">{{ title }}</div>
      <div class="text-xs text-[12px] text-gray-500 mt-1 two-line-ellipsis" :title="introduce">{{ introduce }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { toRefs, withDefaults } from 'vue';

// Define the props expected by this component
interface Props {
  code: string;
  id: string;           // Unique identifier for the assistant
  iconUrl?: string;      // URL for the iconUrl image (optional)
  title: string;        // Title of the assistant
  introduce: string;  // Description of the assistant
}

const props = withDefaults(defineProps<Props>(), {
  title: '未命名助理',      // Default title
  introduce: '暂无描述', // Default description
  iconUrl: undefined,     // Default iconUrl is none
});

// Expose props for potential template use (optional here)
const { id, iconUrl, title, introduce, code } = toRefs(props);

// Define the emits for this component
const emit = defineEmits<{
  (e: 'handleClick', data: any): void; // Emit 'click' with the assistant's ID
}>();

const handleClick = () => {
  emit('handleClick', {
    id: id.value,
    code: code.value,
  });
};

</script>

<style scoped lang="less">
.assistant-card {
  border: 1px solid rgba(255, 255, 255, 0.18);
  height: 82px;
  padding: 16px;
  box-sizing: border-box;
}

/* Improve text contrast */
h3 {
  color: #1f2937; /* gray-800 */
}
p {
  color: #4b5563; /* gray-600 */
}

/* Ensure icons render if using Icones/Unocss */
.i-ant-design-robot-filled {
  display: inline-block; /* Or block depending on context */
}
.two-line-ellipsis {
  line-height: 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
  overflow: hidden;
}
</style>
