{"id": "111111", "meta": {"editor": "0.0.1", "ct": "1234564641"}, "env": {}, "views": [{"id": "xxxx", "code": "", "condition": {}, "name": "页面", "options": {"class": "x-portal", "style": {"width": "100%", "height": "100%", "backgroundColor": "rgba(220,224,227,1)"}, "props": {}}, "children": [{"type": "Layout", "tag": "LayoutAside", "style": {"width": "200px"}, "name": "布局", "condition": false, "options": {}, "children": []}, {"type": "Layout", "class": "flex-1", "tag": "LayoutSection", "name": "布局", "options": {}, "children": [{"type": "Layout", "tag": "LayoutMain", "name": "布局", "class": "flex justify-center", "condition": {"eval": "this.global.globalState.currAgentCode == 'comi'"}, "options": {}, "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "布局", "class": "h-full flex", "options": {}, "children": [{"type": "Layout", "tag": "LayoutContainer", "class": "flex-1 flex justify-center flex-col", "name": "容器", "children": [{"type": "Layout", "tag": "Row", "class": "gap-4", "children": [{"type": "Layout", "tag": "Col", "class": "col-span-12", "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "spec": 23, "children": [{"type": "component", "tag": "CoMiHeader", "props": {}}]}]}]}, {"type": "Layout", "tag": "Row", "class": "gap-3", "style": {"marginTop": "-48px", "position": "relative", "zIndex": 2}, "children": [{"type": "Layout", "tag": "Col", "class": "col-span-8", "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "spec": 23, "children": [{"type": "component", "class": "2xl:h-[512px] xl:h-[454px] lg:h-[454px] md:h-[454px] sm:h-[454px]", "tag": "TodoCardColumn", "copilot": "1", "props": {}}]}]}, {"type": "Layout", "tag": "Col", "class": "col-span-4", "excludePosts": ["7984663429391991964", "8379948970401744659", "******************", "896713504396889013", "*******************", "-8239285696246100530", "814960077346523602", "7718973163547607712", "6277940898801338142", "-1409482450411174285", "7536245928799324272", "-8965136165869172042", "5640490802216390604"], "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "class": "flex flex-col gap-3", "spec": 23, "children": [{"type": "component", "class": "h-[236px]", "tag": "MeetingCardColumn", "copilot": "1", "roles": [], "props": {}}, {"type": "component", "tag": "PortalMessage", "copilot": "1", "class": "2xl:h-[266px] xl:h-[206px] lg:h-[206px] md:h-[206px] sm:h-[206px]", "props": {}}]}]}, {"type": "Layout", "tag": "Col", "class": "col-span-4", "posts": ["7984663429391991964", "8379948970401744659", "******************", "896713504396889013", "*******************", "-8239285696246100530", "814960077346523602", "7718973163547607712", "6277940898801338142", "-1409482450411174285", "7536245928799324272", "-8965136165869172042", "5640490802216390604"], "children": [{"type": "Layout", "tag": "LayoutContainer", "name": "容器", "class": "flex flex-col gap-3", "spec": 23, "children": [{"type": "component", "tag": "PortalMessage", "copilot": "1", "class": "2xl:h-[512px] xl:h-[454px] lg:h-[454px] md:h-[454px] sm:h-[454px]", "props": {}}]}]}]}]}, {"type": "Layout", "tag": "LayoutContainer", "name": "容器", "class": "w-full bg-sky-500", "style": {"height": "200px"}, "condition": false, "children": [{"type": "component", "tag": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}]}]}]}, {"type": "Layout", "condition": false, "tag": "layout-footer", "name": "布局", "options": {}, "children": []}]}]}]}