<template>
  <div class="preview_container" v-if="chartData">
    <changeChartType :chartType="chartType" @changeChartType="handleChartType" />
    <ComiEchart
      class="chart_container"
      :content="chartData"
      :contentHeight="contentHeight"
      style="width: 100%"
      :chartType="chartType"
      :isPreview="true"
    ></ComiEchart>
  </div>
  <div v-else class="no_data">暂无数据</div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue';
import changeChartType from '@/components/commonCard/components/dataChartTable/dataChart/changeChartType.vue';
import dataChart from '@/components/commonCard/components/dataChartTable/dataChart/content.vue';
import type { CardItem } from '@/components/commonCard/types';
import { ComiEchart } from '@seeyon/seeyon-comi-plugins-library';

const props = defineProps({
  transParams: {
    type: Object,
    default: () => ({}),
  },
  height: {
    type: Number,
  },
});

// 兼容处理获取放大图表的参数
const dialogData: CardItem =
  (window?.frameElement as any)?.transParams ||
  window?.parentDialogObj?.chartPreview?.getTransParams() ||
  props.transParams;
const chartData = ref(dialogData?.renderInfo);
const changeType = ref('');

const contentHeight = computed(() => {
  return props.height || 'calc(100% - 74px )';
});

const chartType = computed(() => {
  return changeType.value || dialogData?.renderInfo?.type;
});
function handleChartType(type: string) {
  changeType.value = type;
}
</script>
<style lang="less" scoped>
.preview_container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px 24px 24px;
  background-color: #fff;
  .chart_container {
    flex: 1;
    margin-top: 12px;

    :deep(.data-chart) {
      height: 100%;
    }
  }

  .no_data {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
  }
}
</style>
