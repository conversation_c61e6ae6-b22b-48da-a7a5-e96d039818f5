<template>
  <!-- copilot 通用菜单，目前设计三种形态，第一种为侧边栏菜单:silder，第二种为门户折叠态菜单：portalFold，第三种为门户展开态菜单：portalExpand -->
  <div class="common-menu" :class="{'common-menu-portalFold': internalMenuState === 'portalFold', 'common-menu-portalExpand': internalMenuState === 'portalExpand'}">
    <div class="cm-logo" v-if="internalMenuState !== 'silder'">
      <div class="logo-box" @click="handleLogoClick">
        <img src="@/assets/imgs/comi-logo.png" alt="" class="logo" />
        <img v-if="internalMenuState === 'portalExpand'" src="@/assets/imgs/fullscreen_logo.png" class="comi_logo" />
      </div>
      <Tooltip :title="toolTipTitle" placement="right" v-if="(internalMenuState == 'portalFold' && !showIframeContainer)">
        <button class="toggle-button" @click="toggleCollapsed">
          <AiIcon iconName="ai-icon-a-tubiaohuizhiguifan20" class="text-[22px]" />
        </button>
      </Tooltip>
      <Tooltip :title="toolTipTitle" placement="right" v-if="internalMenuState == 'portalExpand'">
        <button class="toggle-button" @click="toggleCollapsed">
          <AiIcon iconName="ai-icon-a-tubiaohuizhiguifan20" class="text-[22px]" />
        </button>
      </Tooltip>
    </div>
    <div id="guide-step1" class="w-full flex-1 flex flex-col overflow-y-auto">
      <div class="cm-top"  :style="{height: internalMenuState === 'silder' ? 61*menuList.length+'px' : 46*menuList.length+'px'}">
        <div
          v-for="item in menuList"
          :key="item.id"
          :class="[
            'menu-item menu-item-1',
            currentTabKey === item.key ? 'menu-item-active' : 'menu-item-normal',
          ]"
          @click="handleChange(item,false)"
        >
          <Tooltip placement="left" :title="item.label" arrow-point-at-center v-if="internalMenuState === 'portalFold'">
            <i :class="`iconfont ${item.params.iconName}`"></i>
          </Tooltip>
          <i :class="`iconfont ${item.params.iconName}`" v-else></i>
          <p class="menu-label">{{ item.label }}</p>
        </div>
      </div>
      <div class="cm-center">
        <span class="cmc-title">
          <AiIcon iconName="ai-icon-xing" class="text-[20px]" v-if="internalMenuState === 'portalExpand'" />
          常用应用
        </span>
          <div class="cmc-content">
            <div
              class="cmc-content-box"
              :style="{
                transform: `translateX(-${currentScrollNum * 180}px)`,
                transition: 'transform 0.5s cubic-bezier(0.23, 1, 0.32, 1)'
              }"
            >
              <div
                v-for="item in concatAssistantList"
                :key="item.id"
                :class="['menu-item menu-item-2', currentTabKey === item.id ? 'menu-item-active' : '']"
                @click="handleChange(item,true)"
              >
              <Tooltip placement="left" :title="item.name" arrow-point-at-center v-if="internalMenuState == 'portalFold'">
                <!-- OA 图标适配 -->
                <div v-if="item.iconfont">
                  <Image
                    :width="20"
                    :preview="false"
                    :src="item.iconfont"
                  />
                </div>
                <Avatar
                  class="menu-avatar"
                  :src="item.iconUrl"
                  :size="20"
                  v-else
                >
                  {{ item.iconUrl ? '' : item.name.slice(0, 2) }}
                </Avatar>
              </Tooltip>
              <!-- OA 图标适配 -->
              <Image
                v-else-if="item.iconfont"
                :width="20"
                :preview="false"
                :src="item.iconfont"
              />
              <Avatar
                v-else
                class="menu-avatar"
                :src="item.iconUrl"
                :size="internalMenuState !== 'silder' ? 20 : 28"
              >
                {{ item.iconUrl ? '' : item.name.slice(0, 2) }}
              </Avatar>
              <p class="menu-label">{{ item.name }}</p>
            </div>
          </div>
        </div>
        <Arrow v-if="internalMenuState === 'portalExpand' && columnCount > 1" @handleArrowClick="handleArrowClick" :canScrollLeft="canScrollLeft" :canScrollRight="canScrollRight" />
      </div>
      <div class="cm-bottom mt-[8px]">
        <div class="cmb-agent-box" @click="handleChange({id: 'square',label: '智能体广场', key: 'square'},false)">
          <div class="cmb-agent" :class="currentTabKey === 'square' ? 'cmb-agent-active' : ''">
            <Tooltip placement="left" title="智能体" arrow-point-at-center v-if="internalMenuState === 'portalFold'">
              <i class="iconfont ai-icon-zhinengtiguangchang"></i>
            </Tooltip>
            <i class="iconfont ai-icon-zhinengtiguangchang" v-else></i>
            <span>智能体</span>
          </div>
        </div>
      </div>
    </div>
    <div class="cm-bottom mt-0">
      <div class="cmb-avatar" id="guide-step2">
        <!-- 用户头像 -->
        <UserAvatar :internalMenuState="internalMenuState" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  // copilot 通用菜单，目前设计三种形态，第一种为侧边栏菜单:silder，第二种为门户折叠态菜单：portalFold，第三种为门户展开态菜单：portalExpand
  import { ref, computed, onMounted, onUnmounted, inject, h, nextTick, watch } from 'vue';
  import type { UserType, MenuItemType } from '@/types/portalTypes';
  import { Avatar, Image, Menu as AMenu, Avatar as AAvatar, Divider as ADivider, Tooltip } from 'ant-design-vue';
  import portalApi from '@/api/portal/portal-api';
  import AiIcon from '@/components/aiIcon/index.vue';
  import { getAssistantList } from '@/api/common';
  import { useStream, useTempRunningAssistInfo } from '@/stores/homeSearch';
  import { getUsualAssistList } from '@/api/usualAssistant/index';
  import { useGlobal, useMenu, useGeneralAsit } from '@/stores/global';
  import UserAvatar from '@/components/userAvatar/index.vue';
  import { useCustomMenu } from '@/hooks/portal';
  import Arrow from '@/components/common/arrow/index.vue';
  import { useChatList } from '@/stores/chatList';
  import { getAssistIdByCode } from '@/api/home';
  import OAConfig from './Oa.config'; // OA 配置
  import { useCustomData } from '@/stores/global';
  const isPortal = inject('isPortal');
  const viewMode = inject('viewMode') as string;

  const props = defineProps({
    menuState: {
      type: String,
      default: 'silder',
    },
  });

  const internalMenuState = ref(props.menuState);
  const chatListStore = useChatList();

  const toolTipTitle = computed(() => {
    let title = '';
    if (internalMenuState.value === 'portalExpand') {
      title = '收起导航';
    } else if (internalMenuState.value === 'portalFold') {
      title = '展开导航';
    }
    return title;
  });

  // 是否展示的是容器
  const showIframeContainer = computed(() => {
    return chatListStore.dynamicData.dobuleScreenData.show;
  });

  // 监听双屏状态变化，自动调整菜单状态
  watch(
    () => chatListStore.dynamicData.dobuleScreenData.show,
    (newVal) => {
      if (newVal && isPortal) {
        internalMenuState.value = 'portalFold';
        currentScrollNum.value = 0;
      }
    }
  );

  const uTmStream = useStream(); // store实例
  const uMenu = useMenu();
  const config = ref<any>(null);
  const sdkInstance = inject('sdkInstance') as any;
  const defaultAssistId = sdkInstance?.preConfig?.defaultAssistId || '';
  let menuList = ref<any[]>([]);
  const useCustomDataStore = useCustomData();
  const init = async () => {
    config.value = await portalApi.getConfig();
    menuList = useCustomMenu(config).menuList;
    menuList.value.forEach(item => {
      item.assistId  = item.id || ''
      item.id = item.key;
    });
  };


  if (defaultAssistId) {
    uMenu.changeMenu({
      id: defaultAssistId,
    });
  }
  const currentTabKey = computed(() => {
    return uMenu.currentMenuInfo?.id || 'comi';
  });

  const allAssistantList = ref<any[]>([]);
  const assistantsList = ref<any[]>([]);

  // 菜单切换
  const handleChange = async (item: any, isAsit:boolean) => {
    if (currentTabKey.value === item.id) {
      return;
    }
    let _isAsit = isAsit;
    chatListStore.chatActions.setNewChatSessionId();
    uMenu.changeMenu(item, {isAsit: _isAsit});
  };

  // 获取助手列表
  const getAllAssistantList = async () => {
    try {
      const res: any = await getAssistantList({
        pageInfo: {
          pageNumber: 1,
          pageSize: 500,
          needTotal: true,
        },
        params: {
          keyword: '',
          assistantType: 0,
        },
      });
      if (res && res.code === '0') {
        const { content = [] } = res.data;
        allAssistantList.value = [...content];
      } else {
        allAssistantList.value = [];
      }
    } catch (error) {
      console.log('错误', error);
    }
  };

  // OA 配置数据适配
  const buildOAConfig = async () => {
    const codeList = OAConfig.filter(item => item.needAssistId).map(item => item.code);
    const res: any = await getAssistIdByCode(codeList);
    if (res.code === '0' && res.data && res.data.length > 0) {
      OAConfig.forEach(item => {
        if (item.needAssistId) {
          const matchedData = res.data.find((it: any) => it.code === item.code);
          if (matchedData) {
            item.assistId = matchedData.id;
          }
        }
      })
    }
    return OAConfig;
  }

  // 获取常用助手
  const gettingUalAsiLst = async (val: number) => {
    // OA 配置数据适配
    let oaConfig = [] as any;
    try {
      if(isPortal && ['1.1'].includes(viewMode)){
        oaConfig = await buildOAConfig();
      }
    } catch (error) {
    }
    try {
      const res: any = await getUsualAssistList(val, defaultAssistId);
      if (res && res.code == 0 && res.data && res.data.length > 0) {
        assistantsList.value = [...res.data];
      } else {
        // message.error(res.msg);
        assistantsList.value = [];
      }
    } catch (error) {
      assistantsList.value = [];
    }
    assistantsList.value = [...oaConfig, ...assistantsList.value];
  };

  const concatAssistantList = computed(() => {
    const allList = allAssistantList.value.filter((item) => {
      return !assistantsList.value.some((it) => it.id === item.id);
    });
    return [...assistantsList.value, ...allList].slice(0, 10);
  });

  // 处理会话完成后，更新最新常用助手
  uTmStream.$subscribe(async (mute, state) => {
    if (state.chatNum) {
      await gettingUalAsiLst(7);
      // 重新计算列数
      await nextTick();
      updateColumnCount();
    }
  });

  const handleLogoClick = () => {
    uMenu.changeMenu({
      id: 'comi',
    });
    useCustomDataStore.setCustomData('collApproveData', null);
  };
  // 切换菜单折叠状态的方法
  const toggleCollapsed = async () => {
    internalMenuState.value = internalMenuState.value === 'portalFold' ? 'portalExpand' : 'portalFold';
    currentScrollNum.value = 0;
    // 等待 DOM 更新后重新计算列数
    await nextTick();
    updateColumnCount();
  };

  // 获取当前布局分成了多少列
  const getColumnCount = () => {
    const contentBox = document.querySelector('.cmc-content-box');
    if (!contentBox) return 0;

    const menuItems = contentBox.querySelectorAll('.menu-item');
    if (menuItems.length === 0) return 0;

    // 获取所有元素的左边距位置
    const leftPositions = new Set();
    menuItems.forEach(item => {
      const rect = item.getBoundingClientRect();
      leftPositions.add(Math.round(rect.left));
    });

    return leftPositions.size;
  };

  // 监听窗口大小变化，重新计算列数
  const columnCount = ref(0);
  const currentScrollNum = ref(0);
  const canScrollLeft = computed(() => {
    return columnCount.value > 1 && currentScrollNum.value > 0;
  });
  const canScrollRight = computed(() => {
    return columnCount.value > 1 && currentScrollNum.value < columnCount.value - 1;
  });


  const handleArrowClick = (type: string) => {
    if (type === 'pre') {
      currentScrollNum.value = currentScrollNum.value > 0 ? currentScrollNum.value - 1 : 0;
    } else {
      currentScrollNum.value = currentScrollNum.value < columnCount.value - 1 ? currentScrollNum.value + 1 : columnCount.value - 1;
    }
  };

  const updateColumnCount = () => {
    columnCount.value = getColumnCount();
  };
  onMounted(async () => {
    await init();
    await gettingUalAsiLst(7);
    await getAllAssistantList();

    // 等待 DOM 更新后计算列数
    await nextTick();
    updateColumnCount();

    // 监听窗口大小变化
    window.addEventListener('resize', updateColumnCount);
  });

  // 组件卸载时清理事件监听器
  onUnmounted(() => {
    window.removeEventListener('resize', updateColumnCount);
  });

  defineExpose({
    gettingUalAsiLst,
    getColumnCount,
    updateColumnCount,
    columnCount: computed(() => columnCount.value)
  });

</script>

<style scoped lang="less">
  .common-menu{
    width: 68px;
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(40px);
    border-top-left-radius: 12px;
    max-height: 100%;
    padding: 4px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    position: relative;
    z-index: 2;
    .cm-logo{
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
      height: 44px;
      margin: 0 0 12px 0;
      padding: 4px;
      .logo-box{
        display: flex;
        align-items: center;
        box-sizing: border-box;
        gap: 8px;
        cursor: pointer;

        .logo {
          width: 36px;
          height: 36px;
        }
        .comi_logo {
          height: 20px;
        }
      }
      .toggle-button {
        cursor: pointer;
        z-index: 10; // 确保在其他内容之上
        width: 36px; // 固定按钮宽度
        height: 36px; // 固定按钮高度
        background-color: rgba(255, 255, 255, 0.5);
        border: 1px solid rgba(255, 255, 255, 0.4);
        backdrop-filter: blur(20.01100730895996px);
        border-radius: 50%;
        i{
          font-size: 20px;
          color: rgba(111, 118, 134, 1);
        }
        &:hover{
          background-color: rgba(255, 255, 255, 1);
        }
      }
    }

    /* 展开时的按钮位置：定位按钮的中心点在 menu-wrapper 的右边缘 */
    .cm-logo.expanded .toggle-button {
      left: calc(100% - 28px); // 100% 是父容器宽度, 减去按钮宽度的一半 (24px / 2 = 12px)
    }

    /* 折叠时的按钮位置：定位按钮的中心点在 menu-wrapper 的右边缘 */
    .cm-logo.collapsed .toggle-button {
      left: calc(100%); // 同样是 100% 减去按钮宽度一半，但此时父容器宽度是 80px
      // 这会自动将按钮定位在折叠后菜单的右侧外部
    }
    .cm-top{
      height: 244px;
    }
    .cm-center{
      flex: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;
      margin: 8px 0 8px;
      .cmc-title{
        display: inline-block;
        line-height: 18px;
        letter-spacing: 0%;
        text-align: center;
        width: 52px;
        font-family: PingFang SC;
        font-size: 12px;
        color: #4a4e5a;
        font-weight: @font-weight-500;
        padding: 16px 0 8px;
        margin: 0 4px;
        border-top: 1px dashed rgba(111, 118, 134, 0.2);
      }
      .cmc-content{
        overflow-y: scroll;
        .cmc-content-box{

        }
      }
    }
    .cm-bottom{
      display: flex;
      flex-direction: column;
      align-items: center;
      .cmb-agent-box{
        width: 52px;
        border-top: 1px dashed rgba(111, 118, 134, 0.2);
        .cmb-agent{
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          padding: 4px 7px;
          margin: 6px 0 4px;
          i{
            font-size: 20px;
            color: #6F7686;
          }
          span{
            font-weight: @font-weight-400;
            font-size: 12px;
            line-height: 23px;
            text-align: center;
            color: #3F434D;
          }
        }
        .cmb-agent-active{
          background: linear-gradient(90deg, #E0EDF7 -2.73%, #E2F3F9 44.01%, #E5E8F8 100%);
          border-radius: 8px;
          i {
            color: rgba(37, 38, 44, 1)!important;
          }
          span {
            color: rgba(37, 38, 44, 1)!important;
            font-weight: @font-weight-500;
          }
        }
      }
      .cmb-agent:hover{
        cursor: pointer;
        background: linear-gradient(90deg, rgba(224, 237, 247, 0.6) -2.73%, rgba(226, 243, 249, 0.6) 44.19%, rgba(229, 232, 248, 0.6) 100%);
        border-radius: 8px;
      }
      .cmb-setting,.cmb-avatar{
        border-top: 1px dashed rgba(111, 118, 134, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      .cmb-avatar{
        width: 52px;
        height: 52px;
      }
    }


    .menu-item {
      width: 60px;
      cursor: pointer;
      border-radius: 8px;
      display: inline-flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-bottom: 4px;
      &.menu-item-1 {
        height: 56px;
        margin-bottom: 2px;
        .iconfont {
          font-size: 20px;
          line-height: 1;
          color: #6F7686;
          margin-bottom: 2px;
        }
      }
      &.menu-item-2 {
        padding-bottom: 4px;
        .menu-label {
          font-size: 11px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 显示两行 */
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: normal;
        }
      }
      .menu-avatar{
        margin: 12px 16px 6px 16px;
      }
      .menu-label {
        width: 52px;
        color: rgba(63, 67, 77, 1);
        text-align: center;
        font-size: 12px;
        font-style: normal;
        line-height: 15px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        letter-spacing: 0%;
      }
      &:not(.menu-item-active):hover {
        // background: rgba(209, 224, 255, 0.8);
        background: linear-gradient(90deg, rgba(224, 237, 247, 0.6) -2.73%, rgba(226, 243, 249, 0.6) 44.19%, rgba(229, 232, 248, 0.6) 100%);
        .menu-label,
        .iconfont {
          color: rgba(37, 38, 44, 1)!important;
        }
      }
      &-active {
        // background: rgba(209, 224, 255, 0.8);
        background: linear-gradient(90deg, #E0EDF7 -2.73%, #E2F3F9 44.19%, #E5E8F8 100%);
        border-radius: 8px;
        .menu-label,
        .iconfont {
          color: rgba(37, 38, 44, 1)!important;
        }
        .menu-label {
          font-weight: @font-weight-500;
        }
      }
    }
  }
  .common-menu-portalExpand{
    width: 200px;
    margin: 12px 10px;
    padding: 0 10px 4px 10px;
    background-color: transparent;
    height: calc(100vh - 24px);
    .cm-top{
      margin-top: 12px;
      height: 184px;
    }
    .cm-center{
      margin-top: 0;
      .cmc-title{
        width: 100%;
        margin: 0;
        font-size: 14px;
        color: rgba(74, 78, 90, 1);
        font-weight: @font-weight-500;
        vertical-align: middle;
        padding: 12px 12px 8px;
        text-align: left;
        display: flex;
        flex-direction: row;
        i{
          color: rgba(67, 121, 255, 1);
          margin-right: 4px;
        }
      }
      .cmc-content{
        overflow: hidden;
        .cmc-content-box{
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;
          align-content: flex-start;
          height: 100%;
          .menu-item{
            width: 100%;
            padding: 11px 12px;
            flex-direction: row;
            justify-content: flex-start;
            gap: 10px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            &:hover{
              background: linear-gradient(90deg, rgba(224, 237, 247, 0.8) -2.73%, rgba(226, 243, 249, 0.8) 44.01%, rgba(229, 232, 248, 0.8) 100%);
            }

            .menu-avatar{
              margin: 0;
              transition: transform 0.2s ease;
            }

            .menu-label{
              flex: 1;
              min-width: 0;
              -webkit-line-clamp: 1;
              text-align: left;
              width: auto;
              font-size: 13px;
              transition: color 0.2s ease;
            }
          }
        }
      }
      .arrow-wrapper-box{
        margin-left: 12px;
        padding-top: 12px;
      }
    }
    .cm-bottom{
      .cmb-agent-box{
        width: 100%;
        .cmb-agent{
          flex-direction: row;
          gap: 10px;
          height: 42px;
          justify-content: flex-start;
          padding: 0 12px;
        }
      }
      .cmb-avatar{
        width: 100%;
        border-top: none;
        justify-content: flex-start;
      }
    }
    .menu-item{
      width: 100%;
      height: 42px;
      flex-direction: row;
      justify-content: flex-start;
      padding: 11px 12px;
      margin-bottom: 2px;
      gap: 10px;
      font-size: 13px;
      .menu-label{
        width: auto;
        font-size: 13px;
      }
    }
    .menu-item.menu-item-1{
      height: 42px;
    }
    .menu-item-active{
      background: linear-gradient(90deg, #E0EDF7 -2.73%, #E2F3F9 44.01%, #E5E8F8 100%);
    }
    .menu-item:not(.menu-item-active):hover{
      background: linear-gradient(90deg, rgba(224, 237, 247, 0.6) -2.73%, rgba(226, 243, 249, 0.6) 44.01%, rgba(229, 232, 248, 0.6) 100%);
    }
  }
  .common-menu-portalFold{
    width: 44px;
    margin: 12px 20px;
    padding: 0 0 4px;
    height: calc(100vh - 24px);
    background-color: transparent;
    .cm-logo{
      margin: 0 0 12px 0;
      padding: 4px;
      gap: 0;
      .toggle-button{
        position: absolute;
        right: -48px;
      }
    }
    .cm-top{
      margin-top: 12px;
      height: 184px;
    }
    .cm-center{
      border-top: 1px dashed rgba(111, 118, 134, 0.2);
      .cmc-title{
        display: none;
      }
      .cmc-content{
        .cmc-content-box{
          padding-top: 4px;
        }
      }
    }
    .cm-bottom{
      .cmb-agent-box{
        width: 44px;
        .cmb-agent{
          height: 42px;
          padding: 0 12px;
          span{
            display: none;
          }
        }
      }
      .cmb-avatar{
        border-top: none;
      }
    }
    .menu-item{
      width: 100%;
      height: 42px;
      .menu-avatar{
        margin: 11px 16px;
      }
      .menu-label{
        display: none;
      }
    }
    .menu-item.menu-item-1{
      height: 42px;
      padding-bottom: 0;
      .menu-label{
        display: none;
      }
    }
    .menu-item.menu-item-2{
      height: 42px;
      padding-bottom: 0;
      margin-bottom: 0;
      margin: 2px 0;
      .menu-label{
        display: none;
      }
    }
  }
</style>
