# COMI Assistant SDK

> 基于事件驱动的现代化跨窗口通信SDK，支持智能回调管理和第三方系统无缝集成

## 📖 简介

COMI Assistant SDK 是一个专为COMI助手系统设计的现代化通信SDK，提供完整的跨窗口通信、事件管理和业务功能封装，支持复杂的双向嵌套场景和智能回调执行机制。

### ✨ 核心特性

- **事件驱动架构** - 基于 `requestAnimationFrame` 和 `Promise` 的现代化执行机制
- **智能回调管理** - 无需 `setTimeout`，基于真实状态的回调执行
- **comiEventBus集成** - 与现有 `comi-entry.js` 完美兼容
- **防重复执行** - 智能回调分类，确保每个回调只执行一次
- **自动方法绑定** - 自动发现并绑定外部方法（如 `getOaUrl`）
- **双向嵌套支持** - 支持COMI嵌入第三方系统和第三方系统嵌入COMI
- **完整业务功能** - 包含消息发送、助手重定向、菜单控制等完整功能
- **TypeScript支持** - 完整的类型定义，提供优秀的开发体验

### 🏗️ 架构设计

```
COMI Assistant SDK v2.0
├── Core SDK (核心通信层)
│   ├── MessageBus - 统一消息总线
│   ├── ISCOMIManager - 嵌套关系管理  
│   ├── ComiEventBusIntegrator - comiEventBus集成
│   └── MessageQueue - 消息队列管理
└── Business SDK (业务层)
    ├── CallbackExecutor - 智能回调执行器
    ├── EventManager - 事件管理系统
    ├── BindingManager - 对象绑定管理
    ├── MessageSender - 多重消息发送器
    └── BusinessSDK - 完整业务功能封装
```

### 🔄 通信流程

```
第三方系统
    ↓ (引入 comi-entry.js)
window.comiEventBus (全局事件总线)
    ↓ (劫持 preSdk.openDrawer)
CallbackExecutor (智能回调管理)
    ↓ (事件驱动执行)
BusinessSDK (业务功能)
    ↓ (自动绑定外部方法)
COMI 应用完整功能
```

## 🚀 快速开始

### 在Vue应用中使用

```typescript
// main.ts
import { initBusinessSdk } from '@/sdk/business/index.ts';

const sdkInstance = initBusinessSdk({
  defaultAssistId: '默认助手ID',
  autoInit: true
});

app.provide('sdkInstance', sdkInstance);
```

```vue
<!-- 在组件中使用 -->
<script setup>
import { inject } from 'vue'

const sdk = inject('sdkInstance')

// 发送消息
const sendMessage = () => {
  sdk.sendMsg('本周工作总结', { isHide: true })
}

// 重定向助手
const redirectAssist = () => {
  sdk.redirectAssistId('助手ID', true, () => {
    console.log('重定向完成')
  })
}

// 获取OA系统URL
const getOaUrl = () => {
  const url = sdk.getOaUrl({
    appType: '1',      // 协同
    linkId: '123456',
    clientType: '1'
  })
  console.log('生成的URL:', url)
}
</script>
```

### 第三方系统中使用

```javascript
// 通过 preSdk.openDrawer 调用
window.COMI_ASSISTANT.preSdk.openDrawer(() => {
  // SDK完全就绪后执行
  const sdk = window.COMI_ASSISTANT.sdk;
  
  // 重定向到指定助手并发送消息
  sdk.redirectAssistId('5605278465501839487', true, () => {
    sdk.sendMsg('本周工作总结', { isHide: true });
  });
});
```

## 📚 文档导航

- [📖 快速开始](./getting-started.md) - 详细的入门指南和环境配置
- [📋 API参考](./api-reference.md) - 完整的API文档和方法说明
- [🔧 高级用法](./advanced-usage.md) - 高级功能、最佳实践和故障排除
- [🔗 第三方集成指南](./third-party-integration.md) - 第三方系统嵌入COMI的完整指南

## 🌟 主要功能

### 💬 消息通信
- `sendMsg()` - 发送助手消息
- `setSendMsg()` - 设置消息内容
- `pushCustomCard()` - 推送自定义卡片

### 🎯 助手控制
- `redirectAssistId()` - 重定向到指定助手
- `hideInputBox()` - 控制输入框显示
- `hideNextTime()` - 下次不再显示

### 🖥️ 界面控制
- `expand()` - 展开/全屏模式
- `collapse()` - 收缩/普通模式
- `close()` - 关闭界面

### 🔗 业务功能
- `getOaUrl()` - 生成OA系统URL
- `handleTagClick()` - 处理markdown标签点击
- `openWin()` - 打开知识源窗口

### 📊 状态管理
- `getISCOMI()` - 获取嵌套关系标识
- `getSystemStatus()` - 获取系统状态
- `getDebugInfo()` - 获取调试信息

## 🔧 兼容性

- ✅ 现代浏览器 (Chrome 70+, Firefox 65+, Safari 12+)
- ✅ 移动端浏览器
- ✅ Electron应用
- ✅ 微信小程序webview
- ✅ 与现有 `comi-entry.js` 100% 兼容

## 📈 性能特点

- 🚀 **零延迟** - 基于事件驱动，无需固定时间延迟
- ⚡ **智能执行** - 精确的状态检测，回调在最佳时机执行
- 🔄 **防重复** - 智能回调分类，确保不会重复执行
- 🎯 **自动绑定** - 自动发现外部方法，无需手动配置
- 📦 **渐进增强** - 向后兼容，不影响现有功能

## 🔑 核心概念

### CallbackExecutor (回调执行器)
SDK的核心创新，提供智能回调管理：
- **状态检测** - 检查SDK是否完全就绪（包括所有方法绑定）
- **事件驱动** - 基于 `requestAnimationFrame` 和 `Promise`
- **防重复机制** - 区分队列回调和单回调，避免重复执行
- **兼容性** - 完美兼容原有的 `preSdk.openDrawer` 逻辑

### comiEventBus集成
与现有系统的无缝集成：
- **自动发现** - 向上递归查找已存在的 `comiEventBus`
- **智能劫持** - 劫持现有的 `preSdk.openDrawer` 方法
- **平滑过渡** - 新旧SDK无缝切换，不影响现有调用

### 自动方法绑定
自动发现并绑定外部方法：
```javascript
// 自动绑定 window.COMI_ASSISTANT.getOaUrl
sdk.bindGetOaUrl(); // 自动调用
sdk.getOaUrl(params); // 直接使用
```

## 🆕 版本特性

### v2.0 主要更新
- ✅ 移除所有 `setTimeout`，改用事件驱动
- ✅ 新增 `CallbackExecutor` 智能回调管理
- ✅ 新增 `getOaUrl()` 方法，支持生成OA系统URL
- ✅ 完善的防重复执行机制
- ✅ 自动方法绑定和发现
- ✅ 完整的TypeScript类型定义

### 向后兼容
- ✅ 完全兼容现有的 `preSdk.openDrawer` 调用
- ✅ 保持所有原有API接口不变
- ✅ 支持原有的单回调模式
- ✅ 无需修改第三方系统集成代码

## ⚠️ 重要说明

### 关于回调执行
从v2.0开始，所有回调都通过智能执行器管理：
- **无需等待** - 不再需要固定的时间延迟
- **状态驱动** - 基于真实的SDK就绪状态
- **只执行一次** - 智能防重复机制

### 关于方法绑定
SDK会自动绑定外部方法：
- `getOaUrl` - 自动从 `window.COMI_ASSISTANT.getOaUrl` 绑定
- `handleTagClick` - 自动从 `COMI_UTIL.handleTagClick` 绑定
- `openWin` - 自动从 `COMI_UTIL.openWin` 绑定

## 📞 技术支持

如果您在使用过程中遇到问题，可以：
1. 查看 [故障排除指南](./advanced-usage.md#故障排除)
2. 使用 `sdk.getDebugInfo()` 获取调试信息
3. 检查浏览器控制台的详细日志

**智能产品部-应用部-前端团队生产** 