// 状态管理
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { setStreamString, formatContent } from '@/utils/stream';
import StreamManager from './manager'
import type { CardContentType, CardDataType, StreamFnInstance } from '@/types/api';
import { getQueryString } from '@/utils/common';
import { useStream } from '@/stores/homeSearch';
// import Requests from '@/api/requests'

// 观察者
class streamFn implements StreamFnInstance {
  card: CardDataType;
  isStop: boolean;
  timer: any;
  _id: number;
  startTime: number;
  option: any;
  dataHistory: any[];
  isCancel: boolean;
  messageType: Record<number, boolean>;
  retry: number;
  callId?: string;
  notNormalStop?: boolean;
  agentInfo?: any;
  authExpired?: boolean;
  controller: any
  signal: any
  needMarkBlueRunStepsArray: Array<any>

  constructor(card: CardDataType) {
    this.card = card;
    this.isStop = false;
    this.timer = null;
    this._id = new Date().getTime();
    this.startTime = this._id;
    this.option = null;
    this.dataHistory = [];
    this.isCancel = false;
    this.messageType = {};
    this.retry = 0;
    this.controller = new AbortController();
    this.needMarkBlueRunStepsArray = [];
  }

  async run(option: any) {
    const isPortal = getQueryString('isPortal') == 'true' ? true : false;
    const uTmStream = useStream(); // store实例
    if (!option) {
      this.isStop = false;
      this.isCancel = false;
      this.messageType = {};
    }
    this.option = option || this.option;
    const { url, params, agentInfo } = this.option;
    this.agentInfo = agentInfo;
    if (this.option.init) {
      await this.option.init();
    }
    let isFirst = true;
    this.dataHistory = [];
    this.needMarkBlueRunStepsArray = [];
    let lastDataItem: CardContentType = {
      index: 0,
      finish: 0,
      context: ""
    };
    const list: any[] = [];
    const recommandQuestion: Array<object> = []
    // 跳过初始化
    if(this.option.options && this.option.options.skipInit){
      this.messageType[1] = true;
      isFirst = false;
    }
    fetchEventSource(url, {
      openWhenHidden: true,
      method: 'POST',
      headers: {
        'accept': url === '/seeyon/rest/comi-agent/chat/stream'? '*/*' : 'text/event-stream',
        'Content-Type': 'application/json',
        'channel': isPortal ? 'comi_intelligent_portal' : 'comi_copilot',
      },
      signal: this.controller.signal,
      body: JSON.stringify(params),
      onopen: async (e: any) => {
        // this.authExpired = false;
        const status = Number(e.status);
        if (status !== 200) { //不等于200就是异常结束了
          this.notNormalStop = true;
        }
        // 断线重连
        console.log("onopen：this.retry", this.retry);
      },
      onmessage: (data: any) => {
        /* messageType的各种情况
          1：结果非流式
          2：步骤
          5：结果流式
          7：命中知识源
          8：命中关键字（飘蓝）
          9：结果非流式（智能问数部分）
          6：追问  注意，此时finish=1，也只有finish=1的情况下才会出现追问 6要处理，其他的不处理content
        */
        list.push(data);
        if (typeof data.data === 'string') {
          try {
            data.data = JSON.parse(data.data);
          } catch (error) {
            console.log('error', error);
            return;
          }
        }

        const sourceData: any = data.data;
        if (sourceData.id) {
          this.option.eventHandel('onmessageId', sourceData.id);
        }

        // 优先处理finish=1的情况，避免被isStop拦截
        if(sourceData.finish === 1 && sourceData?.messageType !== 6) {
          this.dealFinish(uTmStream, recommandQuestion);
          return;
        }
        
        if (this.isStop) {
          this.callId = sourceData.callId;
          if (this.option.httpStop && this.callId && !this.isCancel) {
            this.option.httpStop(this.callId);
            this.isCancel = true;
          }
          return;
        }
        const citationsJson = sourceData.citationsJson;
        if (Number(sourceData?.status) === 500) {
          this.dealError(data);
        }
        if (isFirst) {
          this.messageType[sourceData.messageType] = true;
          this.option.eventHandel('connecting', data);
          isFirst = false;
        } else {
          if (Number(sourceData.sessionType) === 999) {
            this.option.eventHandel('illegal', sourceData.sessionType);
          }
          switch (sourceData.messageType) {
            // 为了适配智能问数增加了 9 这个类型，参与评审人员：杜勇、陈祥君、赵瑞群 日期：2025.07.08
            case 1:
            case 9:
              if (sourceData.messageType === 1 && !this.messageType[sourceData.messageType]) {
                this.option.eventHandel("onInit", sourceData);
              } else {
                if (sourceData.content) {
                  this.dataHistory = formatContent(sourceData.content, this.dataHistory, this.option.options?.isBlock);
                  if (citationsJson) {
                    this.dealCitationsJson(citationsJson);
                  }
                }
                this.updateTagData();
                this.option.eventHandel('onmessage', [...this.dataHistory]);
                this.dealCitationsJson(citationsJson);
                // TODO: 为了适配智能问数增加了 9 这个类型，参与评审人员：杜勇、陈祥君、赵瑞群 日期：2025.07.08
                if(sourceData.messageType === 9 && this.dataHistory.length > 0){
                  lastDataItem = this.dataHistory[this.dataHistory.length - 1];
                  if (lastDataItem) {
                    lastDataItem.finish = 0;
                  }
                }else if(this.option.options?.isBlock && this.dataHistory.length > 0){
                  lastDataItem = this.dataHistory[this.dataHistory.length - 1];
                }
              }
              // if (sourceData.id) {
              //   this.option.eventHandel('onmessageId', sourceData.id);
              // }
              break;
            case 2:
              this.option.eventHandel("onExecute", sourceData);
              break;
            case 5:
              let content = sourceData.content;
              let context = "";

              // if (sourceData.id) {
              //   this.option.eventHandel('onmessageId', sourceData.id);
              // }
              if (typeof content === 'string' && content) {
                try {
                  content = JSON.parse(content);
                  context = content.choices[0].delta.content;
                } catch (error) {
                  console.log('error', error);
                  context = content;
                }
              }
              if (context) {
                const items = setStreamString(context, lastDataItem);
                const hasDobuleScreenData = this.dataHistory.some(item => item.isDobuleScreen);
                lastDataItem = items[items.length - 1];
                if (items.length == 1 && lastDataItem.isCard) {
                  if (lastDataItem.finish === undefined || lastDataItem.finish === 1) {
                    lastDataItem.finish = 0;
                  }
                }
                items.forEach((item, idx) => {
                  const _idx = hasDobuleScreenData ? item.index + 1 : item.index;
                  this.dataHistory[_idx] = { ...item };
                });
                this.updateTagData();
                this.option.eventHandel('onmessage', [...this.dataHistory]);
              }
              this.dealCitationsJson(citationsJson);
              // this.option.eventHandel('onmessage', [...this.dataHistory])
              break;
            case 6:  //为6的时候是推荐问题
              if (sourceData.content && sourceData.content.startsWith("<questions>")) {
                const regex = /<question>(.*?)<\/question>/g;
                const matches = sourceData.content.match(regex);
                matches.forEach((match: string) => {
                  const question = match.replace(/<question>|<\/question>/g, '');
                  recommandQuestion.push({
                    index: recommandQuestion.length,
                    subject: question
                  });
                });
              }
              // 处理messageType=6且finish=1的情况
              if (sourceData.finish === 1) {
                this.dealFinish(uTmStream, recommandQuestion);
                return;
              }
              break;
            case 7: // 双屏数据
              if (sourceData.content) {
                this.dealDobuleScreenData(sourceData.content);
              }
              break
            case 8: // 数据标记
              if (sourceData.content) {
                this.dealTagDataFn(sourceData.content)
              }
              break
            default:
              break;
          }
          this.messageType[sourceData.messageType] = true;
        }
      },
      onclose: () => {
        //过期会重新请求，不走关闭里面的逻辑
        if (this.isStop || this.authExpired) {
          return;
        }
        if (this.notNormalStop) {
          const errData = {
            data: {
              message: 'comi不小心发生错误啦，请稍后重试'
            }
          }
          this.dealError(errData);
        } else {
          this.stop(true);
        }
        this.option.eventHandel('onclose', event);
      },
      onerror: (err: any) => {
        this.dealError(err);
        throw err;
      },
    });
  }

  stop(isNormal?: boolean) {
    if (!this.isStop) {
      this.isStop = true;
      StreamManager.remove(this);
      // Requests.logRecord({
      //   info: this.dataHistory,
      //   type: 'info'
      // });
      console.log("stop", isNormal);
      this.option?.stop(isNormal);
      this.controller.abort();
    }
  }
  dealError(data: any) {
    StreamManager.remove(this);
    // Requests.logRecord({
    //   info: data,
    //   type: 'error'
    // });
    this.option.eventHandel('onerror', data);
  }
  dealCitationsJson(citationsJson: string) {
    if (typeof citationsJson === 'string' && citationsJson) {
      try {
        citationsJson = JSON.parse(citationsJson)
      } catch (error) {
        console.log('error', error);
      }
    }
    if (citationsJson?.length) {
      const lastDataItem = this.dataHistory[this.dataHistory.length - 1];
      lastDataItem.finish = 1;
      this.dataHistory.push({
        index: this.dataHistory.length,
        isKnowledgeData: true,
        context: citationsJson,
      });
      this.updateTagData();
      this.option.eventHandel('onmessage', [...this.dataHistory]);
    }
  }
  dealDobuleScreenData(data: string) {
    if (typeof data === 'string' && data) {
      try {
        data = JSON.parse(data)
      } catch (error) {
        console.log('error', error);
      }
    }

    if (data && Object.keys(data).length) {
      const hasDobuleScreenData = this.dataHistory.some(item => item.isDobuleScreen);
      if(hasDobuleScreenData) { //已经有双屏的则替换content就行
        const doubleScreenData = this.dataHistory.find(item => item.isDobuleScreen);
        doubleScreenData.context = data;
      }else {
        this.dataHistory.push({
          index: 0,
          isDobuleScreen: true,
          context: data,
          finish: 0,
          timeStamp: Date.now(),
        });
      }
      // this.option.eventHandel('onmessage', [...this.dataHistory]);
    }
  }
  updateTagData() {
    this.dataHistory.forEach(item => {
      if (item.context && !item.isCard && !item.isIframe && !item.isKnowledgeData && !item.isDobuleScreen) {
        item.tagData = this.needMarkBlueRunStepsArray;
      } else {
        item.tagData = [];
      }
    });
  }

  dealTagDataFn(data: string) {
    let result: Array<any> = [];
    if (typeof data === 'string' && data) {
      try {
        result = JSON.parse(data) || [];
      } catch (error) {
        console.log('error', error);
      }
    }
    const filterResult = result.filter(item => item.type.toLowerCase() === 'member');
    this.needMarkBlueRunStepsArray = this.needMarkBlueRunStepsArray.concat(filterResult);
    this.updateTagData();
    this.option.eventHandel('onmessage', [...this.dataHistory]);
  }
  dealFinish(uTmStream: ReturnType<typeof useStream>, recommandQuestion: Array<object>) {
    uTmStream.changeChatNum();
    if (this.dataHistory.length) {
      // 将所有数据项的finish设置为1，表示整个响应已完成
      this.dataHistory.forEach((item, index) => {
        if (item.finish === undefined || item.finish === 0) {
          item.finish = 1;
          this.dataHistory[index] = { ...item };
        }
      });
      this.updateTagData();
      this.option.eventHandel('onmessage', [...this.dataHistory]);
    }
    if (recommandQuestion.length) {
      this.option.eventHandel('onquestion', recommandQuestion);
    }
    if (this.isStop) {
      return;
    }
    this.stop(true);
    this.option.eventHandel('onclose', event);
  }
};

export default streamFn;
