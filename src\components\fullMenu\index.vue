<template>
  <div class="menu-panel">
    <!-- 搜索区域 -->
    <div class="menu-search-section">
      <MenuSearch
        v-model:searchValue="searchValue"
        @search="handleSearch"
        @clear="handleClearSearch"
      />
    </div>

    <div class="loading" v-if="loading">
      <span class="flex items-center ml-1 loader_img">
        <Image :src="LoadingImg" :width="24" :preview="false" />
      </span>
    </div>
    <template v-else>
     <!-- 历史记录区域 -->
     <div class="menu-history-section">
      <MenuHistory
        :historyList="historyList"
        @select="handleMenuSelect"
      />
      <!-- 展开/收起按钮 -->
      <div class="menu-actions">
        <button
          class="expand-button"
          @click="toggleExpandAll"
          :title="expandAll ? '全部收起' : '全部展开'"
        >
          <i :class="expandAll ? 'iconfont ai-icon-Fold-up' : 'iconfont ai-icon-Expand-all'"></i>
          <span>{{ expandAll ? '全部收起' : '全部展开' }}</span>
        </button>
      </div>
    </div>

    <!-- 菜单瀑布流区域 -->
    <div class="menu-waterfall-section">
      <MenuWaterfall
        :menuList="displayMenuList"
        :searchValue="searchValue"
        :expandAll="expandAll"
        :favoriteIds="favoriteIds"
        :favoritesList="favoritesList"
        :navigationList="navigationList"
        :columnCount="columnCount"
        @select="handleMenuSelect"
        @navigationSelect="handleNavigationSelect"
        @favorite="handleToggleFavorite"
        @expand="handleToggleExpand"
        @removeFavorite="handleRemoveFavorite"
      />
    </div>
    </template>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import MenuSearch from './components/MenuSearch.vue'
import MenuHistory from './components/MenuHistory.vue'
import MenuWaterfall from './components/MenuWaterfall.vue'
import { useMenuData } from './hooks/useMenuData'
import { useMenuSearch } from './hooks/useMenuSearch'
import { useMenuFavorites } from './hooks/useMenuFavorites'
import { useMenuNavigation } from './hooks/useMenuNavigation'
import { useMenuWaterfall } from './hooks/useMenuWaterfall'
import { getMainPortalId } from '../../api/menu/index'
import type { MenuItem } from './types/menu'
import { Image } from 'ant-design-vue'
import LoadingImg from '@/assets/imgs/loading.png'

// Portal配置
const portalId = ref('')
const spaceId = ref('')

// 收藏相关的响应式变量
const favoritesList = ref<MenuItem[]>([])
const favoriteIds = ref<Set<string>>(new Set())

// 导航相关的响应式变量
const navigationList = ref<MenuItem[]>([])

// 数据管理
const { menuList, historyList, loading, error, searchMenuUtils, fetchMenuData, fetchHistoryData } = useMenuData(portalId, spaceId)

// 添加调试日志
console.log('🚀 FullMenu component initialized')
onMounted(() => {
  console.log('🎯 FullMenu mounted, menuList length:', menuList.value.length)
  console.log('🔍 SearchMenuUtils instance:', searchMenuUtils.value)
})

// 搜索功能
const { searchValue, displayMenuList, isSearching, handleSearch, handleClearSearch } = useMenuSearch(menuList, navigationList)

// 瀑布流布局
const { columnCount, expandAll: baseExpandAll, toggleExpandAll, handleToggleExpand } = useMenuWaterfall()

// 计算最终的展开状态：搜索时强制展开，否则使用用户设置的状态
const expandAll = computed(() => {
  return isSearching.value || baseExpandAll.value
})

// 监听搜索状态变化，当搜索时自动展开
watch(isSearching, (newIsSearching) => {
  if (newIsSearching) {
    // 开始搜索时，触发全部展开
    console.log('Search started, triggering expand all')
  }
})

// 收藏功能 - 延迟初始化
let favoritesHook: ReturnType<typeof useMenuFavorites> | null = null

// 导航功能 - 延迟初始化
let navigationHook: ReturnType<typeof useMenuNavigation> | null = null

// 获取Portal配置
const fetchPortalConfig = async () => {
  try {
    const result = await getMainPortalId()
    if (result && (result as any).code === '0') {
      portalId.value = (result as any).data?.portalId || ''
      spaceId.value = (result as any).data?.spaceId || ''
      console.log('获取到Portal配置:', { portalId: portalId.value, spaceId: spaceId.value })
    }
  } catch (error) {
    console.error('获取Portal配置失败:', error)
  }
}

// 初始化收藏功能
const initFavorites = () => {
  if (!favoritesHook) {
    favoritesHook = useMenuFavorites(portalId, spaceId)
  }
  return favoritesHook
}

// 初始化导航功能
const initNavigation = () => {
  if (!navigationHook) {
    navigationHook = useMenuNavigation(portalId)
  }
  return navigationHook
}

// 菜单点击处理
const handleMenuSelect = (menuItem: MenuItem) => {
  console.log('选中菜单:', menuItem)

  // 检查是否为导航项
  if (menuItem.isNavigationItem || menuItem.navType) {
    handleNavigationSelect(menuItem)
    return
  }

  // 处理菜单打开逻辑
  if (menuItem.urlKey) {
    // 使用内部的searchMenuUtils的showMenu方法
    if (searchMenuUtils.value && searchMenuUtils.value.isReady) {
      const ctxPath = window._ctxPath || '/seeyon';
      // 判断url是否已经包含ctxPath
      const fullUrl = menuItem.urlKey.startsWith(ctxPath)
        ? menuItem.urlKey
        : `${ctxPath}${menuItem.urlKey}`;

      console.log('调用showMenu方法:', {
        url: fullUrl,
        id: menuItem.idKey,
        target: menuItem.target,
        name: menuItem.nameKey
      });

      searchMenuUtils.value.showMenu(
        fullUrl,
        menuItem.idKey,
        menuItem.target,
        menuItem.resourceCode, // resourceCode
        {}, // _obj
        menuItem.nameKey, // tabName
        undefined, // from
        undefined, // styleObj
        () => {
          // 历史保存完成后更新历史数据
          fetchHistoryData()
        }
      );
    } else {
      console.warn('searchMenuUtils未准备就绪，使用备用方法')
      // 备用方法：使用openDobuleScreen
      // const ctxPath = window._ctxPath || '/seeyon';
      // openDobuleScreen(`${ctxPath}${menuItem.urlKey}`, 'iframe');
    }
  }
}

// 导航菜单点击处理
const handleNavigationSelect = (menuItem: MenuItem) => {
  if (menuItem.navType === "space" && menuItem.openType === "newWindow" || menuItem.navType === "portal") {
    window.top?.openCtpWindow({
      'url': menuItem.urlKey,
      'id': menuItem.id,
      'comiOrigin': 'v5'
    });
    return;
  } else {
    //打开方式
    switch (menuItem.navType) {
      case "space":
        window.top?.openCtpWindow({
          'url': `/seeyon/main.do?method=main&isFromComi=1&ignoreComi=1&spaceId=${menuItem.id}`,
          'id': menuItem.id,
          'comiOrigin': 'v5'
        });
        break;
      case "menu":
        handleMenuSelect(menuItem)
        break;
      case "linkProject":
      case "thirdPartyPortal|5":
      case "linkSystem":
      //portal采用新窗口打开的方式
      default:
        window.top?.openCtpWindow({
          'url': menuItem.urlKey,
          'id': menuItem.id,
          'comiOrigin': 'v5'
        });
    }
  }
}

// 收藏相关处理函数
const handleToggleFavorite = async (menuItem: MenuItem) => {
  console.log('切换收藏状态，当前portalId:', portalId.value, 'spaceId:', spaceId.value)
  if (favoritesHook) {
    const success = await favoritesHook.handleToggleFavorite(menuItem)
    if (success) {
      // 更新本地状态
      favoritesList.value = favoritesHook.favoritesList.value
      favoriteIds.value = favoritesHook.favoriteIds.value
    }
    return success
  }
  return false
}

const handleRemoveFavorite = async (menuItem: MenuItem) => {
  console.log('移除收藏，当前portalId:', portalId.value, 'spaceId:', spaceId.value)
  if (favoritesHook) {
    const success = await favoritesHook.handleRemoveFavorite(menuItem.id)
    if (success) {
      // 更新本地状态
      favoritesList.value = favoritesHook.favoritesList.value
      favoriteIds.value = favoritesHook.favoriteIds.value
    }
    return success
  }
  return false
}

const handleSortFavorites = async (sortedIds: string[]) => {
  if (favoritesHook) {
    const success = await favoritesHook.handleSortFavorites(sortedIds)
    if (success) {
      // 更新本地状态
      favoritesList.value = favoritesHook.favoritesList.value
      favoriteIds.value = favoritesHook.favoriteIds.value
    }
    return success
  }
  return false
}

// 初始化
onMounted(async () => {
  loading.value = true;
  console.log('开始初始化FullMenu组件')

  // 先获取Portal配置
  await fetchPortalConfig()
  console.log('Portal配置获取完成，portalId:', portalId.value, 'spaceId:', spaceId.value)

  // 然后初始化收藏功能和导航功能
  const hook = initFavorites()
  const navHook = initNavigation()
  console.log('收藏功能和导航功能初始化完成')

  // 加载菜单、收藏和导航数据
  await fetchMenuData()
  await hook.fetchFavorites()
  await navHook.fetchNavigation()

  // 更新本地状态
  favoritesList.value = hook.favoritesList.value
  favoriteIds.value = hook.favoriteIds.value
  navigationList.value = navHook.navigationList.value

  // 当菜单数据加载完成后，更新收藏列表的完整信息
  // 等待searchMenuUtils准备就绪
  if (searchMenuUtils.value && searchMenuUtils.value.isReady && menuList.value.length > 0 && hook.updateFavoritesWithMenuData) {
    console.log('searchMenuUtils已准备就绪，更新收藏列表完整信息')
    hook.updateFavoritesWithMenuData(menuList.value)
    // 重新更新本地状态
    favoritesList.value = hook.favoritesList.value
    favoriteIds.value = hook.favoriteIds.value
  } else {
    console.log('searchMenuUtils未准备就绪或菜单数据为空，跳过更新收藏列表')
  }

  console.log('FullMenu组件初始化完成，收藏数量:', favoritesList.value.length)
})

// 暴露方法供外部调用
defineExpose({
  refreshData: fetchMenuData,
  searchValue,
  expandAll,
  toggleExpandAll
})
</script>

<style lang="less" scoped>
@import './styles/menu.less';
</style>
