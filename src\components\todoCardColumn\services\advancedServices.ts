import { h, type Ref } from 'vue';
import { Modal } from 'ant-design-vue';
import { UrlUtils, IframeUtils, DomUtils, ApiUtils, NEXTSTEP_TIMEOUT } from '../utils/pureUtils';
import type { ButtonType, ConfirmActionData } from '../types';

/**
 * NextStep状态管理服务
 */
export class NextStepStateService {
  /**
   * 清理NextStep状态
   */
  static cleanupNextStepState(
    button: ButtonType | undefined,
    nextStepState: Ref<{ isProcessing: boolean; buttonWithPosition: any }>
  ): void {
    if (button) {
      delete (button as any).hasSelectedNextAction;
      delete (button as any).triggerPosition;
    }
    nextStepState.value.isProcessing = false;
    nextStepState.value.buttonWithPosition = null;
  }

  /**
   * 启动NextStep定时器
   */
  static startNextStepTimer(
    nextStepTimer: { current: NodeJS.Timeout | null },
    nextStepState: Ref<{ isProcessing: boolean; buttonWithPosition: any }>,
    cleanupCallback: (button?: ButtonType) => void
  ): void {
    this.clearNextStepTimer(nextStepTimer);
    nextStepTimer.current = setTimeout(() => {
      if (nextStepState.value.isProcessing) {
        console.warn('NextStep操作超时，自动清理状态');
        cleanupCallback(nextStepState.value.buttonWithPosition);
      }
    }, NEXTSTEP_TIMEOUT);
  }

  /**
   * 清理NextStep定时器
   */
  static clearNextStepTimer(nextStepTimer: { current: NodeJS.Timeout | null }): void {
    if (nextStepTimer.current) {
      clearTimeout(nextStepTimer.current);
      nextStepTimer.current = null;
    }
  }
}

/**
 * NextStep处理服务
 */
export class NextStepProcessingService {
  /**
   * 处理NextStep结果（完整的业务逻辑）
   */
  static async handleNextStepResult(
    button: ButtonType,
    returnValue: string,
    nextStepState: Ref<{ isProcessing: boolean; buttonWithPosition: any }>,
    clearNextStepTimer: () => void,
    openOpinionPopoverWithPosition: (button: ButtonType, hasPlaceholder: boolean, needDraft: boolean) => Promise<void>,
    emit: (event: string, ...args: any[]) => void,
    cleanupNextStepState: (button?: ButtonType) => void
  ): Promise<void> {
    try {
      if (returnValue === 'repeal' || returnValue === 'stepStop' || returnValue === 'stepBack') {
        // 使用工具类构建URL和handleType
        const newUrl = UrlUtils.buildUpdatedUrl(returnValue);
        const newHandleType = UrlUtils.getHandleTypeFromReturnValue(returnValue);
        const httpType = 'POST';

        // 更新按钮配置，同时更新handleType避免重复进入nextStep判断
        const updatedButton = {
          ...button,
          url: newUrl,
          httpType: httpType,
          handleType: newHandleType || button.handleType
        };

        // 传递位置信息和标记
        (updatedButton as any).triggerPosition = (button as any).triggerPosition;
        (updatedButton as any).hasSelectedNextAction = true;

        // 检查是否需要填写意见，与原始JS逻辑一致
        if (button.opinionPolicy?.cancelOpinionPolicy === '1') {
          // 检查是否已有意见内容
          if (!button.paramMap?.content) {
            // 需要填写意见，打开意见弹窗
            console.log('需要填写撤销/终止/回退意见，打开意见弹窗');

            // 使用保存的位置信息打开意见弹窗
            await openOpinionPopoverWithPosition(
              updatedButton,
              false, // hasPlaceholder = true
              false // needDraft = false
            );
            return;
          }
        }

        // 清理状态并直接提交
        clearNextStepTimer();
        nextStepState.value.isProcessing = false;
        emit('button-click', updatedButton);
      } else {
        // 其他情况（继续提交），清理状态并直接提交原按钮
        clearNextStepTimer();
        nextStepState.value.isProcessing = false;
        emit('button-click', button);
      }
    } catch (error) {
      console.error('handleNextStepResult处理失败:', error);
      // 出错时清理状态并提交原按钮
      cleanupNextStepState(button);
      clearNextStepTimer();
      emit('button-click', button);
    }
  }

  /**
   * 处理NextStep流程（完整版本）
   */
  static handleNextStep(
    button: ButtonType,
    event: Event | undefined,
    nextStepState: Ref<{ isProcessing: boolean; buttonWithPosition: any }>,
    startNextStepTimer: () => void,
    clearNextStepTimer: () => void,
    cleanupNextStepState: (button?: ButtonType) => void,
    handleNextStepResult: (button: ButtonType, returnValue: string) => Promise<void>,
    emit: (event: string, ...args: any[]) => void,
    popoverManager?: any // 添加弹窗管理器参数
  ): void {
    // 🔥 关键修复：在弹出NextStep弹窗之前，先关闭当前已打开的意见弹窗
    const manager = popoverManager?.value || popoverManager;
    if (manager && typeof manager.closePopover === 'function' && manager.isVisible) {
      console.log('NextStep弹窗：检测到已有意见弹窗打开，先关闭意见弹窗');
      manager.closePopover();
    }

    // 并发保护
    if (nextStepState.value.isProcessing) {
      console.log('NextStep操作正在进行中，忽略重复点击');
      return;
    }

    const customAction = button.paramMap?.customAction;

    if (!customAction) {
      console.error('customAction 参数缺失');
      emit('button-click', button);
      return;
    }

    // 设置处理状态
    nextStepState.value.isProcessing = true;
    nextStepState.value.buttonWithPosition = button;
    startNextStepTimer();

    // 保存触发元素位置信息
    if (event?.target) {
      const targetElement = event.target as HTMLElement;
      (button as any).triggerPosition = DomUtils.saveElementPosition(targetElement);
      console.log('保存触发元素位置:', (button as any).triggerPosition);
    }

    // 构建对话框URL，与原始JS保持一致（使用工具类）
    const dialogUrl = UrlUtils.buildNextStepDialogUrl(customAction);

    console.log('NextStep 对话框URL:', dialogUrl);

    // 创建iframe内容，添加错误处理
    const iframeContent = h('iframe', {
      src: dialogUrl,
      frameborder: '0',
      style: {
        width: '100%',
        height: '120px',
        border: 'none'
      },
      onLoad: () => console.log('NextStep iframe加载成功'),
      onError: () => {
        console.error('NextStep iframe加载失败，使用默认处理');
        cleanupNextStepState(button);
        clearNextStepTimer();
        emit('button-click', button);
      }
    });

    // 创建Modal对话框，与原始JS的$.dialog行为一致
    const modal = Modal.confirm({
      title: '系统提示',
      content: iframeContent,
      width: 500,
      okText: '确定',
      cancelText: '取消',
      wrapClassName: "next-step-modal",
      onOk: () => {
        return new Promise((resolve, reject) => {
          try {
            // 获取iframe元素
            const iframe = document.querySelector('.ant-modal-body iframe') as HTMLIFrameElement;

            if (iframe && iframe.contentWindow) {
              // 使用工具类从iframe获取返回值
              const returnValue = IframeUtils.getIframeReturnValue(iframe);

              console.log('iframe返回值:', returnValue);

              // 处理返回值，与原始JS逻辑一致
              handleNextStepResult(button, returnValue);
              resolve(true);
            } else {
              console.error('无法访问iframe内容');
              cleanupNextStepState(button);
              clearNextStepTimer();
              reject('无法访问iframe内容');
            }
          } catch (error) {
            console.error('获取iframe返回值失败:', error);
            cleanupNextStepState(button);
            clearNextStepTimer();
            reject(error);
          }
        });
      },
      onCancel: () => {
        console.log('用户取消了nextStep操作');
        // 清理状态
        cleanupNextStepState(button);
        clearNextStepTimer();
      }
    });
  }
}

/**
 * 意见弹窗服务
 */
export class OpinionPopoverService {
  /**
   * 使用保存的位置信息打开意见弹窗（用于NextStep后续操作）
   */
  static async openOpinionPopoverWithPosition(
    button: ButtonType,
    hasPlaceholder: boolean = false,
    needDraft: boolean = true,
    popoverManager: any,
    actionButtonsWrapperRef: Ref<HTMLElement | undefined>,
    isCurrentPopoverTrigger: Ref<boolean>,
    buttonState: Ref<any>,
    props: any,
    checkNeedNextStepFlow: (button: ButtonType) => boolean,
    handleNextStepFlow: (button: ButtonType, confirmData: ConfirmActionData) => void,
    cleanupNextStepState: (button?: ButtonType) => void,
    clearNextStepTimer: () => void,
    nextStepState: Ref<{ isProcessing: boolean; buttonWithPosition: any }>,
    emit: (event: string, ...args: any[]) => void
  ): Promise<void> {
    const manager = popoverManager?.value || popoverManager;

    if (!manager || typeof manager.openPopover !== 'function') {
      console.error('弹窗管理器未正确注入或openPopover方法不存在');
      emit('button-click', button);
      return;
    }

    // 标记当前组件为弹窗触发者
    isCurrentPopoverTrigger.value = true;

    // 使用保存的位置信息创建虚拟触发元素
    let triggerElement: HTMLElement | null = null;

    if ((button as any).triggerPosition) {
      // 创建一个虚拟元素用于位置计算
      const virtualElement = document.createElement('div');
      virtualElement.style.position = 'fixed';
      virtualElement.style.left = (button as any).triggerPosition.left + 'px';
      virtualElement.style.top = (button as any).triggerPosition.top + 'px';
      virtualElement.style.width = (button as any).triggerPosition.width + 'px';
      virtualElement.style.height = (button as any).triggerPosition.height + 'px';
      virtualElement.style.opacity = '0';
      virtualElement.style.pointerEvents = 'none';
      virtualElement.style.zIndex = '-1';

      // 临时添加到页面中用于位置计算
      document.body.appendChild(virtualElement);
      triggerElement = virtualElement;

      console.log('使用保存的位置信息创建虚拟触发元素:', (button as any).triggerPosition);

      // 5秒后清理虚拟元素
      setTimeout(() => {
        if (document.body.contains(virtualElement)) {
          document.body.removeChild(virtualElement);
        }
      }, 5000);
    } else {
      // 如果没有保存的位置信息，尝试找到当前的按钮
      triggerElement = actionButtonsWrapperRef.value?.querySelector('.action-buttons .ant-btn') as HTMLElement;
    }

    if (!triggerElement) {
      console.error('找不到触发元素且无保存的位置信息');
      emit('button-click', button);
      return;
    }

    // 处理 attitudeList 清空（终止、撤销不显示态度）
    const buttonCopy = { ...button };
    if (buttonCopy.handleType === 'Terminate' || buttonCopy.handleType === 'Cancel') {
      buttonCopy.attitudeList = [];
    }

    // 根据需要获取草稿内容
    let draftContent = '';
    if (needDraft && props.renderInfo?.affairId) {
      try {
        draftContent = await ApiUtils.getDrafts(props.renderInfo.affairId);
      } catch (error) {
        console.error('获取草稿失败:', error);
        // 继续执行，使用空的草稿内容
      }
    }

    // 打开弹窗
    manager.openPopover(
      buttonCopy,
      triggerElement,
      (confirmedButton: ButtonType, confirmData: ConfirmActionData) => {
        // 弹窗确认后的回调
        // 将意见内容设置到 paramMap.content 中，与后端接口对接
        if (confirmData.content) {
          confirmedButton.paramMap.content = confirmData.content;
        }

        // 如果有态度选择，也设置到 paramMap 中
        if (confirmData.attitude) {
          confirmedButton.paramMap.attitude = confirmData.attitude.showValue;
          confirmedButton.paramMap.attitudeKey = confirmData.attitude.attitudeKey;
        }

        // 检查是否需要触发NextStep逻辑
        const needNextStep = checkNeedNextStepFlow(confirmedButton);

        if (needNextStep) {
          // 触发NextStep流程
          handleNextStepFlow(confirmedButton, confirmData);
        } else {
          // 正常提交流程
          // 保持hasSelectedNextAction标记
          (confirmedButton as any).hasSelectedNextAction = (button as any).hasSelectedNextAction;

          // 清理NextStep状态
          cleanupNextStepState();
          clearNextStepTimer();

          emit('button-click', confirmedButton, confirmData);
        }
      },
      (visible: boolean) => {
        // 弹窗可见性变化回调
        if (!visible) {
          isCurrentPopoverTrigger.value = false;
          // 如果弹窗关闭但没有确认，也要清理状态
          if (nextStepState.value.isProcessing) {
            cleanupNextStepState(button);
            clearNextStepTimer();
          }
        }
        emit('visibility-change', visible);
      },
      {
        // 传递草稿内容和其他配置
        draftContent,
        hasPlaceholder,
        placeholder: hasPlaceholder
          ? '请输入撤销附言（撤销操作不可恢复，请确认后再输入，不超过100字）'
          : '请输入处理意见',
        maxLength: hasPlaceholder ? 100 : 999999999,
        dataType: buttonState.value?.dataType || props.renderInfo?.appId || ''
      }
    );
  }

  /**
   * 获取触发元素
   */
  static getTriggerElement(
    isFromDropdown: boolean,
    event: Event | undefined,
    moreButtonRef: Ref<any>,
    actionButtonsWrapperRef: Ref<HTMLElement | undefined>
  ): HTMLElement | null {
    if (isFromDropdown) {
      // 如果是从下拉菜单点击的，使用"更多"按钮作为触发元素
      const moreButtonComponent = moreButtonRef.value;
      if (moreButtonComponent) {
        return (moreButtonComponent as any).$el || moreButtonComponent;
      }
    } else {
      // 如果是直接点击的按钮，使用事件的目标元素
      return (event?.target as HTMLElement) ||
        (event?.currentTarget as HTMLElement) ||
        actionButtonsWrapperRef.value?.querySelector('.action-buttons .ant-btn') as HTMLElement;
    }
    return null;
  }

  /**
   * 打开意见填写弹窗（标准版）
   */
  static async openOpinionPopover(
    button: ButtonType,
    event: Event | undefined,
    isFromDropdown: boolean = false,
    hasPlaceholder: boolean = false,
    needDraft: boolean = true,
    popoverManager: any,
    moreButtonRef: Ref<any>,
    actionButtonsWrapperRef: Ref<HTMLElement | undefined>,
    isCurrentPopoverTrigger: Ref<boolean>,
    buttonState: Ref<any>,
    props: any,
    checkNeedNextStepFlow: (button: ButtonType) => boolean,
    handleNextStepFlow: (button: ButtonType, confirmData: ConfirmActionData) => void,
    emit: (event: string, ...args: any[]) => void
  ): Promise<void> {
    const manager = popoverManager?.value || popoverManager;

    if (!manager || typeof manager.openPopover !== 'function') {
      console.error('弹窗管理器未正确注入或openPopover方法不存在');
      emit('button-click', button);
      return;
    }

    // 标记当前组件为弹窗触发者
    isCurrentPopoverTrigger.value = true;

    // 获取触发元素
    const triggerElement = this.getTriggerElement(isFromDropdown, event, moreButtonRef, actionButtonsWrapperRef);

    if (!triggerElement) {
      console.error('找不到触发元素');
      emit('button-click', button);
      return;
    }

    // 处理 attitudeList 清空（终止、撤销不显示态度）
    const buttonCopy = { ...button };
    if (buttonCopy.handleType === 'Terminate' || buttonCopy.handleType === 'Cancel') {
      buttonCopy.attitudeList = [];
    }

    // 根据需要获取草稿内容
    let draftContent = '';
    if (needDraft && props.renderInfo?.affairId) {
      try {
        draftContent = await ApiUtils.getDrafts(props.renderInfo.affairId);
      } catch (error) {
        console.error('获取草稿失败:', error);
        // 继续执行，使用空的草稿内容
      }
    }

    // 打开弹窗
    manager.openPopover(
      buttonCopy,
      triggerElement,
      (confirmedButton: ButtonType, confirmData: ConfirmActionData) => {
        // 弹窗确认后的回调
        // 将意见内容设置到 paramMap.content 中，与后端接口对接
        if (confirmData.content) {
          confirmedButton.paramMap.content = confirmData.content;
        }

        // 如果有态度选择，也设置到 paramMap 中
        if (confirmData.attitude) {
          confirmedButton.paramMap.attitude = confirmData.attitude.showValue;
          confirmedButton.paramMap.attitudeKey = confirmData.attitude.attitudeKey;
        }

        // 检查是否需要触发NextStep逻辑
        const needNextStep = checkNeedNextStepFlow(confirmedButton);

        if (needNextStep) {
          // 触发NextStep流程
          handleNextStepFlow(confirmedButton, confirmData);
        } else {
          // 正常提交流程
          emit('button-click', confirmedButton, confirmData);
        }
      },
      (visible: boolean) => {
        // 弹窗可见性变化回调
        if (!visible) {
          isCurrentPopoverTrigger.value = false;
        }
        emit('visibility-change', visible);
      },
      {
        // 传递草稿内容和其他配置
        draftContent,
        hasPlaceholder,
        placeholder: hasPlaceholder
          ? '请输入撤销附言（撤销操作不可恢复，请确认后再输入，不超过100字）'
          : '请输入处理意见',
        maxLength: hasPlaceholder ? 100 : 999999999,
        dataType: buttonState.value?.dataType || props.renderInfo?.appId || ''
      }
    );
  }
}

/**
 * UI定时器管理服务
 */
export class UITimerService {
  /**
   * 清理隐藏定时器
   */
  static clearHideTimer(hideTimer: Ref<NodeJS.Timeout | undefined>): void {
    if (hideTimer.value) {
      clearTimeout(hideTimer.value);
      hideTimer.value = undefined;
    }
  }

  /**
   * 设置隐藏定时器
   */
  static setHideTimer(
    hideTimer: Ref<NodeJS.Timeout | undefined>,
    isCurrentPopoverTrigger: Ref<boolean>,
    isDropdownVisible: Ref<boolean>,
    delay: number = 200
  ): void {
    this.clearHideTimer(hideTimer);
    hideTimer.value = setTimeout(() => {
      if (!isCurrentPopoverTrigger.value) {
        isDropdownVisible.value = false;
      }
    }, delay);
  }
}
