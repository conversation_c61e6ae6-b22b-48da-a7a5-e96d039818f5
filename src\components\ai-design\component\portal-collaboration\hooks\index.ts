import { ref, inject } from 'vue'
import { getUsualAssistList } from '@/api/usualAssistant'
import { useMenu } from '@/stores/global'
import { useChatList } from '@/stores/chatList'
import { getAssistIdByCode } from '@/api/home';
import { Modal } from 'ant-design-vue';

export const useRedirectAssistant = () => {
  // 助手跳转标识
  const hasAssistantRedirect = ref(false)

  // 获取 SDK 实例
  const sdkInstance = inject('sdkInstance') as any

  // 获取 stores
  const uMenu = useMenu()
  const chatListStore = useChatList()

  const getAssistId = async (code: string) => {
    const res: any = await getAssistIdByCode([code]);
    if (res.code === '0' && res.data && res.data.length > 0) {
      return res.data[0].id;
    }
    return null;
  };

  /**
   * 重定向助手方法
   * @param assistId 助手ID
   * @param defaultSendMsg 是否默认发送消息
   * @param callback 回调函数
   * @param showProlog 是否显示开场白
   */
  const redirectToAssit = async (
    assistId?: string,
    defaultSendMsg?: boolean,
    callback?: any,
    showProlog?: boolean
  ) => {
    if (assistId || (sdkInstance?.preConfig && sdkInstance.preConfig.defaultAssistId)) {
      // 标记有助手跳转
      hasAssistantRedirect.value = true

      const defaultAssistId = assistId || sdkInstance.preConfig.defaultAssistId

      // 开始重定向，如果已经是自己了，不再执行
      if (uMenu.currentMenuInfo?.id == defaultAssistId) {
        // TODO 要告知是否静默 发消息，如果要发，这里设置为true，定位到智能体的时候，不请求开场白，直接发消息后再清除isHide,如果不静默发消息，这里就不需要缓存，会请求开场白
        // if ((sdkInstance?.preConfig && sdkInstance.preConfig.defaultSendMsg) || defaultSendMsg) {
        //   localStorage.setItem('isHide', 'true')
        // }
        if (callback) {
          callback()
        }
        return
      }

      // 先接口调用判断一下，常用助手是否存在，不存在则不跳转
      getUsualAssistList(7, defaultAssistId).then(async(res: any) => {
        if (res && res.code == 0 && res.data && res.data.length > 0) {
          const item = res.data.find((item: any) => item.id == defaultAssistId)
          let aist = item
          if (!aist) {
            aist = {
              id: defaultAssistId,
            }
          }
          await uMenu.changeMenu(item, { isAsit: true, isRedirectAist: true })
          chatListStore.chatActions.setDynamicData('showProlog', showProlog || false)
          if (callback) {
            callback()
          }
        }
      })
    } else {
      if (callback) {
        callback()
      }
    }
  }

  /**
   * 通过助手代码跳转到智能助手
   * @param agentCode 助手代码
   * @param showProlog 是否显示开场白
   */
  const redirectToAssistantByCode = async (agentCode: string, showProlog: boolean = true) => {
    try {
      // 等待SDK初始化
      if (sdkInstance?.waitForInitialization) {
        await sdkInstance.waitForInitialization();
      }

      // 获取助手ID
      const assistId = await getAssistId(agentCode);
      if (assistId) {
        await redirectToAssit(assistId, false, null, showProlog);
      } else {
        Modal.confirm({
          title: '提示',
          content: '抱歉，您暂无此功能权限。如有需要，请联系管理员',
          okText: '确认',
          cancelText: '取消',
        });
      }
    } catch (error) {
      console.error('SDK 初始化失败:', error);
      // 如果SDK初始化失败，直接尝试获取助手ID并跳转
      const assistId = await getAssistId(agentCode);
      if (assistId) {
        await redirectToAssit(assistId, false, null, showProlog);
      }
    }
  }

  return {
    redirectToAssit,
    getAssistId,
    redirectToAssistantByCode
  }
}
