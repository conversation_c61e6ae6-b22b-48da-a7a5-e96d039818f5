<template>
  <div class="top px-3">
    <div class="top_left">
      <slot name="topLeft"></slot>
    </div>
    <div class="top_right">
      <CopilotHeader v-if="!isPortal" />
    </div>
  </div>
</template>
<script setup lang="ts">
import CopilotHeader from '@/components/copilotHeader/index.vue';
const isPortal = inject<boolean>('isPortal');
</script>
<style lang="less" scoped>
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 56px;
}
</style>
