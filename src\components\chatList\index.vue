<template>
  <div
    class="relative w-full h-full overflow-auto chat_list"
    ref="chatListRef"
    @scroll="handleScroll($event)"
    @wheel="handleMouseWheel"
  >
    <!-- 无限加载 -->
    <VueEternalLoading
      v-if="showVueEternalLoading"
      position="top"
      :style="{ 'text-align': 'center' }"
      :container="chatListRef"
      v-model:is-initial="isInitial"
      :load="load"
    >
      <template #no-results>
        <div></div>
      </template>
      <template #no-more>
        <div></div>
      </template>
      <template #loading>
        <AiIdentify :loadingStatus="true"></AiIdentify>
      </template>
    </VueEternalLoading>
    <!-- {{ uChatList.dynamicData.allCardData }} -->
    <!-- 开场白以及预置问题 -->
    <!-- 如果是历史会话，初次加载不显示开场白，后续新开会话的时候，再显示开场白 -->
    <!-- 🎯 关键：PortalView始终存在于keep-alive中，内部根据条件决定显示内容 -->
    <keep-alive :max="8">
      <PortalView
        v-show="showPortalView && !collApproveData"
        class="flex-1"
        :key="getPortalViewKey()"
        :view="view"
        :show-portal="showPortalView"
        :isCopilot="!isPortal || (isPortal && showDobuleScreen)"
      />
    </keep-alive>
    <!-- <PresentQuestions v-if="uChatList.dynamicData.allCardData.length === 0 && !uChatList.dynamicData.historyModel && uChatList.dynamicData.showProlog"/> -->
    <!-- 对话列表 -->
    <div class="chat_list-item" v-if="!showPortalView || collApproveData">
      <CollApproval v-if="collApproveData" :collApproveData="collApproveData" @collApproval="collApproval"></CollApproval>
      <div v-for="(item,index) in uChatList.dynamicData.allCardData" :key="index">
        <!-- 用户提问  v-if="!item.isHide && item.messageType == 0"-->
        <div class="question_wrap" v-if="item.componentKey === 'AiRequestCard' && !item.data.isHide">
          <UploadFiles :fileList="item.staticData.citations" :showDeleteBtn="false" :hasMargin="true" />
          <UserQuestion :transUserInfo="item"> </UserQuestion>
        </div>
        <!-- agent回答 -->
        <div class="answer_wrap" v-else-if="item.componentKey === 'AiDataCard'">
          <!-- 助手信息 -->
          <div class="answer_agent_info">
            <AgentInfo />
          </div>
          <!-- 运行过程 意图识别中、Agent执行中、网络超时、comi不小心发生错误 走这段逻辑 -->
          <div class="answer_agent_step" v-if="[6].includes(item.data.status)">
            <RunStep :status="item.data.status"/>
          </div>
          <!-- 回答内容 Agent回答中、回答完毕、取消执行中 、已停止回答 走这段逻辑-->
          <div class="answer_agent_content" v-if="[0,1,2,3,4,5,7].includes(item.data.status)">
            <AiAnswer :transAiInfo="item" :tagData="uChatList.dynamicData.memberTagData" :inx="index" :lastIndex="uChatList.dynamicData.allCardData.length - 1" @handleTagClick="handleTagClick"/>
            <!-- @handleCitationClick="handleCitationClick" -->
          </div>
          <!-- 自定义模块 -->
          <!-- <div class="answer_agent_custom">
            <AiCustom :item="item"/>
          </div> -->
          <!-- 推荐问题 Agent回答中、回答完毕、取消执行中 、已停止回答 可能均会涉及到推荐问题，因此需要走这段逻辑--->
          <div class="answer_agent_recommend" v-if="[2,3,4,5].includes(item.data.status) && index == uChatList.dynamicData.allCardData.length - 1 && (item.data.recommandQuestion && item.data.recommandQuestion.length > 0)">
            <RecommendQuestions :questions="formatRecommendQuestions(item.data.recommandQuestion)"/>
          </div>
          <!-- 人员信息 -->
          <div class="answer_agent_person">
            <PersonalInfo v-if="uChatList.dynamicData.memberTagData && uChatList.dynamicData.memberTagData?.[index]" :personalDatas="uChatList.dynamicData.memberTagData?.[index]" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 传送至最新处 -->
  <div class="absolute bottom-0 right-0 w-8 h-8 scroll-to-bottom-btn" v-if="isToNewBtnShow && uChatList.dynamicData.allCardData.length !== 0" @click="srollToBottom">
    <NewLogBtn />
  </div>
</template>

<script setup lang="ts">
  import { ref, nextTick, onMounted, computed, inject } from 'vue';
  import { useChatList } from '@/stores/chatList';
  // 公共组件
  import NewLogBtn from '@/components/newLogBtn/index.vue';
  import UploadFiles from '@/components/common/uploadFiles/index.vue';
  import AiCustom from '@/components/aiCustom/index.vue';
  import RecommendQuestions from '@/components/recommendQuestions/index.vue';
  import PersonalInfo from '@/components/personalInfo/index.vue';
  import UserQuestion from '@/components/userQuestion/index.vue';
  import AiAnswer from '@/components/aiAnswer/index.vue';
  import AiIdentify from '@/components/aiIdentify/index.vue';
  // chat 特有组件
  import PresentQuestions from '@/components/chatList/components/presentQuestions/index.vue';
  import AgentInfo from '@/components/chatList/components/agentInfo/index.vue';
  import RunStep from '@/components/chatList/components/runStep/index.vue';
  import PortalView from '@/components/ai-design/PortalView.vue';
  import { getHistoryDetail } from '@/api/historyConversation';
  import { VueEternalLoading } from '@ts-pro/vue-eternal-loading';
  import { formatMessageList } from '@/utils/messageFormatter';
  import cardInstance from '@/stores/card';
  import { usePortalStore } from '@/stores/portal';
  import { useCustomData } from '@/stores/global';
  import CollApproval from '@/components/coll-approval/index.vue';
  import { useChatListAndElasticIpt } from '@/hooks/portal';
  import { getAssistIntroduce } from '@/api/common';
import type { TypeResponse } from '@/types/api';
  const { goingSecItem } = useChatListAndElasticIpt();
  const useCustomDataStore = useCustomData();

  const chatListRef = ref<HTMLElement | null>(null);
  const uChatList = useChatList();
  const isToNewBtnShow = ref(false); // 是否展示向下滚动按钮
  let isInitial = ref(false); // 是否初始化

  const portalStore = usePortalStore();
  const isPortal = inject<boolean>('isPortal');
const collApproveData = computed(()=>{
  return useCustomDataStore.customDataObj?.collApproveData;
});
  const showDobuleScreen = computed(() => {
    return uChatList.dynamicData.dobuleScreenData.show;
  });

  // 缓存portal view数据，确保切换助手时数据稳定性
  let cachedView: any = null;
  const view = computed(() => {
    const currentView = portalStore.view;
    // 只有当有有效数据时才更新缓存，否则使用之前的缓存
    if (currentView && currentView.children && currentView.children.length > 0) {
      cachedView = currentView;
    }
    // 始终返回有效的view数据，确保组件稳定性
    return cachedView || { children: [] };
  });

  const showPortalView = computed(() => {
    const realView = portalStore.view;
    const shouldShow = realView && uChatList.dynamicData.allCardData.length === 0 && !showVueEternalLoading.value;
    return shouldShow;
  });

  // 生成PortalView的稳定缓存key - 始终使用统一key保持组件实例
  const getPortalViewKey = () => {
    // 使用统一的key，确保组件实例始终保持，避免切换助手时销毁
    const key = 'portal-view-unified';
    return key;
  };

  // 滚动监听接管
  const handleMouseWheel = (event: WheelEvent) => {
    const scrollContainer = chatListRef.value;
    if (scrollContainer) {
      // 使用 setTimeout 确保在滚动完成后再判断位置
      setTimeout(() => {
        // 判断是否滚动到底部（允许5px的误差）
        const isAtBottom =
          scrollContainer.scrollTop + scrollContainer.clientHeight >=
          scrollContainer.scrollHeight - 50;

        if (isAtBottom) {
          // 滚动到底部，不锁定滚动，允许自动滚动到新消息
          uChatList.chatActions.setDynamicData('lockScroll', false);
        } else {
          // 没有滚动到底部，锁定滚动，用户可能在查看历史消息
          uChatList.chatActions.setDynamicData('lockScroll', true);
        }
      }, 50);
    }
  };

  // 格式化推荐问题
  const formatRecommendQuestions = (questions: any[]) => {
    return questions.map((q, index) => ({
      subject: q.subject || '',
      index
    }));
  };

  // 滚动到底部
  const srollToBottom = () => {
    uChatList.chatActions.setDynamicData('lockScroll', false);
    uChatList.scrollToBottom();
  };

  // 滚动
  const handleScroll = (e: Event) => {
    const etgt = e.target as HTMLElement;
    if (etgt.scrollHeight - etgt.scrollTop > etgt.clientHeight * 2) {
      isToNewBtnShow.value = true;
    } else {
      isToNewBtnShow.value = false;
    }
  };

  const handleTagClick = (datas: any) => {
    console.log('handleTagClick', datas);
    const { matched_word, data, index } = datas;
    if (!uChatList.dynamicData.memberTagData) {
      uChatList.chatActions.setDynamicData('memberTagData', {});
    }
    if (!uChatList.dynamicData.memberTagData[index]) {
      uChatList.dynamicData.memberTagData[index] = [];
    }
    let arr = uChatList.dynamicData.memberTagData[index];
    arr = arr.concat([{ matched_word, data }]);
    uChatList.dynamicData.memberTagData[index] = arr;
    srollToBottom();
  };
  //collApproval 辅助审批
  const collApproval = async (data) =>{
    const res = (await getAssistIntroduce(data.agentCode)) as TypeResponse;
    goingSecItem(res.data);
    cardInstance.sendMessage('帮我生成摘要，affairId:'+ data.id, [], true);
  }
  // 如果有会话id或者portal, 表明是历史会话，就获取历史会话
  const showVueEternalLoading = computed(() => {
    if (uChatList.dynamicData.historyModel) {
      return true;
    }
    return false;
  });
  // 历史会话分页信息
  let historyPageInfo = ref({
    pageNumber: 1,
    pageSize: 10,
    pages: 0,
    needTotal: true,
  });
  // 加载更多
  const load = async (action: any) => {
    if (!uChatList.dynamicData.historySessionId) {
      action.noResults();
      return;
    }
    // 请求
    await getHistoryDetailSteps();
    // 条件判断
    if (historyPageInfo.value.pageNumber < historyPageInfo.value.pages) {
      // console.log('需要加载');
      // 需要加载
      historyPageInfo.value.pageNumber ++;
      action.loaded(uChatList.dynamicData.allCardData.length < 10 ? 10 : uChatList.dynamicData.allCardData.length, historyPageInfo.value.pageSize);
    } else {
      // console.log('无需加载');
      // 无需加载
      action.noMore();
    }
  };
  // 请求历史会话详情
  const getHistoryDetailSteps = async () => {
    const params = {
      params: {
        sessionId: uChatList.dynamicData.historySessionId,
      },
      pageInfo: historyPageInfo.value,
    };
    const { data } = (await getHistoryDetail(params)) as any;
    if (data?.content?.length > 0) {
      uChatList.chatActions.setDynamicData('sesssionId', data.content[0].aiSessionId);
      const sortedList = data?.content
        .reverse()
        .filter((item: any) => item.messageType === 0 || item.messageType === 1);
      const formatedData = formatMessageList(sortedList, {});
      uChatList.chatActions.setDynamicData('allCardData', [...formatedData, ...uChatList.dynamicData.allCardData]);
      nextTick(() => {
        if(historyPageInfo.value.pageNumber === 1){
          uChatList.scrollToBottom('auto');
        }
      });
    }
    if(data.pageInfo){
      historyPageInfo.value = data.pageInfo;
    }
  };
  const dealCopy = (e) => {
    const text_only = document.getSelection().toString();
    const clipdata = e.clipboardData || window.clipboardData;
    if(clipdata) {
      clipdata.setData('text/plain', text_only);
      clipdata.setData('text/html', text_only);
      e.preventDefault();
    }
  }
  // 挂载
  onMounted(() => {
    uChatList.setChatListRef(chatListRef.value);
    document.addEventListener('copy', dealCopy);
  });
  onBeforeUnmount(()=>{
    document.removeEventListener('copy', dealCopy);
  });

</script>

<style scoped lang="less">
  .question_wrap{
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;
  }
  .scroll-to-bottom-btn{
    z-index: 9;
  }
</style>

<!-- 对话的初始状态：
1、获取当前会话列表的类型： 1、新会话 2、历史会话  chatType: 1, // 1、新会话 2、历史会话
2、如果是历史会话则获取历史会话的列表数据并渲染，如果是新会话则获取当前助手的开场白
3、开始新对话或者继续对话，如果有开场白则删除开场白，如果是历史会话则继续添加会话卡片


对话流程：
1、向会话列表推送问题，问题可能包括：附件、@助手(@助手时要改变助手头像和助手名称，这个信息从问题里面拿，注意建立问题和答案的关联关系)
2、回答的步骤：
  2.1 意图识别：展示意图识别卡片
  2.2 agent 执行中：展示 agent 执行中的卡片
  2.3 取消执行，展示取消执行的卡片
  2.4 执行失败，展示执行失败的卡片(包括：网络错误、agent 抛出的异常)
  2.5 执行成功：根据数据显示不同的卡片


卡片的内容划分：
1、提问的卡片：
  1.1 问题内容。
  1.2 问题附件。

2、回答的卡片：
  2.1 回答的助手名称和头像。
  2.2 回答的内容。
  2.3 复制按钮。
  2.4 重新生成按钮。
  2.5 点赞按钮。
  2.6 知识源。
  2.7 更多的问题。

3、取消执行、执行失败、执行成功的卡片：
  3.1 重新生成按钮

对话的功能：
1、清除对话
2、添加消息
3、回到初始状态

输入框的功能：
1、发起消息(包含快捷指令)
2、新开对话
3、切换助手
4、添加附件 -->
