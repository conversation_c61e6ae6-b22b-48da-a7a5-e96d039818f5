<template>
  <main class="portal-layout-main">
    <keep-alive :max="5">
      <component 
        v-for="(item, index) in nodes" 
        :key="getStableKey(item, index)"
        v-bind="item.props" 
        :class="item.class" 
        :nodes="item.processedChildren || item.children" 
        :style="item.style"
        :is="item.tag" 
      />
    </keep-alive>
  </main>
</template>
<script setup lang="ts">
import { parseCondition } from '../utils/conditionUtil';

const { nodes } = defineProps({
  nodes: {
    type: Array,
    default: () => [],
  },
});

// 生成稳定的key
function getStableKey(item: any, index: number): string {
  const parts = [
    item.id,
    item.name, 
    item.tag || 'component',
    item.type || 'default',
    index
  ].filter(Boolean);
  
  return `layoutmain-${parts.join('-')}`;
}


</script>

<style scoped lang="less">
.portal-layout-main {
  flex: 1;
  height: 100%;
  width: 100%;
  display: block;
}
</style>
