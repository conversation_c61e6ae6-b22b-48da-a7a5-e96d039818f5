import { get, post } from '../config.js';
import portalConfigJson from '#/config.json'
const ctxPath = '/seeyon';
import axios from 'axios'
import { getQueryString } from '@/utils/common';


const topWindow:any = window.top || window;
const viewModeName = 'comiTargetVersion';
const viewMode = getQueryString(viewModeName) || window.sessionStorage.getItem(viewModeName) || topWindow[viewModeName] || window.localStorage.getItem(viewModeName) || '1.0';

// 获取用户信息
export const getConfig = () => {
  if (import.meta.env.DEV) {
    return new Promise(async (resolve, reject) => {
      resolve(portalConfigJson);
    })
  }else{
    let url = '';
    if(viewMode === 'A6'){
      url = `/seeyon/ai-platform/ai-static/ai-copilot/public/A6/config.json?t=${new Date().getTime()}`
    }else{
      url = `/seeyon/ai-platform/ai-static/ai-copilot/public/config.json?t=${new Date().getTime()}`;
    }
    return new Promise(async (resolve, reject) => {
      const response = await axios.get(url);
      const config = response.data;
      resolve(config);
    })
  }
};

