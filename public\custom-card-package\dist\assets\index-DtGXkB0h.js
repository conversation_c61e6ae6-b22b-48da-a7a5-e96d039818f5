import { importShared, __tla as __tla_0 } from "./__federation_fn_import-B47IVf2F.js";
let ChartTypeEnum, TableEnum, TypeSourceEnum, _imports_0, _sfc_main$4, _sfc_main$1, _sfc_main$2, _sfc_main, _sfc_main$3, changeChartData, extractAiCards, jsonToMarkdownTable, useWindowSize;
let __tla = Promise.all([
  (() => {
    try {
      return __tla_0;
    } catch {
    }
  })()
]).then(async () => {
  ChartTypeEnum = {
    Line: "line",
    Bar: "bar",
    Pie: "pie"
  };
  TableEnum = {
    Table: "table"
  };
  TypeSourceEnum = {
    PcSide: "pcSide",
    PcSideExpand: "pcSideExpand",
    PcDialog: "pcDialog",
    App: "app",
    AppDialog: "appDialog"
  };
  jsonToMarkdownTable = (data, column_names) => {
    if (!Array.isArray(data) || data.length === 0) {
      return "";
    }
    const headers = column_names.map((item) => item.title);
    const headerRow = headers.join(" | ");
    const separatorRow = headers.map(() => "---").join(" | ");
    const keyToExclude = "key";
    const dataRows = data.map((item) => {
      return Object.keys(item).filter((key) => key !== keyToExclude).map((key) => item[key]).join(" | ");
    });
    const table = [
      `| ${headerRow} |`,
      `| ${separatorRow} |`,
      ...dataRows.map((row) => `| ${row} |`)
    ].join("\n");
    return table;
  };
  changeChartData = (column_names, cardChartData) => {
    try {
      let echartData = [];
      for (let i = 0; i < column_names.length; i++) {
        echartData[i] = cardChartData.map((list) => {
          return list[i];
        });
      }
      echartData = echartData.map((list) => {
        return list.map((item) => {
          if (item === null) {
            return 0;
          }
          return item;
        });
      });
      const echartDataShowIndex = echartData.map((item) => {
        return item.every((itemChild) => {
          return /^[+-]?(?:\d+\.\d+|\.\d+|\d+)$/.test(itemChild) || typeof itemChild === "number" || itemChild > 1e12;
        });
      });
      for (let i = 0; i < column_names.length; i++) {
        if (column_names[i].includes("ID")) {
          echartDataShowIndex[i] = false;
        }
      }
      const echartDataShowList = [];
      const showColumnNames = [];
      echartDataShowIndex.map((item, index) => {
        if (item) {
          echartDataShowList.push(echartData[index].map((i) => Number(i)));
          showColumnNames.push(column_names[index]);
        }
      });
      const legend_datas = cardChartData.map((list) => {
        return list[0];
      });
      return [
        echartDataShowList,
        showColumnNames,
        legend_datas
      ];
    } catch (error) {
      return [
        [],
        [],
        []
      ];
    }
  };
  extractAiCards = (chartData, typeSource) => {
    try {
      const matchesTable = "";
      const { data: cardChart = [], column_names = [], type = "table", title, maxRows = 0, isOverMaxRows = false } = chartData.data.result[0].renderInfo;
      const wordContent = chartData == null ? void 0 : chartData.data.markdown;
      const cardChartData = cardChart.map((list) => {
        return list.map((item) => {
          return item;
        });
      });
      const [echartDataShowList, showColumnNames] = changeChartData(column_names, cardChartData);
      const table_column_names = column_names.map((title2, index) => {
        const item_col = {
          dataIndex: `column_${index}`,
          title: title2,
          resizable: typeSource === TypeSourceEnum.PcDialog && cardChartData.length > 1,
          ellipsis: true,
          sorter: cardChartData.length > 1 ? true : false,
          align: "left"
        };
        if (title2.includes("ID")) {
          item_col.width = cardChartData[0][index] ? Math.max(cardChartData[0][index].length * 10, title2.length * 20) : 140;
        }
        if (title2.includes("\u65E5\u671F") || title2.includes("\u65F6\u95F4")) {
          item_col.width = 140;
        }
        if (title2.includes("\u5730\u5740")) {
          item_col.width = 300;
        }
        return item_col;
      });
      const legend_datas = cardChart.map((list) => {
        return list[0];
      });
      const cardTableData = cardChartData.map((row, rowIndex) => {
        const item = {
          key: rowIndex
        };
        table_column_names.forEach((col, colIndex) => {
          item[col.dataIndex] = row[colIndex] ?? null;
        });
        return item;
      });
      return {
        title,
        chartData: {
          type,
          data: echartDataShowList,
          column_names: legend_datas,
          legend_datas: showColumnNames,
          changeColumnNames: column_names
        },
        tableData: {
          tableColumnNames: table_column_names,
          tableData: cardTableData
        },
        selectChartType: TableEnum.Table,
        total: 0,
        summaryTotal: cardChartData.length,
        summaryContent: wordContent,
        chartsVisibleList: [
          "table",
          "line",
          "bar",
          "pie"
        ],
        isCanLookSql: true,
        backMaxRows: maxRows || 50,
        matchesTable,
        isOverMaxRows
      };
    } catch (error) {
      console.log(error);
      return {
        title: "",
        chartData: {
          type: TableEnum.Table,
          data: [],
          column_names: [],
          legend_datas: [],
          changeColumnNames: []
        },
        tableData: {
          tableColumnNames: [],
          tableData: [],
          backMaxRows: 0,
          summaryTotal: 0
        },
        selectChartType: TableEnum.Table,
        total: 0,
        summaryTotal: 0,
        summaryContent: "",
        chartsVisibleList: [
          "table",
          "line",
          "bar",
          "pie"
        ],
        isCanLookSql: true,
        backMaxRows: 0,
        summaryMaxRows: 50,
        matchesTable: 0,
        isOverMaxRows: false
      };
    }
  };
  const { defineComponent: _defineComponent$5 } = await importShared("vue");
  const { toDisplayString: _toDisplayString$3, createElementVNode: _createElementVNode$3, unref: _unref$4, withCtx: _withCtx, createVNode: _createVNode$1, openBlock: _openBlock$5, createElementBlock: _createElementBlock$4 } = await importShared("vue");
  const _hoisted_1$2 = {
    class: "card-header card-header-arrow-special"
  };
  const _hoisted_2$2 = {
    class: "card-title"
  };
  const _hoisted_3$2 = {
    class: "iconScale"
  };
  const { Tooltip } = await importShared("ant-design-vue");
  _sfc_main$4 = _defineComponent$5({
    __name: "card-header",
    props: {
      title: {}
    },
    emits: [
      "changeStatus"
    ],
    setup(__props, { emit: __emit }) {
      const props = __props;
      const emit = __emit;
      const scaleDialogClick = () => {
        emit("changeStatus", true);
      };
      return (_ctx, _cache) => {
        return _openBlock$5(), _createElementBlock$4("div", _hoisted_1$2, [
          _createElementVNode$3("div", _hoisted_2$2, _toDisplayString$3(props.title), 1),
          _createElementVNode$3("div", _hoisted_3$2, [
            _createVNode$1(_unref$4(Tooltip), {
              "arrow-point-at-center": true,
              color: "#fff",
              arrow: true,
              "overlay-inner-style": {
                borderRadius: "4px"
              }
            }, {
              title: _withCtx(() => _cache[1] || (_cache[1] = [
                _createElementVNode$3("div", {
                  class: "scale-tooltip"
                }, "\u653E\u5927\u56FE\u8868", -1)
              ])),
              default: _withCtx(() => [
                _createElementVNode$3("i", {
                  class: "iconfont ai-icon-fangda",
                  onClick: _cache[0] || (_cache[0] = ($event) => scaleDialogClick())
                })
              ]),
              _: 1
            })
          ])
        ]);
      };
    }
  });
  const iconData = [
    {
      icon: "ai-icon-biaogeshitu",
      type: TableEnum.Table,
      disabled: false,
      name: "\u8868\u683C"
    },
    {
      icon: "ai-icon-bingtu",
      type: ChartTypeEnum.Pie,
      disabled: false,
      name: "\u997C\u56FE"
    },
    {
      icon: "ai-icon-zhexiantu",
      type: ChartTypeEnum.Line,
      disabled: false,
      name: "\u6298\u7EBF\u56FE"
    },
    {
      icon: "ai-icon-zhuxingtu",
      type: ChartTypeEnum.Bar,
      disabled: false,
      name: "\u67F1\u5F62\u56FE"
    }
  ];
  const { defineComponent: _defineComponent$4 } = await importShared("vue");
  const { unref: _unref$3, createElementVNode: _createElementVNode$2, renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock$4, createElementBlock: _createElementBlock$3, normalizeClass: _normalizeClass, toDisplayString: _toDisplayString$2, createCommentVNode: _createCommentVNode$2 } = await importShared("vue");
  const _hoisted_1$1 = [
    "data-sourceType"
  ];
  const _hoisted_2$1 = {
    class: "card-nav__item"
  };
  const _hoisted_3$1 = [
    "onClick"
  ];
  const _hoisted_4$1 = {
    key: 0
  };
  const { inject: inject$5, ref: ref$4 } = await importShared("vue");
  _sfc_main$3 = _defineComponent$4({
    __name: "card-nav",
    props: {
      selectChartType: {},
      title: {},
      isShowEcharts: {
        type: Boolean
      }
    },
    emits: [
      "changeChartType",
      "lookSql",
      "onPrev"
    ],
    setup(__props, { emit: __emit }) {
      const iconDataImg = ref$4([]);
      const props = __props;
      iconDataImg.value = props.isShowEcharts ? iconData : [];
      const { typeSource } = inject$5("cardOptions");
      const emit = __emit;
      const changeChartType = (type) => {
        emit("changeChartType", type);
      };
      return (_ctx, _cache) => {
        return _openBlock$4(), _createElementBlock$3("div", {
          class: "card-nav",
          "data-sourceType": _unref$3(typeSource)
        }, [
          _cache[0] || (_cache[0] = _createElementVNode$2("div", {
            class: "card-nav__select"
          }, null, -1)),
          _createElementVNode$2("div", _hoisted_2$1, [
            (_openBlock$4(true), _createElementBlock$3(_Fragment, null, _renderList(iconDataImg.value, (item, index) => {
              return _openBlock$4(), _createElementBlock$3("div", {
                key: index,
                class: _normalizeClass([
                  _ctx.selectChartType === item.type ? "card-nav__item__Icon--active" : "",
                  "card-nav__item__Icon"
                ]),
                onClick: ($event) => changeChartType(item.type)
              }, [
                _createElementVNode$2("i", {
                  class: _normalizeClass([
                    "iconfont",
                    `${item.icon}`
                  ])
                }, null, 2),
                _unref$3(typeSource) === _unref$3(TypeSourceEnum).PcDialog ? (_openBlock$4(), _createElementBlock$3("span", _hoisted_4$1, _toDisplayString$2(item.name), 1)) : _createCommentVNode$2("", true)
              ], 10, _hoisted_3$1);
            }), 128))
          ])
        ], 8, _hoisted_1$1);
      };
    }
  });
  const { defineComponent: _defineComponent$3 } = await importShared("vue");
  const { unref: _unref$2, openBlock: _openBlock$3, createBlock: _createBlock$1, createCommentVNode: _createCommentVNode$1, createElementBlock: _createElementBlock$2 } = await importShared("vue");
  const { CopilotTable, ComiTable } = await importShared("@seeyon/seeyon-comi-plugins-library");
  const { reactive: reactive$2, inject: inject$4, toRaw: toRaw$1 } = await importShared("vue");
  _sfc_main$2 = _defineComponent$3({
    __name: "card-table",
    props: {
      height: {
        type: Number,
        default: 400
      },
      tableDataInfo: {
        type: Object,
        default: () => ({
          tableColumnNames: [],
          tableData: [],
          backMaxRows: 0,
          summaryTotal: 0,
          isOverMaxRows: false,
          countNumber: 10
        })
      }
    },
    emits: [
      "changeChartData"
    ],
    setup(__props, { expose: __expose, emit: __emit }) {
      const { typeSource } = inject$4("cardOptions");
      const props = __props;
      const CardSizeType = {
        size: "small",
        sizeDefault: "default"
      };
      const tableState = reactive$2({
        datas: props.tableDataInfo.tableData,
        columns: props.tableDataInfo.tableColumnNames,
        isLoading: true,
        pageParams: {
          current: 1,
          pageSize: 10,
          dataTotal: props.tableDataInfo.tableData.length,
          size: typeSource === TypeSourceEnum.App || typeSource === TypeSourceEnum.PcSide ? CardSizeType.size : CardSizeType.sizeDefault,
          showSizeChanger: false
        },
        countNumber: props.tableDataInfo.countNumber,
        showPagination: props.tableDataInfo.tableData.length > 10 && (typeSource === TypeSourceEnum.PcSide || typeSource === TypeSourceEnum.PcSideExpand || typeSource === TypeSourceEnum.PcDialog)
      });
      const sortItem = reactive$2({
        field: "",
        order: ""
      });
      const sortTableData = [
        ...tableState.datas
      ];
      const emit = __emit;
      const handleTableChange = (page) => {
        tableState.pageParams.current = page.pagination.current;
        sortItem.field = page.sorter.field;
        sortItem.order = page.sorter.order;
        if (page.sorter && page.sorter.field) {
          if (page.sorter.order) {
            const sortItem2 = tableState.datas.sort((a, b) => {
              const field = page.sorter.field;
              const valA = a[field];
              const valB = b[field];
              if (typeof valA === "string" && typeof valB === "string") {
                if (page.sorter.order === "ascend") {
                  return valA.localeCompare(valB, void 0, {
                    numeric: true
                  });
                } else {
                  return valB.localeCompare(valA, void 0, {
                    numeric: true
                  });
                }
              }
              if (page.sorter.order === "ascend") {
                if (valA > valB) return 1;
                if (valA < valB) return -1;
                return 0;
              } else {
                if (valA < valB) return 1;
                if (valA > valB) return -1;
                return 0;
              }
            });
            tableState.datas = [
              ...sortItem2
            ];
          } else {
            tableState.datas = [
              ...sortTableData
            ];
          }
          tableState.pageParams.current = 1;
          const { datas } = tableState;
          emit("changeChartData", toRaw$1(datas));
        }
      };
      const getTableData = () => {
        return tableState.datas.slice((tableState.pageParams.current - 1) * 10, tableState.pageParams.current * 10);
      };
      __expose({
        getTableData
      });
      return (_ctx, _cache) => {
        return _openBlock$3(), _createElementBlock$2("div", null, [
          _unref$2(typeSource) === _unref$2(TypeSourceEnum).PcSide || _unref$2(typeSource) === _unref$2(TypeSourceEnum).PcSideExpand || _unref$2(typeSource) === _unref$2(TypeSourceEnum).PcDialog ? (_openBlock$3(), _createBlock$1(_unref$2(CopilotTable), {
            key: 0,
            "max-table-rows": _unref$2(typeSource) === _unref$2(TypeSourceEnum).PcDialog ? tableState.countNumber : 5,
            datas: tableState.datas,
            columns: tableState.columns,
            "page-params": tableState.pageParams,
            "is-full-screen": false,
            bordered: true,
            "show-pagination": tableState.showPagination,
            size: "small",
            "show-total": () => {
              return props.tableDataInfo.isOverMaxRows ? "" : `\u5171${tableState.pageParams.dataTotal}\u884C`;
            },
            onTableChange: handleTableChange
          }, null, 8, [
            "max-table-rows",
            "datas",
            "columns",
            "page-params",
            "show-pagination",
            "show-total"
          ])) : _createCommentVNode$1("", true),
          _unref$2(typeSource) === _unref$2(TypeSourceEnum).App || _unref$2(typeSource) === _unref$2(TypeSourceEnum).AppDialog ? (_openBlock$3(), _createBlock$1(_unref$2(ComiTable), {
            key: 1,
            datas: tableState.datas,
            columns: tableState.columns,
            loading: tableState.isLoading,
            "page-params": tableState.pageParams,
            bordered: true,
            "show-total": () => {
              return props.tableDataInfo.isOverMaxRows ? "" : `\u5171${tableState.pageParams.dataTotal}\u884C`;
            },
            size: _unref$2(typeSource) === _unref$2(TypeSourceEnum).App ? CardSizeType.size : CardSizeType.sizeDefault,
            onTableChange: handleTableChange
          }, null, 8, [
            "datas",
            "columns",
            "loading",
            "page-params",
            "show-total",
            "size"
          ])) : _createCommentVNode$1("", true)
        ]);
      };
    }
  });
  _imports_0 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAbCAYAAACN1PRVAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAK4SURBVHgB3VRLbhNBEK3qblveoDg3mBsknMCTG5CFNyAhfACEWRNlxhawIAuzhkX4CCQkJIsT2LkAMTfIEWw2QWZmilc9NrKC7cxMBAsqitxud9er9+pVE/0vcRpLc/W7oQoxQJJBfBlsO/MpTu40LO3fGEwM7RG5YNPvyihjMzCZnK6yqwRGkmnF4TqQj31pNYycM0vAJMEtlvPPYFkZjIUeWOLW1f1OzNOU011/Rv+YAcBkrZ0OUQhTyTiJf4Y14pHDWkgOHsa18dUzmngORhZrK3z7EEXofmlmYBRphShaHNbrzmjyTNKOEHeXQKXBTqJ5F3xCDwY4wxS+9nt/RoPcpB3xl9W9wjK+iOf7hszIMe14eXC3BhdYkRm77KDzpD65LkchZs8AJGSGUK6JRrH++ypF4AFumsQO3/dl78ZgT6MfXRYeIXGAxKIg1rCoywyr5wiIpDYffejJo225Nsp4dHQZOusiyBbCCGKRE+YgL51fE9bGr7VijIMHN0JjK2mvvcalv8G6+vwkFNTJtSxLaJlbNRTu8r6wxUmAklre6dqDkoKK9VNFC1DxcqG/av23kPwbvk7UlSsyNqbOObKczuCypllWAiDjZdPKvWR+WH2/FgVLvo0PUXZivNzyPaNsV52EaG6VMcbwWqEIjMJcOspZaa+QPGfICxb5pzoHLM8447gd83ijjJviOeaoziYCWNPpIAMARUA+8WDaKzUM9mYZSe/usX25KVehOdMZq8P6YBnk7PKemUWfAHQhkh7ei7fPWuGhHgCwhqGuGd4x6NdSQsg25Sw5uA5Io/Bz9RjJABB7w+RAOmsQUXpFgDRKv/qvegmeLA6tv8xn949NWPRu6Vcfc9xbVpgq078db8DuXT/7SiXDUYVQ+dCradl7lcCMZOPElL9aCSwhd0EplWb2T+MXTvH4JZOsIvMAAAAASUVORK5CYII=";
  const { defineComponent: _defineComponent$2 } = await importShared("vue");
  const { unref: _unref$1, toDisplayString: _toDisplayString$1, createElementVNode: _createElementVNode$1, openBlock: _openBlock$2, createElementBlock: _createElementBlock$1, createCommentVNode: _createCommentVNode, createVNode: _createVNode } = await importShared("vue");
  const _hoisted_1 = {
    class: "card-summary"
  };
  const _hoisted_2 = {
    key: 0,
    class: "card-summary__top"
  };
  const _hoisted_3 = {
    class: "card-summary__top--left"
  };
  const _hoisted_4 = {
    key: 1,
    class: "card-summary__content"
  };
  const _hoisted_5 = {
    class: "card-summary__content--item"
  };
  const _hoisted_6 = {
    class: "card-summary__content--item__content"
  };
  const { ComiMarkdown } = await importShared("@seeyon/seeyon-comi-plugins-library");
  const { toRefs, ref: ref$3, inject: inject$3 } = await importShared("vue");
  _sfc_main$1 = _defineComponent$2({
    __name: "card-summary",
    props: {
      total: {},
      summaryContent: {},
      backMaxRows: {},
      summaryTotal: {},
      isOverMaxRows: {
        type: Boolean
      }
    },
    setup(__props) {
      const { typeSource } = inject$3("cardOptions");
      const props = __props;
      const { summaryContent, backMaxRows, summaryTotal, isOverMaxRows } = toRefs(props);
      const markdownContent = ref$3({
        context: summaryContent.value,
        finish: 1
      });
      return (_ctx, _cache) => {
        return _openBlock$2(), _createElementBlock$1("div", _hoisted_1, [
          _unref$1(summaryTotal) > 1 && _unref$1(isOverMaxRows) ? (_openBlock$2(), _createElementBlock$1("div", _hoisted_2, [
            _createElementVNode$1("div", _hoisted_3, "\u4EC5\u5C55\u793A\u524D" + _toDisplayString$1(_unref$1(backMaxRows)) + "\u6761\u6570\u636E", 1)
          ])) : _createCommentVNode("", true),
          _unref$1(summaryContent) ? (_openBlock$2(), _createElementBlock$1("div", _hoisted_4, [
            _createElementVNode$1("div", _hoisted_5, [
              _cache[0] || (_cache[0] = _createElementVNode$1("div", {
                class: "card-summary__title"
              }, [
                _createElementVNode$1("div", {
                  class: "card-summary__subtitle"
                }, [
                  _createElementVNode$1("img", {
                    src: _imports_0
                  }),
                  _createElementVNode$1("span", null, "\u603B\u7ED3\uFF1A")
                ])
              ], -1)),
              _createElementVNode$1("div", _hoisted_6, [
                _createVNode(_unref$1(ComiMarkdown), {
                  content: markdownContent.value,
                  "is-app-model": _unref$1(typeSource) === _unref$1(TypeSourceEnum).App
                }, null, 8, [
                  "content",
                  "is-app-model"
                ])
              ])
            ])
          ])) : _createCommentVNode("", true)
        ]);
      };
    }
  });
  const { defineComponent: _defineComponent$1 } = await importShared("vue");
  const { toDisplayString: _toDisplayString, createElementVNode: _createElementVNode, openBlock: _openBlock$1, createElementBlock: _createElementBlock } = await importShared("vue");
  const { shallowRef: shallowRef$1, watchEffect: watchEffect$1, readonly: readonly$1, watch: watch$1, customRef: customRef$1, getCurrentScope: getCurrentScope$1, onScopeDispose, effectScope, getCurrentInstance: getCurrentInstance$1, hasInjectionContext: hasInjectionContext$1, inject: inject$2, provide, ref: ref$2, isRef: isRef$1, unref: unref$1, toValue: toValue$1, computed: computed$2, reactive: reactive$1, toRefs: toRefs$1, toRef: toRef$1, shallowReadonly, onBeforeMount, nextTick: nextTick$1, onBeforeUnmount, onMounted: onMounted$1, onUnmounted, isReactive } = await importShared("vue");
  function tryOnScopeDispose(fn) {
    if (getCurrentScope$1()) {
      onScopeDispose(fn);
      return true;
    }
    return false;
  }
  const localProvidedStateMap = /* @__PURE__ */ new WeakMap();
  const injectLocal = (...args) => {
    var _a;
    const key = args[0];
    const instance = (_a = getCurrentInstance$1()) == null ? void 0 : _a.proxy;
    if (instance == null && !hasInjectionContext$1()) throw new Error("injectLocal must be called in setup");
    if (instance && localProvidedStateMap.has(instance) && key in localProvidedStateMap.get(instance)) return localProvidedStateMap.get(instance)[key];
    return inject$2(...args);
  };
  const isClient = typeof window !== "undefined" && typeof document !== "undefined";
  typeof WorkerGlobalScope !== "undefined" && globalThis instanceof WorkerGlobalScope;
  const toString = Object.prototype.toString;
  const isObject = (val) => toString.call(val) === "[object Object]";
  function pxValue(px) {
    return px.endsWith("rem") ? Number.parseFloat(px) * 16 : Number.parseFloat(px);
  }
  function toArray(value) {
    return Array.isArray(value) ? value : [
      value
    ];
  }
  function getLifeCycleTarget(target) {
    return getCurrentInstance$1();
  }
  function tryOnMounted(fn, sync = true, target) {
    const instance = getLifeCycleTarget();
    if (instance) onMounted$1(fn, target);
    else if (sync) fn();
    else nextTick$1(fn);
  }
  function watchImmediate(source, cb, options) {
    return watch$1(source, cb, {
      ...options,
      immediate: true
    });
  }
  const { isRef, shallowRef, ref: ref$1, watchEffect, computed: computed$1, inject: inject$1, defineComponent, h, TransitionGroup, shallowReactive, Fragment, toValue, unref, getCurrentInstance, onMounted, watch, customRef, onUpdated, readonly, reactive, hasInjectionContext, toRaw, nextTick, markRaw, getCurrentScope, isReadonly, onBeforeUpdate } = await importShared("vue");
  const defaultWindow = isClient ? window : void 0;
  function unrefElement(elRef) {
    var _a;
    const plain = toValue(elRef);
    return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;
  }
  function useEventListener(...args) {
    const cleanups = [];
    const cleanup = () => {
      cleanups.forEach((fn) => fn());
      cleanups.length = 0;
    };
    const register = (el, event, listener, options) => {
      el.addEventListener(event, listener, options);
      return () => el.removeEventListener(event, listener, options);
    };
    const firstParamTargets = computed$1(() => {
      const test = toArray(toValue(args[0])).filter((e) => e != null);
      return test.every((e) => typeof e !== "string") ? test : void 0;
    });
    const stopWatch = watchImmediate(() => {
      var _a, _b;
      return [
        (_b = (_a = firstParamTargets.value) == null ? void 0 : _a.map((e) => unrefElement(e))) != null ? _b : [
          defaultWindow
        ].filter((e) => e != null),
        toArray(toValue(firstParamTargets.value ? args[1] : args[0])),
        toArray(unref(firstParamTargets.value ? args[2] : args[1])),
        toValue(firstParamTargets.value ? args[3] : args[2])
      ];
    }, ([raw_targets, raw_events, raw_listeners, raw_options]) => {
      cleanup();
      if (!(raw_targets == null ? void 0 : raw_targets.length) || !(raw_events == null ? void 0 : raw_events.length) || !(raw_listeners == null ? void 0 : raw_listeners.length)) return;
      const optionsClone = isObject(raw_options) ? {
        ...raw_options
      } : raw_options;
      cleanups.push(...raw_targets.flatMap((el) => raw_events.flatMap((event) => raw_listeners.map((listener) => register(el, event, listener, optionsClone)))));
    }, {
      flush: "post"
    });
    const stop = () => {
      stopWatch();
      cleanup();
    };
    tryOnScopeDispose(cleanup);
    return stop;
  }
  function useMounted() {
    const isMounted = shallowRef(false);
    const instance = getCurrentInstance();
    if (instance) {
      onMounted(() => {
        isMounted.value = true;
      }, instance);
    }
    return isMounted;
  }
  function useSupported(callback) {
    const isMounted = useMounted();
    return computed$1(() => {
      isMounted.value;
      return Boolean(callback());
    });
  }
  const ssrWidthSymbol = Symbol("vueuse-ssr-width");
  function useSSRWidth() {
    const ssrWidth = hasInjectionContext() ? injectLocal(ssrWidthSymbol, null) : null;
    return typeof ssrWidth === "number" ? ssrWidth : void 0;
  }
  function useMediaQuery(query, options = {}) {
    const { window: window2 = defaultWindow, ssrWidth = useSSRWidth() } = options;
    const isSupported = useSupported(() => window2 && "matchMedia" in window2 && typeof window2.matchMedia === "function");
    const ssrSupport = shallowRef(typeof ssrWidth === "number");
    const mediaQuery = shallowRef();
    const matches = shallowRef(false);
    const handler = (event) => {
      matches.value = event.matches;
    };
    watchEffect(() => {
      if (ssrSupport.value) {
        ssrSupport.value = !isSupported.value;
        const queryStrings = toValue(query).split(",");
        matches.value = queryStrings.some((queryString) => {
          const not = queryString.includes("not all");
          const minWidth = queryString.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);
          const maxWidth = queryString.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);
          let res = Boolean(minWidth || maxWidth);
          if (minWidth && res) {
            res = ssrWidth >= pxValue(minWidth[1]);
          }
          if (maxWidth && res) {
            res = ssrWidth <= pxValue(maxWidth[1]);
          }
          return not ? !res : res;
        });
        return;
      }
      if (!isSupported.value) return;
      mediaQuery.value = window2.matchMedia(toValue(query));
      matches.value = mediaQuery.value.matches;
    });
    useEventListener(mediaQuery, "change", handler, {
      passive: true
    });
    return computed$1(() => matches.value);
  }
  useWindowSize = function(options = {}) {
    const { window: window2 = defaultWindow, initialWidth = Number.POSITIVE_INFINITY, initialHeight = Number.POSITIVE_INFINITY, listenOrientation = true, includeScrollbar = true, type = "inner" } = options;
    const width = shallowRef(initialWidth);
    const height = shallowRef(initialHeight);
    const update = () => {
      if (window2) {
        if (type === "outer") {
          width.value = window2.outerWidth;
          height.value = window2.outerHeight;
        } else if (type === "visual" && window2.visualViewport) {
          const { width: visualViewportWidth, height: visualViewportHeight, scale } = window2.visualViewport;
          width.value = Math.round(visualViewportWidth * scale);
          height.value = Math.round(visualViewportHeight * scale);
        } else if (includeScrollbar) {
          width.value = window2.innerWidth;
          height.value = window2.innerHeight;
        } else {
          width.value = window2.document.documentElement.clientWidth;
          height.value = window2.document.documentElement.clientHeight;
        }
      }
    };
    update();
    tryOnMounted(update);
    const listenerOptions = {
      passive: true
    };
    useEventListener("resize", update, listenerOptions);
    if (window2 && type === "visual" && window2.visualViewport) {
      useEventListener(window2.visualViewport, "resize", update, listenerOptions);
    }
    if (listenOrientation) {
      const matches = useMediaQuery("(orientation: portrait)");
      watch(matches, () => update());
    }
    return {
      width,
      height
    };
  };
  const { defineComponent: _defineComponent } = await importShared("vue");
  const { unref: _unref, openBlock: _openBlock, createBlock: _createBlock } = await importShared("vue");
  const { ComiEchart: ComiEchart$1 } = await importShared("@seeyon/seeyon-comi-plugins-library");
  const { computed, inject, ref } = await importShared("vue");
  _sfc_main = _defineComponent({
    __name: "card-comi-charts-bi",
    props: {
      chartData: {
        default: () => ({
          version: "2",
          type: "table",
          data: [],
          column_names: [],
          legend_datas: [],
          legendPosition: "right",
          changeColumnNames: []
        })
      },
      filedName: {
        default: ""
      },
      height: {
        default: 400
      },
      selectChartType: {
        default: "table"
      }
    },
    setup(__props) {
      const { height: appScreenHeight } = useWindowSize();
      const props = __props;
      const { typeSource } = inject("cardOptions");
      const realHeight = ref(typeSource === TypeSourceEnum.AppDialog ? appScreenHeight.value - 72 - 50 : props.height);
      const transformedChartData = computed(() => {
        const transformed = {
          ...props.chartData
        };
        if (props.filedName) {
          transformed.type = typeof props.filedName === "object" ? props.filedName[props.selectChartType] : props.filedName;
        } else {
          transformed.type = props.selectChartType;
        }
        if (typeSource === TypeSourceEnum.PcDialog || typeSource === TypeSourceEnum.PcSideExpand || typeSource === TypeSourceEnum.AppDialog) {
          transformed.legendPosition = "right";
        }
        if (transformed.type === "pie_chart") {
          const { column_names, legend_datas } = transformed;
          transformed.column_names = legend_datas;
          transformed.legend_datas = column_names.map((item) => {
            return item === null ? "" : item;
          });
          if (transformed.data && transformed.data[0]) {
            transformed.data = column_names.map((_item, index) => {
              return transformed.data.map((itemChild) => {
                return itemChild[index];
              });
            });
          }
        }
        transformed.version = "2";
        return transformed;
      });
      return (_ctx, _cache) => {
        return _openBlock(), _createBlock(_unref(ComiEchart$1), {
          content: transformedChartData.value,
          "content-height": realHeight.value,
          verson: "2"
        }, null, 8, [
          "content",
          "content-height"
        ]);
      };
    }
  });
  const { ComiEchart } = await importShared("@seeyon/seeyon-comi-plugins-library");
});
export {
  ChartTypeEnum,
  TableEnum,
  TypeSourceEnum,
  __tla,
  _imports_0,
  _sfc_main$4 as _sfc_main,
  _sfc_main$1,
  _sfc_main$2,
  _sfc_main as _sfc_main$3,
  _sfc_main$3 as _sfc_main$4,
  changeChartData,
  extractAiCards,
  jsonToMarkdownTable,
  useWindowSize
};
