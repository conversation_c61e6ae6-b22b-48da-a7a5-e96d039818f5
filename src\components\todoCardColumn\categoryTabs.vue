<template>
  <div class="category-tabs-wrapper mb-[8px]" ref="categoryTabsWrapper">
    <div
      class="category-tab-content"
      ref="tabContent"
      :class="{ 'can-wheel': canWheel, hasArrow: showArrow }"
      v-if="categories.length > 0"
    >
      <!-- 具体分类选项 -->
      <div
        v-for="item in categories"
        :key="item.appId + '-' + item.classificationName"
        class="category-item"
        :class="{ active: activeCategory === item.appId }"
        @click="handleCategoryClick(item.appId, item)"
        ref="categoryItem"
      >
        <span>{{ item.classificationName }}</span>·{{ formatDisplayCount(item.total, props.threshold) }}
      </div>
    </div>

    <!-- 翻页箭头 -->
    <template v-if="showArrow">
      <div
        v-for="icon in icons"
        :key="icon.class"
        class="arrow-wrapper"
        :class="{
          'arrow-disabled': icon.type === 'pre' ? !canScrollLeft : !canScrollRight,
        }"
        @click="handleArrowClick(icon.type)"
      >
        <i class="iconfont" :class="icon.class"></i>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';

interface CategoryItem {
  portletParams: string;        // JSON字符串，包含各种参数
  isPortlet: string;           // "1" 表示是portlet
  predicate: boolean;          // 断言条件
  total: string;               // 总数
  appId: string;               // 应用ID
  classificationName: string;   // 分类名称
  isHide: string;              // "1" 表示隐藏
  // 可选字段
  subAppId?: string;
  isThird?: string;
  isLabel?: string;
  canDrag?: string;
}

interface Props {
  categories: CategoryItem[];
  activeCategory: string;
  canWheel?: boolean;
  threshold?: number;
}

const props = withDefaults(defineProps<Props>(), {
  categories: () => [],
  activeCategory: '',
  canWheel: false,
  threshold: undefined
});

const emit = defineEmits<{
  'category-click': [categoryId: string, categoryData?: CategoryItem];
}>();

// 响应式引用
const categoryTabsWrapper = ref<HTMLElement>();
const tabContent = ref<HTMLElement>();
const categoryItem = ref<HTMLElement[]>([]);
const showArrow = ref<boolean>(false);

// 滚动状态管理
const scrollPosition = ref<number>(0);
const containerWidth = ref<number>(0);
const contentWidth = ref<number>(0);

// 翻页图标配置
const icons = [
  {
    type: 'pre',
    class: 'ai-icon-zuo',
  },
  {
    type: 'next',
    class: 'ai-icon-you',
  },
];

// 计算属性
const totalCategories = computed(() => {
  return props.categories.length;
});

const currentCategoryIndex = computed(() => {
  const categoryIndex = props.categories.findIndex((item) => item.appId === props.activeCategory);
  return categoryIndex >= 0 ? categoryIndex : 0;
});

// 格式化显示数量
const formatDisplayCount = (total: string, threshold?: number): string => {
  const count = parseInt(total) || 0;
  if (threshold && count > threshold) {
    return `${threshold}+`;
  }
  return total;
};

// 计算是否可以向左滚动
const canScrollLeft = computed(() => {
  // 添加1px的容差，避免浮点数精度问题
  return scrollPosition.value > 1;
});

// 计算是否可以向右滚动
const canScrollRight = computed(() => {
  const maxScrollLeft = contentWidth.value - containerWidth.value;
  // 添加1px的容差，避免浮点数精度问题
  return scrollPosition.value < maxScrollLeft - 1;
});

// 获取隐藏内容的真实宽度
const getHiddenWidth = () => {
  return new Promise<number>((resolve) => {
    const element = tabContent.value;
    if (!element) {
      resolve(0);
      return;
    }

    // 克隆节点
    const clone = element.cloneNode(true) as HTMLElement;
    clone.style.cssText = `
      position: absolute;
      visibility: hidden;
      max-width: fit-content;
      overflow: visible;
    `;

    // 添加到DOM
    categoryTabsWrapper.value?.appendChild(clone);

    nextTick(() => {
      const width = clone.scrollWidth;
      // 移除克隆节点
      categoryTabsWrapper.value?.removeChild(clone);
      resolve(width);
    });
  });
};

// 更新滚动状态
const updateScrollState = () => {
  if (!tabContent.value) return;

  const currentScrollLeft = tabContent.value.scrollLeft;
  const currentClientWidth = tabContent.value.clientWidth;
  const currentScrollWidth = tabContent.value.scrollWidth;

  scrollPosition.value = currentScrollLeft;
  containerWidth.value = currentClientWidth;
  contentWidth.value = currentScrollWidth;
};

// 分类点击处理
const handleCategoryClick = (categoryId: string, categoryData?: CategoryItem) => {
  emit('category-click', categoryId, categoryData);

  // 滚动到当前选中的分类
  // nextTick(() => {
    const targetIndex = categoryId === '' ? 0 : props.categories.findIndex(item => item.appId === categoryId);
    if (categoryItem.value && categoryItem.value[targetIndex]) {
      categoryItem.value[targetIndex].scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        // inline: 'center',
      });
    }
  // });
};

// 箭头点击处理 - 改为滚动控制
const handleArrowClick = (type: string) => {
  if (!tabContent.value) return;

  const scrollAmount = containerWidth.value * 0.8; // 每次滚动容器宽度的80%
  const currentScrollLeft = tabContent.value.scrollLeft;
  const maxScrollLeft = contentWidth.value - containerWidth.value;

  let targetScrollLeft = currentScrollLeft;

  if (type === 'next' && canScrollRight.value) {
    // 向右滚动
    targetScrollLeft = Math.min(currentScrollLeft + scrollAmount, maxScrollLeft);
    tabContent.value.scrollBy({
      left: scrollAmount,
      behavior: 'smooth'
    });
  } else if (type === 'pre' && canScrollLeft.value) {
    // 向左滚动
    targetScrollLeft = Math.max(currentScrollLeft - scrollAmount, 0);
    tabContent.value.scrollBy({
      left: -scrollAmount,
      behavior: 'smooth'
    });
  }

  // 立即根据预期的滚动位置更新状态
  scrollPosition.value = targetScrollLeft;

  // 调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log('箭头点击 - 立即更新状态:', {
      type,
      currentScrollLeft,
      targetScrollLeft,
      maxScrollLeft,
      canScrollLeft: targetScrollLeft > 0,
      canScrollRight: targetScrollLeft < maxScrollLeft - 1
    });
  }

  // 同时设置延迟更新作为保险，确保最终状态正确
  setTimeout(() => {
    updateScrollState();
  }, 300);
};

// 滚轮处理
const handleWheel = (e: WheelEvent) => {
  if (tabContent.value) {
    e.preventDefault();
    tabContent.value.scrollLeft += e.deltaY;
  }
};

// 监听滚动事件
const handleScroll = () => {
  updateScrollState();
};

// 添加防抖的滚动监听器，用于处理频繁的滚动事件
const debounceUpdateState = (() => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;
  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      updateScrollState();
    }, 50); // 增加延迟到50ms，给smooth滚动更多时间
  };
})();

// 检查内容宽度，决定是否显示箭头
const checkContentWidth = async () => {
  try {
    const hiddenWidth = await getHiddenWidth();
    const wrapperWidth = categoryTabsWrapper.value?.offsetWidth || 0;

    showArrow.value = hiddenWidth > wrapperWidth;

    // 更新滚动状态
    nextTick(() => {
      updateScrollState();
    });
  } catch (error) {
    console.warn('检查内容宽度失败:', error);
    showArrow.value = false;
  }
};

// 生命周期
onMounted(() => {
  checkContentWidth();

  if (props.canWheel && tabContent.value) {
    tabContent.value.addEventListener('wheel', handleWheel, { passive: false });
  }

  // 添加滚动监听
  if (tabContent.value) {
    tabContent.value.addEventListener('scroll', handleScroll, { passive: true });
    tabContent.value.addEventListener('scroll', debounceUpdateState, { passive: true });
  }

  window.addEventListener('resize', checkContentWidth);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkContentWidth);

  if (props.canWheel && tabContent.value) {
    tabContent.value.removeEventListener('wheel', handleWheel);
  }

  // 移除滚动监听
  if (tabContent.value) {
    tabContent.value.removeEventListener('scroll', handleScroll);
    tabContent.value.removeEventListener('scroll', debounceUpdateState);
  }
});

// 监听分类数据变化
watch(() => props.categories, () => {
  nextTick(() => {
    checkContentWidth();
  });
}, { deep: true, immediate: true });

// 暴露方法
defineExpose({
  handleCategoryClick,
  checkContentWidth
});
</script>

<style scoped lang="less">
.category-tabs-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  width: 100%;
  height: auto;
  height: 28px;

  .arrow-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.4);

    .iconfont {
      font-size: 14px;
      color: rgba(142, 148, 162, 1);
    }

    &:hover:not(.arrow-disabled) {
      background: #fff;

      .iconfont {
        color: rgba(111, 118, 134, 1);
      }
    }

    &:active:not(.arrow-disabled) {
      background: #fff;
      .iconfont {
        color: rgba(111, 118, 134, 1);
      }
    }

    &.arrow-disabled {
      cursor: not-allowed;

      .iconfont {
        color: rgba(216, 218, 223, 1);
      }
    }
  }

  .category-tab-content {
    display: flex;
    overflow: hidden;
    min-width: 1px;
    gap: 12px;
    max-width: 100%;
    flex: 1;

    &.hasArrow {
      max-width: calc(100% - 52px);
    }

    &.can-wheel {
      overflow-x: auto;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */

      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, and Opera */
      }
    }

    .category-item {
      padding: 2px 8px;
      cursor: pointer;
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-size: 12px;
      line-height: 24px;
      white-space: nowrap;
      color: #000;
      transition: all 0.2s ease;
      flex-shrink: 0;
      border-radius: 4px;

      &:hover {
        background: #fff;
      }

      &.active {
        background: #fff;
        color: #4379FF;
        font-weight: @font-weight-500;
      }
    }
  }
}
</style>
