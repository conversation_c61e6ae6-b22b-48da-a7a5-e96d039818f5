<template>
  <div class="sec_assist_wrap">
    <div v-if="astlist.length > 0"  class="sec_assist_shirt" ref="scrollContainer">
      <div
        v-for="(item, index) in astlist"
        :class="{
          sec_assist_item: true,
          active_sec_assist_item: index === currentIndex,
          actived_sec_assist_item: item.id === transCkAst[0]?.id,
        }"
        :key="item.id"
        @mousedown="preventBlur($event)"
        @click="() => goSecItem(item)"
        :ref="(el) => (itemRefs[index] = el)"
      >
        <Avatar :class="item.iconUrl ? '' : 'sky-bg'" :size="20" :src="item.iconUrl">{{ item.name.slice(0, 2) }}</Avatar>
        <div class="sec_tiDec_com">
          <div class="sec_assist_title ellipsis">{{ item.name }}</div>
          <div class="sec_assist_desc ellipsis">{{ item.introduce }}</div>
        </div>
      </div>
    </div>
    <CustomEmpty v-else-if="!loading" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import type { TypeResponse } from '@/types/api';
import type { AssistInfo } from '@/types/index';
import CustomEmpty from '@/components/empty/index.vue';
// a-b-c
import { Avatar, Image, message } from 'ant-design-vue';
import AiAvatar from '@/assets/imgs/default-avatar.png';
// 1-2-3
import { getAssistantList } from '@/api/common';
import { useCheckAssistantParams } from '@/stores/homeSearch';

const uCkAstPms = useCheckAssistantParams();
// props
const props = defineProps<{
  transCkAst: AssistInfo[];
}>();
// emit
const emit = defineEmits(['toGoSecItem', 'toCloseSal']);
const currentIndex = ref<undefined | number>(undefined);
// 助手列表
const astlist = ref<
  {
    id: string;
    name: string;
    iconUrl: string;
    introduce: string;
  }[]
>([]);
const scrollContainer = ref<HTMLElement | null>(null);
const itemRefs = ref<HTMLElement[]>([]);
  const loading = ref<boolean>(false);

// 获取助手列表
const gettingAsiLst = async (keyStr: string) => {
  loading.value = true;
  try {
    const res: TypeResponse = (await getAssistantList({
      pageInfo: {
        pageNumber: 1,
        pageSize: 500,
        needTotal: true,
      },
      params: {
        keyword: keyStr,
        assistantType: 0,
      },
    })) as TypeResponse;
    loading.value = false;
    if (res && res.code === '0') {
      const { content = [] } = res.data;
      // 未查到就关闭
      if (content.length === 0) {
        // emit('toCloseSal');
        astlist.value = [];
        return;
      }
      astlist.value = [...content];
    } else {
      message.error(res.message);
    }
    // console.log('api助手列表______', res);
  } catch (error) {
    loading.value = false;
    console.log('错误', error);
  }
};
// 鼠标事件
const preventBlur = (e: any) => {
  e.preventDefault();
};
// 点选
const goSecItem = (val: any) => {
  emit('toGoSecItem', val);
  currentIndex.value = undefined;
};
// 监听
uCkAstPms.$subscribe((mute, state) => {
  gettingAsiLst(state.ckAstStr);
});

// 滚动到可视区域
const scrollIntoViewIfNeeded = (index: number | undefined) => {
  if (index !== undefined && itemRefs.value[index]) {
    const itemElement = itemRefs.value[index];
    const container = scrollContainer.value;
    if (itemElement && container) {
      const itemRect = itemElement.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      if (itemRect.top < containerRect.top) {
        itemElement.scrollIntoView({ block: 'nearest' });
      } else if (itemRect.bottom > containerRect.bottom) {
        itemElement.scrollIntoView({ block: 'nearest' });
      }
    }
  }
};

// 键盘事件
const toUpOrDownFnc = (e: KeyboardEvent) => {
  if (e.key == 'ArrowUp') {
    if (currentIndex.value === undefined || currentIndex.value <= 0) {
      currentIndex.value = astlist.value.length - 1;
    } else {
      currentIndex.value--;
    }
  }
  if (e.key == 'ArrowDown') {
    if (currentIndex.value === undefined || currentIndex.value >= astlist.value.length - 1) {
      currentIndex.value = 0;
    } else {
      currentIndex.value++;
    }
  }
  if (e.key == 'Enter') {
    if (currentIndex.value != undefined) {
      emit('toGoSecItem', astlist.value[currentIndex.value]);
      e.preventDefault();
      e.stopPropagation();
      currentIndex.value = undefined;
    }
  }
  if (e.key == 'Escape') {
    currentIndex.value = undefined;
    emit('toCloseSal');
  }
  scrollIntoViewIfNeeded(currentIndex.value);
};

// 周期
onMounted(() => {
  gettingAsiLst('');
  document.addEventListener('keydown', toUpOrDownFnc);
});
onUnmounted(() => {
  document.removeEventListener('keydown', toUpOrDownFnc);
});
</script>

<style scoped lang="less">
.sec_assist_wrap {
  // width: 150px;
  max-height: 206px;
  padding: 8px;
  border-radius: 12px;
  backdrop-filter: blur(100px);
  background: #ffffff;
  // box-shadow: 0px 6px 14px 0px #5450640a;
  box-shadow: 0px 0px 12px 0px #00000014;
  position: relative;
  left: 0%;
  .sec_assist_shirt {
    height: 100%;
    max-height: 188px;
    overflow-y: auto;
  }
  .sec_assist_shirt::-webkit-scrollbar {
    width: 4px;
  }
  .sec_assist_shirt::-webkit-scrollbar-thumb {
    border-radius: 999px;
    background: #ddd;
    -webkit-box-shadow: inset 0 0 2x rgba(0, 0, 0, 0.5);
  }
  .sec_assist_shirt::-webkit-scrollbar-track {
    -webkit-box-shadow: none;
  }
  .sec_assist_item {
    width: 100%;
    height: 38px;
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 6px;
    :deep(.ant-image-img) {
      border-radius: 50%;
    }
    .sec_tiDec_com {
      flex: 1;
      display: flex;
      height: 100%;
      align-items: center;
      margin-left: 8px;
      overflow-x: hidden;
      .sec_assist_title {
        // max-width: 50%;
        font-family: PingFang SC;
        font-weight: @font-weight-500;
        font-size: 14px;
        line-height: 22px;
        letter-spacing: 0%;
        color: #000000;
      }
      .sec_assist_desc {
        flex: 1;
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: @font-weight-400;
        line-height: 22px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: rgba(0, 0, 0, 0.4);
        margin-left: 8px;
      }
    }
  }
  .sec_assist_item:hover,
  .active_sec_assist_item {
    background-color: #f6f6f8;
    cursor: pointer;
    border-radius: 8px;
  }
  .actived_sec_assist_item {
    background: rgba(237, 242, 252, 1);
    border-radius: 8px;
    .sec_assist_title {
      color: @sky !important;
    }
  }
}
</style>
