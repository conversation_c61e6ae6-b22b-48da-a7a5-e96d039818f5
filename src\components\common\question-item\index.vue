<template>
  <div class="item" @click="handleClick(item)">
    <img :src="questionXing" class="xing" />
    <div class="text" :title="text">{{ text }}</div>
  </div>
</template>
<script lang="ts" setup>
import questionXing from '@/assets/imgs/question-xing.png';
defineComponent({
  name: 'ComponentQuestionItem',
});
const props = defineProps<{
  text: string;
  item: any;
}>();
const emit = defineEmits(['click']);
const handleClick = (item: any) => {
  emit('click', item);
};
</script>
<style lang="less" scoped>
.item {
  display: flex;
  margin-bottom: 16px;
  align-items: center;
  padding: 7px 8px;
  border-radius: 24px;
  border: 1px solid #d1e0ff;
  cursor: pointer;

  &:hover {
    background: #f6f6f8;
    .text {
      color: @sky;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }

  .xing {
    width: 20px;
    height: 20px;
    margin-top: 1px;
  }

  .text {
    color: #000;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: @font-weight-400;
    line-height: 22px;
    margin-left: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: keep-all;
  }
}
</style>
