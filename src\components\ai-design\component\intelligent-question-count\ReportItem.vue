<template>
  <div :class="{
      'content-inner-wrapper-small': isCopilotScreen,
      'content-inner-wrapper': !isCopilotScreen
    }">
    <div :class="{
      'content-item-small': isCopilotScreen,
      'content-item': !isCopilotScreen
    }" class="" v-for="(item, index) in data" :key="index" @click="openReport(item)">
      <div class="title-wrapper flex items-center">
        <div class="title" :title="item.design.title">{{ item.design.title }}</div>
      </div>
      <div class="footer">
        <span>{{ item.design.createMemberName }}</span>
        &nbsp;
        <span>{{ item.design.createTime }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { ReportItemType } from '@/types/portalTypes';

defineOptions({
  name: 'reportItem',
});
const props = defineProps<{
  data: ReportItemType[];
  isCopilotScreen: {
    type: Boolean,
    default: false
  }
}>();

const emit = defineEmits(['openReport']);
const openReport = (item: ReportItemType) => {
  emit('openReport', item);
};
</script>
<style lang="less" scoped>
.ellipis_text() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: keep-all;
}
.content-inner-wrapper {
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  .content-item {
    padding: 12px 0px;
    height: 62px;
    cursor: pointer;
     width: calc(100% / 4 - 10px); 

     &:hover .title {
      color: @sky;
    }
  }
}
@media (max-width: 1440px) {
  .content-inner-wrapper { 
     .content-item {
    width: calc(100% / 3 - 10px); 
  }
  }
}
.content-inner-wrapper-small {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;

  .content-item-small{
    padding: 12px 16px;
    width: 100%;
    cursor: pointer;

    &:hover .title {
      color: @sky;
    }
  }    
}
.title {
  /* 14px/Semibold */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: @font-weight-500;
  line-height: 22px; /* 157.143% */
  .ellipis_text();
}
.footer {
  margin-top: 4px;
  color: rgba(0, 0, 0, 0.6);
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: @font-weight-400;
  line-height: 20px; /* 166.667% */
  .ellipis_text();
}


</style>
