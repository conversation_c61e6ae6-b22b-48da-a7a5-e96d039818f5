<template>
  <div
    :class="['check-button', type === 0 ? 'check-button-0' : 'check-button-1']"
    @click="$emit('handleClick')"
  >
    <span>查看更多</span>
    <i :class="['iconfont', type === 0 ? 'ai-icon-you' : 'ai-icon-xia']" />
  </div>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits } from 'vue';

// 定义组件的 props
const props = defineProps({
  type: {
    required: true,
    type: Number,
    default: 0, // 0:右箭头 1:下箭头
  },
});

// 定义组件的 emits
const emit = defineEmits<{
  (e: 'handleClick'): void;
}>();

</script>


<style lang="less" scoped>
.check-button {
  width: 100%;
  height: 50px;
  // padding: 0 14px;
  border-top: 1px solid  #E4E4E4;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.4);
  .iconfont {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.4);
  }
  &:hover {
    color: #7559F8;
    .iconfont {
      color: #7559F8;
    }
  }
  span {
    height: 22px;
    font-weight: @font-weight-400;
    font-size: 14px;
    line-height: 22px;
  }
}
.check-button-0 {
  justify-content: space-between;
}
.check-button-1 {
  justify-content: center;
  i {
    margin-left: 2px;
  }
}
</style>
