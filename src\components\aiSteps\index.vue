<template>
  <div :class="[isOpen ? 'opened_panel tr_pan' : 'w-full tr_pan']" >
    <Collapse :bordered="false" class="!bg-white" v-if="transtSteps?.length" @change="changePanel">
      <CollapsePanel
        header="运行过程"
        class="!bg-white"
        :style="{
          'border-radius': '4px',
          'margin-bottom': '8px',
          border: 0,
          overflow: 'hidden',
          width: 'fitContent',
          maxWidth:'100%'
        }"
      >
        <Collapse :bordered="false" class="!bg-white">
          <CollapsePanel
            v-for="(step, i) in transtSteps"
            :key="i"
            :header="step.stepName"
            :style="{
              'background-color': '#F5F5F5',
              'border-radius': '4px',
              'margin-bottom': '8px',
              border: 0,
              overflow: 'hidden',
            }"
          >
            <template #extra>
              <div class="text-xs text-gray-500">
                <span>{{ step.totalTime || '-' }} ms </span> |
                <span>{{ step.completionTokens || '-' }} tokens </span>
              </div>
            </template>
            <div class="p-2">
              <vue-json-pretty  showLength showIcon :data="step.content" />
            </div>
          </CollapsePanel>
        </Collapse>
      </CollapsePanel>
    </Collapse>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Collapse, CollapsePanel } from 'ant-design-vue';
import VueJsonPretty from 'vue-json-pretty';
import 'vue-json-pretty/lib/styles.css';

const props = defineProps<{
  transtSteps: any[];
}>();
watch(() => props.transtSteps, (newVal:any) => {
    // console.log('0-------',newVal);
    
})
const isOpen = ref(false);
const changePanel = (key: any) => {
  // console.log('key', key);
  if (key && key.length) {
    isOpen.value = true;
  }else{
    isOpen.value = false;

  }
};
</script>

<style scoped lang="less">
.opened_panel {
  width: 100%;
}
.tr_pan{
  transition: 300ms;
}
</style>
