// 态度选项类型
export type AttitudeOption = {
  showValue: string;      // 显示值
  attitudeKey: string;    // 态度键
  attitudeId: string;     // 态度ID（用于图标映射）
  attitude: string;       // 态度标识
};

// 意见策略类型
export type OpinionPolicy = {
  opinionPolicy: string;           // 处理意见必填 "0"|"1"
  cancelOpinionPolicy: string;     // 撤销/终止意见必填 "0"|"1"
  disAgreeOpinionPolicy: string;   // 不同意意见必填 "0"|"1"
};

// 自定义操作配置类型
export type CustomAction = {
  isOptional: string;       // 是否可选 "0"|"1"
  optionalAction: string;   // 可选操作列表
  defaultAction: string;    // 默认操作
};

// 按钮参数映射类型
export type ButtonParamMap = {
  affairId: string;           // 事务ID
  attitudeKey: string;        // 态度键
  attitude: string;           // 态度值
  customAction: CustomAction; // 自定义操作配置
  paramVO: Record<string, any>; // 业务参数
  content?: string;           // 意见内容
  [key: string]: any;         // 其他参数
};

// 按钮类型定义（基于需求文档）
export type ButtonType = {
  handleType: string;           // 操作类型：ContinueSubmit, Cancel, Terminate等
  name: string;                 // 按钮显示名称
  url: string;                  // 请求URL
  needOpinion: boolean;         // 是否需要意见
  paramMap: ButtonParamMap;     // 参数映射
  attitudeList: AttitudeOption[]; // 态度选择列表
  httpType: string;             // HTTP请求类型
  opinionPolicy: OpinionPolicy; // 意见策略
};

// 确认操作数据类型
export type ConfirmActionData = {
  attitude: AttitudeOption | null;  // 选择的态度（没有态度选择器时为 null）
  content: string;          // 输入的意见内容
  actionType: 'direct' | 'confirm' | 'next-step'; // 操作类型
};

// 常用语类型
export type PhraseItem = {
  content: string;    // 常用语内容
  id?: string;        // 常用语ID
};

// 草稿数据类型
export type DraftData = {
  content: string;    // 草稿内容
  affairId: string;   // 事务ID
};

// 下一步操作选择类型
export type NextStepAction = 'repeal' | 'stepStop' | 'stepBack' | 'continue';

// 渲染信息类型（基于实际数据结构）
export type RenderInfoItemType = {
  content: string;                    // 待办内容
  title: string;                      // 标题
  appId: string;                      // 应用ID
  subAppId: string;                   // 子应用ID
  appName: string;                    // 应用名称
  iconUrl: string;                    // 图标URL
  grade: string;                      // 紧急程度
  gotoParams: string;                 // 跳转参数
  createTime: string;                 // 创建时间
  status: string;                     // 状态
  senderName: string;                 // 发送人姓名
  serverIdentifier: string;           // 服务器标识
  senderMark: string;                 // 发送人标记
  senderFaceUrl: string;              // 发送人头像URL
  affairId: string;                   // 事务ID
  displayValueList: Array<{           // 显示值列表
    value: string;
    display: string;
  }>;
  hasAttachment: boolean;             // 是否有附件
  beBack: boolean;                    // 是否退回
  joinMeetingState: any;              // 会议状态
  remainingTime: string;              // 剩余时间
  readonly: string;                   // 只读状态
  receiveTime: string;                // 接收时间
  completeTime: string;               // 完成时间
  meetingImpart: string;              // 会议
  handleParam: Record<string, any>;   // 处理参数
  extParam: Record<string, any>;      // 扩展参数
  edocMark: string;                   // 电子文档标记
  thirdApp: boolean;                  // 第三方应用
    rowButtons?: ButtonType[];          // 操作按钮
  top: boolean;                       // 是否置顶
  grab: boolean;                      // 是否抢占

  // 状态标签相关字段
  affairSubStatus?: number | string;  // 事务子状态(31:参加, 33:待定, 7:回退)

  // 兼容旧数据结构的字段
  companyName?: string;
  createDate?: string;
  endDate?: number;
  jumpUrl?: string;
  templateId?: string;
  type?: string;
  typeName?: string;
  userName?: string;
  userPhoto?: string;
  businessTypeName?: string;
  categoryLabel?: string;
};

