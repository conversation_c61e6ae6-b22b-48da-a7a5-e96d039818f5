<template>
  <div :class="{'portal-layout':true,'coplit-silder': !isPortal}">
    <div class="portal-layout-header">
      <h3> <span class="gradient-text">智·协同&nbsp;&nbsp;效·非凡!</span></h3>
      <img :src="isPortal?ComiWelcomeBg:ComiPrologBg" alt="" class="portal-logo">
    </div>
    <img src="@/assets/imgs/portal-bg.png" alt="" class="portal-bg">
  </div>
</template>
<script setup lang="ts">
  import { inject } from 'vue';
  import ComiPrologBg from '@/assets/imgs/comi_prolog_bg.gif';
  import ComiWelcomeBg from '@/assets/imgs/welcome-min.gif';
  const isPortal = inject('isPortal');
</script>
<style scoped lang="less">
.portal-layout{
  width: 100%;
  height: 113px;
  background: linear-gradient(90deg, #D1DFFF 0%, #CDE9FF 27.88%, #EAECFF 58.17%, #E9EDFD 96.63%);
  border-radius: 12px;
  padding-bottom: 48px;
  margin-top: 20px;
  position: relative;
  z-index: 1;
  .portal-bg{
    width: 225px;
    height: 65px;
    position: absolute;
    top: 80px;
    left: calc(66.7% - 112.5px);
  }
  .portal-layout-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 68px;
    padding: 0 26px;
    h3{
      font-family: PingFang SC;
      font-weight: @font-weight-500;
      font-size: 20px;
      line-height: 16px;
      letter-spacing: 0px;
      margin-bottom: 0;
      color: rgba(23, 29, 38, 0.8);
      span{
        font-family: PingFang SC;
        font-weight: @font-weight-500;
        font-size: 20px;
        line-height: 16px;
        letter-spacing: 0px;
      }
      .gradient-text{
        width: fit-content;
        /* 1. 设置背景为从左到右的渐变 */
        background: linear-gradient(158deg, #4AEAFF -10.62%, #97F0FA -6.23%, #4FE1F7 -1.4%, #566BFF 35.5%, #4379FF 80.57%);

        /* 2. 将背景裁剪为文字形状 (需要 Webkit 前缀兼容) */
        -webkit-background-clip: text;
        background-clip: text;

        /* 3. 使文字本身透明，显示背景渐变 */
        color: transparent;

        /* 4. (可选) 确保元素是 inline-block 或 block 以便背景正确应用 */
        /* display: inline-block; */
      }
    }
    .portal-logo{
      width: 88px;
      height: 88px;
    }
  }
  .portal-layout-carousel {
    width: 100%;
    min-height: 200px;
  }
}
.coplit-silder{
  height: 86px;
  padding-bottom: 31px;
  margin-top: 0;
  .portal-layout-header{
    height: 55px;
    padding: 0 20px;
    img{
      width: 84px;
      height: 84px;
      position: relative;
      bottom: -3px;
    }
  }
}
</style>

