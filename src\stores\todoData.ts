import { defineStore } from 'pinia'

interface todoData {
    searchParams: {
        pageNumber: number
        pageSize: number
        portletParams: string
    },
    dataInx: number,
    dataList: any

}

// 代办数据状态
export const useTodo = defineStore('todoData', () => {

    const todoState: todoData = reactive({
        searchParams: {
            pageNumber: 1,
            pageSize: 1,
            portletParams: ''
        },
        dataInx: 0,
        dataList: []
    })

    const changeState = (key: keyof todoData, val: any) => {
        todoState[key] = val
    }

    const cleanState = () => {
        todoState.dataInx = 0
        todoState.dataList = [];
        changeState('searchParams', { pageNumber: 1, pageSize: 1, portletParams: '' })
    }

    return {
        todoState,
        changeState,
        cleanState
    }

});
