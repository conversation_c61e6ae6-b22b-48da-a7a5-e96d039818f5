(function () {
  // var COMI_UTIL = window.top.COMI_UTIL;
  var getTopWindow = function () {
    return window.top?.vPortal ? window.top : window;
  }
  function v5JsloadScript(url) {
    return new Promise((resolve, reject) => {
      const allScript = document.getElementsByTagName('script');
      for (let i = 0; i < allScript.length; i++) {
        if (allScript[i].src.indexOf(url) !== -1) {
          resolve('loaded'); // 已存在则直接返回
          return;
        }
      }

      const script = document.createElement('script');
      script.src = url;
      script.onload = () => resolve('loaded');
      script.onerror = () => reject(new Error(`加载脚本失败: ${url}`));
      document.body.appendChild(script);
    });
  }


  var _ctxPath = getTopWindow()._ctxPath || '/seeyon';
  v5JsloadScript(_ctxPath + '/ajaxStub.js')

  var Ajax = {
    get: function get(url) {
      return new Promise(function (resolve, reject) {
        var xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);

        xhr.onreadystatechange = function () {
          if (xhr.readyState == 4) {
            if (xhr.status == 200 || xhr.status == 304) {
              try {
                var res = xhr.responseText ? JSON.parse(xhr.responseText) : {};
                resolve(res);
              } catch (e) {
                reject(e);
              }
            } else {
              reject(new Error('Request failed with status ' + xhr.status));
            }
          }
        };

        xhr.send();
      });
    },
    post: function post(url, data) {
      return new Promise(function (resolve, reject) {
        if (!data) return reject(new Error('No data provided'));

        if (typeof (data) == 'object') {
          data = JSON.stringify(data);
        }

        var xhr = new XMLHttpRequest();
        xhr.open("POST", url, true);

        xhr.setRequestHeader("Content-Type", "application/json;charset=utf-8");

        xhr.onreadystatechange = function () {
          if (xhr.readyState == 4) {
            if (xhr.status == 200 || xhr.status == 304) {
              try {
                var res = xhr.responseText ? JSON.parse(xhr.responseText) : {};
                resolve(res);
              } catch (e) {
                reject(e);
              }
            } else {
              reject(new Error('Request failed with status ' + xhr.status));
            }
          }
        };

        xhr.send(data);
      });
    }
  };

  var V5API = {

    // V9.X 1130任务项新增
    /**
     * @description 根据名称查询应用菜单
     * @param {Object} params 
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     * @param {String} params.name
     */

    getAppDataList: function (params) {
      return Ajax.post(_ctxPath + `/rest/seeyon-ai/colFusion/search/appData?pageNo=${params.pageNo}&pageSize=${params.pageSize}&option.n_a_s=1`, params);
    },
    /**
     * @description 根据文档名称进行查询
     * @param {Object} params 
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     * @param {String} params.name
     */
    getDocDataList: function (params) {
      return Ajax.post(_ctxPath + `/rest/seeyon-ai/colFusion/search/doc?pageNo=${params.pageNo}&pageSize=${params.pageSize}&option.n_a_s=1`, params);
    },
    /**
     * @description 根据名称查询审批模板
     * @param {Object} params
     * @param {String} params.templateName
     * @param {String} params.categoryIdList //分类，默认查公共模板就传-1
     * @param {String} params.showRecent //是否显示最近使用
     * @param {String} params.fullMatch 是否完全匹配
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     */
    getTemplateTable: function (params) {
      return Ajax.post(_ctxPath + `/rest/template/myTemplate/table?option.n_a_s=1`, params);
    },

    /**
     * @description 根据名称查询人员列表,带分页
     * @param {Object} params 
     * @param {String} params.name
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     */
    getMemberList: function (params) {
      return Ajax.post(_ctxPath + `/rest/seeyon-comi/colFusion/search/member?pageNo=${params.pageNo}&pageSize=${params.pageSize}&option.n_a_s=1`, params);
    },
    /**
     * @description 根据名称获取新闻列表,带分页
     * @param {Object} params 
     * @param {String} params.name
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     * @returns 
     */
    getNewsList: function (params) {
      return Ajax.post(_ctxPath + `/rest/seeyon-ai/colFusion/search/news?pageNo=${params.pageNo}&pageSize=${params.pageSize}&option.n_a_s=1`, params);
    },

    /**
     * @description 根据名称获取公告列表,带分页
     * @param {Object} params 
     * @param {String} params.name
     * @param {String} params.pageNo
     * @param {String} params.pageSize
     * @returns 
     */
    getBulletinList: function (params) {
      return Ajax.post(_ctxPath + `/rest/seeyon-ai/colFusion/search/bulletin?pageNo=${params.pageNo}&pageSize=${params.pageSize}&option.n_a_s=1`, params);
    },
    /**
     * @description 文档中心 检查文档是否支持查看
     * @param {Object} params
     * @param {String} params.docId 文档ID
     * @param {String} params.entrance 入口类型
     * @param {String} params.userId 用户ID 非必填
     * @param {String} params.baseDocId  非必填
     * @returns 
     */
    docValidInfo: function (params) {
      return Ajax.post(_ctxPath + `/rest/doc/validInfo?option.n_a_s=1`, params);
    },
    /**
     * 消息标记全部已读
    */
    markAllRead: function () {
      return Ajax.post(_ctxPath + `/rest/seeyon-ai/comi/message/update-all?option.n_a_s=1`, {});
    }
  }

  // 事件处理集合
  var collectionHandler = function (keyword, apiKey) {
    var params = {
      name: keyword,
      pageNo: 1,
      pageSize: 5,
    };
    if (apiKey == 'getTemplateTable') {
      params = {
        templateName: keyword,
        categoryIdList: [-1],
        showRecent: false,
        fullMatch: true,
        page: 1,
        size: 5,
      };
    }
    return V5API[apiKey](params).then(function (result) {
      var list = result.data.data;
      var total = result.data.total;
      if (list && list.length) {
        if (total == 1) {
          return list[0];
        } else if (apiKey == 'getTemplateTable' && list.length == 0) {
          return 'NoPermission';
        } else {
          return list; // 返回整个列表
        }
      }
      return false;
    }).catch(function (error) {
      console.error("Error in collectionHandler:", error);
      throw error;
    });
  };

  // 获取门户空间url地址
  function getPortalUrl(_portalType, linkId) {
    if (_portalType == "portal") { // 门户
      return "/main.do?method=main&portalId=" + linkId + "&subPortal=true";
    } else if (_portalType == "space") { // 空间
      var _url = linkId;
      // 打开新门户
      var requestCaller = new XMLHttpRequestCaller(this, "portalManager", "getSpaceBelongedPortalId", false);
      requestCaller.addParameter(1, "Long", _url);
      var subPortalId = requestCaller.serviceRequest();
      if (subPortalId) {
        return "/portal/spaceController.do?method=showThemSpace&spaceId=" + _url
      }
    }
  }

  // 暂时无用
  function getMenuUrl(_openType, _menuUrl) {
    if (_openType == 'workspace') {
      return _menuUrl.indexOf("http") == 0 || _menuUrl.indexOf("ftp") == 0 ? _menuUrl : _menuUrl;
    } else if (_openType == 'open') {
      return _menuUrl.indexOf("http") == 0 || _menuUrl.indexOf("ftp") == 0 ? _menuUrl : _menuUrl;
    }
  }
  function openLeaderagenda(id) {
    var ajaxManager = new leaderAgendaManager();
    var data = ajaxManager.getAgendaDetailsById({
      agendaId: id
    });
    v5JsloadScript(_ctxPath + '/apps_res/leaderagenda/js/agenda_api_debug.js').then(function (res) {
      if (res == 'loaded') {
        agendaAPI.showAgendaWindows(data, true)
      }
    })
  }

  // 打开任务
  function openOATask(id, actionAfterClose, category) {
    if (typeof taskinfoDetailDialog !== "undefined") {
      new taskinfoDetailDialog({
        taskId: id,
        openB: true,
        animate: false,
        onUpdate: actionAfterClose
      })
    } else {
      $.ajax({
        url: _ctxPath + "/apps_res/taskmanage/js/detail/taskDetailDialog-debug.js",
        dataType: "script",
        success: function () {
          new taskinfoDetailDialog({
            taskId: id,
            openB: true,
            animate: false,
            onUpdate: actionAfterClose
          })
        }
      })
    }
  }
  
  // 获取oaUrl地址
  var getOaUrl = function (params) {
    var _params = dealParams(params);
    var _appType = _params._appType;
    var _linkId = _params._linkId;
    var clientType = _params.clientType;
    var linkId = _params.linkId;
    var _openUrl = _params._openUrl;
    var _isMobile = _params._isMobile;
    var _portalType = _params._portalType;

    var url = ''
    var imailSubAppIds = "141, 142, 143, 144";
    var linkIdArr = _linkId.split('|');
    if (_appType == '99') {
      // 应用菜单
      url = _openUrl;
    } else if (_appType == '83') {
      // cap4业务空间
      url = "/cap4/bizportal.do?method=viewBizPortalSpace&platform=1&spaceId=" + _linkId
    } else if (_appType == '65') {
      // 门户空间
      if (clientType == '2') {
        window.alert('不支持')
      } else {
        url = getPortalUrl(_portalType, _linkId)
      }
    } else if (_appType == '101') {
      // 流程模板
      url = _openUrl
    } else if (_appType == '1') {
      // 协同
      url = '/collaboration/collaboration.do?method=summary&openFrom=listPending&affairId=' + _linkId + '&showTab=false';
    } else if (_appType == '2') {
      // cap3无流程表单
      url = '/form/formData.do?method=viewUnflowFormData&moduleId=' + linkIdArr[0] + '&moduleType=' + linkIdArr[1] + '&rightId=' + linkIdArr[2]
    } else if (_appType == '66') {
      // cap4无流程表单
      url = '/cap4/businessTemplateController.do?method=formContent&type=browse&moduleId=' + linkIdArr[0] + '&moduleType=' + linkIdArr[1] + '&rightId=' + linkIdArr[2]
    } else if (_appType == '4') {
      // 公文
      url = '/edocController.do?method=detailIFrame&from=Done&affairId=' + _linkId;
    } else if (_appType == '70') {
      // 报表
      if (clientType == '2') {
        window.alert('不支持');
      } else {
        url = '/report4Result.do?method=showResult&designId=' + linkId;
      }
    } else if (_appType == '3') {
      // 文档
      url = '/doc.do?method=docOpenIframeOnlyId&docResId=' + _linkId;
    } else if (_appType == '8') {
      // 新闻
      url = '/newsData.do?method=newsView&newsId=' + _linkId;
    } else if (_appType == '7') {
      // 公告
      url = '/bulData.do?method=bulView&bulId=' + _linkId;
    } else if (_appType == '9') {
      // 讨论
      url = "/bbs.do?method=bbsView&articleId=" + _linkId + "&spaceType=&spaceId="
    } else if (_appType == '10') {
      // 调查
      url = '/inquiryData.do?method=inquiryView&inquiryId=' + _linkId;
    } else if (_appType == '94') {
      // 领导行程
      openLeaderagenda(_linkId)
    } else if (_appType == '6') {
      // 会议
      url = '/meeting.do?method=view&meetingId=' + linkId;
    } else if (_appType == '30') {
      // 任务
      openOATask(_linkId)
    } else if (_appType == '11') {
      // 事件
      // url = "/calendar/calEvent.do?method=editCalEvent&id=" + linkId
      var vPortalUserId = topWindow.vPortal && topWindow.vPortal.CurrentUser.id;
      var envUserId = topWindow._currentUser && topWindow._currentUser.id;
      var otherUserId = topWindow.$ && topWindow.$.ctx && topWindow.$.ctx.CurrentUser && topWindow.$.ctx.CurrentUser.id;
      var userId = vPortalUserId || envUserId || otherUserId;
      var ajaxTestBean = new calEventManager();
      var isOnePerson = ajaxTestBean.isOnePerson(_linkId);
      var isReceiveMember = ajaxTestBean.isReceiveMember(userId, _linkId);
      var res = ajaxTestBean.isHasDeleteByType(_linkId, "event");
      if (res != null && res != "") {
        $.alert({
          msg: res,
          ok_fn: function () { }
        })
      } else {
        var options = {
          id: _linkId
        };
        var bottomHTML = "<span id='btn_wakeZX' style='display: none;' class='common_button common_button_icon common_button_gray' ";
        bottomHTML += "title=' " + $.i18n("calendar.label.zhixinCommunication") + "'><em class='syIcon sy-m3-zhixin-fill'></em>";
        bottomHTML += $.i18n("calendar.label.zhixinCommunication") + "</span>";
        var dialogCalEventUpdate = $.dialog({
          id: "calEventUpdate",
          url: _ctxPath + "/calendar/calEvent.do?method=editCalEvent&id=" + options.id + CsrfGuard.getUrlSurffix(),
          width: 600,
          height: 550,
          checkMax: true,
          targetWindow: getCtpTop(),
          title: $.i18n("calendar.event.search.title"),
          bottomHTML: bottomHTML,
          transParams: options.transParams || {},
          buttons: [{
            id: "sure",
            isEmphasize: true,
            text: $.i18n("common.button.ok.label"),
            handler: function () {
              dialogCalEventUpdate.getReturnValue({
                type: "updataEventOk",
                dialog: dialogCalEventUpdate,
                okCallback: function () {
                  dialogCalEventUpdate.close()
                }
              })
            }
          }, {
            id: "update",
            text: $.i18n("common.toolbar.update.label"),
            handler: function () {
              dialogCalEventUpdate.getReturnValue({
                type: "viewToUpdate",
                dialog: dialogCalEventUpdate
              })
            }
          }, {
            id: "cancel",
            text: $.i18n("common.button.cancel.label"),
            handler: function () {
              dialogCalEventUpdate.close()
            }
          }, {
            id: "btnClose",
            text: $.i18n("common.button.close.label"),
            handler: function () {
              dialogCalEventUpdate.close()
            }
          }]
        });
        dialogCalEventUpdate.hideBtn("sure");
        dialogCalEventUpdate.hideBtn("btnClose");
        dialogCalEventUpdate.hideBtn("update");
        dialogCalEventUpdate.hideBtn("cancel");
        if (isOnePerson == "no" && !isReceiveMember) {
          dialogCalEventUpdate.showBtn("btnClose")
        } else {
          dialogCalEventUpdate.showBtn("update");
          dialogCalEventUpdate.showBtn("cancel")
        }
      }
    } else if (_appType == '5') {
      // 计划
      url = "/plan/plan.do?method=initPlanDetailFrame&planId=" + linkId + "&_isModalDialog=true"
    } else if (_appType == '39') {
      // 第三方待办
      if (_isMobile == 'true') {
        $.alert($.i18n('cip.index.search.tip'));
        return;
      }
      url = _openUrl
    } else if (imailSubAppIds.indexOf(_appType) != -1) {
      // 协同邮箱
      var boxType = 1;
      var mailType = '';
      // 内部邮箱
      if (_appType == '141') {
        // 收件箱
        mailType = 1;
      } else if (_appType == '142') {
        // 发件箱
        boxType = 2;
        mailType = 2;
      } else if (_appType == '143') {
        // 草稿箱
        boxType = 3;
      } else if (_appType == '144') {
        // 垃圾箱
        boxType = 4;
      }
      url = "/common/internalmail/index.html?mailId=" + linkId + "&showModel=all&renderType=7&boxType=" + boxType + "&origin=msg&showTab=true&mailType=" + mailType;
    }
    if (url) {
      if (url.indexOf("http") == 0 || url.indexOf("ftp") == 0) {
        return url;
      }
      var real = _ctxPath + url;
      return real;
    }

  };

  // 处理参数
  var dealParams = function (params) {
    var appType = params.appType;
    var linkId = params.linkId;
    var clientType = params.clientType;
    var topWindow = getTopWindow();
    var XMLHttpRequestCaller = topWindow.XMLHttpRequestCaller;

    var _appType = appType;
    var _linkId = linkId;
    var _portalType = '';
    var _openType = ''; // 打开方式
    var _openUrl = ''; // 打开url
    var _isMobile = '';

    if (
      appType == '99' ||
      appType == '65' ||
      appType == '101' ||
      appType == '1' ||
      appType == '2' ||
      appType == '4' ||
      appType == '39'
    ) {
      var requestCaller = new XMLHttpRequestCaller(
        this,
        'ajaxIndexController',
        'getIndexParams',
        false
      );
      requestCaller.addParameter(1, 'String', appType);
      requestCaller.addParameter(2, 'String', linkId);
      var ds = requestCaller.serviceRequest();
      if (ds) {
        _appType = ds.get('appType');
        _linkId = ds.get('linkId');
        if (appType == '99' || appType == '101' || _appType == '39') {
          _openType = ds.get('openType');
          _openUrl = ds.get('openUrl');
          _isMobile = ds.get('isMobile');
        } else if (appType == '65') {
          _portalType = ds.get('type');
          var realSpaceId = ds.get('realSpaceId');
          if (realSpaceId) {
            _linkId = realSpaceId;
          }
        }
      }
    }
    return {
      _appType: _appType,
      _linkId: _linkId,
      _portalType: _portalType,
      _openType: _openType,
      _openUrl: _openUrl,
      _isMobile: _isMobile,
      clientType: clientType,
      linkId: linkId
    }

  };

  var COMI_UTIL = {
    // md标签打开
    /**
     * 
     * @param {*} data md数据
     * @param {*} callback 回调函数
     * @returns 
     */
    handleTagClick: function (data, callback) {
      console.log(1)
      if (!V5API) return;
      var topWindow = getTopWindow();
      var $ = topWindow.$;
      var openCtpWindow = topWindow.openCtpWindow;
      var _ctxPath = topWindow._ctxPath;
      var getCtpTop = topWindow.getCtpTop;

      // 这里可以根据data内容执行不同的业务逻辑
      if (data) {
        var matched_word = data.matched_word;
        var type = data.type;
        var keyword = matched_word;
        var isMultipleTabs = getCtpTop().isMultipleTabs;
        try {
          var keyData = typeof keyword === 'string' && JSON.parse(keyword) || keyword;
        } catch (e) {
          keyData = keyword || {};
          // console.log(e);
        }
        switch (type) {
          case 'link':
            // 检查链接是否以 "http://" 或 "https://" 开头
            if (!/^https?:\/\//i.test(keyword)) {
              // 如果不是，则在前面添加 "http://"
              keyword = 'http://' + keyword;
            }
            window.open(keyword);
            return;
          case 'member':
            collectionHandler(keyword, 'getMemberList').then(function (memberResult) {
              if (Array.isArray(memberResult)) {
                if (callback) callback({
                  type: 'member',
                  data: memberResult,
                }); // 多条数据的情况
              } else if (memberResult) {
                COMI_UTIL.openPersonalInfo(memberResult);
              }
            }).catch(function (error) {
              console.error("Error fetching member data:", error);
            });
            return;
          case 'doc':
            collectionHandler(keyword, 'getDocDataList').then(function (fileResult) {
              if (Array.isArray(fileResult)) {
                if (callback) callback({
                  type: 'doc',
                  data: fileResult,
                }); // 多条数据的情况
              } else if (fileResult) {
                // 先校验是否支持查看
                V5API.docValidInfo({
                  docId: fileResult.id,
                  entrance: fileResult.entrance,
                }).then(function (ret) {
                  var isExist = ret.data.charAt(0);
                  if (isExist === '4') {
                    $.alert($.i18n('doc.prompt.docLib.disabled'));
                    return;
                  } else if (isExist !== '0') {
                    $.alert($.i18n('doc.prompt.inexistence'));
                    return;
                  }
                  var hasOP = ret.data.charAt(1);
                  if (hasOP != 0) {
                    $.alert($.i18n('doc.prompt.noright'));
                    return;
                  }
                  var url = _ctxPath + ret.data.substring(2);
                  openCtpWindow({
                    url: url,
                  });
                }).catch(function (error) {
                  console.error("Error validating document:", error);
                });
              }
            }).catch(function (error) {
              console.error("Error fetching document data:", error);
            });
            return;
          case 'Menu':
            collectionHandler(keyword, 'getAppDataList').then(function (appResult) {
              if (Array.isArray(appResult)) {
                if (callback) callback({
                  type: 'Menu',
                  data: appResult,
                }); // 多条数据的情况
              } else if (appResult) {
                var url = appResult.url;
                openCtpWindow({
                  url: getCtpTop()._ctxPath + url,
                });
              }
            }).catch(function (error) {
              console.error("Error fetching app data:", error);
            });
            return;
          case 'news':
            collectionHandler(keyword, 'getNewsList').then(function (cultureResult) {
              if (Array.isArray(cultureResult)) {
                if (callback) callback({
                  type: 'news',
                  data: cultureResult,
                }); // 多条数据的情况
              } else if (cultureResult) {
                var culturePath = "/newsData.do?method=newsView&newsId=" + cultureResult.id;
                openCtpWindow({
                  url: getCtpTop()._ctxPath + culturePath,
                });
              }
            }).catch(function (error) {
              console.error("Error fetching news data:", error);
            });
            return;
          case 'announcements':
            collectionHandler(keyword, 'getBulletinList').then(function (announcementsResult) {
              if (Array.isArray(announcementsResult)) {
                if (callback) callback({
                  type: 'announcements',
                  data: announcementsResult,
                }); // 多条数据的情况
              } else if (announcementsResult) {
                var announcementsPath = "/bulData.do?method=bulView&bulId=" + announcementsResult.id;
                openCtpWindow({
                  url: getCtpTop()._ctxPath + announcementsPath,
                });
              }
            }).catch(function (error) {
              console.error("Error fetching announcements data:", error);
            });
            return;
          // 协同
          case 'Template':
            collectionHandler(keyword, 'getTemplateTable').then(function (templateResult) {
              if (Array.isArray(templateResult)) {
                if (callback) callback({
                  type: 'Template',
                  data: templateResult,
                }); // 多条数据的情况
              } else if (templateResult == 'NoPermission') {
                // 无权限的话，则直接提示一个无权限 后端要求
                $.alert($.i18n('无权限访问'));
                return;
              }
              if (templateResult) {
                var templatePath = "/collaboration/collaboration.do?method=newColl&showTab=true&fromNewItem=true&templateId=" + templateResult.id + "&bodyType=" + templateResult.bodyType + "&theme=simple&designTplId=67071001";
                openCtpWindow({
                  url: getCtpTop()._ctxPath + templatePath,
                });
              }
            }).catch(function (error) {
              console.error("Error fetching template data:", error);
            });
            return;
          case 'Cooperate': // 自由协同的话
            var url = "/app/collaboration/index.html?method=newColl&showTab=true&fromNewItem=true&rescode=F01_newColl&bodyType=10&theme=simple&designTplId=1001102";
            openCtpWindow({
              url: getCtpTop()._ctxPath + url,
            });
            return;
          // TODO 协同相关事件处理
          // case 'collaboration':
          //   collaborationUtils.mdEventHandler(data);
          //   break;
          // 发起协同
          case 'newCollaboration':
            collaborationUtils.handleInfo(data);
            break;
          case 'menuLink':
            getCtpTop().showMenu(keyData.url, keyData.id, keyData.target, undefined, undefined, keyData.name);
            isMultipleTabs && getCtpTop().COMI_ASSISTANT.sdk.menuAction({
              action: 'collapse'
            });
            break;
          case 'openWin':
            var showTab = data.other_data && data.other_data.showTab;
            if (keyword && getCtpTop().COMI_ASSISTANT && getCtpTop().COMI_ASSISTANT.sdk) {
              getCtpTop().openCtpWindow({ url: keyword });
              isMultipleTabs && (showTab === undefined || showTab) && getCtpTop().COMI_ASSISTANT.sdk.menuAction({
                action: 'collapse'
              });
            }
            break;
          // 协同批量处理
          case 'batchDealCol':
            if (keyData && getCtpTop().COMI_ASSISTANT && getCtpTop().COMI_ASSISTANT.sdk) {
              getCtpTop().COMI_ASSISTANT.sdk.sendMsg('帮我批量处理' + keyData.title);
            }
            break;
          // 协同处理单条
          case 'dealCol':
            if (keyData && getCtpTop().COMI_ASSISTANT && getCtpTop().COMI_ASSISTANT.sdk) {
              getCtpTop().COMI_ASSISTANT.sdk.sendMsg('帮我处理' + keyData.title);
            }
            break;
          // 协同查看详情
          case 'viewSummary':
            if (getCtpTop().COMI_ASSISTANT && getCtpTop().COMI_ASSISTANT.sdk) {
              getCtpTop().openCtpWindow({ url: _ctxPath + '/collaboration/collaboration.do?method=summary&openFrom=listPending&affairId=' + ((data && data.id) || (keyData && keyData.affairId)) + '&showTab=true' });
              isMultipleTabs && getCtpTop().COMI_ASSISTANT.sdk.menuAction({
                action: 'collapse'
              });
            }
            break;
          // 协同查看更多
          case 'collaborationListMore':
            var stateTypeMap = {
              1: 'listWaitSend',
              2: 'listSent',
              3: 'listPending',
              4: 'listDone'
            }
            if (keyData && keyData.state && getCtpTop().COMI_ASSISTANT && getCtpTop().COMI_ASSISTANT.sdk) {
              var methodType = stateTypeMap[keyData.state] || '';
              getCtpTop().openCtpWindow({ url: _ctxPath + '/collaboration/collaboration.do?method=' + methodType + '&showTab=true' });
              isMultipleTabs && getCtpTop().COMI_ASSISTANT.sdk.menuAction({
                action: 'collapse'
              });
            }
            break;
          // 协同不显示摘要
          case 'notDisplayAbstract':
            collaborationUtils.mdEventHandler(data);
            break;
          // 选择人员
          case 'selectMember':
            if (keyData && keyData.title && getCtpTop().COMI_ASSISTANT && getCtpTop().COMI_ASSISTANT.sdk) {
              getCtpTop().COMI_ASSISTANT.sdk.sendMsg('帮我选择' + keyData.title);
            }
            break;
          // 选择，并将信息自动填充到输入框
          case 'select':
            if (keyData && keyData.title && getCtpTop().COMI_ASSISTANT && getCtpTop().COMI_ASSISTANT.sdk) {
              getCtpTop().COMI_ASSISTANT.sdk.setSendMsg(keyData.title + '、', { isAdd: true });
            }
            break;
          case 'confirm':
            if (getCtpTop().COMI_ASSISTANT && getCtpTop().COMI_ASSISTANT.sdk) {
              getCtpTop().COMI_ASSISTANT.sdk.sendMsg('确定');
            }
            break;

          case 'ajax':
            // 处理ajax请求
            if (keyword == 'message') {
              // 消息标记全部已读
              V5API.markAllRead().then(function (ret) {
                console.log("标记消息已读接口返回", ret)
              })
            }
            break;
          default:
            break;
        }
      } else {
        console.log('您捕获到了目标节点，但是没有数据');
      }
    },
    // 知识源打开
    /**
     * 
     * @param {*} appType 
     * @param {*} linkId 
     * @param {*} clientType 
     * @returns 
     */
    openWin: function (params) {

      // 门户方式打开
      if (window.top.vPortal) {
        var _params = dealParams(params);
        var _appType = _params._appType;
        var _linkId = _params._linkId;
        var clientType = _params.clientType;
        var linkId = _params.linkId;
        var _portalType = _params._portalType;
        var _openType = _params._openType;
        var _openUrl = _params._openUrl;
        var _isMobile = _params._isMobile;

        var getCtpTop = topWindow.getCtpTop;
        var openIndex4Menu = topWindow.openIndex4Menu;
        var openIndex4Portal = topWindow.openIndex4Portal;
        var openCtpWindow = topWindow.openCtpWindow;
        var $ = topWindow.$;

        // hack：解决portal-min.js中的openDocument方法中可能没有messageLinkConstants引起的报错问题
        if (window.messageLinkConstants && !getCtpTop().messageLinkConstants) {
          getCtpTop().messageLinkConstants = window.messageLinkConstants;
        }
        var openDocument = getCtpTop().openDocument || window.openDocument;

        var _title = $('#' + linkId + '_title').text();
        if (_appType == '99') {
          // 应用菜单
          openIndex4Menu(_openType, _openUrl);
        } else if (_appType == '83') {
          // cap4业务空间
          openIndex4Menu(
            'workspace',
            '/cap4/bizportal.do?method=viewBizPortalSpace&platform=1&spaceId=' + _linkId
          );
        } else if (_appType == '65') {
          // 门户空间
          if (clientType == '2') {
            getCtpTop().$.alert(getCtpTop().$.i18n('index.application.portal.msg1', _title));
          } else {
            openIndex4Portal(_portalType, _linkId);
          }
        } else if (_appType == '101') {
          // 流程模板
          openCtpWindow({
            url: getCtpTop()._ctxPath + _openUrl,
          });
        } else if (_appType == '1') {
          // 协同
          openDocument('message.link.col.pending|' + _linkId, 0);
        } else if (_appType == '2') {
          // cap3无流程表单
          openDocument('message.link.form.unFlow.view|' + _linkId, 0);
        } else if (_appType == '66') {
          // cap4无流程表单
          openDocument('message.link.formtrigger.cap4.msg.unflow|' + _linkId, 0);
        } else if (_appType == '4') {
          // 公文
          openDocument('message.link.edoc.done|' + _linkId, 0);
        } else if (_appType == '70') {
          // 报表
          if (clientType == '2') {
            getCtpTop().$.alert(getCtpTop().$.i18n('index.application.vreport.msg1', _title));
          } else {
            openCtpWindow({
              url: getCtpTop()._ctxPath + '/vreport/vReport.do?method=showReport&id=' + _linkId,
            });
          }
        } else if (_appType == '3') {
          // 文档
          openDocument('message.link.doc.open.index|' + _linkId, 0);
        } else if (_appType == '8') {
          // 新闻
          openDocument('message.link.news.open|' + _linkId, 0);
        } else if (_appType == '7') {
          // 公告
          openDocument('message.link.bulletin.open|' + _linkId, 0);
        } else if (_appType == '9') {
          // 讨论
          openDocument('message.link.bbs.open|' + _linkId, 0);
        } else if (_appType == '10') {
          // 调查
          openDocument('message.link.inquiry.send|' + _linkId, 0);
        } else if (_appType == '94') {
          // 领导行程
          openLeaderagenda(_linkId)
        } else if (_appType == '6') {
          // 会议
          openDocument('message.link.mt.send|' + _linkId, 0);
        } else if (_appType == '30') {
          // 任务
          openDocument('message.link.taskmanage.view|' + _linkId, 0);
        } else if (_appType == '11') {
          // 事件
          openDocument('message.link.cal.view|' + _linkId, 0);
        } else if (_appType == '5') {
          // 计划
          openDocument('message.link.plan.summary|' + _linkId, 0);
        } else if (_appType == '39') {
          // 第三方待办
          if (_isMobile == 'true') {
            $.alert($.i18n('cip.index.search.tip'));
            return;
          }
          openCtpWindow({
            url: _openUrl,
          });
        }
      } else {
        var oaUrl = getOaUrl(params)
        if (oaUrl) {
          window.open(oaUrl, '_blank');
        }
      }


    },
    openPersonalInfo: function (memberResult) {
      var $ = getTopWindow().$;
      if (memberResult) {
        var dialog = $.dialog({
          url: _ctxPath + "/organization/peopleCard.do?method=showPeoPleCard&type=withbutton&memberId=" + memberResult.id,
          id: 'pc_new_item',
          footer: null,
          bodyStyle: 'padding: 2px 0px;',
          isHead: false,
          width: 440,
          height: 490,
          closable: false,
          isClear: false,
          transParams: {
            parentWin: {
              newPeopleCardDialog: {
                close: function () {
                  dialog.close();
                },
              },
            },
          },
          onCancel: function () {
            dialog.close();
          },
        });
      }

    }
  };
  // 协同md标签消息处理
  var collaborationUtils = {
    messageListenerBind: false,
    childWindows: {},
    // TODO 事件分发处理
    mdEventHandler: function (data) {
      // var keyword = data.matched_word;
      // try {
      //   keyData = JSON.parse(keyword);
      // } catch (e) {
      //   console.log(e);
      // }
      var type = data.type;
      // type && delete keyData.type;
      switch (type) {
        // 摘要显示状态设置
        case 'notDisplayAbstract':
          this.setAbstractState(false);
          break;
        default:
          break;
      }
    },
    // 设置摘要显示状态
    setAbstractState: function (state) {
      // 用户id(兼容后期comi挂载不同页面)
      var topWindow = getCtpTop();
      var vPortalUserId = topWindow.vPortal && topWindow.vPortal.CurrentUser.id;
      var envUserId = topWindow._currentUser && topWindow._currentUser.id;
      var otherUserId = topWindow.$ && topWindow.$.ctx && topWindow.$.ctx.CurrentUser && topWindow.$.ctx.CurrentUser.id;
      var userId = vPortalUserId || envUserId || otherUserId;
      // 按登录用户记录状态
      if (userId) {
        topWindow.localStorage.setItem("comiCollaborationAbstractState_" + userId, state);
        var msg = '操作成功';
        if (topWindow.Fiber && topWindow.Fiber.message) {
          topWindow.Fiber.message.success(msg);
        } else if (topWindow.$ && topWindow.$.messageBox) {
          topWindow.$.messageBox({
            msg: msg
          });
        } else {
          topWindow.alert(msg);
        }
      }
    },
    handleInfo: function (data) {
      var keywordData = data.matched_word;
      if (!data.id) return;
      var url = '/collaboration/collaboration.do?method=newColl&fromNewItem=true&showTab=true&templateId=' + data.id;
      var iframeId = data.id;
      this.openPage(iframeId, getCtpTop()._ctxPath + url, keywordData);
    },
    openPage: function (iframeId, url, data) {
      var topWindow = getCtpTop();
      var openCtpWindow = topWindow.openCtpWindow;
      var childWin = topWindow;
      var childInfo = this.childWindows[iframeId];

      if (childInfo) {
        // 已经打开过该页面，则直接推送消息
        childWin = openCtpWindow({ url: url + (this.childWindows[iframeId].isOpenedByUser ? '' : '&isComi=true') });
        this.sendMessageToChild(iframeId, data);
      } else {
        // 打开新页面
        url = url + '&isComi=true';
        childWin = openCtpWindow({ url: url });
        this.childWindows[iframeId] = {
          data: data,
          childWin: childWin || topWindow
        };
      }
      isMultipleTabs && getCtpTop().COMI_ASSISTANT.sdk.menuAction({
        action: 'collapse'
      });

      return childWin;
    },
    initMessageListener: function () {
      if (!this.messageListenerBind) {
        topWindow.addEventListener('message', this.handleMessage.bind(this));
        this.messageListenerBind = true;
      }
    },
    handleMessage: function (event) {
      if (!event.data || !event.source) return;

      try {
        var message = JSON.parse(event.data);
        var iframeId = message.iframeId;
        var childInfo = iframeId && this.childWindows[iframeId]

        if (message.type === 'comiReady' && !childInfo) {
          // 不是通过comi打开的页面
          this.childWindows[iframeId] = {
            isOpenedByUser: true
          };
          return;
        }

        if (message.type === 'comiReady') {
          console.log('comiReady');
          if (childInfo.data && childInfo.childWin) {
            this.sendMessageToChild(iframeId, childInfo.data);
            delete childInfo.data;
          }
        } else if (message.type === 'comiClose') {
          this.close(iframeId);
        }

      } catch (e) {
        console.log('handleMessage error');
      }
    },
    sendMessageToChild: function (iframeId, payload) {
      var childInfo = iframeId && this.childWindows[iframeId];
      if (childInfo) {
        var messageToSend = JSON.stringify({
          type: 'comiData',
          iframeId: iframeId,
          payload: payload
        });
        console.log('sendMessageToChild send message:', messageToSend);
        childInfo.childWin && childInfo.childWin.postMessage(messageToSend);
        childInfo.isOpenedByUser && getCtpTop().comiRefreshCollData && getCtpTop().comiRefreshCollData[iframeId] && getCtpTop().comiRefreshCollData[iframeId](messageToSend);
      } else {
        console.log('sendMessageToChild error');
      }
    },
    close: function (iframeId) {
      if (this.childWindows.hasOwnProperty(iframeId)) {
        delete this.childWindows[iframeId];
      }
    }
  };
  try {
    var topWindow = getTopWindow();
    if (topWindow.COMI_ASSISTANT) {
      return;
    }
    // 不显示图标，只显示drawer, 图标由外面控制
    var hideComiBtn = topWindow.hideComiBtn;
    var topDocument = topWindow.document;

    var polyFillRequestAnimationFrame = topWindow.requestAnimationFrame;
    var polyFillCancelAnimationFrame = topWindow.cancelAnimationFrame;
    (function () {
      if (polyFillRequestAnimationFrame) {
        return;
      }
      var lastTime = 0;
      var vendors = ["ms", "moz", "webkit", "o"];
      for (
        var x = 0;
        x < vendors.length && !polyFillRequestAnimationFrame;
        ++x
      ) {
        polyFillRequestAnimationFrame =
          topWindow[vendors[x] + "RequestAnimationFrame"];
        polyFillCancelAnimationFrame =
          topWindow[vendors[x] + "CancelAnimationFrame"];
      }
      if (!polyFillRequestAnimationFrame) {
        polyFillRequestAnimationFrame = function (callback, element) {
          var currTime = Date.now();
          var timeToCall = Math.max(0, 16 - (currTime - lastTime));
          var id = topWindow.setTimeout(function () {
            callback(currTime + timeToCall);
          }, timeToCall);
          lastTime = currTime + timeToCall;
          return id;
        };
        polyFillCancelAnimationFrame = function (timerId) {
          return clearTimeout(timerId);
        };
      }
    })();
    var iconPositionUpdateId = null;

    var isMouseDown = false;
    var movedWhenMouseDown = false;
    var initialX, initialY;
    var initX, initY;
    var lastx = 0;
    var lasty = 0;
    var timer = null;
    var timeNum = 0;
    var intervalTimer = null;
    var animationed = false


    var newDiv = topDocument.createElement("div");
    newDiv.className = "comi-entry-btn";
    newDiv.style.cursor = "pointer";

    var button = topDocument.createElement("button");
    button.id = "comi-assistant-btn";
    button.style.width = "60px";
    button.style.height = "60px";
    button.style.borderRadius = "50%";
    button.style.border = "none";
    button.style.backgroundImage =
      "url(" + _ctxPath + "/apps_res/aiAssistantEntry/img/comi.gif)";
    button.style.padding = "0";
    button.style.overflow = "hidden";
    button.style.cursor = "pointer";
    button.style.backgroundSize = "cover"
    button.style.backgroundRepeat = "no-repeat"
    button.style.backgroundColor = "transparent"

    // 在comi内部不显示入口comi图标
    if (window.top.location.href.indexOf("inComi") > -1) {
      button.style.display = "none";
    }

    var drawer = topDocument.createElement("div");
    drawer.id = "comi-assistant-drawer";

    drawer.style.position = "fixed";
    drawer.style.top = "0";
    drawer.style.right = "-450px";
    drawer.style.width = "450px";
    drawer.style.height = "100%";
    //drawer.style.overflow = 'hidden';
    drawer.style.borderRadius = "0";
    drawer.style.background = "#EDF2FC";
    drawer.style.boxShadow = "-4px 0 8px 0 rgba(145, 142, 159, 0.12)";
    // drawer.style.transition = "width 0.1s linear 0s"; //使用线性动画

    var aiEntryBtn;
    var openDrawer = function () {
      if (movedWhenMouseDown) return;
      if (drawer.style.right === "-450px") {
        drawer.style.right = "0";
        drawer.style.display = "block";
        if (!isVreport && !isLoadIframe) {
          newDiv.appendChild(drawer);
          isLoadIframe = true;
        }
      } 
      // else {
      //   drawer.style.right = "-450px";
      //   drawer.style.display = "none";
      //   drawer.style.width = "450px";
      // }
      // var __arrowDrawer = document.querySelector(".arrowDrawer");
      // __arrowDrawer.classList.contains("active") &&
      //   __arrowDrawer.classList.remove("active");
      // button.style.display = "none";

      // 隐藏AI入口按钮
      aiEntryBtn = document.getElementsByClassName("AI-entry-btn")[0];
      aiEntryBtn && (aiEntryBtn.style.display = "none");
    };
    var iframe = topDocument.createElement("iframe");
    iframe.id = "content-frame";
    iframe.style.width = "100%";
    iframe.style.height = "100%";
    iframe.style.border = "none";
    iframe.src = _ctxPath + "/ai-platform/copilot?v=" + new Date().getTime() + '&inComi=true';
    drawer.appendChild(iframe);

    var loadingImg = topDocument.createElement("img");
    loadingImg.src =
      _ctxPath +
      "/apps_res/aiAssistantEntry/img/comi-entry-loading.svg";
    loadingImg.style.position = "absolute";
    loadingImg.style.top = "50%";
    loadingImg.style.left = "50%";
    loadingImg.style.transform = "translate(-50%, -50%)";
    loadingImg.style.zIndex = "-1";

    drawer.appendChild(loadingImg);

    var expandUrl = "url(" +
      _ctxPath +
      "/apps_res/aiAssistantEntry/img/expand.png)";
    var collapseUrl = "url(" +
      _ctxPath +
      "/apps_res/aiAssistantEntry/img/collapse.png)";


    //加载抽屉箭头
    // var arrowDrawer = document.createElement("div");
    // arrowDrawer.className = 'comi-entry-arrow-drawer';
    // arrowDrawer.innerHTML = '';

    // arrowDrawer.style.cssText += `
    //       cursor: pointer;
    //       position: absolute;
    //       top: 50%;
    //       width: 20px !important;
    //       height: 80px !important;
    //       overflow: hidden;
    //       left: -20px;
    //       background-size: cover;
    //       background-position: center;
    //       background-repeat: no-repeat;
    //       transform: translate(0%, -50%);
    //     `;
    // arrowDrawer.style.cssText += "background-image:" + expandUrl + ";";
    // var arrowDrawerIcon = document.createElement("div");
    // arrowDrawerIcon.className = 'comi-arrow-icon vp-back';
    // // vp-forward
    // arrowDrawerIcon.style.cssText += `
    //       position: absolute;
    //       top: 50%;
    //       transform: translate(50%, -50%);
    //       right: 50%;
    // `
    // arrowDrawer.appendChild(arrowDrawerIcon);

    // drawer.appendChild(arrowDrawer);


    // function changeExpandIcon(__width) {
    //   if (__width <= 450) {
    //     arrowDrawer.style.cssText += "background-image:" + expandUrl + ";";
    //     arrowDrawer.style.cssText += "left: -20px;";
    //     arrowDrawerIcon.className = 'comi-arrow-icon vp-back';
    //   } else {
    //     arrowDrawer.style.cssText += "background-image:" + collapseUrl + ";";
    //     arrowDrawer.style.cssText += "left: 0px;";
    //     arrowDrawerIcon.className = 'comi-arrow-icon vp-forward';
    //   }
    // }

    // function handleExpandOrCollapse() {
    //   var isOpen = arrowDrawerIcon.classList.contains('vp-forward');
    //   var __width = isOpen ? 450 : window.innerWidth;
    //   __width = __width <= 450 ? 450 : __width;
    //   changeExpandIcon(__width);
    //   drawer.style.width = __width + 'px';
    // }

    // arrowDrawer.addEventListener('mouseenter', () => {
    //   arrowDrawerIcon.style.color = "#1f85ec";
    // });

    // arrowDrawer.addEventListener('mouseleave', () => {
    //   arrowDrawerIcon.style.color = "#666";
    // });

    // arrowDrawer.addEventListener('click', function (e) {
    //   e.stopPropagation();
    //   handleExpandOrCollapse();
    // })
    // arrowDrawerIcon.addEventListener('click', function (e) {
    //   e.stopPropagation();
    //   handleExpandOrCollapse();
    // })



    newDiv.addEventListener('mousedown', function (event) {
      isMouseDown = true;
      movedWhenMouseDown = false;
      iconPositionUpdateId = null;
      initialX = event.clientX - newDiv.offsetLeft;
      initialY = event.clientY - newDiv.offsetTop;
      lastx = event.clientX;
      lasty = event.clientY;
      initX = event.clientX;
      initY = event.clientY;
    });

    var ICON_STICK_RIGHT = "5px";
    topWindow.addEventListener("mousemove", function (event) {
      if (!isMouseDown) {
        return;
      }
      movedWhenMouseDown = true;
      if (lastx === event.clientX && lasty === event.clientY) {
        return false;
      } else {
        lastx = event.clientX;
        lasty = event.clientY;

        var updatePosition = function () {
          var newX = lastx - initialX;
          var newY = lasty - initialY;
          var rect = newDiv.getBoundingClientRect();
          if (newY <= 0) {
            newDiv.style.top = "0px";
          } else if (newY + rect.height > window.top.innerHeight) {
            newDiv.style.top = window.top.innerHeight - rect.height + "px";
          } else {
            newDiv.style.top = newY + "px";
          }
          if (newX <= 0) {
            newDiv.style.left = "0px";
            newDiv.style.right = "initial";
          } else if (newX + rect.width - 12 > window.top.innerWidth) {
            newDiv.style.left = "initial";
            newDiv.style.right = ICON_STICK_RIGHT;
          } else {
            newDiv.style.left = newX + "px";
            newDiv.style.right = "initial";
          }
        };
        updatePosition();
      }
    });

    topWindow.addEventListener("mouseup", function (event) {
      var openDrawerTag = false;
      if (isMouseDown) {
        console.log(
          "click:",
          Math.abs(initX - lastx) + Math.abs(initY - lasty)
        );
        // 位移不够算click
        if (Math.abs(initX - lastx) + Math.abs(initY - lasty) < 5) {
          openDrawerTag = true;
        }
      }
      isMouseDown = false;
      movedWhenMouseDown = false;
      if (iconPositionUpdateId) {
        polyFillCancelAnimationFrame(iconPositionUpdateId);
        iconPositionUpdateId = null;
      }
      topDocument.body.style.pointerEvents = "initial";
      newDiv.className = newDiv.className.replace(/\bAIhover\b/, "");
      newDiv.style.left = "initial";
      newDiv.style.right = ICON_STICK_RIGHT;
      if (openDrawerTag) {
        !event.target.classList.contains("comi-entry-arrow-drawer") && openDrawer();
      }
    });

    newDiv.style.position = "fixed";
    newDiv.style.top = "50%";
    newDiv.style.right = '5px';
    newDiv.style.zIndex = "999";

    var isLoadIframe = false;
    var isVreport = topWindow.location.href.indexOf("vReport.do") > -1;

    if (hideComiBtn === true) {
      button.style.display = "none";
    }
    var sdkOption = {
      defaultAssistId: '', // 默认打开助手id
      hideInputBoxAssistIds: [], // 隐藏输入框的助手id 
      defaultSendMsg: false, // 默认发送消息
    };
    topWindow.COMI_ASSISTANT = {
      preOption: sdkOption,
      preSdk: {
        init: function (params) {
          Object.assign(sdkOption, params || {});
        },
        callback: function () {
          console.log("初始化回调");
        },
        openDrawer: function (callback) {
          if (topWindow.COMI_ASSISTANT.sdk) {
            openDrawer();
            callback.apply(this, [topWindow.COMI_ASSISTANT.sdk]);
          } else {
            topWindow.COMI_ASSISTANT.preSdk.callback = callback;
            openDrawer();
          }
        },
      },
      sdk: null,
      close: function () {
        if (hideComiBtn !== true) {
          button.style.display = "block";
        }
        // changeExpandIcon(0);
        drawer.style.right = "-450px";
        drawer.style.width = "450px";
        drawer.style.display = "none";
        aiEntryBtn && (aiEntryBtn.style.display = "block");
      },
      // 全屏展开
      expand: function (right,width) {
        drawer.style.right = (right || '0') +'px';
        drawer.style.width = width || '100%';
        drawer.style.display = "block";
        // changeExpandIcon(window.innerWidth);
      },
      // 全屏收缩
      collapse: function () {
        drawer.style.right = "0";
        drawer.style.width = "450px";
        // changeExpandIcon(450);
      },
      handleTagClick: COMI_UTIL.handleTagClick,
      openWin: COMI_UTIL.openWin,
      openDialog: function (config) {
        config.targetWindow = getCtpTop()
        return getTopWindow().$.dialog(config)
      },
      openPersonalInfo: COMI_UTIL.openPersonalInfo,

      // 获取侧边栏状态，false关闭，true打开
      getOpenStatus: function () {
        if (drawer.style.display == "none" || drawer.style.display == "") {
          return false;
        } else {
          return true
        }
      },
      getOaUrl: getOaUrl,
    };
    // markdown标签点击事件
    // if (COMI_UTIL) {
    //   if (COMI_UTIL.handleTagClick) {
    //     topWindow.COMI_ASSISTANT.handleTagClick = COMI_UTIL.handleTagClick;
    //   }
    //   if (COMI_UTIL.openWin) {
    //     topWindow.COMI_ASSISTANT.openWin = COMI_UTIL.openWin;

    //   }
    // }

    var styleElement = topDocument.createElement("style");
    styleElement.innerHTML =
      "@media print {.comi-entry-btn {display:none}} .AIhover {width: 48px; height: 48px; overflow: hidden; border-radius: 28px !important;}.comi-entry-btn{right: 0px;border-bottom-right-radius: 0px;border-top-right-radius: 0px;border-top-left-radius: 48px;border-bottom-left-radius: 48px;padding: 0;border: none;}.comi-entry-btn:hover{right: 5px !important;}";
    topDocument.head.appendChild(styleElement);

    var gifAnimationTime = 5210; // gif动画时间
    // 替换为静态图片
    function replaceToStaticPng(time) {
      var waitNum = time - 10
      timer = setTimeout(function () {
        button.style.backgroundImage = "url(" +
          _ctxPath +
          "/apps_res/aiAssistantEntry/img/comi-static.png)";
        animationed = true
      }, waitNum)
    }

    newDiv.appendChild(button);
    replaceToStaticPng(gifAnimationTime)

    // 避免第一次加载鼠标移入移出
    intervalTimer = setInterval(() => {
      timeNum = timeNum + 10;
    }, 10)

    newDiv.addEventListener("mouseenter", function () {
      // 动画完成之后在处理
      if (animationed) {
        if (timer) {
          clearTimeout(timer);
        }
        if (intervalTimer) {
          clearInterval(intervalTimer);
        }
        animationed = false;
        button.style.backgroundImage = "url(" + _ctxPath +
          "/apps_res/aiAssistantEntry/img/comi.gif)";
        intervalTimer = setInterval(() => {
          timeNum = timeNum + 10;
        }, 10)
      }


    })
    newDiv.addEventListener("mouseleave", function () {
      if (timer) {
        clearTimeout(timer);
      }
      if (intervalTimer) {
        clearInterval(intervalTimer);
      }
      const time = Math.abs(timeNum - gifAnimationTime);
      if (timeNum <= gifAnimationTime) {
        replaceToStaticPng(time)
      } else {
        replaceToStaticPng(time % gifAnimationTime)
      }
      timeNum = 0;
    })


    window.addEventListener('resize', function () {
      if (drawer.style.display == "block" && drawer.clientWidth != 450) {
        var width = window.innerWidth;
        // width = width <= 450 ? 450 : width;
        // changeExpandIcon(width);
        drawer.style.width = width + 'px';
      }
    }); // 每200ms最多执行一次

    var headlessSaerch = topWindow.location.search;
    var searchArray = headlessSaerch.split("?");
    if (searchArray.length > 1) {
      var searchObj = {};
      var searchStr = searchArray[1];
      var searchParams = searchStr.split("&");
      for (var j = 0; j < searchParams.length; j++) {
        var searchItem = searchParams[j].split("=");
        if (searchItem) {
          searchObj[searchItem[0]] = searchItem[1];
        }
      }
      if (searchObj["headless"] !== "true") {
        topDocument.body.appendChild(newDiv);
      }
    } else {
      topDocument.body.appendChild(newDiv);
    }

    // 延迟加载iframe
    function delayApppendIframe() {
      setTimeout(function () {
        isLoadIframe = true;
        newDiv.appendChild(drawer);
      }, 1000);
    }
    // 报表中心且未加载过
    if (isVreport && !isLoadIframe) {
      delayApppendIframe();
    }

    // 通用回调处理函数
    function executeCallback(data, actionName) {
      if (data && typeof data.callback === 'function') {
        try {
          data.callback();
        } catch (e) {
          console.error('执行 ' + actionName + ' 回调时出错:', e);
        }
      }
    }

    // 添加 comiEventBus 监听器来处理 openDrawer 消息
    if (topWindow.comiEventBus && typeof topWindow.comiEventBus.$on === 'function') {
      topWindow.comiEventBus.$on('openDrawer', function(data) {
        console.log('收到 comiEventBus openDrawer 消息:', data);
        openDrawer();
        executeCallback(data, 'openDrawer');
      });
      
      topWindow.comiEventBus.$on('hideComi', function(data) {
        console.log('收到 comiEventBus hideComi 消息:', data);
        newDiv.style.display = 'none';
        executeCallback(data, 'hideComi');
      });
      
      topWindow.comiEventBus.$on('showComi', function(data) {
        console.log('收到 comiEventBus showComi 消息:', data);
        newDiv.style.display = 'block';
        executeCallback(data, 'showComi');
      });
      
      console.log('✅ comiEventBus UI相关监听器已注册');
    }

    // 初始化协同消息监听
    collaborationUtils.initMessageListener();

    // 加载完成回调
    var comiReadyFn = window.comiReady || (window.top && window.top.comiReady);
    if (comiReadyFn && typeof comiReadyFn == "function") {
      console.log("==> comiReadyFn");
      comiReadyFn();
    }
  } catch (error) {
    console.log("==> ai-entry-error", error);
  }
})();
