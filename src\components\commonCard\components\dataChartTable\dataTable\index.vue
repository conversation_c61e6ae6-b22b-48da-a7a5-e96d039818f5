<template>
  <div class="data-table-wrap">
    <TableContent :isPreview="isPreview" :cardItem="cardItem" @preview="preview" />
  </div>
</template>
<script setup lang="ts">
import TableContent from './content.vue';
import { ref, type PropType } from 'vue';
import type { ChatItem } from '@/types/index';
import type { CardItem } from '../../../types';

defineProps({
  isPreview: {
    type: Boolean,
    default: false,
  },
  chatData: {
    type: Object as PropType<ChatItem>,
    default: () => {},
  },
  cardItem: {
    type: Object as PropType<CardItem>,
    default: () => {},
  },
});
</script>
<style>
.data-table-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
}
</style>
