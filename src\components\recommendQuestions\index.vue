<template>
  <div class="recommend-questions" v-if="!isClicked">
    <div class="tips">您可以继续跟我说：</div>
    <div class="questions-content">
      <div
        class="question-list ellipsis"
        :title="item.subject"
        v-for="(item, index) in questions"
        :key="index"
        @click="handleInfo(item)"
      >
        {{ item.subject }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from 'vue';
import cardInstance from '@/stores/card';

interface Question {
  subject: string;
  index: number
}

const props = defineProps<{
  questions: Question[];
}>();
const isClicked = ref(false);
const handleInfo = (item: any) => {
  isClicked.value = true;
  cardInstance.sendMessage(item.subject);
};
</script>

<style lang="less" scoped>
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.recommend-questions {
  margin-top: 16px;
  .tips {
    font-size: 12px;
    color: #999999;
    margin-bottom: 8px;
  }
  .questions-content {
    // display: table-caption;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .question-list {
    line-height: 18px;
    display: inline-block;
    padding: 8px 14px 8px 14px;
    border-radius: 99px;
    background-color: rgba(255, 255, 255, 0.6);
    margin-bottom: 8px;
    font-size: 14px;
    color: #000000;
    cursor: pointer;
    max-width: 85%;
    min-width: 10px;
    &:hover {
      color: @sky;
    }
  }
}
</style>
