<template>
  <CoMiHeader/>
  <!-- <Carousel>
    <component :key="index" v-for="(item, index) in nodes" v-bind="item.props" :class="item.class" :nodes="parseCondition(item.children)" :style="getStyle(item.style)" :is="item.tag" />
  </Carousel> -->
  <Carousel :infinite="false">
      <div :key="index" v-for="(item, index) in nodes">
        <component  v-bind="item.props" :class="item.class" :nodes="parseCondition(item.children)" :style="getStyle(item.style)" :is="item.tag" />
      </div>
  </Carousel>
</template>
<script setup lang="ts">
import { parseCondition } from '../utils/conditionUtil';
import { Carousel } from 'ant-design-vue';
const { nodes } = defineProps({
  nodes: {
    type: Array,
    default: () => [],
  },
});
const getStyle = (style: any) => {
  if (style) {
    return style;
  }
  return {
    height: '350px',
  };
}

</script>

<style scoped lang="less">
.portal-layout-carousel {
  width: 100%;
  min-height: 200px;
}
:deep(.slick-dots-bottom){
  bottom: -32px;
  margin-bottom: 0;
  height: 24px;
  align-items: center;
  li{
    width: 8px;
    height: 8px;
    border-radius: 4px;
    button{
      width: 8px;
      height: 8px;
      border-radius: 4px;
      opacity: 1;
      background: #D8DADF;
    }
  }
  li.slick-active{
    width: 16px;
    button{
      width: 16px;
      opacity: 1;
      background: #6394FF;
    }
  }
}
</style>
