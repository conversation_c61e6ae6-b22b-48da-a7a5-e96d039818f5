<template>
  <div
    class="intelligent-question-count-wrapper w-full relative"
    :class="{
      'intelligent-question-count-small': isCopilotScreen,
    }"
  >
    <div class="top" v-if="!isCopilotScreen">
      <div class="mb-[12px] title">数智仓</div>
      <div class="mb-[22px] title-tips">释放数据全流程价值，驱动业务持续增长</div>
      <div class="mb-[12px] synopsis">
        <div class="synopsis-content">
          <div class="synopsis-item">
            <div class="synopsis-title">数据采集</div>
            <div class="data-desc data-desc-title">多渠道快速汇集内外数据</div>
            <img
          src="@/assets/imgs/Polygon.png"
          alt="createCardColumnBg"
          class="absolute top-[32px] right-[0px] w-[14px] h-[32px] object-cover"
        />
          </div>
           <div class="synopsis-item">
            <div class="synopsis-title">数据处理</div>
            <div class="data-desc data-desc-title">专业引擎完成原始数据治理</div>
             <img
          src="@/assets/imgs/Polygon.png"
          alt="createCardColumnBg"
          class="absolute top-[32px] right-[0px] w-[14px] h-[32px] object-cover"
        />
          </div>
           <div class="synopsis-item">
            <div class="synopsis-title">数据分析</div>
            <div class="data-desc data-desc-title">AI智能分析，驱动业务决策</div>
          </div>
        </div>
        <img
          src="@/assets/imgs/smart-sarehouse.png"
          alt="createCardColumnBg"
          class="absolute top-[-54px] right-[-22px] w-[100px] h-[100px] object-cover"
        />
      </div>
      <div class="mb-[12px] middle-data">
        <div class="filling data-item">
          <div class="filling-bg">
          <div class="mb-[24px] inner-filling" @click="openFilling">
            <div class="fill-left">
              <img
                src="@/assets/imgs/data-filling.png"
                alt="createCardColumnBg"
                class="w-[50px] h-[50px] object-cover"
                />
            </div>
            <div class="fill-content">
              <div class="fill-content-title">
                <span class="content-title text-[16px] text-black-600 mr-[12px]">发起组织内填报</span>
                <AImage :src="ArrowImg" :width="14" :height="14" :preview="false" class=" mt-[5px]"/>
              </div>
              <div class="data-desc">内部经营数据收集</div>
            </div>
          </div>
          <div class="inner-filling" @click="openIform">
            <div class="fill-left">
               <img
                src="@/assets/imgs/data-filling1.png"
                alt="createCardColumnBg"
                class="w-[50px] h-[50px] object-cover"
                />
            </div>
            <div class="fill-content">
              <div class="fill-content-title">
                <span class="content-title text-[16px] text-black-600 hover:text-blue-600 mr-[12px]">发起社会化填报</span>
                <AImage :src="ArrowImg" :width="14" :height="14" :preview="false" class=" mt-[5px]"/>
              </div>
              <div class="data-desc">在线制作问卷表单</div>
            </div>
          </div>
          </div>

        </div>
        <div class="deal data-item">
          <div class="deal-bg">
          <div class="deal-content">
            <div class="fill-left">
               <img
                src="@/assets/imgs/data-deal.png"
                alt="createCardColumnBg"
                class="w-[50px] h-[50px] object-cover"
                />
            </div>
            <div class="fill-content">
              <span class="fill-content-title content-title text-[16px] text-black-600">数据处理</span>
              <div class="data-desc data-desc-item mt-[8px]">进行多维度的复杂数据钻取与可视化分析</div>
            </div>
          </div>
          <button @click="openColl" class="deal-btn">进入协同驾驶舱</button>
          </div>

        </div>
        <div class="analysis data-item">
          <div class="analysis-bg">
          <div class="analysis-content">
            <div class="fill-left">
               <img
                src="@/assets/imgs/data-analysis.png"
                alt="createCardColumnBg"
                class="w-[50px] h-[50px] object-cover"
                />
            </div>
            <div class="fill-content">
              <span class="fill-content-title content-title text-[16px] text-black-600">数据分析</span>
              <div class="data-desc data-desc-item mt-[8px]">上传本地Excel，AI自动完成数据洞察，高效辅助决策。</div>
            </div>
          </div>
          <Upload class="upload-content" v-if="chatSessionIdU" ref="portalDataAnalysisRef" :chatSessionId="chatSessionIdU" uploadType="dragger" :supportFileTypes="['.xlsx']" :notShowPopover="true" @getRusultFiles="getRusultFilesFn"></Upload>
          <button class="analysis-btn">上传Excel文件</button>
          </div>

          <!-- <button @click="openColl" class="analysis-btn">上传Excel文件</button> -->
        </div>
      </div>
      <!-- <template > -->
        <div class="offen-report">
          <div class="report-title">常用报表</div>
          <div class="report-list" v-if="reportData.length > 0">
            <ReportItem :data="reportData" @openReport="openReport" />
          </div>
          <PortalEmptyColumn
            v-else
            :image="EmptyPendingImg"
            text="暂无数据哦~"
            :width="90"
            :height="90"
          />
           <!-- <Empty v-else description="暂无数据" :image="EmptyPendingImg"></Empty> -->
        </div>
    </div>
    <IntelligentQuestionCountSmall :reportDataSource="reportDataSource" v-else></IntelligentQuestionCountSmall>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, computed, inject } from 'vue';
import AiTabs from '@/components/aiTabs/index.vue';
import { getReportStatus } from '@/api/portal';
import ReportItem from './ReportItem.vue';
import PortalEmptyColumn from '@/components/portalEmptyColumn/index.vue';
import { openDobuleScreen } from '@/utils/storesUtils';
import { getAgentResult } from '@/api/common';
import { generateSecure16DigitNumber } from '@/utils/uuid';
import  IntelligentQuestionCountSmall  from "./IntelligentQuestionCountSmall.vue";
import CommonQuestionItem from '@/components/common/question-item/index.vue';
import type { TypeResponse } from '@/types/api';
import { isJSON } from '@/utils/is';
import Arrow from '@/components/common/arrow/index.vue';
import { Carousel } from 'ant-design-vue';
import Empty from '@/components/empty/index.vue';
import AiIdentify from '@/components/aiIdentify/index.vue';
import ArrowImg from '@/assets/imgs/arrow.png';
import { Image as AImage, Modal } from 'ant-design-vue';
import Upload from '@/components/common/upload/index.vue';
import EmptyPendingImg from '@/assets/imgs/empty_pending.png';

export default defineComponent({
  name: 'IntelligentQuestionCount',
});
</script>
<script setup lang="ts">
import type { ReportItemType } from '@/types/portalTypes';
import { useChatList } from '@/stores/chatList';
import { useGlobal } from '@/stores/global';
import cardInstance from '@/stores/card';
import { cutIt, getFileType } from '@/utils/tool';
import { FILE_TYPE_MAP } from '@/utils/constant';
  import { buildUUID } from '@/utils/uuid';


type QuestionItemType = {
  name: string;
  question: string;
};

const isPortal = inject('isPortal');
const uGlobal = useGlobal();
const uChatList = useChatList();
const sdkInstance = inject('sdkInstance') as any;

const reportData = ref<any[]>([]);
const reportDataSource = ref<any[]>([]);
const currentLabel = ref<string>('');

const recommendQuestions = ref<QuestionItemType[]>([]);
const currentScrollNum = ref(0);
const columnCount = ref(0);
const carouselCount = ref(0);
const isEmpty = ref(false);
const chatSessionId = generateSecure16DigitNumber();
const chatSessionIdU = buildUUID();

const canScrollLeft = computed(() => {
  return columnCount.value - 4 > 0 && currentScrollNum.value > 0;
});
const canScrollRight = computed(() => {
  return (
    columnCount.value - currentScrollNum.value > 4 && currentScrollNum.value < columnCount.value - 1
  );
});

const childrenData = computed(() => {
  const currentLabelData = reportData.value.find((item) => item.key === 'all');
  console.log("currentLabelData:", currentLabelData)
  let dataArr = [];
  if (currentLabelData?.children) {
    for(let i = 0; i < currentLabelData.children.length; ++i) {

      dataArr.push(currentLabelData.children[i].children);
    }
  }
  return dataArr;
});

// 如果不是全屏或者双屏模式下，采用小页面模式显示
const isCopilotScreen = computed(() => {
  return !isPortal || uChatList.dynamicData.dobuleScreenData.show;
});

// 标签点击
const handleClick = (key: string) => {
  currentLabel.value = key;
};

const handleCarouselChange = (current: number) => {
  carouselCount.value = current;
};

// 打开填报
const openFilling = () => {
  if (top?.ctpEnv?.currentUser?.userRoles?.contains('DataFillingAdmin')) {
    const ctxPath = window._ctxPath || '/seeyon';
    const url = `${ctxPath}/app/dataTable/new.html?taskType=0`;
    window.open(url)
  } else {
    noPluginTips();
  }
}
const noPluginTips = () => {
  Modal.confirm({
    title: '提示',
    content: '抱歉，您暂无此功能权限。如有需要，请联系管理员',
    okText: '确认',
    cancelText: '取消'
  })
}
// 打开ifrom
const openIform = () => {
  const url = 'https://www.iform.cc/dashboard/workplace';
  window.open(url);
}
// 打开协同驾驶舱
const openColl = () => {
  const roles = top?.ctpEnv?.currentUser?.userRoles || [];
  const hasRole = roles.includes("DashboardSystemAdmin") ||
                  roles.includes("DashboardReportDesigner") ||
                  roles.includes("DashboardDataManager");
  if (top?.ctpEnv?.plugins?.contains("bi-datamanager-platform") && hasRole) {
    const ctxPath = window._ctxPath || '/seeyon';
    const url = `${ctxPath}/biinside.do?method=vReportDataService`;
    window.open(url)
  } else {
    noPluginTips();
  }
}

// 打开报表
const openReport = (item: ReportItemType) => {
  const { categoryId, reportId } = item.design;
  const ctxPath = window._ctxPath || '/seeyon';
  const url = `${ctxPath}/report4Result.do?method=showResult&designId=${reportId}`;
  openDobuleScreen(url, 'iframe');
};

const handleArrowClick = (type: string) => {
  if (type === 'pre' && canScrollLeft.value) {
    currentScrollNum.value = currentScrollNum.value > 0 ? currentScrollNum.value - 1 : 0;
  } else if (type === 'next' && canScrollRight.value) {
    currentScrollNum.value =
      currentScrollNum.value < columnCount.value - 1
        ? currentScrollNum.value + 1
        : columnCount.value - 1;
  }
};

const getRecommendQuestions = async () => {
  const chatSessionId = generateSecure16DigitNumber();
  const data = {
    input: '推荐问题',
    chatSessionId: chatSessionId,
    agentCode: 'smartQuestionList',
  };
  const res = (await getAgentResult(data)) as TypeResponse;
  if (res.code == '0' && res.data?.content) {
    if (isJSON(res.data.content)) {
      const data = JSON.parse(res.data.content);
      recommendQuestions.value = data;
      columnCount.value = recommendQuestions.value.length;
    }
  }
};

const questionClick = (item: QuestionItemType) => {
  cardInstance.sendMessage(item.question);
};

// 格式化数据
const formatData = (data: any) => {
  const result: any = {};
  data.forEach((it: any) => {
    const reportCategory = it.design.reportCategory;
    if (reportCategory) {
      if (!result[reportCategory]) {
        result[reportCategory] = {
          label: it.design.name,
          key: reportCategory,
          children: [it],
        };
      } else {
        result[reportCategory].children.push(it);
      }
    }
  });
  const newData = Object.values(result);
  if (newData.length) {
    const allReportData: any[] = [];
    newData.forEach((it: any) => {
      allReportData.push(...it.children);
    });
    const allReportDataResult: any = {};
    allReportData
      .sort((a, b) => a.views - b.sort.views)
      .slice(0, 10)
      .forEach((it: any) => {
        const reportCategory = it.design.reportCategory;
        if (!allReportDataResult[reportCategory]) {
          allReportDataResult[reportCategory] = {
            label: it.design.name,
            key: reportCategory,
            children: [it],
          };
        } else {
          allReportDataResult[reportCategory].children.push(it);
        }
      });
    newData.unshift({
      label: '常用报表',
      key: 'all',
      children: Object.values(allReportDataResult),
      // .concat(Object.values(allReportDataResult))
      // .concat(Object.values(allReportDataResult)),
    });
  }
  return newData;
};

// 获取报表数据
const getReportData = async () => {
  const res: any = await getReportStatus();
  if (res.code == '0') {
    const data = res.data
    reportDataSource.value = formatData(res.data);
    // reportDataSource = formatData(res.data);
    // console.log("reportDataSource:", reportDataSource.value)
    // formatData(res.data);data
    if (data?.length) {
      // UE 要求只展示6个
      reportData.value = data.length > 6 ? data.slice(0, 6) : data;;
      // currentLabel.value = reportData.value[0].key;
    } else {
      isEmpty.value = true;
    }
  } else {
    isEmpty.value = true;
  }
};
const getRusultFilesFn = (obj: any) => {
  if (!(obj.confileList?.length > 0)) {
    return;

  }
  const files = obj.confileList;
    let requestFiles =
    files.length > 0
      ? files.map((d: any) => {
          const fileTypeKey = getFileType(d.name) as string;
          const mappedFileType = FILE_TYPE_MAP[fileTypeKey] || 'unknown'; // 添加默认值处理
          return {
            fileNumber: d.number,
            name: d.name,
            id: d.id,
            fileUrl: d.fileUrl,
            fileType: mappedFileType,
            chatSessionId,
            size: d.fileSize,
            fileRelativeUrl: d.fileRelativeUrl,
          };
        })
      : [];

  cardInstance.sendMessage('针对上传文件中的数据，帮我做简要分析', requestFiles);
}
const initInput = () =>{
  setTimeout(() => { //外面组件的显示用了nextTick，这里用setTimeout，确保组件显示了才传值
    // 设置输入框默认值
    sdkInstance.sendMsg('', {
      inputTemplate: [
        {
          id: '1',
          text: '帮我查询',
        },
        {
          id: '2',
          type: 'input',
          placeholder: '报表名称',
          focus: true,
        },
        {
          id: '3',
          text: '报表，',
        },
        {
          id: '4',
          type: 'input',
          placeholder: '希望查询的问题或数据',
        },
      ],
    });
  });
}
onMounted(() => {
  getReportData();
  // getRecommendQuestions();
  if(!isPortal) { //侧边栏目前没缓存，在mounted里面初始化
    initInput();
  }

});
onActivated(()=>{
  initInput();
})
</script>
<style scoped lang="less">
.intelligent-question-count-wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  width: 100%;
  justify-content: space-between;

  .custom_empty {
    height: 100%;
  }
  .synopsis {
    width: 100%;
    height: 96px;
    background-color: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(30px);
    position: relative;
    border-radius: 12px;
  }
  .synopsis-content {
    display: flex;
    flex-direction: row;
    border-radius: 12px;
  }
  .data-desc {
    font-size: 12px;
    color: rgba(63, 67, 77, 1);
    line-height: 18px;
  }
  .data-desc-title {
    width: calc(100% - 20px);
  }
  .data-desc-item {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  .synopsis-item {
    position: relative;
    flex-grow: 1;
    padding-left: 16px;
    padding-top: 16px;
    .title {
      color: rgba(0, 0, 0, 1);
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 10px;
    }
    .icon-right {
      top:15px;
      right: 0;
      width: 20px;
      height: 40px;
      position: absolute;
      background-image: url('@/assets/imgs/Polygon.png');
    }
  }
  .synopsis-title {
    font-weight: 600;
    font-size: 16px;
    color: rgba(0, 0, 0, 1);
    margin-bottom: 8px;
    line-height: 24px;
  }
  .middle-data {
    display: flex;
    gap: 12px;
    width: 100%;

    .data-item {
      height: 156px;
      border-radius: 12px;
      flex: 1;
      // padding: 16px 12px;
    }
    .filling-bg {
      height: 100%;
      background: linear-gradient(129deg, rgba(184, 248, 255, 0.56) -3.1%, rgba(255, 255, 255, 0.72) 51.8%);
      padding: 16px 12px;
      border-radius: 12px;
    }
    .filling {
      display: flex;
      flex-direction: column;
      border: 1px solid rgba(117, 219, 253, 0.3);
      // background: linear-gradient(to bottom right, rgba(117, 219, 253, 0.3), rgba(255, 255, 255, 0.9));

    }
    .inner-filling {
      display: flex;
      flex-direction: row;
      cursor: pointer;

      &:hover .content-title {
        color: @sky;
      }
    }
    .fill-left {
      width: 50px;
      min-width: 50px;
      height: 50px;
      border-radius: 50%;
      margin-right: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(233, 254, 255, 1);
    }
    .fill-content {
      // width: calc(100% - 58px);
      // padding-top: 2px;
      .fill-content-title {
        line-height: 24px;
        font-weight: 600;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: flex;


        .content-title {
          font-weight: 600;

		      overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: calc(100% - 26px);
        }
      }

      .data-desc {
        line-height: 24px;
      }
    }
    .deal {
      cursor: pointer;
      border: 1px solid rgba(133, 155, 255, 0.3);
      backdrop-filter: blur(30px);
      // background: linear-gradient(to bottom, rgba(187, 188, 255, 0.7), rgba(255, 255, 255, 0.9));

      .deal-bg {
        height: 100%;
        padding: 16px 12px;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        background: linear-gradient(182.51deg, rgba(187, 188, 255, 0.56) -5.07%, rgba(255, 255, 255, 0.72) 58.66%);
      }

      .deal-content {
        display: flex;
        flex-direction: row;
      }
      .deal-btn {
        width: 100%;
        height: 34px;
        background-color: rgba(80, 41, 255, 0.12);
        color: rgba(117, 89, 248, 1);
        border-radius: 8px;
        font-size: 13px;
        font-weight: 500;
      }
      &:hover .deal-btn{
        background-color: rgba(80, 41, 255, 0.24);
        color: rgba(117, 89, 248, 1);
      }
    }

    .analysis {
      position: relative;
      cursor: pointer;
      border: 1px solid rgba(133, 182, 255, 0.3);
      // background: linear-gradient(to bottom, rgba(187, 217, 255, 0.7), rgba(255, 255, 255, 0.9));


      .analysis-bg {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 16px 12px;
        border-radius: 12px;
        background: linear-gradient(182.51deg, rgba(187, 217, 255, 0.56) -5.07%, rgba(255, 255, 255, 0.72) 58.66%);
        height: 100%;
      }

      .analysis-content {
        display: flex;
        flex-direction: row;
      }
      .analysis-btn {
        width: 100%;
        height: 34px;
        background-color: rgba(41, 126, 255, 0.12);
        color: rgba(67, 121, 255, 1);
         border-radius: 8px;
         font-size: 13px;
        font-weight: 500;
      }
      &:hover .analysis-btn{
        background-color: rgba(41, 126, 255, 0.24);
        color: rgba(67, 121, 255, 1)
      }

      .upload-content{
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          z-index: 2;
          opacity: 0;
          display: inline-block;
        }
    }
  }

  .top {
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;

    .title {
      font-family: PingFang SC;
      font-weight: @font-weight-500;
      font-style: Semibold;
      font-size: 24px;
      line-height: 32px;
    }
    .title-tips {
      font-weight: 400;
      color: rgba(0, 0, 0, 0.9);
      font-size: 13px;
    }

    .content {
      margin-top: 15px;
      max-height: calc(100% - 110px);
      overflow: auto;
    }
  }
  .offen-report {
    width: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 12px;
    // height: 172px;
    max-height: 172px;
    padding: 16px;
  }
  .report-title {
    font-weight: 600;
    font-size: 16px;
    color: rgba(0, 0, 0, 1);
  }
  .report-list {
    display: flex;
    flex-wrap: wrap; /* 允许换行 */
  }

  .tabs {
    width: calc(100% - 116px);
  }

  :deep(.ai-tabs-wrapper) {
    margin: 0px;
    gap: 8px;
    height: auto;
    .ai-tab-content {
      gap: 12px;
    }
    .tab-item {
      height: 32px;
      border-radius: 12px;
      padding: 7px 26.5px;
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.528) 19.98%,
        rgba(255, 255, 255, 0.704) 79.94%
      );
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-size: 13px;
      line-height: 19px;

      &.active {
        background: rgba(0, 0, 0, 0.9);
        font-weight: @font-weight-500;
        color: #ffffff;
        backdrop-filter: blur(59.30232620239258px);
      }
    }
  }

  .content-wrapper {
    flex: 1;
    min-height: 1px;
    overflow-y: auto;
    background-color: #ffffff80;
    border-radius: 12px 12px 0px 0px;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(15px);
    padding: 8px 0px;
    border-radius: 12px;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .content-inner-title {
      //   backdrop-filter: blur(15px);
      padding-left: 16px;
      color: #000;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: @font-weight-500;
      line-height: 24px; /* 150% */
    }

    :deep(.content-inner-wrapper) {
      .content-item {
        width: 33.33%;
      }
    }
  }
  .recommend-question {
    height: 94px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    padding: 8px 16px 12px 16px;

    :deep(.ant-spin-spinning) {
      width: 100%;
    }

    .title-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .title {
        font-family: PingFang SC;
        font-weight: @font-weight-500;
        font-size: 16px;
        line-height: 24px;
      }
    }

    .question-content {
      display: flex;
      width: 100%;

      .question-item {
        margin-right: 32px;
        margin-bottom: 0;
        min-width: 174px;
        max-width: 174px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
    .no-data {
      font-size: 14px;
      color: #8e94a2;
      text-align: center;
    }
  }
}
.intelligent-question-count-small {
  .top {
    height: 100%;
    .title,
    img {
      display: none;
    }
    .tabs {
      width: calc(100% - 5px);
    }

    :deep(.ai-tabs-wrapper) {
      .ai-tab-content {
        gap: 12px;
      }
      .tab-item {
        line-height: 22px;
        border-radius: 8px;
        padding: 5px 8px;
        background: transparent;
        font-weight: @font-weight-500;

        &.active {
          background: #d1e0ff;
          font-weight: @font-weight-500;
          color: #2962f0;
        }
      }
    }
    .content-small {
      margin-top: 12px;
      .content-wrapper {
        overflow: auto;
        background: #ffffff80;
        height: 316px;
        backdrop-filter: blur(1000px);
      }

      :deep(.content-inner-wrapper) {
        flex-direction: column;
        .content-item {
          width: 100%;
        }
      }
      :deep(.custom-slick-arrow) {
        z-index: 10;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        backdrop-filter: blur(59.30232620239258px);
        background: #ffffffb2;
        position: absolute;
        border: 1px solid #ffffff66;
        color: #8e94a2;

        &.slick-prev {
          top: 94%;
          left: 83%;
        }
        &.slick-next {
          top: 94%;
          right: 2%;
        }
        .iconfont {
          font-size: 14px;
          position: absolute;
          top: 11px;
          left: 4px;
        }
        &.disabled {
          cursor: not-allowed;
          color: #d8dadf;
        }
      }
    }
    .recommend-question-small {
      margin-top: 12px;
    }
  }
}
</style>
