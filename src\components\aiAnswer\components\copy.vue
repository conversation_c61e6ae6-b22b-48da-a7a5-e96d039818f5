<template>
  <span class="copy_btn" @click="goCopy">
    <Tooltip title="复制"
      ><i class="iconfont ans_font ai-icon-quanwenfuzhi"></i></Tooltip
  ></span>
</template>
<script setup lang="ts">
  import { ref, inject } from 'vue';
  import type { Ref } from 'vue';
  import { message, Tooltip } from 'ant-design-vue';
  import { copyTextToClipboard } from '@/hooks/web/useCopyToClipboard';
  import { cateAsistantLogs } from '@/api/common/index';
  /**
   * 处理通用的复制能力
   */
  const props = defineProps({
    transAiInfo: {
      type: Object,
      default: () => {},
    },
  });
  
  // 注入祖先提供的 ref
  const aiAnswerRef = inject<Ref<any>>('aiAnswerRef', ref())
  const comiMarkdownRef = inject<Ref<any>>('comiMarkdownRef', ref())

  // 复制
  const goCopy = async () => {
    let biMarkdownContent = '';
    let text = '';

    if(aiAnswerRef.value){
      biMarkdownContent = aiAnswerRef.value.getMarkdownContent()
    }
    if (comiMarkdownRef.value) {
      comiMarkdownRef.value.forEach((item: any) => {
        const context = item.$props.content.context;
        if (context) {
          // 移除 span 标签，只保留内容
          const cleanText = context.replace(/<span[^>]*>(.*?)<\/span>/g, '$1');
          text += cleanText + '\n';
        }
      });
    }
    
    try {
      const copyResult = await copyTextToClipboard(text + biMarkdownContent);
      if (copyResult) {
        message.success('复制成功');
      } else {
        message.error('复制失败，请重试');
      }
    } catch (error) {
      console.error('复制失败:', error);
      message.error('复制失败，请重试');
    }

    toCateAnewAnswerLog({
      type: 1,
      assistantId: props.transAiInfo.staticData.requestParams.assistantId,
      sessionId: props.transAiInfo.data.aiSessionId,
      citations: props.transAiInfo.staticData.requestParams.citations,
    });
  };

  // 复制采集
  const toCateAnewAnswerLog = (val: any) => {
    cateAsistantLogs({
      chatSessionId: props.transAiInfo.staticData.requestParams.chatSessionId,
      ...val,
    });
  };
</script>
<style scoped lang="less">
  .copy_btn {
    margin-right: 8px;
    color: rgba(0, 0, 0, 0.4);
  }
  .copy_btn:hover {
    color: @sky;
    span {
      color: @sky;
    }
  }
  .iconfont:hover {
    color: @sky;
  }
</style>