<template>
  <footer class="content-footer flex w-full flex-col justify-center rounded-[12px] relative">
    <!-- 数据分析 -->
    <div class="footer-tool">
      <PortalFooterAgent
        :agentList="agentList"
        @handleAgentClick="handleAgentClick"
        class="agent_wrap"
        v-if="useGlobalStore.globalState.comiContainerName === 'v5'"
      />
      <SpecIcon
        @go-operate="addNewChatHandel"
        :class="['new_btn', isFinish && !isCanceling ? 'un_ck_btn' : '']"
      >
        <template #default> 发起新对话 </template>
        <template #tail_icon>
          <img class="img" src="@/assets/imgs/table_card_logo.png" alt="" />
        </template>
      </SpecIcon>
    </div>
    <div class="relative input_wrap">
      <DataAnalysis
        v-model:open="showDataAnalysis"
        :chatSessionId="chatSessionId"
        ref="dataAnalysisRef"
        class="data-analysis"
        @getRusultFiles="getRusultFiles"
      />
      <ElasticIpt
        ref="inputBox"
        :class="{ chatting_wrap: isFinish }"
        :transShowSal="false"
        :transCkAssist="ckAssist"
        :showStop="showStop"
        :toSearchWithquestion="searchingAnswers"
        :toOperteSal="operatingSal"
        :toOpertePrologue="opertePrologue"
        :toStop="cancelingChat"
        :transSchPams="transSchParams"
        :dataAnalysisFiles="confilelist"
        :quickMsg="quickMsg"
        :clearQuikcMsg="clearQuikcMsg"
        :isGeneralAssist="isGeneralAssist"
        class="portal_ipt"
      >
        <!-- 辅助助手 -->
        <template #checkAssist v-if="ckAssist.length > 0 && ckAssist[0].type != 'general' && isGeneralAssist">
          <CheckAssist :ckAssist="ckAssist" :goDelSecAssist="goDelSecAssist" />
        </template>
      </ElasticIpt>
      <!-- 助手列表 -->
      <div class="absolute w-full assistnat_list" ref="assistsRef">
        <SearchAssistList
          v-if="isShowSal"
          :transCkAst="ckAssist"
          @toGoSecItem="goingSecItem"
          @toCloseSal="operatingSal(false)"
        />
      </div>
      <!-- 开场白列表 -->
      <div class="absolute w-full prologue_list" ref="prologueRef">
        <PrologueList
          v-if="isShowPropolugeList"
          :transSchPams="transSchParams"
          :toOpertePrologue="opertePrologue"
          :toSearchWithquestion="searchingAnswers"
        />
      </div>
    </div>
    <!-- tip信息 -->
    <div class="ai_tip">AI生成内容仅供参考，请注意甄别信息准确性</div>
  </footer>
</template>
<script lang="ts">
import { defineComponent, toRefs } from 'vue';
export default defineComponent({
  name: 'PortalFooter',
});
</script>
<script setup lang="ts">
import type { PropType } from 'vue';
import type { AssistInfo } from '@/types/index';
import { defineProps, ref, onMounted, onUnmounted } from 'vue';
import { usePortalEvents } from '@/hooks/portal';
import PortalFooterAgent from '@/components/portalFooterAgent/index.vue';
import ElasticIpt from '@/components/elasticIpt/index.vue';
import SearchAssistList from '@/components/searchAssistList/index.vue';
import PrologueList from '@/components/prologueList/index.vue';
import CheckAssist from '@/components/checkAssist/index.vue';
import DataAnalysis from '@/components/dataAnalysis/index.vue';
import SpecIcon from '@/components/specButton/index.vue';
import { useGlobal } from '@/stores/global';
const useGlobalStore = useGlobal();

type agentListType = {
  id: string;
  name: string;
};
const props = defineProps({
  isFinish: {
    type: Boolean,
    default: false,
  },
  showStop: {
    type: Boolean,
    default: false,
  },
  transSchParams: {
    type: Object,
    default: () => ({}),
  },
  agentList: {
    type: Array as PropType<agentListType[]>,
    default: () => [],
  },
  showChatList: {
    type: Boolean,
    default: false,
  },
  ckAssist: {
    type: Array as PropType<AssistInfo[]>,
    default: () => [],
  },
  isShowSal: {
    type: Boolean,
    default: false,
  },
  isShowPropolugeList: {
    type: Boolean,
    default: false,
  },
  chatSessionId: {
    type: String,
    default: '',
  },
  isCanceling: {
    type: Boolean,
    default: false,
  },
  finish: {
    type: Number,
    default: 0,
  },
  isGeneralAssist: {
    type: Boolean
  }
});
const emit = defineEmits([
  'searchingAnswers',
  'operatingSal',
  'opertePrologue',
  'cancelingChat',
  'handleAgentClick',
  'goingSecItem',
  'goDelSecAssist',
  'addNewChat',
]);
const {
  agentList,
  showStop,
  isFinish,
  transSchParams,
  showChatList,
  ckAssist,
  isShowSal,
  isShowPropolugeList,
  isCanceling,
} = toRefs(props);
const {
  searchingAnswers,
  operatingSal,
  opertePrologue,
  cancelingChat,
  // handleAgentClick,
  goingSecItem,
  goDelSecAssist,
  addNewChat,
} = usePortalEvents(emit);

const inputBox = ref();
const dataAnalysisRef = ref();
// 控制数据分析上传组件的显示隐藏
const showDataAnalysis = ref(false);
const quickMsg = ref(''); // 快速回复内容
const confilelist = ref<object>({}); // 真实上传文件列表

// 清理输入框内容的方法
const cleanAllFile = () => {
  inputBox.value?.cleanAll();
};

// 暴露方法给父组件
defineExpose({
  cleanAllFile,
});

onMounted(() => {
  document.addEventListener('click', closeDataAnalysis, true);
});

function closeDataAnalysis(event) {
  const targetDom = event.target;
  if (!targetDom.closest('.data-analysis')) {
    showDataAnalysis.value = false;
  }
}
// 获取上传的文件信息
// console.log(dataAnalysisRef.value?.getDataAnalysisFilesFn());
const addNewChatHandel = (value: any) => {
  cleanAllFile();
  addNewChat(value);
};
const handleAgentClick = (id: string) => {
  if (id === '数据分析') {
    showDataAnalysis.value = true;
  } else {
    emit('handleAgentClick', id);
  }
};

const getRusultFiles = (obj: any) => {
  showDataAnalysis.value = false;
  confilelist.value = obj;
  quickMsg.value = '针对上传文件中的数据，帮我做简要分析';
};
// 用于发送消息后，清空快捷消息，避免再次赋值同样的，输入框值不更新
const clearQuikcMsg = () => {
  quickMsg.value = '';
};

const delFileFn = (id: string) => {
  const index = confilelist.value.findIndex((item) => item.id === id);
  if (index !== -1) {
    confilelist.value.splice(index, 1);
  }
};
onUnmounted(() => {
  document.removeEventListener('click', closeDataAnalysis, true);
});
</script>
<style scoped lang="less">
.content-footer {
  margin-bottom: 8px;
  :deep(.search_box_wrap) {
    margin-top: 10px;
  }

  .assistnat_list,
  .prologue_list {
    top: 0px;
    transform: translateY(-100%);
    z-index: 101;
    // box-shadow: 0px 0px 8px 0px #00000014;
  }
  .data-analysis {
    position: absolute;
    width: 100%;
    left: 0;
    top: -230px;
    z-index: 100;
  }
  .hasFile {
    top: -130px;
  }
  .footer-tool {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 32px;
    margin-top: 10px;

    .agent_wrap {
      flex: 1;
      flex-wrap: wrap;
      gap: 10px;
    }

    .un_ck_btn {
      pointer-events: none;
    }
    .new_btn {
      padding: 1px 1px 1px 12px;
      gap: 11px;
      background: linear-gradient(
        90deg,
        #d1dfff 0%,
        #cde9ff 27.88%,
        #eaecff 58.17%,
        #e9edfd 96.63%
      );

      .img {
        width: 28px;
        height: 28px;
      }
    }
  }

  // 输入框
  .portal_ipt {
    border: 1px solid #ffffff66;
    backdrop-filter: blur(59.30232620239258px);
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.528) 19.98%,
      rgba(255, 255, 255, 0.704) 79.94%
    );

    &:hover,
    &.search_box_wrap_active {
      border-color: #2a69fe;
    }
    :deep(.search_box_shirt) {
      backdrop-filter: blur(59.30232620239258px);
      .ele_txtara {
        height: 44px;
        max-height: 44px;
        min-height: 44px;
      }
    }
  }
  .ai_tip {
    margin-top: 8px;
    font-family: PingFang SC;
    font-weight: @font-weight-400;
    font-size: 10px;
    line-height: 18px;
    letter-spacing: 0px;
    text-align: center;
    color: rgba(0, 0, 0, 0.2);
    text-align: center;
  }
}
.empty-box {
  .content-footer {
    display: flex;
    flex-direction: column-reverse;
    position: relative;

    .input_wrap {
      margin-top: 24px;
    }

    .footer-tool {
      margin-top: 16px;
    }

    .ai_tip {
      display: none;
    }
    .data-analysis {
      top: -190px;
    }
  }
}
</style>
