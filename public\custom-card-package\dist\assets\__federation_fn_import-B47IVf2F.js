let importShared, getShared<PERSON>romLocal, getSharedFromRuntime;
let __tla = (async () => {
  const buildIdentifier = "[0-9A-Za-z-]+";
  const build = `(?:\\+(${buildIdentifier}(?:\\.${buildIdentifier})*))`;
  const numericIdentifier = "0|[1-9]\\d*";
  const numericIdentifierLoose = "[0-9]+";
  const nonNumericIdentifier = "\\d*[a-zA-Z-][a-zA-Z0-9-]*";
  const preReleaseIdentifierLoose = `(?:${numericIdentifierLoose}|${nonNumericIdentifier})`;
  const preReleaseLoose = `(?:-?(${preReleaseIdentifierLoose}(?:\\.${preReleaseIdentifierLoose})*))`;
  const preReleaseIdentifier = `(?:${numericIdentifier}|${nonNumericIdentifier})`;
  const preRelease = `(?:-(${preReleaseIdentifier}(?:\\.${preReleaseIdentifier})*))`;
  const xRangeIdentifier = `${numericIdentifier}|x|X|\\*`;
  const xRangePlain = `[v=\\s]*(${xRangeIdentifier})(?:\\.(${xRangeIdentifier})(?:\\.(${xRangeIdentifier})(?:${preRelease})?${build}?)?)?`;
  const hyphenRange = `^\\s*(${xRangePlain})\\s+-\\s+(${xRangePlain})\\s*$`;
  const mainVersionLoose = `(${numericIdentifierLoose})\\.(${numericIdentifierLoose})\\.(${numericIdentifierLoose})`;
  const loosePlain = `[v=\\s]*${mainVersionLoose}${preReleaseLoose}?${build}?`;
  const gtlt = "((?:<|>)?=?)";
  const comparatorTrim = `(\\s*)${gtlt}\\s*(${loosePlain}|${xRangePlain})`;
  const loneTilde = "(?:~>?)";
  const tildeTrim = `(\\s*)${loneTilde}\\s+`;
  const loneCaret = "(?:\\^)";
  const caretTrim = `(\\s*)${loneCaret}\\s+`;
  const star = "(<|>)?=?\\s*\\*";
  const caret = `^${loneCaret}${xRangePlain}$`;
  const mainVersion = `(${numericIdentifier})\\.(${numericIdentifier})\\.(${numericIdentifier})`;
  const fullPlain = `v?${mainVersion}${preRelease}?${build}?`;
  const tilde = `^${loneTilde}${xRangePlain}$`;
  const xRange = `^${gtlt}\\s*${xRangePlain}$`;
  const comparator = `^${gtlt}\\s*(${fullPlain})$|^$`;
  const gte0 = "^\\s*>=\\s*0.0.0\\s*$";
  function parseRegex(source) {
    return new RegExp(source);
  }
  function isXVersion(version) {
    return !version || version.toLowerCase() === "x" || version === "*";
  }
  function pipe(...fns) {
    return (x) => {
      return fns.reduce((v, f) => f(v), x);
    };
  }
  function extractComparator(comparatorString) {
    return comparatorString.match(parseRegex(comparator));
  }
  function combineVersion(major, minor, patch, preRelease2) {
    const mainVersion2 = `${major}.${minor}.${patch}`;
    if (preRelease2) {
      return `${mainVersion2}-${preRelease2}`;
    }
    return mainVersion2;
  }
  function parseHyphen(range) {
    return range.replace(parseRegex(hyphenRange), (_range, from, fromMajor, fromMinor, fromPatch, _fromPreRelease, _fromBuild, to, toMajor, toMinor, toPatch, toPreRelease) => {
      if (isXVersion(fromMajor)) {
        from = "";
      } else if (isXVersion(fromMinor)) {
        from = `>=${fromMajor}.0.0`;
      } else if (isXVersion(fromPatch)) {
        from = `>=${fromMajor}.${fromMinor}.0`;
      } else {
        from = `>=${from}`;
      }
      if (isXVersion(toMajor)) {
        to = "";
      } else if (isXVersion(toMinor)) {
        to = `<${+toMajor + 1}.0.0-0`;
      } else if (isXVersion(toPatch)) {
        to = `<${toMajor}.${+toMinor + 1}.0-0`;
      } else if (toPreRelease) {
        to = `<=${toMajor}.${toMinor}.${toPatch}-${toPreRelease}`;
      } else {
        to = `<=${to}`;
      }
      return `${from} ${to}`.trim();
    });
  }
  function parseComparatorTrim(range) {
    return range.replace(parseRegex(comparatorTrim), "$1$2$3");
  }
  function parseTildeTrim(range) {
    return range.replace(parseRegex(tildeTrim), "$1~");
  }
  function parseCaretTrim(range) {
    return range.replace(parseRegex(caretTrim), "$1^");
  }
  function parseCarets(range) {
    return range.trim().split(/\s+/).map((rangeVersion) => {
      return rangeVersion.replace(parseRegex(caret), (_, major, minor, patch, preRelease2) => {
        if (isXVersion(major)) {
          return "";
        } else if (isXVersion(minor)) {
          return `>=${major}.0.0 <${+major + 1}.0.0-0`;
        } else if (isXVersion(patch)) {
          if (major === "0") {
            return `>=${major}.${minor}.0 <${major}.${+minor + 1}.0-0`;
          } else {
            return `>=${major}.${minor}.0 <${+major + 1}.0.0-0`;
          }
        } else if (preRelease2) {
          if (major === "0") {
            if (minor === "0") {
              return `>=${major}.${minor}.${patch}-${preRelease2} <${major}.${minor}.${+patch + 1}-0`;
            } else {
              return `>=${major}.${minor}.${patch}-${preRelease2} <${major}.${+minor + 1}.0-0`;
            }
          } else {
            return `>=${major}.${minor}.${patch}-${preRelease2} <${+major + 1}.0.0-0`;
          }
        } else {
          if (major === "0") {
            if (minor === "0") {
              return `>=${major}.${minor}.${patch} <${major}.${minor}.${+patch + 1}-0`;
            } else {
              return `>=${major}.${minor}.${patch} <${major}.${+minor + 1}.0-0`;
            }
          }
          return `>=${major}.${minor}.${patch} <${+major + 1}.0.0-0`;
        }
      });
    }).join(" ");
  }
  function parseTildes(range) {
    return range.trim().split(/\s+/).map((rangeVersion) => {
      return rangeVersion.replace(parseRegex(tilde), (_, major, minor, patch, preRelease2) => {
        if (isXVersion(major)) {
          return "";
        } else if (isXVersion(minor)) {
          return `>=${major}.0.0 <${+major + 1}.0.0-0`;
        } else if (isXVersion(patch)) {
          return `>=${major}.${minor}.0 <${major}.${+minor + 1}.0-0`;
        } else if (preRelease2) {
          return `>=${major}.${minor}.${patch}-${preRelease2} <${major}.${+minor + 1}.0-0`;
        }
        return `>=${major}.${minor}.${patch} <${major}.${+minor + 1}.0-0`;
      });
    }).join(" ");
  }
  function parseXRanges(range) {
    return range.split(/\s+/).map((rangeVersion) => {
      return rangeVersion.trim().replace(parseRegex(xRange), (ret, gtlt2, major, minor, patch, preRelease2) => {
        const isXMajor = isXVersion(major);
        const isXMinor = isXMajor || isXVersion(minor);
        const isXPatch = isXMinor || isXVersion(patch);
        if (gtlt2 === "=" && isXPatch) {
          gtlt2 = "";
        }
        preRelease2 = "";
        if (isXMajor) {
          if (gtlt2 === ">" || gtlt2 === "<") {
            return "<0.0.0-0";
          } else {
            return "*";
          }
        } else if (gtlt2 && isXPatch) {
          if (isXMinor) {
            minor = 0;
          }
          patch = 0;
          if (gtlt2 === ">") {
            gtlt2 = ">=";
            if (isXMinor) {
              major = +major + 1;
              minor = 0;
              patch = 0;
            } else {
              minor = +minor + 1;
              patch = 0;
            }
          } else if (gtlt2 === "<=") {
            gtlt2 = "<";
            if (isXMinor) {
              major = +major + 1;
            } else {
              minor = +minor + 1;
            }
          }
          if (gtlt2 === "<") {
            preRelease2 = "-0";
          }
          return `${gtlt2 + major}.${minor}.${patch}${preRelease2}`;
        } else if (isXMinor) {
          return `>=${major}.0.0${preRelease2} <${+major + 1}.0.0-0`;
        } else if (isXPatch) {
          return `>=${major}.${minor}.0${preRelease2} <${major}.${+minor + 1}.0-0`;
        }
        return ret;
      });
    }).join(" ");
  }
  function parseStar(range) {
    return range.trim().replace(parseRegex(star), "");
  }
  function parseGTE0(comparatorString) {
    return comparatorString.trim().replace(parseRegex(gte0), "");
  }
  function compareAtom(rangeAtom, versionAtom) {
    rangeAtom = +rangeAtom || rangeAtom;
    versionAtom = +versionAtom || versionAtom;
    if (rangeAtom > versionAtom) {
      return 1;
    }
    if (rangeAtom === versionAtom) {
      return 0;
    }
    return -1;
  }
  function comparePreRelease(rangeAtom, versionAtom) {
    const { preRelease: rangePreRelease } = rangeAtom;
    const { preRelease: versionPreRelease } = versionAtom;
    if (rangePreRelease === void 0 && !!versionPreRelease) {
      return 1;
    }
    if (!!rangePreRelease && versionPreRelease === void 0) {
      return -1;
    }
    if (rangePreRelease === void 0 && versionPreRelease === void 0) {
      return 0;
    }
    for (let i = 0, n = rangePreRelease.length; i <= n; i++) {
      const rangeElement = rangePreRelease[i];
      const versionElement = versionPreRelease[i];
      if (rangeElement === versionElement) {
        continue;
      }
      if (rangeElement === void 0 && versionElement === void 0) {
        return 0;
      }
      if (!rangeElement) {
        return 1;
      }
      if (!versionElement) {
        return -1;
      }
      return compareAtom(rangeElement, versionElement);
    }
    return 0;
  }
  function compareVersion(rangeAtom, versionAtom) {
    return compareAtom(rangeAtom.major, versionAtom.major) || compareAtom(rangeAtom.minor, versionAtom.minor) || compareAtom(rangeAtom.patch, versionAtom.patch) || comparePreRelease(rangeAtom, versionAtom);
  }
  function eq(rangeAtom, versionAtom) {
    return rangeAtom.version === versionAtom.version;
  }
  function compare(rangeAtom, versionAtom) {
    switch (rangeAtom.operator) {
      case "":
      case "=":
        return eq(rangeAtom, versionAtom);
      case ">":
        return compareVersion(rangeAtom, versionAtom) < 0;
      case ">=":
        return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) < 0;
      case "<":
        return compareVersion(rangeAtom, versionAtom) > 0;
      case "<=":
        return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) > 0;
      case void 0: {
        return true;
      }
      default:
        return false;
    }
  }
  function parseComparatorString(range) {
    return pipe(parseCarets, parseTildes, parseXRanges, parseStar)(range);
  }
  function parseRange(range) {
    return pipe(parseHyphen, parseComparatorTrim, parseTildeTrim, parseCaretTrim)(range.trim()).split(/\s+/).join(" ");
  }
  function satisfy(version, range) {
    if (!version) {
      return false;
    }
    const parsedRange = parseRange(range);
    const parsedComparator = parsedRange.split(" ").map((rangeVersion) => parseComparatorString(rangeVersion)).join(" ");
    const comparators = parsedComparator.split(/\s+/).map((comparator2) => parseGTE0(comparator2));
    const extractedVersion = extractComparator(version);
    if (!extractedVersion) {
      return false;
    }
    const [, versionOperator, , versionMajor, versionMinor, versionPatch, versionPreRelease] = extractedVersion;
    const versionAtom = {
      version: combineVersion(versionMajor, versionMinor, versionPatch, versionPreRelease),
      major: versionMajor,
      minor: versionMinor,
      patch: versionPatch,
      preRelease: versionPreRelease == null ? void 0 : versionPreRelease.split(".")
    };
    for (const comparator2 of comparators) {
      const extractedComparator = extractComparator(comparator2);
      if (!extractedComparator) {
        return false;
      }
      const [, rangeOperator, , rangeMajor, rangeMinor, rangePatch, rangePreRelease] = extractedComparator;
      const rangeAtom = {
        operator: rangeOperator,
        version: combineVersion(rangeMajor, rangeMinor, rangePatch, rangePreRelease),
        major: rangeMajor,
        minor: rangeMinor,
        patch: rangePatch,
        preRelease: rangePreRelease == null ? void 0 : rangePreRelease.split(".")
      };
      if (!compare(rangeAtom, versionAtom)) {
        return false;
      }
    }
    return true;
  }
  const currentImports = {};
  const moduleMap = {
    "vue": {
      get: () => () => __federation_import(new URL("__federation_shared_vue-8OM9IrvL.js", import.meta.url).href),
      import: true
    },
    "@seeyon/seeyon-comi-plugins-library": {
      get: () => () => __federation_import(new URL("__federation_shared_@seeyon/seeyon-comi-plugins-library-NcsYKdvi.js", import.meta.url).href),
      import: true
    },
    "ant-design-vue": {
      get: () => () => __federation_import(new URL("__federation_shared_ant-design-vue-1AycU9a4.js", import.meta.url).href),
      import: true
    }
  };
  const moduleCache = /* @__PURE__ */ Object.create(null);
  importShared = async function(name, shareScope = "default") {
    return moduleCache[name] ? new Promise((r) => r(moduleCache[name])) : await getSharedFromRuntime(name, shareScope) || getSharedFromLocal(name);
  };
  async function __federation_import(name) {
    currentImports[name] ?? (currentImports[name] = import(name).then(async (m) => {
      await m.__tla;
      return m;
    }));
    return currentImports[name];
  }
  getSharedFromRuntime = async function(name, shareScope) {
    var _a, _b, _c;
    let module = null;
    if ((_b = (_a = globalThis == null ? void 0 : globalThis.__federation_shared__) == null ? void 0 : _a[shareScope]) == null ? void 0 : _b[name]) {
      const versionObj = globalThis.__federation_shared__[shareScope][name];
      const requiredVersion = (_c = moduleMap[name]) == null ? void 0 : _c.requiredVersion;
      const hasRequiredVersion = !!requiredVersion;
      if (hasRequiredVersion) {
        const versionKey = Object.keys(versionObj).find((version) => satisfy(version, requiredVersion));
        if (versionKey) {
          const versionValue = versionObj[versionKey];
          module = await (await versionValue.get())();
        } else {
          console.log(`provider support ${name}(${versionKey}) is not satisfied requiredVersion(\${moduleMap[name].requiredVersion})`);
        }
      } else {
        const versionKey = Object.keys(versionObj)[0];
        const versionValue = versionObj[versionKey];
        module = await (await versionValue.get())();
      }
    }
    if (module) {
      return flattenModule(module, name);
    }
  };
  getSharedFromLocal = async function(name) {
    var _a;
    if ((_a = moduleMap[name]) == null ? void 0 : _a.import) {
      let module = await (await moduleMap[name].get())();
      return flattenModule(module, name);
    } else {
      console.error(`consumer config import=false,so cant use callback shared module`);
    }
  };
  function flattenModule(module, name) {
    if (typeof module.default === "function") {
      Object.keys(module).forEach((key) => {
        if (key !== "default") {
          module.default[key] = module[key];
        }
      });
      moduleCache[name] = module.default;
      return module.default;
    }
    if (module.default) module = Object.assign({}, module.default, module);
    moduleCache[name] = module;
    return module;
  }
})();
export {
  __tla,
  importShared,
  getSharedFromLocal as importSharedLocal,
  getSharedFromRuntime as importSharedRuntime
};
