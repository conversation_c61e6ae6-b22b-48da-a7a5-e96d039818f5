import { getDobuleScreenIframeWindow, openDobuleScreen } from "./storesUtils";

// 重写window方法
export const injectWindowFun = (funcName: string) => {
    const topWindow = window.top || window;
    // 点击业务类标签的时候，
    const originalFunc = (topWindow as any)[funcName];
    let timer: any = null;
    let iframeWindow: Window | null = null;

    (topWindow as any).openCtpWindow = (data: any) => {
        if (data?.url) {
            openDobuleScreen(data.url, 'iframe');
            timer = setInterval(() => {
                iframeWindow = getDobuleScreenIframeWindow();
                if (iframeWindow) {
                    clearInterval(timer);
                    (topWindow as any)[funcName] = originalFunc;
                    return iframeWindow;
                }
            }, 500);
        }
    };
};

