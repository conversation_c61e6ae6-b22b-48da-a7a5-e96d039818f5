import type { ChatMessage } from "@/types";
import { formatHistoryContent } from "./stream";


interface MessageFormatterOptions {
  assistantInfo?: Record<string, any>;
}

const isJSON = (str: string) => {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};

/**
 * 格式化用户消息
 */
const formatUserMessage = (item: ChatMessage) => ({
  _id: item.id,
  _t: item.messageTime,
  _index: item.index,
  componentKey: 'AiRequestCard',
  data: {
    isLoading: false,
    isError: false,
    isHistory: true,
    isPlay: false,
    status: 3
  },
  staticData: {
    citations: item.citationsJson && isJSON(item.citationsJson) ? JSON.parse(item.citationsJson) : [],
    message: item.content
  }
});

/**
 * 格式化 AI 回复消息
 */
const formatAIMessage = (item: ChatMessage, assistantInfo?: Record<string, any>) => ({
  _id: item.id,
  _t: item.messageTime,
  _index: item.index,
  componentKey: 'AiDataCard',
  data: {
    isLoading: false,
    isError: false,
    illageSessionType: false,
    isHistory: true,
    isPlay: false,
    isLast: true,
    isCompleted: true,
    messageId: item.id,
    isUnlike: false,
    cardData: item.cardData,
    isKnowledgeData: typeof item.citationsJson === 'string' && item.citationsJson,
    knowledgeData: item.citationList || [],
    assistantInfo: item.assistant,
    status: 3
  },
  staticData: {
    citations: item.citationsJson || '',
    message: item.content,
    requestParams: {
      assistantCode: item.assistantCode,
      assistantId: item.assistantId,
      chatSessionId: item.chatSessionId,
      citations: [],
      input: item.input || '',
      sessionType: item.sessionType,
    }
  }
});

/**
 * 格式化消息列表
 */
export const formatMessageList = (list: ChatMessage[], options: MessageFormatterOptions = {}) => {
  return list.map((item: ChatMessage, index: number) => {
    item.cardData = formatHistoryContent(item.content, item.citationsJson || '', item.messageType, item.hitKnowledgeRunSteps || [], item.needMarkBlueRunSteps || [], []);
    return item.messageType === 0
      ? formatUserMessage(item)
      : formatAIMessage(item);
  });
};