<template>
  <div class="smart-office-card" @click="handleClick">
    <div class="mb-[6px]">
      <div class="smart-office-card__title">
        <slot name="title">
          <span class="title-text text-lg">智能办公应用</span>
          <span class="title-icon">
            <!-- <Image :width="14" :src="ArrowIcon" :preview="false" /> -->
          </span>
        </slot>
      </div>
    </div>

    <div class="smart-office-card__body text-sm">
      <slot name="content">
        <div class="line-clamp-2">
          您的专属办公助理，发起流程、发起会议、查询待办等样样精通
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Image } from 'ant-design-vue';
import ArrowIcon from '@/assets/imgs/arrow.png';
import { inject } from 'vue';
import { useRedirectAssistant } from '../hooks';

const { redirectToAssistantByCode } = useRedirectAssistant();

const handleClick = async () => {
  // await redirectToAssistantByCode('assist1345764042842262481', true);
};

</script>

<style scoped lang="less">
.smart-office-card {
  border-radius: 12px;
  padding: 24px;
  // cursor: pointer;
  padding: 17px;
  height: 84px;
  background-image: url('@/assets/imgs/coll-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  &:hover {
    // box-shadow: 0px 0px 28px 0px #083EDD1F;
  }

  &__title {
    display: flex;
    align-items: center;
    gap: 9px;
    font-weight: 600;
    color: #1890ff;

    .title-text {
      background: linear-gradient(117.49deg, #4AEAFF -64.27%, #97F0FA -42.04%, #4FE1F7 -16.15%, #566BFF 67.99%, #4379FF 136.31%);
      background-clip: text;
      color: transparent;
    }

    .title-icon {
      text-align: right;
    }
  }

  &__body {
    line-height: 20px;
    color: rgba(0, 0, 0, 0.6);
  }

  .line-clamp-2 {
    line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
