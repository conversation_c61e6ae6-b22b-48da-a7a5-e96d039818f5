// 只过滤布尔条件
// 角色条件
import { useGlobal } from '@/stores/global';

function getIntersection<T>(arr1: T[], arr2: T[]): T[] {
  const set = new Set(arr2);
  return arr1.filter((item) => set.has(item));
}
function conditionCheck(node: any, children: [], index: number) {
  // 核心条件
  if (!node.delete && node.condition !== undefined) {
    if (node.condition === false) {
      // 从children 移除node
      node.delete = true;
    }
  }
  if (!node.delete && node.roles && node.roles.length) {
    const global = useGlobal();
    // 角色权限
    if (getIntersection(node.roles, global.globalState.roles).length == 0) {
      // 角色权限不通过
      node.delete = true;
    }
  }
  // 岗位判断
  if (!node.delete && node.posts && node.posts.length) {
    const global = useGlobal();
    // 角色权限
    if (getIntersection(node.posts, global.globalState.posts).length == 0) {
      // 角色权限不通过
      node.delete = true;
    }
  }
  // 岗位非判断
  if (!node.delete && node.excludePosts && node.excludePosts.length) {
    const global = useGlobal();
    // 角色权限
    if (getIntersection(node.excludePosts, global.globalState.posts).length > 0) {
      // 角色权限不通过
      node.delete = true;
    }
  }
  // 如果没有删除，需要递归处理
  if (!node.delete) {
    if (node.children && node.children.length > 0) {
      for (let j = 0; j < node.children.length; j++) {
        conditionCheck(node.children[j], node.children, j);
      }
      node.children = node.children.filter((el: any) => !el.delete);
    }
  }
}
// 预处理视图
function preprocessView(page: any): [] {
  for (let i = 0; i < page.views.length; i++) {
    conditionCheck(page.views[i], page.views, i);
    page.views = page.views.filter((el: any) => !el.delete);
  }
  return page.views;
}
function runEval(expr: string) {
  try {
    // TODO：实际项目中应避免直接使用 eval，建议使用 Function 或其他安全方式替代
    return eval(expr);
  } catch (error) {
    console.error('Error evaluating expression:', error);
    return false;
  }
}
// 自动处理condition条件
function parseCondition(children: any[]) {
  const global = useGlobal();
  // 浅拷贝
  children = children ? children.slice() : [];
  for (let i = 0; i < children.length; i++) {
    const node = children[i];
    if (node.condition && typeof node.condition === 'object') {
      if (node.condition.eval) {
        const status = runEval.apply({ global }, [node.condition.eval]);
        if (!status) {
          children.splice(i, 1);
          i--;
        }
      }
    }
  }
  // 删除节点
  return children;
}
// 获取 copilot
function getCopilotNode(children: any[], list?: any[]) {
  const res = list || [];
  const authChildren = parseCondition(children);
  for (let i = 0; i < authChildren.length; i++) {
    const node = authChildren[i]; 
    if (node.copilot === '1') {
      res.push(node);
    }
    if (node.children && node.children.length > 0) {
      getCopilotNode(node.children, res);
    }
  }
  return res
}

function findNodeByType(
  layoutConfig: any,
  type: string,
  options: {
    /** 是否查找第一个匹配的节点，默认为true */
    findFirst?: boolean;
    /** 是否查找所有匹配的节点，默认为false */
    findAll?: boolean;
    /** 是否包含完整的节点路径，默认为false */
    includePath?: boolean;
  } = {}
): any {
  const { findFirst = true, findAll = false, includePath = false } = options;

  if (!layoutConfig || typeof layoutConfig !== 'object' || !type) {
    return findAll ? [] : null;
  }

  const results: any[] = [];
  const stack: Array<{ node: any; path: string[] }> = [{ node: layoutConfig, path: [] }];
  while (stack.length > 0) {
    const { node: current, path } = stack.pop()!;

    // 检查当前节点是否匹配
    if (current.type === type) {
      const result = includePath
        ? { node: current, path: [...path, current.type || 'root'] }
        : current;

      if (findFirst && !findAll) {
        return result;
      }

      if (findAll) {
        results.push(result);
      }
    }

    // 如果有children属性且是数组，将子节点加入栈（逆序添加保持遍历顺序）
    // 并且children要有权限
    const authChildren = parseCondition(current.children);
    if (authChildren.length) {
      // 判断条件
      for (let i = current.children.length - 1; i >= 0; i--) {
          const child = current.children[i];
          const childPath = [...path, current.type || 'root'];
          stack.push({ node: child, path: childPath });
      }
    }
  }

  return findAll ? results : null;
}
export { preprocessView, parseCondition, getCopilotNode, findNodeByType };
