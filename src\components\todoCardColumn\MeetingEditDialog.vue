<template>
  <Modal
    v-model:open="visible"
    title="会议编辑"
    :width="615"
    :destroyOnClose="true"
    wrapClassName="meeting-edit-modal"
    @cancel="handleCancel"
  >
    <template #footer>
      <div class="flex justify-end gap-2">
        <Button @click="handleCancel">取消</Button>
        <Button
          type="primary"
          @click="handleOk"
          :loading="isLoading"
        >确定</Button>
      </div>
    </template>

    <div class="meeting-edit-content">
      <iframe
        v-if="visible && iframeUrl"
        :src="iframeUrl"
        frameborder="0"
        class="w-full h-[470px] border-0 rounded"
      />
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Modal, Button } from 'ant-design-vue';

interface Props {
  open?: boolean;
  meetingId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  meetingId: ''
});

const emit = defineEmits<{
  'update:open': [visible: boolean];
  'ok': [];
  'cancel': [];
  'refresh': [];
}>();

// 响应式状态
const visible = ref(false);
const iframeUrl = ref('');
const isLoading = ref(false);

// 监听外部传入的open状态
watch(() => props.open, (newOpen) => {
  visible.value = newOpen;
  if (newOpen && props.meetingId) {
    generateIframeUrl(props.meetingId);
  }
}, { immediate: true });

// 监听内部visible状态变化，同步到外部
watch(visible, (newVisible) => {
  emit('update:open', newVisible);
  if (!newVisible) {
    // 弹窗关闭时清空URL
    iframeUrl.value = '';
  }
});

// 监听meetingId变化
watch(() => props.meetingId, (newMeetingId) => {
  if (newMeetingId && visible.value) {
    generateIframeUrl(newMeetingId);
  }
});

/**
 * 获取CSRF后缀
 */
const getCsrfSuffix = (): string => {
  try {
    const topWindow = (window as any).top || window;
    return topWindow.CsrfGuard?.getUrlSurffix?.() || '';
  } catch (error) {
    console.warn('获取CSRF后缀失败:', error);
    return '';
  }
};

/**
 * 生成iframe URL
 */
const generateIframeUrl = (meetingId: string) => {
  if (!meetingId) {
    console.error('meetingId不能为空');
    return;
  }

  // 构建会议编辑URL
  const ctxPath = (window as any)._ctxPath || '/seeyon';
  const csrfSuffix = getCsrfSuffix();
  iframeUrl.value = `${ctxPath}/apps_res/meetingintegrate/instantMeeting/html/appointmentMeeting.html?meetingId=${meetingId}&r=${csrfSuffix}&isEdit=true`;

  console.log('会议编辑弹窗URL:', iframeUrl.value);
};

/**
 * 处理确认操作
 */
const handleOk = async () => {
  try {
    isLoading.value = true;

    // 获取iframe元素
    const iframe = document.querySelector('.meeting-edit-modal iframe') as HTMLIFrameElement;

    if (iframe && iframe.contentWindow) {
      const iframeWindow = iframe.contentWindow as any;

      // 检查iframe是否有OK方法
      if (typeof iframeWindow.OK === 'function') {
        console.log('调用iframe内部的OK方法');

        // 定义回调函数，iframe保存完成后会调用
        const callBk = (result?: any) => {
          console.log('iframe保存完成，回调被调用:', result);

          // 触发刷新事件
          emit('refresh');

          // 触发确认事件
          emit('ok');

          // 关闭弹窗
          visible.value = false;
          isLoading.value = false;
        };

        // 调用iframe内部的OK方法，传递回调函数
        const result = iframeWindow.OK({ callBk });

        // 如果有保存页面消息方法，调用它
        if (result && result.savePageMesage) {
          result.savePageMesage();
        }

      } else {
        console.warn('iframe内部没有找到OK方法，直接刷新数据');
        // 如果没有OK方法，直接刷新并关闭
        emit('refresh');
        emit('ok');
        visible.value = false;
        isLoading.value = false;
      }
    } else {
      console.warn('没有找到iframe或无法访问iframe内容');
      // 找不到iframe时的降级处理
      emit('refresh');
      emit('ok');
      visible.value = false;
      isLoading.value = false;
    }
  } catch (error) {
    console.error('调用iframe OK方法失败:', error);
    // 出错时的降级处理
    emit('refresh');
    emit('ok');
    visible.value = false;
    isLoading.value = false;
  }
};

/**
 * 处理取消操作
 */
const handleCancel = () => {
  console.log('取消会议编辑');
  emit('cancel');
  visible.value = false;
};

/**
 * 打开会议编辑弹窗
 */
const openDialog = (meetingId: string) => {
  if (!meetingId) {
    console.error('meetingId不能为空');
    return;
  }

  generateIframeUrl(meetingId);
  visible.value = true;
};

// 暴露方法给父组件
defineExpose({
  openDialog
});
</script>

<style scoped>
.meeting-edit-content {
  padding: 0;
}

/* 确保iframe完全填充容器 */
.meeting-edit-content iframe {
  display: block;
}
</style>