import { get, post, postUpload } from '../config.js';
import type { TypeCHeckAssistParams, TypeUploadFileParams, TypeOperateAnswerParams, TypeCancelChatParams } from '@/types/api.ts';
import { fetchEventSource } from '@microsoft/fetch-event-source';

// 助手列表
export const getAssistantList = (data: TypeCHeckAssistParams) => {
  return post(`ai-manager/assistant/info/app/page`, data);
};

// 上传文件-单一
export const toUploadFile = (data: any, config: object) => {
  return postUpload(`ai-manager/agent/file/upload`, data, config);
};

// 上传文件-多个
export const toUploadMutyFile = (data: any, config: object) => {
  return postUpload(`ai-manager/agent/file/upload-files`, data, config);
}

// 助手会话sse
export const getToChatByAssist = () => {
  return `ai-manager/assistant/info/call/stream`;
};

// 取消访问接口
export const cancelChat = (data: TypeCancelChatParams) => {
  return post(`ai-manager/assistant/info/call/cancel`, data)
};

// 助手开场白
export const getAssistIntroduce = (id: string) => {
  return get(`ai-manager/assistant/info/${id}/prologue`);
};

// 点赞  点踩
export const toChangeAnswerStatus = (data: TypeOperateAnswerParams) => {
  return post(`ai-manager/assistant/info/evaluate`, data);
};

// 通用助手列表
export const getGeneralAssist = () => {
  return get(`ai-manager/assistant/info/type/general`);
};

// 采集复制-重新生成的接口
export const cateAsistantLogs = (data: any) => {
  return post(`ai-manager/assistant/info/regenerateAssistant`, data)
}

// 获取当前对话的sessionId，主要防止立即中断，没返回sessionId,再次对话会形成一个新的对话
export const getSessionId = (data: any) => {
  return post(`ai-manager/assistant/info/getCurrentSessionId`, data)
}

// 统一 agent 请求
export const getAgentResult = (data: any) => {
  return post(`rest/comi-agent/call-agent`, data, {})
}

// 获取流式助手请求
export const getStreamAgentResult = (params: any, callback: any,errorCallback: any) => {
  const url = `/seeyon/ai-platform/ai-manager/assistant/info/call/stream`
  return fetchEventSource(url, {
        openWhenHidden: true,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'channel': 'comi_intelligent_portal',
        },
        // signal: this.controller.signal,
        body: JSON.stringify(params),
        onopen: async (e: any) => {
          // this.authExpired = false;

        },
        onmessage: (data: any) => {
          if (typeof data.data === 'string') {
            data.data = JSON.parse(data.data);
          }
          const sourceData: any = data.data;
          let context = "";
          if(sourceData.messageType == 5){
            let content = sourceData.content;
            if (typeof content === 'string' && content) {
                try {
                  content = JSON.parse(content);
                  context = content.choices[0].delta.content;
                } catch (error) {
                  console.log('error', error);
                  context = "";
                }
            }
          }
          console.log('onmessage', context);
          callback(context);
        },
        onclose: () => {

        },
        onerror: (err: any) => {
          errorCallback(err)
        },
      })
}
