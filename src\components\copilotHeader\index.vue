<template>
  <div class="menus">
    <div
      class="menu_item"
      v-for="(itm, index) in menus"
      :key="index"
      :class="{ active: index === 0 }"
      @click="menuAction(itm, index)"
    >
      <Tooltip :title="itm.name" placement="bottomRight">
        <i :class="`iconfont ${itm.icon} ass_icon`"></i>
      </Tooltip>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useChatList } from '@/stores/chatList';
import { useGlobal, useMenu } from '@/stores/global';
import { commonBusinessClass } from '@/utils/commonBusinessClass';
import { Tooltip } from 'ant-design-vue';
import { nextTick, ref, inject } from 'vue';

const sdkInstance = inject('sdkInstance') as any;
type MenuType = {
  icon: string;
  name: string;
  action: string;
};
const uGlobal = useGlobal();
const uChatList = useChatList();
const uMenu = useMenu();
const expandItem: MenuType = {
  icon: 'ai-icon-fangda',
  name: '展开全屏',
  action: 'expand',
};
const collapseItem: MenuType = {
  icon: 'ai-icon-suoxiao',
  name: '退出全屏',
  action: 'collapse',
};
const closeItem1 = {
  icon: 'ai-icon-cha',
  name: '关闭侧边栏',
  action: 'close',
};

const menus = ref<MenuType[]>([]);
// if (uGlobal.globalState.isFullScreen) {
//   menus.value.unshift(collapseItem, closeItem1);
// } else {
//   menus.value.unshift(expandItem, closeItem1);
// }
menus.value.unshift(closeItem1);
const menuAction = (itm: MenuType, index: number) => {
  if (itm.action === 'expand') {
    uGlobal.changeState('isFullScreen', true);

    // 如果有双屏数据，则展开的时候，打开双屏数据
    if (uChatList.dynamicData.dobuleScreenData.data) {
      const data = {
        ...uChatList.dynamicData.dobuleScreenData,
        show: true,
      };
      useChatList().chatActions.setDynamicData('dobuleScreenData', data);
    }
  }
  if (itm.action === 'collapse' || itm.action === 'close') {
    // 全屏设置/关于/智能创作的时候，缩小、关闭，定位到超级comi
    const needRedirect = ['setting', 'about', 'create'];
    if (needRedirect.includes(uMenu.currentMenuInfo.id)) {
      uMenu.changeMenu({
        id: 'comi',
      });
    }

    // 如果全屏知识源，缩小侧边栏的时候，关闭全屏知识源
    if (uChatList.dynamicData.dobuleScreenIsKnowledge) {
      uChatList.chatActions.setDynamicData('dobuleScreenIsKnowledge', false);
      uChatList.chatActions.setDynamicData('dobuleScreenIsFull', false);
    }
    uGlobal.changeState('isFullScreen', false);

    // 收起的时候，只处理显示，不清除数据
    const dobuleScreenData = {
      ...uChatList.dynamicData.dobuleScreenData,
      show: false,
    };
    uChatList.chatActions.setDynamicData('dobuleScreenData', dobuleScreenData);
  }
  nextTick(() => {
    try {
      // 直接调用对应的功能方法，而不是通过SDK
      const businessClass = commonBusinessClass();
      console.log(`尝试调用功能方法: ${itm.action}`, businessClass);

      if (!businessClass) {
        console.error('业务类实例不可用');
        return;
      }

      if (typeof businessClass[itm.action] === 'function') {
        console.log(`执行功能方法: ${itm.action}`);
        // 直接在对象上调用方法，保持 this 上下文
        businessClass[itm.action]();
      } else {
        console.warn(`功能方法 ${itm.action} 不存在或不是函数`);

        // 尝试调试信息
        console.log(
          '可用的方法:',
          Object.getOwnPropertyNames(businessClass).filter(
            (name) => typeof businessClass[name] === 'function',
          ),
        );
      }
    } catch (error) {
      console.error(`调用功能方法 ${itm.action} 时出错:`, error);
    }
  });
};
sdkInstance.bind('menuAction', menuAction);
</script>
<style scoped lang="less">
.ass_icon {
  cursor: pointer;
}
.menus {
  display: flex;
  justify-content: space-around;
  .menu_item {
    cursor: pointer;
    margin-left: 16px;
  }
  .ass_icon {
    font-size: 20px;
    color: #8e94a2;
  }
}
</style>
