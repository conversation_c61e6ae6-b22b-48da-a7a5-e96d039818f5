<template>
  <div class="about-wrap" :class="{ 'about-wrap-small': !isPortal }">
    <div class="about-content" v-if="isPortal">
      <div class="logo">
        <img src="@/assets/imgs/comi-logo.png" alt="" class="ip_logo" />
        <img src="@/assets/imgs/fullscreen_logo.png" class="comi_logo" />
      </div>
      <div class="desc">Collaborative Operating with me (人智协同)</div>
      <div class="content">
        <template v-for="item in renderData" :key="item.key">
          <div class="item">
            <div class="title">{{ item.title }}</div>
            <div class="value">{{ item.value }}</div>
          </div>
        </template>
      </div>

      <div class="footer">
        Copyright@ {{ getCurrentYear() }} 北京致远互联软件股份有限公司.版权所有
      </div>
    </div>
    <div class="about-content-small" v-else>
      <div class="content">
        <div class="logo">
          <img src="@/assets/imgs/comi-logo.png" alt="" class="ip_logo" />
          <img src="@/assets/imgs/fullscreen_logo.png" class="comi_logo" />
          <span v-if="version">版本：{{ version }}</span>
        </div>
        <div class="desc">Collaborative Operating with me(人智协同）</div>
        <div class="item-wrapper">
          <template v-for="item in renderData" :key="item.key">
            <div class="item">
              <div class="title">{{ item.title }}</div>
              <div class="value">{{ item.value }}</div>
            </div>
          </template>
        </div>
      </div>
      <div class="footer">
        Copyright@ {{ getCurrentYear() }} 北京致远互联软件股份有限公司.版权所有
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getAboutInfo, getSystemBuildId, getComiLicenseInfo } from '@/api/comi';
const isPortal = inject('isPortal');

const aboutInfo = reactive<any>([
  {
    key: 'version',
    value: '',
    title: '版本：',
  },
  {
    key: 'comiBuilderCount',
    value: '',
    title: 'Builder并发数：',
  },
  {
    key: 'comiSuperEntryCount',
    value: '',
    title: '超级入口并发数：',
  },
  {
    key: 'buildId',
    value: '',
    title: 'Build Id（from comi-builder）：',
  },
  {
    key: 'comiServerBuildId',
    value: '',
    title: 'Build Id（from comi-serve）：',
  },
  // {
  //   key: 'edition',
  //   value: '',
  //   title: '版本类型：',
  // },
  // {
  //   key: 'oaVersion',
  //   value: '',
  //   title: 'OA版本：',
  // },
]);

const version = computed(() => {
  return aboutInfo.find((item: any) => item.key === 'version')?.value;
});

const renderData = computed(() => {
  console.log(aboutInfo);
  if (isPortal) {
    return aboutInfo;
  } else {
    return aboutInfo.filter((item: any) => item.key !== 'version');
  }
});

const getCurrentYear = () => {
  return new Date().getFullYear();
};

const getAbout = async () => {
  const res: any = await getAboutInfo();
  if (res.code == '0' && res.data) {
    aboutInfo.value = res.data;
  }
};

const getBuild = async () => {
  const res: any = await getSystemBuildId();
  if (res.code == '0' && res.data) {
    console.log(res);
    aboutInfo.forEach((item: any) => {
      if (res.data[item.key]) {
        item.value = res.data[item.key];
      }
    });
  }
};
const getLicense = async () => {
  const res: any = await getComiLicenseInfo();
  if (res.code == '0' && res.data) {
    console.log(res);
    aboutInfo.forEach((item: any) => {
      // 过滤掉info中得version，那是oa得版本
      if (item.key === 'oaVersion') {
        item.value = res.data?.version;
      } else if (res.data[item.key] && item.key !== 'version') {
        item.value = res.data[item.key];
      }
    });
  }
};

onMounted(async () => {
  // getAbout();
  await getBuild();
  await getLicense();
});
</script>

<style lang="less" scoped>
@common-font: {
  font-weight: @font-weight-400;
  font-size: 14px;
  line-height: 22px;
  font-family: PingFang SC;
};
.about-wrap {
  height: 100%;
  width: 100%;
  background-color: #ffffffbf;
  display: flex;
  align-items: center;
  padding: 0px 16px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;

  .about-content {
    // background-color: #fff;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 12px;
    padding: 15.5px 0;

    .logo {
      display: flex;
      align-items: center;
      gap: 13px;

      .ip_logo {
        height: 70px;
      }
      .comi_logo {
        height: 30px;
      }
    }
    .desc {
      font-weight: @font-weight-500;
      font-size: 16px;
      line-height: 24px;
      margin-top: 12px;
      font-family: PingFang SC;
    }
    .content {
      width: 460px;
      height: 110px;
      margin: 40px 0;

      .item {
        display: flex;
        .title {
          text-align: right;
          color: #00000099;
          width: 250px;
          text-align: right;
          @common-font();
        }
        .value {
          color: #000000e5;
          flex: 1;
          word-break: break-all;
          @common-font();
        }
      }
    }
    .footer {
      @common-font();
    }
  }

  &.about-wrap-small {
    padding: 0;
    background: transparent;
    padding: 0 28px;
    padding-bottom: 52px;

    .content {
      margin: 0;
      padding: 0;
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .about-content-small {
      width: 100%;
      height: 100%;
      flex-direction: column;
      justify-content: space-between;
      display: flex;

      .logo {
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-between;
        gap: 13px;

        .ip_logo {
          height: 62px;
        }
        .comi_logo {
          height: 24px;
          margin-top: 16px;
          margin-bottom: 4px;
        }
      }
      .desc {
        margin-top: 16px;
        margin-bottom: 46px;
        @common-font();
        text-align: center;
      }
      .item-wrapper {
        width: 100%;
        .item {
          margin-bottom: 16px;
          &:last-child {
            margin-bottom: 0;
          }
          .title {
            @common-font();
            color: #00000066;
          }
          .value {
            @common-font();
          }
        }
      }
      .footer {
        padding: 0 44px;
        font-family: PingFang SC;
        font-weight: @font-weight-400;
        font-size: 12px;
        line-height: 20px;
        color: #00000066;
        text-align: center;
      }
    }
  }
}
</style>
