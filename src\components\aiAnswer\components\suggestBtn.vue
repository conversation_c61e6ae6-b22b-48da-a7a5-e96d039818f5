<template>
  <span
    :class="{ down_btn: true, ud_ac_btn: isWhichActive == 1, suggest_btn: true }"
    @click="goPointThis(1)"
  >
    <Tooltip title="意见反馈" v-if="isWhichActive == 1"
      ><i class="iconfont ai-icon-diancai-mian down_thumb"></i
    ></Tooltip>
    <Tooltip title="意见反馈" v-else>
      <i class="iconfont ans_font ai-icon-diancai-xian down_thumb"></i
    ></Tooltip>
  </span>
</template>

<script lang="ts" setup>
import { Tooltip } from 'ant-design-vue';
const props = defineProps<{
  isWhichActive: undefined | number;
  goPointThis: (type: 0 | 1) => void;
}>();
</script>
<style lang="less" scoped>
.suggest_btn {
  .iconfont {
    color: rgba(0, 0, 0, 0.4);
    cursor: pointer;
  }
  .ai-icon-diancai-mian {
    color: @sky;
  }
}
</style>
