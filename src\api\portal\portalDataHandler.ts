import { getPortalInfo } from './index';
import type {
  RowNode,
  BaseResponse
} from '@/types/portalTypes';


export const fetchAndFillPortalData = async (chatSessionId: string, callback?: (index: number, summaryText: string) => void): Promise<BaseResponse<RowNode> | null> => {
  try {
    // 获取 portal 信息
    const portalRes = await getPortalInfo() as unknown as Promise<BaseResponse<RowNode>>;

    const { code, data } = await portalRes;
    if (code === 0 && data) {
      // 现在会议数据和待办数据都由组件内部自己获取，这里只返回布局配置
      return {
        code: 0,
        data: data
      } as unknown as BaseResponse<RowNode>;
    }
    return {
      code:0,
      data:{}
    } as unknown as  BaseResponse<RowNode>;
  } catch (error) {
    console.error('获取门户数据失败:', error);
    return {
      code:500,
      data:{}
    } as unknown as  BaseResponse<RowNode>;
  }
};
