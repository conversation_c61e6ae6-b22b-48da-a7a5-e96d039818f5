import { get, post } from '../config.js';
import { generateSecure16DigitNumber } from '@/utils/uuid';
import {getAgentResult} from '@/api/common/index';
const ctxPath = '/seeyon';

// 新的分类数据接口类型 - 匹配API返回的实际结构
export interface ClassificationItem {
  portletParams: string;        // JSON字符串，包含各种参数
  isPortlet: string;           // "1" 表示是portlet
  predicate: boolean;          // 断言条件
  total: string;               // 总数
  appId: string;               // 应用ID
  classificationName: string;   // 分类名称
  isHide: string;              // "1" 表示隐藏
  // 可选字段
  subAppId?: string;
  isThird?: string;
  isLabel?: string;
  canDrag?: string;
}

// 分类数据响应接口
export interface ClassificationResponse {
  data: {
    navData: ClassificationItem[];
    threshold?: number;
  };
  success: boolean;
  message?: string;
}

// 协同紧急待办列表
export const getUrgentPendingList = () => {
  return get(`${ctxPath}/rest/seeyon-ai/comi/pending/urgent?option.n_a_s=1`, {}, true);
};

// 获取协同待办分类数据
export const getPendingClassification = () => {
  return get(`${ctxPath}/rest/seeyon-ai/comi/pending-section/classificationAll?option.n_a_s=1`,{},true);
};

// 根据分类获取待办数据
export const getPendingDataByCategory = (pageNumber: number = 1, pageSize: number = 20, portletParams?: string) => {
  // 解析portletParams参数
  let requestParams = {};
  if (portletParams) {
    try {
      // 如果portletParams是JSON字符串，解析它
      requestParams = typeof portletParams === 'string' ? JSON.parse(portletParams) : portletParams;
    } catch (error) {
      console.warn('解析portletParams失败:', error);
      requestParams = {};
    }
  }

  return post(`${ctxPath}/rest/seeyon-ai/comi/pending-section/portlet/${pageNumber}/${pageSize}?option.n_a_s=1`, requestParams, {}, true);
};

// 获取待办数量统计
export const getPendingCount = (sectionBeanId: string, condition?: string) => {
  const requestParams = {
    sectionBeanId: 'pendingSection',
    condition: condition || ''
  };

  return post(`${ctxPath}/rest/seeyon-ai/comi/pending-section/count?option.n_a_s=1`, requestParams, {}, true);
};

// 查询我的待开会议
export const getMyPendingMeeting = () => {
  return post(`${ctxPath}/rest/seeyon-ai/comi/query/data?option.n_a_s=1`, {
    "type": "meeting",
    "listType": "pending",
  }, {}, true);
};

// 一键处理待办接口
export const autoRunPending = (params: {
  importLevelList: number[];
  batchIdList: string[];
  count: number;
}) => {
  return post(`${ctxPath}/rest/app/affair/autoRun/run?option.n_a_s=1`, params, {}, true);
};

// 获取智能预审摘要信息
export const getIntelligentReviewSummary = async () => {
  return post(`${ctxPath}/rest/seeyon-ai/comi/collaboration/simple/query?option.n_a_s=1`, {
    "nodePolicy": "inform",
    "limit": "999",
  }, {}, true);
};

// 信息提取接口
export const extractPendingInfo = (pendingIds: string[]) => {
  const chatSessionId = generateSecure16DigitNumber();

  return getAgentResult({
    "chatSessionId": chatSessionId,
    "input": pendingIds,
    "agentCode": "pending_info_extract"
  });
};

/**
 * 获取待办分类列表
 */
export const getClassificationAll = async (): Promise<ClassificationResponse> => {
  try {
    const response: any = await get('rest/seeyon-ai/comi/pending-section/classificationAll');
    return {
      data: response.data || { navData: [] },
      success: response.success !== false,
      message: response.message
    };
  } catch (error) {
    console.error('获取分类数据失败:', error);
    return {
      data: { navData: [] },
      success: false,
      message: '获取分类数据失败'
    };
  }
};

/**
 * 获取待办数量统计
 */
export const getTodoCount = async (sectionBeanId: string, condition?: string) => {
  try {
    const requestParams = {
      sectionBeanId,
      condition: condition || ''
    };
    const response: any = await post('rest/seeyon-ai/comi/pending-section/count', requestParams);
    return {
      count: response.data || 0,
      success: response.success !== false,
      message: response.message
    };
  } catch (error) {
    console.error('获取待办数量失败:', error);
    return {
      count: 0,
      success: false,
      message: '获取待办数量失败'
    };
  }
};

/**
 * 根据分类获取待办数据
 */
export const getTodoDataByCategory = async (categoryKey: string) => {
  try {
    const response: any = await get(`api/todo/category/${categoryKey}`);
    return response.data || [];
  } catch (error) {
    console.error('获取分类数据失败:', error);
    return [];
  }
};

/**
 * 置顶待办
 */
export const setPendingTop = async (affairId: string) => {
  const params = new URLSearchParams();
  params.append('managerMethod', 'toTheTop');
  params.append('arguments', JSON.stringify([{ affairId: affairId, isTop: 'true' }]));
  return fetch('/seeyon/ajax.do?method=ajaxAction&managerName=pendingManager&nn=toTheTop', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: params
  }).then(res => res.json());
};

/**
 * 取消置顶待办
 */
export const cancelPendingTop = async (affairId: string) => {
  const params = new URLSearchParams();
  params.append('managerMethod', 'toTheTop');
  params.append('arguments', JSON.stringify([{ affairId: affairId, isTop: 'false' }]));
  return fetch('/seeyon/ajax.do?method=ajaxAction&managerName=pendingManager&nn=toTheTop', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: params
  }).then(res => res.json());
};

// 常用语缓存
let phrasesCache: any[] | null = null;
let phrasesCacheTime: number = 0;
const PHRASES_CACHE_DURATION = 10 * 60 * 1000; // 10分钟缓存

/**
 * 获取常用语列表（带缓存）
 */
export const getAllPhrases = async () => {
  const now = Date.now();
  
  // 检查缓存是否有效
  if (phrasesCache && (now - phrasesCacheTime) < PHRASES_CACHE_DURATION) {
    console.log('使用常用语缓存数据');
    return phrasesCache;
  }
  
  const params = new URLSearchParams();
  params.append('managerMethod', 'getAllPhrases');
  params.append('arguments', JSON.stringify([]));
  
  try {
    console.log('请求常用语接口数据');
    const response = await fetch(`${ctxPath}/ajax.do?method=ajaxAction&managerName=phraseManager`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cache-Control': 'no-cache'
      },
      body: params
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    // 更新缓存
    phrasesCache = data || [];
    phrasesCacheTime = now;
    
    return phrasesCache;
  } catch (error) {
    console.error('获取常用语失败:', error);
    // 如果请求失败但有缓存数据，返回缓存数据
    if (phrasesCache) {
      console.log('接口失败，使用缓存的常用语数据');
      return phrasesCache;
    }
    return [];
  }
};

/**
 * 清除常用语缓存（可在用户添加、删除常用语后调用）
 */
export const clearPhrasesCache = () => {
  phrasesCache = null;
  phrasesCacheTime = 0;
  console.log('常用语缓存已清除');
};
