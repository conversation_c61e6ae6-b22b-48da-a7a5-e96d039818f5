<template>
  <div class="card" @click="handleItem">
    <div class="avatar" v-if="dataInfo.avatarUrl">
      <Avatar class="avatar-img" :class="styles?.avatarUrl?.class" :style="styles?.avatarUrl?.style" :src="dataInfo.avatarUrl" />
    </div>
    <div class="info-wrap">
      <div class="info-item">
        <div class="title" :class="styles?.title?.class" :style="styles?.title?.style">{{ dataInfo.title }}</div>
        <CommonTag :content="dataInfo.tag" :style="styles?.tag?.style" :iconClass="styles?.tag?.class" />
      </div>
      <div class="description-box">
        <div style="margin-bottom: 2px;">
          <span class="description" :class="styles?.description?.class" :style="styles?.description?.style" ref="depart" :title="dataInfo.description">{{ dataInfo.description || '描述描述描述' }}</span>
        </div>
        <div>
          <span class="sub-description" :class="styles?.subDescripiton?.class" :style="styles?.subDescripiton?.style" :title="dataInfo.subDescripiton">{{ dataInfo.subDescripiton || '子描述子描述子描述子描述' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, type PropType } from 'vue';
import { Avatar } from 'ant-design-vue';
import CommonTag from '../common-tag.vue';
import { CardItem } from '@/components/commonCard/types'

interface RenderInfo {
  id?: string;
  title?: string;
  avatarUrl?: string;
  description?: string;
  subDescripiton?: string;
  tag?: string;
}

const props = defineProps({
  item: {
    type: Object as PropType<CardItem>,
    default: () => {}
  },
  styles: {
    type: Object as PropType<any>,
    default: () => {}
  }
})

const dataInfo: RenderInfo = props.item.renderInfo


const handleItem = () => {
  // todo 中间层回调函数
};
</script>

<style lang="less" scoped>
.query-list .query-item:hover {
  background: @gray-bg !important;
  .card {
    background: @gray-bg;
  }
}
.card {
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  cursor: pointer;
  padding: 8px;
  margin-top: 8px;
  border-radius: 4px;
  background: #fafafa;

  &:hover {
    background: @primary-bg;
  }

  &:hover .title {
    color: #4379FF;
  }
}
.avatar {
  .avatar-img {
    margin-right: 10px;
    width: 36px;
    height: 36px;
    object-fit: cover;
    border-radius: 50%;
    background-color: #E3E3E3;
  }
}
.info-wrap {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}
.title {
  font-weight: @font-weight-500;
  font-size: 14px;
  color: #000000;
  line-height: 22px;
}
.description,
.sub-description {
  font-weight: @font-weight-400;
  font-size: 14px;
  color: @gray-color;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
