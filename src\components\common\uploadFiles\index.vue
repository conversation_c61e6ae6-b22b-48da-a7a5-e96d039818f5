<template>
  <div
    :class="{
      files_list: true,
      no_background: !showBackground,
      has_margin: hasMargin,
    }"
  >
    <!-- 文件列表 -->
    <div v-if="props.fileList && props.fileList.length > 0">
      <div
        class="files_item"
        v-for="(itm, inx) in props.fileList"
        :key="inx"
        :style="{ cursor: !showDeleteBtn ? 'pointer' : 'default' }"
      >
        <div class="item_shirt" @click="() => goDownFile(itm)">
          <Image
            v-if="itm.fileIcon?.type === 'img'"
            :width="36"
            :height="36"
            :src="itm.fileIcon?.icon"
            :preview="false"
            class="file_img"
          />
          <FileIcon class="file_icon" v-else :iconClass="itm.fileIcon?.icon" :size="36" />

          <div class="file_info">
            <div class="file_name ellipsis">
              {{ itm.name }}
            </div>
            <div class="file_size" v-if="!itm.isUploading">
              <span>{{ getFileType(itm.name) }}，</span>
              <span> {{ getfilesize(itm.size) }}</span>
            </div>
            <div class="uploading" v-else>解析中...</div>
          </div>

          <div
            class="close_btn"
            @click="goDelFile(itm, inx)"
            v-show="!itm.isUploading && showDeleteBtn"
          >
            <img :width="12" :height="12" :src="ClosePic" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="uploadFiles">
import type { PropType } from 'vue';
import { watch } from 'vue';
import ClosePic from '@/assets/imgs/close.png';
import { getfilesize, getFileType, downloadFile, getFileIcon } from '@/utils/tool';
import FileIcon from '@/components/fileIcon/index.vue';
import { Image } from 'ant-design-vue';
const props = defineProps({
  showBackground: {
    type: Boolean,
    default: false,
  },
  fileList: {
    type: Array as PropType<
      Array<{
        url: string;
        name: string;
        size: number;
        isUploading?: boolean;
        fileIcon?: {
          icon: string;
          type: string;
        };
      }>
    >,
    default: () => [],
  },
  showDeleteBtn: {
    type: Boolean,
    default: true,
  },
  hasMargin: {
    type: Boolean,
    default: false,
  },
});

// 处理文件icon的函数
const processFileIcons = (fileList: any[]) => {
  fileList?.forEach((item) => {
    if (!item.fileIcon) {
      const fileType = getFileType(item.name);
      item.fileIcon = getFileIcon(fileType || 'unknown');
    }
  });
};

// 初始化时处理文件图标
if (props?.fileList) {
  processFileIcons(props.fileList);
}

// 监听 fileList 变化，重新处理文件图标
watch(
  () => props.fileList,
  (newFileList) => {
    if (newFileList) {
      processFileIcons(newFileList);
    }
  },
  { deep: true }
);

const $emit = defineEmits(['delFile']);
// 删除文件 取消上传
const goDelFile = (itm: any, inx: number) => {
  $emit('delFile', itm, inx);
};
// 下载已经上传的文件
const goDownFile = (itm: any) => {
  if (!props.showDeleteBtn) {
    const url = '/seeyon/ai-platform/' + itm.fileRelativeUrl;
    downloadFile(url, itm.name);
  }
};


</script>
<style lang="less" scoped>
.files_list {
  max-width: 100%;
  .files_item {
    max-width: 100%;
    height: 48px;
    padding: 4px 4px 4px 8px;
    border-radius: 8px;
    background: #fff;
    position: relative;
    display: inline-block;
    vertical-align: middle;
    align-items: center;
    border: 1px solid #EDEDF1;

    .file_icon {
      margin-top: -2px;
    }
    .item_shirt {
      display: flex;
      height: 100%;
      width: 100%;
      // align-items: center;
      .file_img,.file_icon {
        width: 36px;
      }
      .spin_box {
        width: 36px;
        position: relative;
        display: flex;
        align-items: center;
      }
      .file_info {
        margin-left: 4px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        flex: 1;
        min-width: 0;
        margin-right: 8px;
        .file_name {
          //styleName: 12px/Regular;
          max-width: 100%;
          font-family: PingFang SC;
          font-size: 12px;
          font-weight: @font-weight-400;
          text-align: left;
          line-height: 20px;
          height: 20px;
          color: #000;
          align-items: center;
        }
        .file_size {
          height: 20px;
          //styleName: 12px/Regular;
          font-family: PingFang SC;
          font-size: 12px;
          font-weight: @font-weight-400;
          line-height: 20px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: rgba(0, 0, 0, 0.4);
        }
        .uploading {
          font-family: PingFang SC;
          font-weight: @font-weight-400;
          font-size: 12px;
          line-height: 20px;
          letter-spacing: 0%;
          color: #4379ff;
        }
      }
    }
    .close_btn {
      // position: absolute;
      // right: 0px;
      // top: 50%;
      // transform: translateX(6px) translateY(-5px);
      cursor: pointer;
    }
  }
}
.no_radius {
  border-radius: 0px;
}
.no_background {
  background: none;
  padding: 0;
}
.has_margin {
  margin: 8px 0;
}
</style>
