<template>
  <div class="relative flex justify-center w-full h-full">
    <div class="flex flex-col h-full new_dlg_wrap">
      <FullScreenHeader v-if="isFullScreen && showDobuleScreen && isPortal" class="fullscreen_header" />

      <!-- 对话列表 -->
      <div class="relative flex-1 mb-[10px] main_dlg">
        <ChatList ref="chatListRef" />
      </div>
      <!-- <AiFooter /> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, defineOptions } from 'vue';
import ChatList from '@/components/chatList/index.vue';
import AiFooter from '@/components/aiFooter/index.vue';
import '@seeyon/seeyon-comi-plugins-library/dist/seeyon-comi-plugins-library.css';
import FullScreenHeader from '@/components/fullscreenHeader/index.vue';
import { useGlobal } from '@/stores/global';
import { useChatList } from '@/stores/chatList';
defineOptions({
  name: 'AiDialogue',
});
const isPortal = inject('isPortal');
const chatListStore = useChatList();
// 侧边栏全屏
const isFullScreen = computed(() => {
  return useGlobal().globalState.isFullScreen;
});
// 展示双屏
const showDobuleScreen = computed(() => {
  return chatListStore.dynamicData.dobuleScreenData.show;
});
</script>

<style scoped lang="less">
.new_dlg_wrap {
  width: 100%;

  .fullscreen_header {
    padding: 0;
  }
  .main_dlg {
    overflow-y: auto;
  }
}
.as_ck_btn {
  max-width: 92px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
