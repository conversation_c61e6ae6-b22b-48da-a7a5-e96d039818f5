import { openDobuleScreen } from '@/utils/storesUtils'
import {
  message
} from 'ant-design-vue';

// 全局类型声明
declare global {
  interface Window {
    _ctxPath?: string
    $?: any
    getA8Top?: () => any
    openCtpWindow?: (params: any) => void
    CsrfGuard?: {
      getUrlSurffix: (url: string) => string
    }
    CtpUiMessage?: any
    getCtpTop?: () => any
  }
}

/**
 * 菜单打开处理器
 */
export class MenuOpenHandler {
  private portalId: string
  private spaceId: string

  constructor(portalId: string, spaceId: string) {
    this.portalId = portalId
    this.spaceId = spaceId
  }

  /**
   * 获取URL参数
   */
  private getUrlParams(url: string): Record<string, string> {
    const params: Record<string, string> = {}
    const queryString = url.split('?')[1]
    if (queryString) {
      const pairs = queryString.split('&')
      for (const pair of pairs) {
        const [key, value] = pair.split('=')
        params[key] = decodeURIComponent(value || '')
      }
    }
    return params
  }

  /**
   * 获取上下文路径
   */
  private getCtxPath(): string {
    return (window as any)._ctxPath || '/seeyon'
  }

  /**
   * 新建协同事项分离，自由协同打开
   */
  newFreeCollaboration(url: string): boolean {
    if (url.indexOf("/collaboration/collaboration.do?method=newColl") === -1) {
      return false
    }

    const urlParams = this.getUrlParams(url)
    if (urlParams && (urlParams.fromNewItem === 'true' || urlParams.fromNewCollaboration === 'true' || urlParams.from === 'peopleCard')) {
      // 从新建事项来的或者人员卡片，不管模版协同还是自由协同都放行
      return false
    }

    let freeColl = true // 自由协同
    for (const key in urlParams) {
      if (key === 'templateId' && (urlParams[key] && urlParams[key] !== "")) {
        freeColl = false
        break
      }
      if ((key === 'summaryId' || key === 'affairId') && urlParams[key] !== "") {
        // 如果有affairId或者summaryId就不是新建
        freeColl = false
        break
      }
      if (key === 'cashId' && urlParams[key] !== "") {
        // 转发协同
        freeColl = false
        break
      }
    }

    if (!freeColl) {
      // 不是自由协同
      return false
    }

    // 检查IE版本
    const userAgent = navigator.userAgent.toLowerCase()
    const isIE = userAgent.match(/rv:([\d.]+)\) like gecko/) || userAgent.match(/msie ([\d.]+)/)
    const belowIE10 = isIE && isIE.length && parseInt(isIE[1]) < 10

    if (belowIE10) {
      // IE10以下使用新窗口打开
      const ctxPath = this.getCtxPath()
      window.open(ctxPath + "/collaboration/collaboration.do?method=newColl&rescode=F01_newColl", "newWindow")
    } else {
      // 使用对话框打开新建协同
      const ctxPath = this.getCtxPath()
      const pcNewColl = (window as any).$.dialog({
        id: "pc_new_item",
        url: ctxPath + "/common/new-item/dist/index.html",
        isHead: false,
        width: 1040,
        height: 700,
        isClear: false,
        transParams: {
          callback: (result: any) => {
            const url = result.url
            const windowOpener = (window as any).getA8Top ? (window as any).getA8Top() : (window as any).parent.getA8Top()
            if (windowOpener != null && windowOpener.openCtpWindow) {
              windowOpener.openCtpWindow({ url: ctxPath + url, comiOrigin: 'v5' })
            } else {
              window.open(ctxPath + url, 'newWindow')
            }
          },
          closeBackFun: () => {
            pcNewColl.close()
          }
        },
        closeParam: {
          show: false,
          handler: () => {
            // 关闭处理
          }
        }
      })
    }

    return true
  }

  /**
   * 文档协作新建
   */
  openNewDolColl(): void {
    const ctxPath = this.getCtxPath()
    const newCollPage = (window as any).$.dialog({
      id: "new_coll_page",
      url: ctxPath + "/apps_res/docCollaboration/create-collaboration.html",
      title: (window as any).$.i18n("collaboration.doc.task.new"),
      width: 600,
      height: 650,
      transParams: {
        isNew: true
      },
      buttons: [
        {
          text: (window as any).$.i18n("common.button.ok.label"),
          handler: () => {
            const returnValue = newCollPage.getReturnValue()
            if (returnValue) {
              const params = {
                url: ctxPath + "/rest/docCol/saveOrUpdate",
                type: 'post',
                contentType: "application/json",
                data: JSON.stringify(returnValue),
                success: (res: any) => {
                  message.success((window as any).$.i18n("collaboration.doc.new.create.success"));
                  newCollPage.close()
                },
                error: (res: any) => {
                  message.error((window as any).$.i18n("collaboration.doc.list.save.failed"));
                }
              }
              ;(window as any).$.ajax(params)
            }
          }
        },
        {
          text: (window as any).$.i18n("common.button.cancel.label"),
          handler: () => {
            newCollPage.close()
          }
        }
      ]
    })
  }

  /**
   * 是否公文自由流程新建url
   */
  private isNewGovdoc(url: string, urlParams: Record<string, string>): boolean {
    if (url.indexOf("/govdoc/govdoc.do?method=newGovdoc") === -1 &&
      (url.indexOf("/resourceEngine/dataShow.do?method=showConfigData") === -1 || url.indexOf("action=newGovdoc") === -1)) {
      return false
    }
    if (urlParams && urlParams.formgovdocTemplateSelect === 'true') {
      // 从新模版来，直接放行
      return false
    }
    let freeEdoc = true // 自由公文
    for (const key in urlParams) {
      if (key === 'templateId' && (urlParams[key] && urlParams[key] !== "")) {
        freeEdoc = false
        break
      }
      if ((key === 'summaryId' || key === 'affairId') && urlParams[key] !== "") {
        // 如果有affairId或者summaryId就不是新建
        freeEdoc = false
        break
      }
      if (key === 'isQuickSend' && urlParams[key] !== "") {
        freeEdoc = false
        break
      }
    }
    return freeEdoc
  }

  /**
   * 获取公文模板数量
   */
  private getGovdocTemplate(subApp: string, callback: (categoryId: string, data: any) => void): void {
    let categoryId = ''
    if (subApp === '1') { // 发文
      categoryId = '401'
    } else if (subApp === '2') {
      categoryId = '402'
    } else if (subApp === '3') {
      categoryId = '404'
    }

    const ctxPath = this.getCtxPath()
    ;(window as any).$.ajax({
      url: ctxPath + '/rest/template/edocTemplate?option.n_a_s=1&categoryId=' + categoryId,
      dataType: 'json',
      type: 'get',
      success: (ret: any) => {
        if (ret.code != 0) {
          ;(window as any).$.alert(ret.message)
          callback('', null)
          return
        }
        callback(categoryId, ret.data)
      }
    })
  }

  /**
   * 公文打开
   */
  private govdocOpenFun(url: string): void {
    const windowOpener = (window as any).getA8Top ? (window as any).getA8Top() : (window as any).parent.getA8Top()
    if (windowOpener) {
      windowOpener.openCtpWindow({ url: url, comiOrigin: 'v5' })
    } else {
      window.open(url)
    }
  }

  /**
   * 公文模板选择
   */
  govdocTemplateSelect(url: string): boolean {
    const userAgent = navigator.userAgent.toLowerCase()
    const isIE = userAgent.match(/rv:([\d.]+)\) like gecko/) || userAgent.match(/msie ([\d.]+)/)
    if (isIE && isIE.length && parseInt(isIE[1]) < 10) {
      // ie10以下不处理
      return false
    }

    const urlParams = this.getUrlParams(url)
    if (!this.isNewGovdoc(url, urlParams)) {
      // 不是新建自由流程公文不做处理
      return false
    }

    const subApp = urlParams["sub_app"]
    // 不是发文拟文、收文登记、签报拟文
    if (!subApp || (subApp !== '1' && subApp !== '2' && subApp !== '3')) {
      return false
    }

    this.getGovdocTemplate(subApp, (categoryId: string, data: any) => {
      const canSelfCreateFlow = data.canSelfCreateFlow === true || data.canSelfCreateFlow === 'true'
      const templateSize = data.templateSize
      const ctxPath = this.getCtxPath()

      // 直接打开新建公文，两种情况，1.没有模板，可以新建自由流程 2. 有1模板，并且不可新建自由流程
      if (templateSize == 0 && canSelfCreateFlow === false) {
        ;(window as any).$.alert((window as any).$.i18n('govdoc.content.template.typeSetting.tip'))
        return
      }
      if (templateSize == 0 && canSelfCreateFlow === true) {
        this.govdocOpenFun(ctxPath + '/govdoc/govdoc.do?method=newGovdoc&sub_app=' + subApp)
        return
      }
      if (templateSize == 1 && canSelfCreateFlow === false) {
        this.govdocOpenFun(ctxPath + '/govdoc/govdoc.do?method=newGovdoc&sub_app=' + subApp + '&from=template&templateId=' + data.uniqueTemplate.id)
        return
      }

      const pcNewEdoc = (window as any).$.dialog({
        id: "edoc_new_template",
        url: ctxPath + "/app/edoc/template/selector.html?categoryId=" + categoryId + "&subApp=" + subApp,
        isHead: false,
        width: 1020,
        height: 700,
        isClear: false,
        transParams: {
          data: data,
          callback: (result: any) => {
            this.govdocOpenFun(ctxPath + result.url)
          },
          closeBackFun: () => {
            pcNewEdoc.close()
          }
        }
      })
    })

    return true
  }

  /**
   * 打开新窗口
   */
  private openCtpWindow(params: any): void {
    const newParams = {
      ...params,
      comiOrigin: 'v5'
    }
    if ((window as any).openCtpWindow) {
      ;(window as any).openCtpWindow(newParams)
    } else {
      window.open(params.url, 'newWindow')
    }
  }

  /**
   * 添加CSRF保护
   */
  private addCsrfProtection(url: string): string {
    if ((window as any).CsrfGuard) {
      return url + (window as any).CsrfGuard.getUrlSurffix(url)
    }
    return url
  }

  /**
   * 处理菜单打开
   */
  handleMenuOpen(oUrl: string, id?: string, target?: string, resourceCode?: string, _obj?: any, tabName?: string, from?: string, styleObj?: any): void {
    let url = oUrl
    const ctxPath = (window as any)._ctxPath || '/seeyon';
    // URL处理
    if (url && url.indexOf(ctxPath) == 0) {
      const uArr = url.split(ctxPath)
      const realUrl = uArr[1] || ''
      if (realUrl && realUrl.indexOf('www.') == 0) {
        url = 'http://' + realUrl
      }
    }

    // 添加菜单ID参数
    if (id) {
      const idIndex = id.indexOf('_') > -1 ? Number(id.indexOf('_')) + 1 : ''
      const idStr = idIndex ? id.substring(idIndex) : id

      if (url.indexOf('?') == -1) {
        // 兼容/seeyon12345678 情况
        if (/^\/seeyon[0-9]{5,}/.test(url) || /^\/seeyon-[0-9]{5,}/.test(url)) {
          url += "&"
        } else {
          url += "?"
        }
      } else {
        url += "&"
      }
      url += "recommendMenuId=" + idStr + "&menuSummary=add"
    }

    const oldUrl = url

    // 新建协同事项分离
    if (url.indexOf("/collaboration/collaboration.do?method=newColl") !== -1 && this.newFreeCollaboration(url)) {
      return
    }

    // 文档协作的新建
    if (url.indexOf("docCollaboration/create-collaboration") > -1) {
      this.openNewDolColl()
      return
    }

    // 公文新建
    if ((url.indexOf("/govdoc/govdoc.do?method=newGovdoc") !== -1 ||
      (url.indexOf("/resourceEngine/dataShow.do?method=showConfigData") !== -1 && url.indexOf("action=newGovdoc") !== -1)) && this.govdocTemplateSelect(url)) {
      return
    }

    // 部门空间管理
    const departmentSpaceManageUrl = ctxPath + "/showDepartmentSpaceDesigner.do?spaceId="
    if (url.indexOf(departmentSpaceManageUrl) == 0) {
      const spaceId = url.substring(departmentSpaceManageUrl.length)
      // 调用编辑部门空间方法
      return
    }

    // 空间URL处理
    const showThemSpaceUrl = "/portal/spaceController.do?method=showThemSpace&belongPortalId="
    if (url.indexOf(showThemSpaceUrl) >= 0) {
      const beginIndex = url.indexOf(showThemSpaceUrl) + showThemSpaceUrl.length
      const params0Str = url.substring(beginIndex)
      const params0Arr = params0Str.split("&")
      const myBelongPortalId = params0Arr[0]
      const mySpaceId = params0Arr[1]
      if (myBelongPortalId != this.portalId) {
        url = ctxPath + "/main.do?method=main&isFromComi=1&ignoreComi=1&subPortal=true&portalId=" + myBelongPortalId + "&" + mySpaceId
        target = "newWindow"
      }
    }

    // 添加门户ID参数
    const signalUrl = "/seeyonhttp"
    if (url.indexOf(signalUrl) == -1) {
      if (url.indexOf('?') == -1) {
        url += "?"
      } else {
        url += "&"
      }
      url += "portalId=" + this.portalId
    }

    // 添加资源代码
    if (resourceCode) {
      if (url.indexOf("_resourceCode") == -1) {
        if (url.indexOf('?') == -1) {
          url += "?"
        } else {
          url += "&"
        }
        url += "_resourceCode=" + resourceCode
      }
    }

    // URL协议处理
    const checkUrl1 = ctxPath + "http"
    const checkUrl2 = ctxPath + "https"
    if (url.indexOf(checkUrl1) == 0) {
      url = url.substring(7, url.length)
    } else if (url.indexOf(checkUrl2) == 0) {
      url = url.substring(8, url.length)
    }

    // 添加CSRF保护
    url = this.addCsrfProtection(url)

    if(resourceCode && (resourceCode === 'F09_meetingInstant' || resourceCode.indexOf('_meetingInstant') !== -1)) {
      this.openCtpWindow({
        'url': `/seeyon/main.do?method=main&isFromComi=1&ignoreComi=1&comiTargetUrl=F09_meetingInstant`,
        'id': id
      })
      return
    }

    // 判断打开类型
    if (target === "newWindow") {
      if (url.indexOf("showHelp") != -1) {
        this.openCtpWindow({
          'url': `/seeyon/main.do?method=main&isFromComi=1&ignoreComi=1&comiTargetUrl=showHelp`,
          'id': 'vPortal.CurrentUser.id'
        })
        return
      }else if (url.indexOf("newPlan") != -1) {
        this.openCtpWindow({
          'url': url,
          'id': 'vPortal.CurrentUser.id'
        })
        return
      } else if (url.indexOf("dataShow.do") != -1) {
        this.openCtpWindow({
          'url': url,
          'pid': id
        })
        return
      } else {
        this.openCtpWindow({
          'url': url
        })
        return
      }
    } else if (target === "portal") {
      let portalId = oldUrl
      if (oldUrl.indexOf(ctxPath) == 0) {
        portalId = oldUrl.substring(7, oldUrl.length)
      }
      if (id == this.portalId) {
        // $.alert($.i18n('portal.currentportalIsOpened'))
        return
      } else {
        const _openUrl = ctxPath + "/main.do?method=main&isFromComi=1&ignoreComi=1&portalId=" + portalId + "&subPortal=true"
        this.openCtpWindow({
          'url': this.addCsrfProtection(_openUrl),
          'id': portalId
        })
        return
      }
    } else if (target == 'dialog') {
      // 弹出框方式打开
      const urlParams = this.getUrlParams(url)
      const showTitle = urlParams.st == '0' || true

      const dialogParams: any = {}
      dialogParams.id = "menuDialog-" + id
      dialogParams.url = url
      if (showTitle) {
        dialogParams.title = _obj?.getAttribute("title")
      } else {
        dialogParams.isHead = false
      }
      dialogParams.width = urlParams.w || '1040'
      dialogParams.height = urlParams.h || '700'
      // $.dialog(dialogParams)
      return
    } else {
      // 默认iframe方式打开
      // 链接连接处理
      // if (url.indexOf("linkConnectForMenu") != -1) {
      //   url = url + "&target=mainFrame"
      // }
      this.openCtpWindow({
        'url': `/seeyon/main.do?method=main&isFromComi=1&ignoreComi=1&comiTargetUrl=${encodeURIComponent(url)}`,
        'id': id
      });
      // 使用openDobuleScreen打开
      // openDobuleScreen(url, 'iframe')
      // 临时修改，等待代琪的oa变更
      // this.openCtpWindow({
      //   'url': url
      // })
    }
  }
}

// 创建单例实例
let menuOpenHandlerInstance: MenuOpenHandler | null = null

export function createMenuOpenHandler(portalId: string, spaceId: string): MenuOpenHandler {
  if (!menuOpenHandlerInstance) {
    menuOpenHandlerInstance = new MenuOpenHandler(portalId, spaceId)
  }
  return menuOpenHandlerInstance
}

export function getMenuOpenHandler(): MenuOpenHandler | null {
  return menuOpenHandlerInstance
}

export default MenuOpenHandler
