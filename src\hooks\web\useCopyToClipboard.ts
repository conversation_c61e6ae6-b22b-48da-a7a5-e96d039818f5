import { ref, watch } from 'vue'

import { isDef } from '@/utils/is'
interface Options {
  target?: HTMLElement
}
export function useCopyToClipboard(initial?: string) {
  const clipboardRef = ref(initial || '')
  const isSuccessRef = ref(false)
  const copiedRef = ref(false)

  watch(
    clipboardRef,
    (str?: string) => {
      if (isDef(str)) {
        copiedRef.value = true
        isSuccessRef.value = copyTextToClipboard(str)
      }
    },
    { immediate: !!initial, flush: 'sync' },
  )

  return { clipboardRef, isSuccessRef, copiedRef }
}

export function copyTextToClipboard(input: string, { target = document.body }: Options = {}): Promise<boolean> {
  // 检测是否支持现代剪贴板API
  if (navigator.clipboard && window.isSecureContext) {
    // 使用现代剪贴板API（Safari支持）
    return Promise.race([
      navigator.clipboard.writeText(input).then(() => true),
      new Promise<boolean>((_, reject) => 
        setTimeout(() => reject(new Error('复制超时')), 3000)
      )
    ]).catch((error) => {
      console.warn('现代剪贴板API失败，降级到传统方法:', error);
      return fallbackCopyTextToClipboard(input, { target });
    })
  } else {
    // 降级到传统方法
    return fallbackCopyTextToClipboard(input, { target })
  }
}

// 传统的复制方法，针对Safari进行优化
function fallbackCopyTextToClipboard(input: string, { target = document.body }: Options = {}): Promise<boolean> {
  const element = document.createElement('textarea')
  const previouslyFocusedElement = document.activeElement

  element.value = input

  // Safari特殊处理
  element.setAttribute('readonly', '')
  element.style.position = 'absolute'
  element.style.left = '-9999px'
  element.style.top = '-9999px'
  element.style.fontSize = '12pt'
  element.style.border = '0'
  element.style.padding = '0'
  element.style.margin = '0'
  element.style.contain = 'strict'

  const selection = document.getSelection()
  let originalRange
  if (selection && selection.rangeCount > 0) {
    originalRange = selection.getRangeAt(0)
  }

  target.append(element)
  
  // Safari需要先focus再select
  element.focus()
  element.select()

  // 确保选择了全部文本
  element.selectionStart = 0
  element.selectionEnd = input.length

  let isSuccess = false
  try {
    // 检测Safari浏览器
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
    
    if (isSafari) {
      // Safari特殊处理：创建一个临时的可编辑div
      const tempDiv = document.createElement('div')
      tempDiv.contentEditable = 'true'
      tempDiv.style.position = 'absolute'
      tempDiv.style.left = '-9999px'
      tempDiv.style.top = '-9999px'
      tempDiv.style.opacity = '0'
      tempDiv.textContent = input
      
      target.appendChild(tempDiv)
      
      // 选择内容
      const range = document.createRange()
      range.selectNodeContents(tempDiv)
      selection?.removeAllRanges()
      selection?.addRange(range)
      
      // 尝试复制
      isSuccess = document.execCommand('copy')
      
      // 清理
      target.removeChild(tempDiv)
    } else {
      isSuccess = document.execCommand('copy')
    }
  } catch (e: any) {
    console.error('Copy failed:', e)
    isSuccess = false
  }

  element.remove()

  if (originalRange && selection) {
    selection.removeAllRanges()
    selection.addRange(originalRange)
  }

  if (previouslyFocusedElement) {
    ;(previouslyFocusedElement as HTMLElement).focus()
  }
  
  return Promise.resolve(isSuccess)
}
