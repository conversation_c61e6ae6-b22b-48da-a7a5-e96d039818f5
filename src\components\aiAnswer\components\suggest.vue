<template>
  <!-- 非全屏反馈弹窗 -->
  <template v-if="!uGlobal.globalState.isFullScreen">
    <SuggestBtn :isWhichActive="isWhichActive" :goPointThis="goPointThis" />
    <Modal
      v-model:open="isOpen"
      width="370px"
      @cancel="closeModal"
      wrap-class-name="suggest_modal"
      centered
      :maskStyle="{
        background: 'rgba(255, 255, 255, 0.5)',
        'backdrop-filter': 'blur(2px)',
      }"
    >
      <template #title>
        <div class="suggest_top">
          <div class="suggest_title">{{ suggestTitle }}</div>
        </div>
      </template>
      <SuggestContent
        :adviceTags="adviceTags"
        :suggestContent="suggestContent"
        :goSecThisAdvice="goSecThisAdvice"
      />
      <Textarea
        class="suggest_textarea"
        v-model:value="suggestContent"
        @focus="isFocused = true"
        @blur="isFocused = false"
        :placeholder="textPlaceholder"
      ></Textarea>
      <template #footer>
        <Button type="primary" @click="goSubmit" :disabled="!isHaveActAdvice">提交</Button>
      </template>
    </Modal>
  </template>
  <!-- 全屏反馈弹窗 -->
  <template v-else>
    <!-- 用户建议 -->
    <Popover
      placement="topRight"
      :arrow="false"
      trigger="click"
      :open="isOpen"
      overlayClassName="suggest_popover"
      :mouseLeaveDelay="0.1"
      autoAdjustOverflow
    >
      <!-- 点踩 -->
      <SuggestBtn :isWhichActive="isWhichActive" :goPointThis="goPointThis" />
      <template #title>
        <div class="suggest_top">
          <div class="suggest_title">{{ suggestTitle }}</div>
          <i class="iconfont ai-icon-cha suggest_close" @click="isOpen = false" />
        </div>
      </template>
      <template #content>
        <div class="suggest_btm">
          <SuggestContent
            :adviceTags="adviceTags"
            :suggestContent="suggestContent"
            :goSecThisAdvice="goSecThisAdvice"
          />
          <Textarea
            class="suggest_textarea"
            v-model:value="suggestContent"
            @blur="isFocused = false"
            :placeholder="textPlaceholder"
          ></Textarea>
          <div class="suggest_btn_box">
            <Button
              @click="goSubmit"
              type="primary"
              :disabled="!isHaveActAdvice"
              class="suggest_btn"
            >
              提交
            </Button>
          </div>
        </div>
      </template>
    </Popover>
  </template>

  <!-- 点赞 -->
  <!-- <span
    :class="{ up_btn: true, ud_ac_btn: isWhichActive == 0 }"
    @click="goPointThis(0, transAiInfo.messageId)"
  >
    <i v-if="isWhichActive == 0" class="iconfont ai-icon-dianzan-surface"></i>
    <i v-else class="iconfont ai-icon-dianzan-line"></i>
  </span> -->
</template>
<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from 'vue';
import { message, Popover, Textarea, Modal, Button } from 'ant-design-vue';
import { toChangeAnswerStatus, cateAsistantLogs } from '@/api/common/index';
import type { TypeOperateAnswerParams } from '@/types/api.ts';
import { useGlobal } from '@/stores/global';
import SuggestBtn from './suggestBtn.vue';
import SuggestContent from './suggestContent.vue';
const isWhichActive = ref<undefined | 0 | 1>(undefined); // 锚点态
const props = defineProps({
  transAiInfo: {
    type: Object,
    default: () => {},
  },
});

const isOpen = ref<boolean>(false); //建议框子开关状态
const suggestContent = ref<string>(''); //建议内容
const uGlobal = useGlobal();
const isFocused = ref<boolean>(false);
const adviceTags = ref([
  { id: 1, name: '没有帮助', isUnActive: false },
  { id: 2, name: '结果不理想', isUnActive: false },
  { id: 3, name: '信息虚假不真实', isUnActive: false },
  { id: 4, name: '有害/不安全', isUnActive: false },
  { id: 5, name: '没有理解我的问题', isUnActive: false },
  { id: 6, name: '没有完成任务', isUnActive: false },
]);
const isHaveActAdvice = computed(() => {
  const isHaveAdv =
    adviceTags.value.some((item) => item.isUnActive) || suggestContent.value.trim().length > 0;
  return isHaveAdv;
});
const textPlaceholder = '如果您愿意的话，欢迎说说您的想法，CoMi会努力做的更好！';
const suggestTitle = '很抱歉，CoMi让您有了不好的感受';

// 点踩/点赞-enent
const goPointThis = (type: 0 | 1) => {
  // 点踩
  if (type == 1) {
    if (isWhichActive.value == 1) {
      isWhichActive.value = undefined;
      isOpen.value = false;
      return;
    }
    isOpen.value = true;
  }
  // 点赞
  if (type == 0) {
    isWhichActive.value = type;
    changingAnswerStatus({
      type: type,
      messageId: props.transAiInfo.data.messageId,
      content: '',
    });
  }
};
// 提交
const goSubmit = () => {
  // 获取选中标签的内容
  const suggest_labels_str = adviceTags.value
    .filter((item) => item.isUnActive)
    .map((item) => item.name)
    .join(',');
  console.log('suggest_labels_str', suggest_labels_str);
  let sunmit_content = '';
  if (suggest_labels_str.length > 0) {
    sunmit_content = suggest_labels_str + '，';
  }
  sunmit_content += suggestContent.value;

  // 意见截断500字
  sunmit_content = sunmit_content.slice(0, 500);
  if (isHaveActAdvice.value) {
    changingAnswerStatus({
      type: 1,
      messageId: props.transAiInfo.data.messageId,
      content: sunmit_content,
    });
    isWhichActive.value = 1;
  }
};

// 关闭modal
const closeModal = () => {
  isOpen.value = false;
};

// 点赞-点踩-api
const changingAnswerStatus = async (data: TypeOperateAnswerParams) => {
  try {
    const res: any = await toChangeAnswerStatus(data);
    if (res && res.code == 0 && res.data) {
      message.success('感谢您的反馈，我们将会持续改进~');
      // 提交建议
      isOpen.value = false;
    }
  } catch (error) {
    console.log('error___', error);
  }
  suggestContent.value = '';
};
// 选择某个意见
const goSecThisAdvice = (d: string | number, inx: number) => {
  adviceTags.value[inx].isUnActive = !adviceTags.value[inx].isUnActive;
};
// 监视弹窗关闭
watch(isOpen, (newVal: boolean) => {
  if (!newVal) {
    suggestContent.value = '';
    adviceTags.value.forEach((item) => {
      item.isUnActive = false;
    });
  }
});

// 关闭弹窗
const closePopover = () => {
  isOpen.value = false;
};

onMounted(() => {
  window.addEventListener('resize', closePopover);
});
onUnmounted(() => {
  window.removeEventListener('resize', closePopover);
});
</script>
<style lang="less">
.suggest_modal {
  .ant-modal-content {
    padding: 12px;

    .ant-modal-header {
      margin-bottom: 12px;
    }

    .ant-modal-close {
      top: 12px;

      .ant-modal-close-x {
        line-height: 14px;
      }
    }

    .suggest_title {
      font-family: PingFang SC;
      font-weight: @font-weight-500;
      font-size: 16px;
      line-height: 22px;
      letter-spacing: 0%;
    }
    .suggest_textarea {
      height: 126px;
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
      border: 1px solid #f7f7f7;
      border-radius: 8px;
      background: #f7f7f7;
      resize: none;
      overflow: auto;
      outline: 0;
      -webkit-appearance: none;
      -webkit-user-select: text;
      font-size: 14px;
      caret-color: @sky;
      &:focus,
      &:hover {
        border: 1px solid @sky;
      }
    }
  }
}
// 全屏反馈弹窗样式
.suggest_popover {
  padding: 8px;
  border-radius: 8px;
  .ant-popover-inner {
    padding: 12px;
    width: 370px;
    height: 317px;

    .ant-popover-title {
      margin-bottom: 12px;
    }

    .ant-popover-inner-content {
      width: 100% !important;
      max-width: 100% !important;
    }
  }

  .suggest_top {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .suggest_title {
      //styleName: 14px/Regular;
      font-family: PingFang SC;
      font-weight: @font-weight-500;
      font-size: 16px;
      line-height: 22px;
      letter-spacing: 0%;
    }

    .suggest_close {
      cursor: pointer;
      font-size: 20px;
      color: #00000099;
    }
  }

  .suggest_btm {
    display: flex;
    flex-direction: column;

    .suggest_textarea {
      width: 100%;
      height: 126px;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #f5f5f5;
      resize: none;
      box-shadow: none;
      background-color: #f5f5f5;
      caret-color: @sky;
      &:focus,
      &:hover {
        border: 1px solid @sky;
      }
    }
    .suggest_btn_box {
      margin-top: 12px;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;

      .suggest_tip {
        color: red;
        font-size: 12px;
      }
    }

    .suggest_btn {
      float: right;
      font-family: PingFang SC;
      font-weight: @font-weight-400;
      font-size: 14px;
      line-height: 22px;
      background-color: @sky;

      &:disabled {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }
}
.suggest_textarea:focus {
  box-shadow: none !important;
}
</style>
