<template>
  <div :class="['common-tag', typeClass]" :style="style">
    <i v-if="iconClass" :class="['iconfont', iconClass]" />
    {{ content }}
  </div>
</template>
<script setup lang="ts">
import {ref, computed} from "vue"

const props = defineProps({
  type: {
    type: String,
    default: 'primary', // violet、primary、success、error
  },
  content: {
    type: String,
    default: '',
  },
  style: {
    type: Object,
   },
  iconClass: {
    type: String,
    default: '',
  },
  showBorder: {
    type: Boolean,
    default: true,
  }
})

  const typeClass = computed(() => {
    return `${props.type}-color ${props.type}-bg` + props.showBorder ? `${props.type}-border-color` : ''
  })

</script>
<style lang="less" scoped>
  .common-tag {
    background: @primary-bg;
    border-radius: 3px;
    border: 1px solid @primary-color;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: @primary-color;
    height: 20px;
    padding: 0 10px;
    .iconfont {
      font-size: 12px;
    }
  }
</style>
