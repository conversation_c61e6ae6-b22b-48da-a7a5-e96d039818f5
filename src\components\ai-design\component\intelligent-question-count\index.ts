import type { App } from 'vue';
import IntelligentQuestionCount from './IntelligentQuestionCount.vue';

IntelligentQuestionCount.name = 'IntelligentQuestionCount';

IntelligentQuestionCount.install = function (app: App) {
  app.component(IntelligentQuestionCount.name as string, IntelligentQuestionCount);
  return app;
};

export default IntelligentQuestionCount as typeof IntelligentQuestionCount & {
  install(app: App): void;
};;
